{"permissions": {"allow": ["mcp__prmthttp__promptx_init", "mcp__prmthttp__promptx_welcome", "mcp__prmthttp__promptx_action", "<PERSON><PERSON>(claude mcp list)", "<PERSON><PERSON>(env)", "mcp__promptx__promptx_welcome", "mcp__promptx__promptx_init", "mcp__promptx__promptx_action", "mcp__promptx__promptx_learn", "mcp__promptx__promptx_remember", "Bash(find . -name \"*TrainReceptionChatroomServiceImplTest*\" -type f)", "Bash(mvn test -Dtest=TrainReceptionChatroomServiceImplTest)", "Bash(mvn clean compile -q)", "Bash(mvn test-compile -q)", "Bash(java -version)", "Bash(mvn -version)", "Bash(timeout 300 mvn test -Dtest=TrainReceptionChatroomServiceImplTest)", "Bash(ls -la ./target/surefire-reports/)", "<PERSON><PERSON>(find . -name \"*TrainChatroomTaskMapper*\" -type f)", "<PERSON><PERSON>(mvn clean -q)", "Bash(mvn test -Dtest=TrainReceptionChatroomServiceImplTest -Dspring.profiles.active=home)", "Bash(mkdir -p /home/<USER>/myaiprojects/yiyi_ai_playground/docs/staff)", "Bash(mvn test -Dtest=TrainStaffTagServiceImplTest -Dspring.profiles.active=home)", "Bash(mvn test -Dtest=TrainStaffServiceImplTest -Dspring.profiles.active=home)", "Bash(mv /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/staff/TrainStaffController.java /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/staff/TrainStaffManagementController.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/TrainStaffController.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/TrainStaffService.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/impl/TrainStaffServiceImpl.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/mapper/TrainStaffMapper.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/resources/mapper/TrainStaffMapper.xml)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/impl/TrainStaffServiceImplTest.java)", "Bash(git restore src/main/java/com/yiyi/ai_train_playground/controller/TrainStaffController.java src/main/java/com/yiyi/ai_train_playground/mapper/TrainStaffMapper.java src/main/java/com/yiyi/ai_train_playground/service/TrainStaffService.java src/main/java/com/yiyi/ai_train_playground/service/impl/TrainStaffServiceImpl.java src/main/resources/mapper/TrainStaffMapper.xml src/test/java/com/yiyi/ai_train_playground/service/impl/TrainStaffServiceImplTest.java)", "Bash(ls -la src/main/java/com/yiyi/ai_train_playground/controller/TrainStaffController.java)", "Bash(ls -la src/main/java/com/yiyi/ai_train_playground/service/TrainStaffService.java)", "Bash(rm -rf src/main/java/com/yiyi/ai_train_playground/service/staff/TrainStaffService.java src/main/java/com/yiyi/ai_train_playground/service/staff/impl/TrainStaffServiceImpl.java src/test/java/com/yiyi/ai_train_playground/service/staff/impl/TrainStaffServiceImplTest.java)", "Bash(mv src/main/java/com/yiyi/ai_train_playground/service/TrainStaffService.java src/main/java/com/yiyi/ai_train_playground/service/staff/)", "Bash(mv src/main/java/com/yiyi/ai_train_playground/service/impl/TrainStaffServiceImpl.java src/main/java/com/yiyi/ai_train_playground/service/staff/impl/)", "Bash(mv src/test/java/com/yiyi/ai_train_playground/service/impl/TrainStaffServiceImplTest.java src/test/java/com/yiyi/ai_train_playground/service/staff/impl/)", "Bash(mkdir -p src/main/java/com/yiyi/ai_train_playground/mapper/staff src/main/java/com/yiyi/ai_train_playground/controller/staff)", "Bash(mv src/main/java/com/yiyi/ai_train_playground/mapper/TrainStaffMapper.java src/main/java/com/yiyi/ai_train_playground/mapper/staff/)", "Bash(mv src/main/java/com/yiyi/ai_train_playground/controller/TrainStaffController.java src/main/java/com/yiyi/ai_train_playground/controller/staff/)", "Bash(mkdir -p src/main/resources/mapper/staff)", "Bash(mv src/main/resources/mapper/TrainStaffMapper.xml src/main/resources/mapper/staff/)", "Bash(ls -la src/main/java/com/yiyi/ai_train_playground/controller/staff/)", "Bash(rm -f src/main/java/com/yiyi/ai_train_playground/controller/staff/TrainStaffManagementController.java)", "Bash(rm -rf src/main/java/com/yiyi/ai_train_playground/dto/staff/TrainStaff*)", "Bash(rm -rf src/main/java/com/yiyi/ai_train_playground/entity/staff/TrainStaff*)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/staff/TrainStaffController.java)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/staff/TrainStaffService.java)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/staff/impl/TrainStaffServiceImpl.java)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/mapper/staff/TrainStaffMapper.java)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/resources/mapper/staff/TrainStaffMapper.xml)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/staff/impl/TrainStaffServiceImplTest.java)", "Bash(mkdir -p /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/entity/staff)", "Bash(mkdir -p /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/dto/staff)", "Bash(mvn compile -q)", "Bash(find src/main/java/com/yiyi/ai_train_playground/entity -name \"*Tag*\" -type f)", "Bash(mkdir -p /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/resources/mapper/staff)", "Bash(mvn clean install -DskipTests)", "mcp__promptx__promptx_recall", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"USE ai_train_playground; DESCRIBE train_staff;\")", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"SHOW DATABASES;\")", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 yiyi_ai_db -e \"SHOW TABLES LIKE ''train_staff%'';\")", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 yiyi_ai_db -e \"DESCRIBE train_staff;\")", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"USE ai_train_playground; DESC train_staff;\")", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; DESC train_staff;\")", "Bash(grep -A 10 -B 5 \"Tests run:\" /home/<USER>/myaiprojects/yiyi_ai_playground/target/surefire-reports/*.txt)", "Bash(mvn test -Dtest=StaffAuthServiceImplTest -Dspring.profiles.active=home)", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"DESC yiyi_ai_db.train_staff;\")", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/dto/task/MyTasksDTO.java)", "Bash(mvn compile -DskipTests)", "Bash(mvn test -Dtest=JwtAuthenticationIntegrationTest -Dspring.profiles.active=home)", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"SELECT username, password_hash FROM yiyi_ai_db.train_staff WHERE username = ''user1''\")", "Bash(curl -X GET \"http://localhost:8081/api/chatrooms/my-tasks\" -H \"Authorization: Bearer ey<PERSON>hbGciOiJIUzI1NiJ9.****************************************************************************************************.vs3f3KlLmzLEoBE9VzF1pO46Lxl8Xo_qgUpCqRKxnJw\")", "Bash(curl -X GET \"http://localhost:8081/actuator/health\")", "<PERSON><PERSON>(curl -X POST \"http://localhost:8081/api/staff/auth/login\" )", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"username\"\": \"\"staff001\"\",\n    \"\"password\"\": \"\"123456\"\",\n    \"\"rememberMe\"\": false\n  }')", "Bash(pkill -f \"spring-boot:run\")", "<PERSON><PERSON>(kill -9 35004)", "Bash(curl -X GET \"http://localhost:8081/api/chatrooms/my-tasks\" )", "Bash(-H \"Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************.3E4nRN7_6H6DvEYhz2j_hj_hl2wX4H7qwvIlVXAkH-g\")", "Bash(curl -X GET \"http://localhost:8081/api/chatrooms/my-tasks?page=1&pageSize=10\" )", "Bash(-H \"Content-Type: application/json\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, user_id, username, name, team_id FROM train_staff WHERE id = 93 OR user_id = 93;\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, user_id, username, team_id FROM train_staff WHERE id = 93 OR user_id = 93;\")", "Bash(grep -n \"getStaffDetail\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/staff/impl/TrainStaffServiceImpl.java)", "Bash(-v)", "Bash(find . -name \"*.log\" -type f)", "Bash(grep -A 10 -B 5 \"用户不是员工或员工信息不存在\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/task/TrainReceptionChatroomController.java)", "Bash(grep -r \"用户不是员工或员工信息不存在\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/)", "Bash(grep -r \"当前用户不是员工\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/)", "Bash(grep -r \"根据用户ID查询员工\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/)", "Bash(grep -A 5 -B 5 \"getStaffByUserId\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/task/TrainReceptionChatroomController.java)", "Bash(grep -n -A 15 \"通过staffId.*JWT中的userId实际是staffId\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/task/TrainReceptionChatroomController.java)", "Bash(pkill -f \"ai_train_playground\")", "Bash(curl -s http://localhost:8081/actuator/health)", "Bash(grep -n \"我的接待任务\" /home/<USER>/myaiprojects/yiyi_ai_playground/docs/task/TrainReceptionChatroomController_curl调用示例.md)", "Bash(mvn test -Dtest=BigmodelPromptsServiceImplTest -Dspring.profiles.active=home)", "Bash(curl -X GET \"http://**************:6333/collections\" -H \"Content-Type: application/json\")", "<PERSON><PERSON>(curl -X PUT \"http://**************:6333/collections/train_prod_collection\" )", "Bash(-d '{\n    \"\"vectors\"\": {\n      \"\"size\"\": 1536,\n      \"\"distance\"\": \"\"Cosine\"\"\n    }\n  }')", "Bash(curl -X GET \"http://**************:6333/collections/train_prod_collection\" -H \"Content-Type: application/json\")", "Bash(curl -X GET \"http://**************:6333/collections/train_prod_jd_collection\" -H \"Content-Type: application/json\")", "<PERSON><PERSON>(curl -X POST \"http://**************:6333/collections/train_prod_collection/points/search\" )", "Bash(-d '{\n    \"\"vector\"\": [0.1, 0.2, 0.3],\n    \"\"limit\"\": 5,\n    \"\"with_payload\"\": true,\n    \"\"with_vector\"\": false\n  }')", "<PERSON><PERSON>(curl -X GET \"http://**************:6333/collections/train_prod_jd_collection\")", "<PERSON><PERSON>(curl -X DELETE \"http://**************:6333/collections/train_prod_collection\")", "Bash(-d '{\n    \"\"vectors\"\": {\n      \"\"size\"\": 2560,\n      \"\"distance\"\": \"\"Cosine\"\"\n    }\n  }')", "<PERSON><PERSON>(curl -X GET \"http://**************:6333/collections/train_prod_collection\")", "Bash(-d '{\n    \"\"vector\"\": [0.1] * 2560,\n    \"\"limit\"\": 5,\n    \"\"with_payload\"\": true,\n    \"\"with_vector\"\": false\n  }')", "<PERSON><PERSON>(curl -X POST \"http://**************:6333/collections/train_prod_jd_collection/points/scroll\" )", "Bash(-d '{\n    \"\"limit\"\": 10,\n    \"\"with_payload\"\": true,\n    \"\"with_vector\"\": false,\n    \"\"filter\"\": {\n      \"\"must\"\": [\n        {\n          \"\"key\"\": \"\"productId\"\",\n          \"\"match\"\": {\n            \"\"value\"\": \"\"10030237827895\"\"\n          }\n        }\n      ]\n    }\n  }')", "Bash(-d '{\n    \"\"limit\"\": 10,\n    \"\"with_payload\"\": true,\n    \"\"with_vector\"\": false,\n    \"\"filter\"\": {\n      \"\"must\"\": [\n        {\n          \"\"key\"\": \"\"productId\"\",\n          \"\"match\"\": {\n            \"\"value\"\": 10030237827895\n          }\n        }\n      ]\n    }\n  }')", "Bash(-d '{\n    \"\"limit\"\": 1,\n    \"\"with_payload\"\": true,\n    \"\"with_vector\"\": false,\n    \"\"filter\"\": {\n      \"\"must\"\": [\n        {\n          \"\"key\"\": \"\"productId\"\",\n          \"\"match\"\": {\n            \"\"value\"\": \"\"10030237827895\"\"\n          }\n        }\n      ]\n    }\n  }')", "Bash(curl -X POST \"http://**************:6333/collections/train_prod_jd_collection/points/search\" -H \"Content-Type: application/json\" -d '{\n    \"\"\"\"vector\"\"\"\": [0.1, 0.2, 0.3, 0.4, 0.5],\n    \"\"\"\"limit\"\"\"\": 1,\n    \"\"\"\"with_payload\"\"\"\": true,\n    \"\"\"\"with_vector\"\"\"\": false,\n    \"\"\"\"filter\"\"\"\": {\n      \"\"\"\"must\"\"\"\": [\n        {\n          \"\"\"\"key\"\"\"\": \"\"\"\"productId\"\"\"\",\n          \"\"\"\"match\"\"\"\": {\n            \"\"\"\"value\"\"\"\": 10030237827895\n          }\n        }\n      ]\n    }\n  }')", "Bash(mvn spring-boot:run -Dspring-boot.run.profiles=home)", "<PERSON>sh(curl -X POST \"http://localhost:8081/api/vector/search\" )", "Bash(-d '{\n    \"\"collection\"\": \"\"train_prod_jd_collection\"\",\n    \"\"query\"\": \"\"测试查询\"\",\n    \"\"limit\"\": 5,\n    \"\"prodType\"\": 1,\n    \"\"externalProductId\"\": \"\"10030237827895\"\"\n  }')", "Bash(-H \"Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6OTMsInN1YiI6InN0YWZmMDAxIiwiaWF0IjoxNzUzODk2NTAxLCJleHAiOjE3NTM5ODI5MDF9.7WLOLp1ryujAKSX7_gKbeGhmSSy55JWsKEB_cMoLq_M\" )", "Bash(curl -X POST \"http://**************:6333/collections/train_prod_jd_collection/points/search\" -H \"Content-Type: application/json\" -H \"Authorization: Bearer qdrant:qdrant123\" -d '{\n    \"\"\"\"vector\"\"\"\": [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],\n    \"\"\"\"limit\"\"\"\": 5,\n    \"\"\"\"with_payload\"\"\"\": true,\n    \"\"\"\"filter\"\"\"\": {\n      \"\"\"\"must\"\"\"\": [\n        {\n          \"\"\"\"key\"\"\"\": \"\"\"\"productId\"\"\"\",\n          \"\"\"\"match\"\"\"\": {\n            \"\"\"\"value\"\"\"\": 10030237827895\n          }\n        }\n      ]\n    }\n  }')", "<PERSON><PERSON>(curl -X POST \"http://**************:6333/collections/train_prod_jd_collection/points/search\" )", "<PERSON><PERSON>(-H \"Authorization: Bearer qdrant:qdrant123\" )", "Bash(-d @/tmp/query_vector.json)", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, product_name, color_options FROM train_jd_products WHERE id = 10030237827895 LIMIT 1;\")", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; DESCRIBE train_jd_products;\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, title, brand_name, features FROM train_jd_products WHERE id = 10030237827895 LIMIT 1;\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT COUNT(*) as count FROM train_jd_products WHERE id = 10030237827895;\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, title, brand_name FROM train_jd_products WHERE title LIKE ''%小米%'' AND title LIKE ''%手环%'' LIMIT 5;\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, title, features, jd_prod_dtl FROM train_jd_products WHERE id = 467 LIMIT 1\\G\")", "<PERSON><PERSON>(-H \"Authorization: Bearer qdrant\" )", "Bash(-d '{\n  \"\"vector\"\": [0.1, 0.2, 0.3, 0.4, 0.5],\n  \"\"filter\"\": {\n    \"\"must\"\": [\n      {\"\"key\"\": \"\"productId\"\", \"\"match\"\": {\"\"value\"\": 467}},\n      {\"\"key\"\": \"\"teamId\"\", \"\"match\"\": {\"\"value\"\": 1}}\n    ]\n  },\n  \"\"limit\"\": 1,\n  \"\"with_payload\"\": true,\n  \"\"with_vector\"\": false\n}')", "Bash(-d '{\n  \"\"filter\"\": {\n    \"\"must\"\": [\n      {\"\"key\"\": \"\"productId\"\", \"\"match\"\": {\"\"value\"\": 467}},\n      {\"\"key\"\": \"\"teamId\"\", \"\"match\"\": {\"\"value\"\": 1}}\n    ]\n  },\n  \"\"limit\"\": 3,\n  \"\"with_payload\"\": true,\n  \"\"with_vector\"\": false\n}')", "Bash(-d '{\n  \"\"filter\"\": {\n    \"\"must\"\": [\n      {\"\"key\"\": \"\"productId\"\", \"\"match\"\": {\"\"value\"\": 10030237827895}},\n      {\"\"key\"\": \"\"teamId\"\", \"\"match\"\": {\"\"value\"\": 1}}\n    ]\n  },\n  \"\"limit\"\": 5,\n  \"\"with_payload\"\": true,\n  \"\"with_vector\"\": false\n}')", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/UpdateXiaomiColorsTest.java)", "<PERSON><PERSON>(curl -X PUT \"http://**************:6333/collections/train_prod_jd_collection/points\" )", "Bash(-d @/tmp/xiaomi_colors_vector.json)", "Bash(curl -X PUT \"http://**************:6333/collections/train_prod_jd_collection/points\" -H \"Content-Type: application/json\" -d @/tmp/xiaomi_colors_vector.json)", "Bash(curl -X POST \"http://localhost:6333/collections/train_prod_jd_collection/points/search\" -H \"Content-Type: application/json\" -d '{\n  \"\"\"\"vector\"\"\"\": [0.1, 0.2, 0.3, 0.4, 0.5],\n  \"\"\"\"limit\"\"\"\": 10,\n  \"\"\"\"filter\"\"\"\": {\n    \"\"\"\"must\"\"\"\": [\n      {\"\"\"\"key\"\"\"\": \"\"\"\"productId\"\"\"\", \"\"\"\"match\"\"\"\": {\"\"\"\"value\"\"\"\": 10029889755385}},\n      {\"\"\"\"key\"\"\"\": \"\"\"\"teamId\"\"\"\", \"\"\"\"match\"\"\"\": {\"\"\"\"value\"\"\"\": 1}}\n    ]\n  }\n}')", "Bash(curl -X POST \"http://localhost:6333/collections/train_prod_jd_collection/points/scroll\" )", "Bash(-d '{\n  \"\"filter\"\": {\n    \"\"must\"\": [\n      {\"\"key\"\": \"\"productId\"\", \"\"match\"\": {\"\"value\"\": 10029889755385}},\n      {\"\"key\"\": \"\"teamId\"\", \"\"match\"\": {\"\"value\"\": 1}}\n    ]\n  },\n  \"\"limit\"\": 10\n}')", "Bash(mvn test -Dtest=VectorSearchManualTest -Dspring.profiles.active=home)", "Bash(export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64)", "Bash(ls -la /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/impl/DoubaoBigModelServiceImpl.java)", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground -name \"DoubaoBigModelServiceImpl.java\" -type f)", "Bash(find . -name \"*DoubaiBigModelServiceImpl*\" -type f)", "Bash(grep -n \"testGenerateVectorForXiaomiColors\" /home/<USER>/aiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/VectorSearchManualTest.java)", "Bash(grep -A5 -B5 \"ARK_API_KEY\" /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/impl/DoubaoBigModelServiceImpl.java)", "mcp__thinking__sequentialthinking", "<PERSON><PERSON>(mysql -h ************ -P 3306 -u root -p123456 -e \"SHOW DATABASES;\")", "Bash(sudo apt update)", "<PERSON>sh(sudo apt install -y mysql-client)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456)", "<PERSON><PERSON>(mysql -h ************** -P 3306 -u root -p123456)", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; \n\nCREATE TABLE \\`train_kb_tpl_main\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`name\\` varchar(100) NOT NULL COMMENT ''会话知识库模板名称'',\n  \\`tokens\\` bigint COMMENT ''本次会话累计消耗token数'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''会话知识库模板主表'';\")", "Bash(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db;\n\nCREATE TABLE \\`train_kb_tpl_detail\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`tpl_id\\` bigint NOT NULL COMMENT ''会话知识库模板主表ID'',\n  \\`sender\\` varchar(100) NOT NULL COMMENT ''发送者昵称'',\n  \\`content\\` text NOT NULL COMMENT ''对话内容'',\n  \\`send_time\\` datetime(3) NOT NULL COMMENT ''消息发送时间'',\n  \\`sender_type\\` enum(''buyer'',''staff'') NOT NULL COMMENT ''发送者类型'',\n  \\`tpl_type\\` enum(''pre_sales'',''saling'',''after_sale'',''other'') NOT NULL COMMENT ''模板类型'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  KEY \\`idx_tpl_id\\` (\\`tpl_id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''会话知识库模板明细表'';\")", "<PERSON>sh(mysql -h ************** -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SHOW TABLES LIKE ''train_kb_tpl_%'';\")", "Bash(mvn test -Dtest=TrainKbTplServiceImplTest -Dspring.profiles.active=home)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; ALTER TABLE train_reception_task ADD COLUMN conv_kb_id BIGINT NOT NULL DEFAULT 0 COMMENT ''关联的知识库模板ID，关联train_kb_tpl_main表主键''; DESCRIBE train_reception_task;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; ALTER TABLE train_kb_tpl_main ADD COLUMN description VARCHAR(500) NOT NULL DEFAULT '''';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; UPDATE train_kb_tpl_main SET description = name WHERE description = '''';\")", "<PERSON><PERSON>(curl -s \"http://localhost:8081/actuator/health\")", "Bash(pgrep -f \"ai_train_playground\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"SELECT id FROM train_kb_tpl_main WHERE team_id = 1 LIMIT 1;\")", "Bash(mvn test -Dtest=ReceptionTaskServiceImplTest -Dspring.profiles.active=home -q)", "Bash(mvn compile)", "Bash(mvn compile -q -DskipTests=true)", "WebFetch(domain:www.volcengine.com)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"DESC train_product;\")", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D ai_train_playground -e \"DESC train_product;\")", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"SHOW DATABASES;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESC train_product;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESC train_jd_products;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"INSERT INTO train_bigmodel_prompts (team_id, keyword, content, create_time, update_time, creator, updater, version) VALUES (1, ''gene_2nd_chat_tpl:S'', ''# 角色设定  \n**电商聊天记录修改专家**  \n\n## 核心任务  \n1. 修改聊天记录chatLogPart中的 **买家身份** → 替换为 \\`targetRolePart\\`  \n2. 修改聊天记录chatLogPart中的 **商品信息** → 替换为 \\`targetProductTitlePart\\`  \n2. 修改聊天记录chatLogPart中的 **商品信息知识库** → 替换为 \\`targetProKnowledgePart\\`  \n3. 严格学习原始聊天记录(chatLogPart)的 **表达风格**（语气/句式/用词习惯）  \n\n## 输入数据  \n\\`\\`\\`\n### chatLogPart ###  \n{{chat_log}}\n###  \n\n\n### 真实的商品信息知识库如下面4个#括起来的，命名为targetProKnowledgePart ###  \n####\n{{targetProKnowledge}}  \n####\n###  \n\n\n\\`\\`\\`  \n\n\n## 买家身份\n\\`\\`\\`  \n### targetRolePart ###  \n{{targetRole}}  \n###  \n\\`\\`\\`  \n\n## 商品标题\n\\`\\`\\`  \n### targetProductTitlePart ###  \n{{targetProductTitle}}  \n###  \n\\`\\`\\`  \n\n## 输出要求  \n✅ 生成 **全新聊天记录**  \n✅ 保留原始对话逻辑和场景  \n✅ 确保 \\`targetRolePart\\` 、 \\`targetProductPart\\`、 \\`targetProKnowledgePart\\` 自然融入对话  \n❌ 禁止修改非指定内容（如时间、售后流程等）'', NOW(), NOW(), ''AI_Assistant'', ''AI_Assistant'', 0) ON DUPLICATE KEY UPDATE content = VALUES(content), update_time = NOW(), updater = ''AI_Assistant'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE ai_train_playground; \n\nCREATE TABLE \\`train_conv_winchat_main\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`chatroom_id\\` bigint NOT NULL COMMENT ''聊天室ID，关联train_reception_chatroom'',\n  \\`staff_id\\` bigint NOT NULL COMMENT ''员工ID，关联train_staff'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  KEY \\`idx_chatroom_id\\` (\\`chatroom_id\\`),\n  KEY \\`idx_staff_id\\` (\\`staff_id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''模拟聊天室窗口主表'';\n\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; \n\nCREATE TABLE \\`train_conv_winchat_main\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`chatroom_id\\` bigint NOT NULL COMMENT ''聊天室ID，关联train_reception_chatroom'',\n  \\`staff_id\\` bigint NOT NULL COMMENT ''员工ID，关联train_staff'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  KEY \\`idx_chatroom_id\\` (\\`chatroom_id\\`),\n  KEY \\`idx_staff_id\\` (\\`staff_id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''模拟聊天室窗口主表'';\n\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; \n\nCREATE TABLE \\`train_conv_winchat_dtl\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`conv_winchat_main_id\\` bigint NOT NULL COMMENT ''聊天窗口主表ID，关联train_conv_winchat_main'',\n  \\`task_id\\` bigint NOT NULL COMMENT ''接待任务ID，关联train_reception_task'',\n  \\`session_id\\` varchar(300) NOT NULL COMMENT ''当前窗口会话的在redis中的sessionId，全局唯一'',\n  \\`f1st_raw_chatlog\\` text COMMENT ''最原始的聊天记录'',\n  \\`s2nd_agg_sys_prompt\\` text COMMENT ''聚合过后的系统提示词'',\n  \\`t3rd_rewrite\\` text COMMENT ''重写过后的聊天记录'',\n  \\`f4th_final_sys_prompt\\` text COMMENT ''最终的系统提示词'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  UNIQUE KEY \\`uk_session_id\\` (\\`session_id\\`),\n  KEY \\`idx_conv_winchat_main_id\\` (\\`conv_winchat_main_id\\`),\n  KEY \\`idx_task_id\\` (\\`task_id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''模拟聊天室窗口明细表'';\n\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; \n\nCREATE TABLE \\`train_conv_winchat_log\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`conv_dtl_id\\` bigint NOT NULL COMMENT ''聊天窗口明细表ID，关联train_conv_winchat_dtl'',\n  \\`session_id\\` varchar(300) NOT NULL COMMENT ''当前窗口会话的在redis中的sessionId，在此表中不唯一'',\n  \\`sender\\` varchar(100) NOT NULL COMMENT ''发送者昵称'',\n  \\`content\\` text NOT NULL COMMENT ''对话内容'',\n  \\`send_time\\` datetime(3) NOT NULL COMMENT ''消息发送时间'',\n  \\`sender_type\\` enum(''buyer'',''staff'') NOT NULL COMMENT ''发送者类型'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  KEY \\`idx_conv_dtl_id\\` (\\`conv_dtl_id\\`),\n  KEY \\`idx_session_id\\` (\\`session_id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_send_time\\` (\\`send_time\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''模拟聊天室窗口聊天记录表'';\n\n\")", "Bash(mvn test -Dtest=TrainConvWinchatMainServiceImplTest -Dspring.profiles.active=home)", "Bash(mvn test -Dtest=TrainConvWinchatMainServiceImplTest -Dspring.profiles.active=company)", "Bash(mvn test -Dtest=TrainConvWinchatDtlServiceImplTest -Dspring.profiles.active=company)", "Bash(mvn test -Dtest=TrainConvWinchatLogServiceImplTest -Dspring.profiles.active=company)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"ALTER TABLE yiyi_ai_playground.train_reception_task ADD COLUMN learning_status ENUM(''un_learn'',''learning'',''learned'') NOT NULL DEFAULT ''un_learn'' COMMENT ''学习状态'', ADD COLUMN amt_to_be_learned BIGINT NOT NULL DEFAULT 0 COMMENT ''待学习的总条数'', ADD COLUMN amt_has_learned BIGINT NOT NULL DEFAULT 0 COMMENT ''目前已经学习条数'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SHOW TABLES LIKE ''train_reception_task'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; ALTER TABLE train_reception_task ADD COLUMN learning_status ENUM(''un_learn'',''learning'',''learned'') NOT NULL DEFAULT ''un_learn'' COMMENT ''学习状态'', ADD COLUMN amt_to_be_learned BIGINT NOT NULL DEFAULT 0 COMMENT ''待学习的总条数'', ADD COLUMN amt_has_learned BIGINT NOT NULL DEFAULT 0 COMMENT ''目前已经学习条数'';\")", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; DESC train_reception_task;\")", "Bash(mvn test -Dtest=ReceptionTaskServiceImplTest -Dspring.profiles.active=home)", "Bash(find . -name \"*.java\" -type f)", "Bash(mv /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/entity/task/ReceptionTask.java /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/entity/task/TrainReceptionTask.java)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/dto/task)", "Bash(for file in ReceptionTask*.java)", "Bash(do mv \"$file\" \"Train$file\")", "Bash(done)", "Bash(ls /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/dto/task/Train*)", "Bash(for file in /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/dto/task/TrainReceptionTask*.java)", "Bash(do sed -i 's/public class ReceptionTask/public class TrainReceptionTask/g' \"$file\")", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/task)", "Bash(mv ReceptionTaskService.java TrainReceptionTaskService.java)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/service/task/impl)", "Bash(mv ReceptionTaskServiceImpl.java TrainReceptionTaskServiceImpl.java)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/mapper/task)", "<PERSON><PERSON>(mv ReceptionTaskMapper.java TrainReceptionTaskMapper.java)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/task)", "<PERSON><PERSON>(mv ReceptionTaskController.java TrainReceptionTaskController.java)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/main/resources/mapper/task)", "Bash(mv ReceptionTaskMapper.xml TrainReceptionTaskMapper.xml)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/task/impl)", "Bash(mv ReceptionTaskServiceImplTest.java TrainReceptionTaskServiceImplTest.java)", "Bash(sed -i 's/com\\.yiyi\\.ai_train_playground\\.dto\\.task\\.ReceptionTask/com.yiyi.ai_train_playground.dto.task.TrainReceptionTask/g' /home/<USER>/aiprojects/yiyi_ai_playground/src/main/resources/mapper/task/TrainReceptionTaskMapper.xml)", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground/src -name \"*.java\" -type f -exec grep -l \"ReceptionTask\" {} ;)", "Bash(sed -i 's/receptionTaskService\\./trainReceptionTaskService\\./g' /home/<USER>/aiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/task/impl/TrainReceptionTaskServiceImplTest.java)", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground/docs -name \"*ReceptionTask*\" -type f)", "Bash(cd /home/<USER>/aiprojects/yiyi_ai_playground/docs/task)", "Bash(mv ReceptionTaskController_API调用示例.md TrainReceptionTaskController_API调用示例.md)", "Bash(sed -i 's/ReceptionTask/TrainReceptionTask/g' /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "Bash(sed -i 's|/api/reception-tasks|/api/train-reception-tasks|g' /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "Bash(sed -i 's#/api/reception-tasks#/api/train-reception-tasks#g' /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"DESCRIBE train_task_conv_kb_dtl;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"\nCREATE TABLE \\`train_task_conv_kb_dtl\\` (\n  \\`id\\` bigint NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',\n  \\`task_id\\` bigint NOT NULL COMMENT ''任务ID'',\n  \\`kb_dtl_id\\` bigint NOT NULL COMMENT ''train_kb_tpl_detail的主键ID'',\n  \\`learning_status\\` enum(''un_learn'',''learning'',''learned'') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''un_learn'' COMMENT ''学习状态'',\n  \\`final_chat_log\\` text COLLATE utf8mb4_unicode_ci COMMENT ''最终生成的聊天记录'',\n  \\`team_id\\` bigint NOT NULL COMMENT ''团队ID'',\n  \\`create_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',\n  \\`update_time\\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',\n  \\`creator\\` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''0'' COMMENT ''创建人'',\n  \\`updater\\` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''0'' COMMENT ''更新人'',\n  \\`version\\` bigint NOT NULL DEFAULT ''0'' COMMENT ''版本号（用于乐观锁）'',\n  PRIMARY KEY (\\`id\\`),\n  KEY \\`idx_task_id\\` (\\`task_id\\`),\n  KEY \\`idx_kb_dtl_id\\` (\\`kb_dtl_id\\`),\n  KEY \\`idx_team_id\\` (\\`team_id\\`),\n  KEY \\`idx_create_time\\` (\\`create_time\\`),\n  KEY \\`idx_learning_status\\` (\\`learning_status\\`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''任务会话明细表'';\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; ALTER TABLE train_task_conv_kb_dtl ADD COLUMN f1st_raw_chatlog text COLLATE utf8mb4_unicode_ci COMMENT ''初始的聊天记录'';\")", "Bash(mvn test -Dtest=TrainReceptionTaskIntegrationTest -Dspring.profiles.active=home)", "Bash(find /home/<USER>/myaiprojects/yiyi_ai_playground -name \"*TrainReceptionTaskServiceImpl.java\" -type f)", "Bash(curl -X GET \"http://localhost:8081/actuator/health\" -H \"Content-Type: application/json\")", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"SELECT @@global.time_zone, @@session.time_zone, NOW();\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"SET GLOBAL time_zone = ''+08:00''; SELECT @@global.time_zone, @@session.time_zone, NOW();\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"SET GLOBAL time_zone = ''+08:00''; SET SESSION time_zone = ''+08:00''; SELECT @@global.time_zone, @@session.time_zone, NOW();\")", "<PERSON>sh(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"SHOW VARIABLES LIKE ''%time_zone%'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; SELECT id, task_name, create_time, update_time FROM train_reception_task ORDER BY update_time DESC LIMIT 3;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"\nUSE yiyi_ai_db;\nALTER TABLE train_conv_winchat_log ADD COLUMN prod_refer_answer text NOT NULL DEFAULT '''';\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"\nUSE yiyi_ai_db;\nALTER TABLE train_conv_winchat_log ADD COLUMN prod_refer_answer text NOT NULL;\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"\nUSE yiyi_ai_db;\nDESCRIBE train_conv_winchat_log;\n\")", "Bash(find /home/<USER>/myaiprojects/yiyi_ai_playground -name \"*DTO*.java\" -path \"*/kb/*\")", "Bash(find /home/<USER>/myaiprojects/yiyi_ai_playground -name \"*.java\" -path \"*/dto/*\")", "Bash(mvn clean compile)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"\nUSE yiyi_ai_db;\nALTER TABLE train_conv_winchat_log MODIFY COLUMN prod_refer_answer text NULL;\n\")", "Bash(mvn test -Dtest=TrainConvWinchatLogServiceImplTest -Dspring.profiles.active=home)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nINSERT INTO system_config (team_id, namespace, config_key, config_value, create_time, update_time, creator, updater, version) \nVALUES \n(1, ''reception_skin'', ''0'', ''千牛'', NOW(), NOW(), ''system'', ''system'', 0),\n(1, ''reception_skin'', ''1'', ''咚咚'', NOW(), NOW(), ''system'', ''system'', 0),\n(1, ''reception_skin'', ''2'', ''抖音'', NOW(), NOW(), ''system'', ''system'', 0)\nON DUPLICATE KEY UPDATE \nconfig_value = VALUES(config_value),\nupdate_time = NOW(),\nupdater = ''system'',\nversion = version + 1;\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nSELECT * FROM system_config WHERE team_id = 1 AND namespace = ''reception_skin'' ORDER BY config_key;\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nINSERT INTO system_config (team_id, namespace, config_key, config_value, create_time, update_time, creator, updater, version) \nVALUES \n(1, ''scene_mode'', ''0'', ''萌新友好'', NOW(), NOW(), ''system'', ''system'', 0),\n(1, ''scene_mode'', ''1'', ''压力考核'', NOW(), NOW(), ''system'', ''system'', 0),\n(1, ''scene_mode'', ''2'', ''自定义'', NOW(), NOW(), ''system'', ''system'', 0)\nON DUPLICATE KEY UPDATE \nconfig_value = VALUES(config_value),\nupdate_time = NOW(),\nupdater = ''system'',\nversion = version + 1;\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nSELECT * FROM system_config WHERE team_id = 1 AND namespace = ''scene_mode'' ORDER BY config_key;\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nINSERT INTO system_config (team_id, namespace, config_key, config_value, create_time, update_time, creator, updater, version) \nVALUES \n(1, ''reception_skin'', ''3'', ''默认'', NOW(), NOW(), ''system'', ''system'', 0)\nON DUPLICATE KEY UPDATE \nconfig_value = VALUES(config_value),\nupdate_time = NOW(),\nupdater = ''system'',\nversion = version + 1;\n\")", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nDESCRIBE train_reception_chatroom;\n\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE train_reception_chatroom \nMODIFY COLUMN reception_skin varchar(50) DEFAULT ''0'' COMMENT ''接待皮肤'';\n\")", "Bash(mvn test -Dtest=TrainReceptionChatroomServiceImplTest -Dspring.profiles.active=home -q)", "Bash(grep -r \"ReceptionTask\" /home/<USER>/aiprojects/yiyi_ai_playground/target/)", "<PERSON><PERSON>(mvn clean)", "Bash(find target/surefire-reports -name \"*TrainReceptionChatroomServiceImplTest*\")", "Bash(find docs/ -name \"*chatroom*\" -o -name \"*Cha<PERSON><PERSON>*\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; ALTER TABLE train_conv_winchat_dtl ADD COLUMN conv_kb_id bigint NULL COMMENT ''train_task_conv_kb_dtl表的主键ID'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"USE yiyi_ai_db; DESCRIBE train_conv_winchat_dtl;\")", "Bash(mvn compile -Dspring.profiles.active=home)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"ALTER TABLE train_conv_winchat_log ADD COLUMN score int DEFAULT NULL COMMENT ''意图识别评分'', ADD COLUMN intent_result text DEFAULT NULL COMMENT ''意图识别完整JSON结果'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESC train_conv_winchat_log;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"ALTER TABLE train_kb_tpl_detail ADD COLUMN pre_id bigint NULL COMMENT ''train_kb_tpl_pre表的主键ID'';\")", "<PERSON>sh(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESC train_kb_tpl_detail;\")", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground/src/test -name \"*Test.java\")", "<PERSON><PERSON>(mvn test-compile)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESCRIBE train_reception_task;\")", "Ba<PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SHOW TABLES;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE bigmodel_prompts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE system_config CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_chatroom_staff CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_chatroom_task CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_conv_winchat_dtl CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_conv_winchat_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_conv_winchat_main CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_evaluation_dimension CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_evaluation_group CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_evaluation_plan CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE train_evaluation_script CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_flow_node CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_intent CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_accesstoken CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_prod_images CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_products CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_products_features CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_products_mcp CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_sku CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_sku_features CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE train_jd_sku_mcp CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_sku_props CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_jd_sku_sale_attrs CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_kb_tpl_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_kb_tpl_main CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_kb_tpl_pre CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_product CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_qa_import_dtl CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_qa_import_main CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_reception_chatroom CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE train_reception_task CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_related_image CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_script CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_script_group CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_script_jd_products CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_script_products CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_shortcut_phrases CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_shortcut_phrases_group CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_staff CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE train_staff_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_staff_tag CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_staff_tag_relation CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_task_conv_kb_dtl CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_team CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE train_team_shops CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\nALTER TABLE user_purchased_product CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"\nALTER TABLE train_reception_task \nADD COLUMN is_show_resolve tinyint(1) NOT NULL DEFAULT 0 COMMENT ''是否显示解析详情'',\nADD COLUMN is_show_correct tinyint(1) NOT NULL DEFAULT 0 COMMENT ''是否显示正确答案'',\nADD COLUMN freq_aues_cnt int NOT NULL DEFAULT 0 COMMENT ''选取的知识库条数'';\n\")", "Bash(mvn test -Dtest=TrainReceptionTaskServiceImplTest -Dspring.profiles.active=home)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -e \"DESCRIBE yiyi_ai_db.train_reception_task;\")", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/java/com/yiyi/ai_train_playground/controller/task/SimulateChatWebSocketController.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/controller/task/SimulateChatWebSocketControllerTest.java)", "Bash(rm /home/<USER>/myaiprojects/yiyi_ai_playground/docs/task/SimulateChatWebSocketController-使用说明.md)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/target/classes/com/yiyi/ai_train_playground/controller/task/SimulateChatWebSocketController.class)", "Bash(rm -f /home/<USER>/myaiprojects/yiyi_ai_playground/target/test-classes/com/yiyi/ai_train_playground/controller/task/SimulateChatWebSocketControllerTest.class)", "Bash(mvn test -Dtest=TenantInterceptorTest -Dspring.profiles.active=home)", "Bash(find /home/<USER>/myaiprojects/yiyi_ai_playground -name \".promptx\" -type d)", "Bash(ls -la /home/<USER>/myaiprojects/yiyi_ai_playground/.promptx)", "Bash(find /home/<USER>/myaiprojects/yiyi_ai_playground/.promptx -name \"*.dpml\" -o -name \"*declarative*\")", "Bash(grep -A 20 -B 5 \"LEFT JOIN train_script ts ON trt.script_id = ts.id\" /home/<USER>/myaiprojects/yiyi_ai_playground/src/main/resources/mapper/task/TrainReceptionTaskMapper.xml)", "Bash(mvn test -Dtest=TenantInterceptorMultiTableTest -Dspring.profiles.active=home)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"ALTER TABLE train_qa_rdm ADD COLUMN report_dtl_id bigint COMMENT ''报告详情ID，关联train_qa_report_dtl表主键'';\")", "Bash(mvn test -Dtest=ScWS4QaAnoControllerTest -Dspring.profiles.active=home)", "Bash(mvn test -Dtest=TrainQaRdmMapperTest -Dspring.profiles.active=home)", "Bash(mvn test -Dtest=QaReslUtilTest -Dspring.profiles.active=home)", "Bash(mvn test -Dtest=TrainQaReportMainServiceImplTest -Dspring.profiles.active=home)", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESCRIBE train_qa_rdm;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SELECT ques_no, COUNT(*) as cnt FROM train_qa_rdm GROUP BY ques_no ORDER BY cnt DESC LIMIT 10;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"ALTER TABLE train_reception_chatroom ADD COLUMN room_name varchar(64) NOT NULL COMMENT ''聊天室名称'' AFTER id;\")", "<PERSON><PERSON>(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"DESCRIBE train_reception_chatroom;\")", "Bash(grep -n \"我的任务\" /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionChatroomController_curl调用示例.md)", "Bash(grep -n \"my-tasks\" /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionChatroomController_curl调用示例.md)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"ALTER TABLE train_reception_task ADD COLUMN is_show_inspect tinyint(1) NOT NULL DEFAULT 0 COMMENT ''是否显示考察点'' AFTER freq_aues_cnt;\")", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground -name \"*DTO.java\" -path \"*/task/*\")", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground -name \"*Request.java\" -path \"*/task/*\")", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground -name \"TrainReceptionTaskMapper.xml\")", "Bash(grep -n \"createTestTask\" /home/<USER>/aiprojects/yiyi_ai_playground/src/test/java/com/yiyi/ai_train_playground/service/task/impl/TrainReceptionTaskServiceImplTest.java)", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground -name \"*TrainReceptionTask*\" -path \"*/docs/*\" -type f)", "Bash(grep -n \"isShowCorrect\\|freqAuesCnt\\|字段更新历史\" /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "Bash(grep -n -A 5 -B 5 \"freqAuesCnt.*:\" /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "Bash(grep -n -A 10 -B 3 \"freqAuesCnt.*Integer.*否\" /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "Bash(grep -c \"isShowInspect\" /home/<USER>/aiprojects/yiyi_ai_playground/docs/task/TrainReceptionTaskController_API调用示例.md)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"SELECT * FROM system_config WHERE namespace=''reception_skin'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"SELECT CONCAT(''INSERT INTO system_config (id, team_id, namespace, config_key, config_value, description, version, creator, updater, create_time, update_time) VALUES ('', id, '', '', team_id, '', '''''', namespace, '''''', '''''', config_key, '''''', '''''', IFNULL(config_value, ''NULL''), '''''', '', IFNULL(CONCAT(''\\'''', description, ''\\''''), ''NULL''), '', '', version, '', '''''', creator, '''''', '''''', updater, '''''', '''''', create_time, '''''', '''''', update_time, '''''');'') as insert_statement FROM system_config WHERE namespace=''reception_skin'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"SELECT * FROM system_config WHERE namespace=''task_purpose'';\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 yiyi_ai_db -e \"SELECT CONCAT(''INSERT INTO system_config (id, team_id, namespace, config_key, config_value, description, version, creator, updater, create_time, update_time) VALUES ('', id, '', '', team_id, '', '''''', namespace, '''''', '''''', config_key, '''''', '''''', IFNULL(config_value, ''NULL''), '''''', '', IFNULL(CONCAT(''\\'''', description, ''\\''''), ''NULL''), '', '', version, '', '''''', creator, '''''', '''''', updater, '''''', '''''', create_time, '''''', '''''', update_time, '''''');'') as insert_statement FROM system_config WHERE namespace=''task_purpose'';\")", "Bash(curl -X GET http://localhost:8081/actuator/health)", "Bash(mvn test -Dtest=SqlUtilComplexQueryTest -Dspring.profiles.active=home)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SELECT * FROM train_staff WHERE id = 110\")", "Bash(java debug_json_test.java)", "Bash(rm debug_json_test.java)", "Bash(find /home/<USER>/aiprojects/yiyi_ai_playground -name \"*.dpml\" -type f)", "Bash(mv /home/<USER>/aiprojects/yiyi_ai_playground/.promptx/memory/java-backend-expert/declarative.dpml /home/<USER>/aiprojects/yiyi_ai_playground/.promptx/memory/java-backend-expert/declarative_backup.dpml)", "Bash(mv /home/<USER>/aiprojects/yiyi_ai_playground/.promptx/memory/java-backend-expert/declarative_slim.dpml /home/<USER>/aiprojects/yiyi_ai_playground/.promptx/memory/java-backend-expert/declarative.dpml)", "Bash(mvn test -Dtest=ScWS4QaAnoControllerIsolationTest -Dspring.profiles.active=home)", "Bash(mvn test -Dtest=TrainQaImportMainServiceImplTest -Dspring.profiles.active=home)", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SELECT ques_no, id FROM train_qa_rdm WHERE ques_no IS NOT NULL ORDER BY ques_no LIMIT 20;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SELECT DISTINCT ques_no FROM train_qa_rdm WHERE ques_no IS NOT NULL ORDER BY ques_no;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SELECT DISTINCT ques_no, CAST(SUBSTRING_INDEX(ques_no, ''/'', 1) AS UNSIGNED) as question_num, CAST(SUBSTRING_INDEX(ques_no, ''/'', -1) AS UNSIGNED) as total_num FROM train_qa_rdm WHERE ques_no IS NOT NULL ORDER BY CAST(SUBSTRING_INDEX(ques_no, ''/'', 1) AS UNSIGNED) ASC, CAST(SUBSTRING_INDEX(ques_no, ''/'', -1) AS UNSIGNED) ASC LIMIT 20;\")", "Bash(mysql -h www.yiyiailocal.com -P 3306 -u root -p123456 -D yiyi_ai_db -e \"SELECT tqr.ques_no FROM train_qa_report_main trm LEFT JOIN train_qa_report_dtl trd ON trm.id = trd.rp_main_id LEFT JOIN train_qa_rdm tqr ON trd.id = tqr.report_dtl_id WHERE trm.id = (SELECT id FROM train_qa_report_main LIMIT 1) AND tqr.ques_no IS NOT NULL ORDER BY CAST(SUBSTRING_INDEX(tqr.ques_no, ''/'', 1) AS UNSIGNED) ASC, CAST(SUBSTRING_INDEX(tqr.ques_no, ''/'', -1) AS UNSIGNED) ASC;\")"], "deny": []}}