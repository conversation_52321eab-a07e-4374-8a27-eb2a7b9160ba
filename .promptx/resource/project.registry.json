{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-09-08T01:53:22.368Z", "updatedAt": "2025-09-08T01:53:22.374Z", "resourceCount": 3}, "resources": [{"id": "java-backend-workflow", "source": "project", "protocol": "execution", "name": "Java Backend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-backend-expert/execution/java-backend-workflow.execution.md", "metadata": {"createdAt": "2025-09-08T01:53:22.371Z", "updatedAt": "2025-09-08T01:53:22.371Z", "scannedAt": "2025-09-08T01:53:22.371Z", "path": "role/java-backend-expert/execution/java-backend-workflow.execution.md"}}, {"id": "java-backend-expert", "source": "project", "protocol": "role", "name": "Java Backend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/java-backend-expert/java-backend-expert.role.md", "metadata": {"createdAt": "2025-09-08T01:53:22.372Z", "updatedAt": "2025-09-08T01:53:22.372Z", "scannedAt": "2025-09-08T01:53:22.372Z", "path": "role/java-backend-expert/java-backend-expert.role.md"}}, {"id": "java-backend-thinking", "source": "project", "protocol": "thought", "name": "Java Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-backend-expert/thought/java-backend-thinking.thought.md", "metadata": {"createdAt": "2025-09-08T01:53:22.373Z", "updatedAt": "2025-09-08T01:53:22.373Z", "scannedAt": "2025-09-08T01:53:22.373Z", "path": "role/java-backend-expert/thought/java-backend-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}