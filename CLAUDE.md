# CLAUDE.md


# AI Train Playground 项目开发规范

## 1. 项目结构规范

### 1.1 功能模块组织
- 新增功能需要单独建立子包，避免单个文件夹下文件爆炸
- 按功能模块组织代码结构，例如：
  ```
  com.yiyi.ai_train_playground.controller.task
  com.yiyi.ai_train_playground.service.task
  com.yiyi.ai_train_playground.mapper.task
  com.yiyi.ai_train_playground.entity.task
  com.yiyi.ai_train_playground.dto.task
  ```

### 1.2 包命名规范
- Controller层：`controller.{模块名}`
- Service层：`service.{模块名}`
- Mapper层：`mapper.{模块名}`
- Entity层：`entity.{模块名}`
- DTO层：`dto.{模块名}`

## 2. 测试规范

### 2.1 单元测试要求
- 每个修改或新增的功能都必须编写单元测试
- 只需要对Service层进行单元测试
- 复用现有的测试类，在原有ServiceTest中添加新的测试方法
- 示例：原有`ServiceA`，新增方法`m1()`，则在`ServiceATest`中添加`testM1()`方法

### 2.2 测试文件组织
- 测试类放在`/test`目录下的同包名路径
- 示例：类路径`com.yiyi.ai_train_playground.service.jd.impl`
- 测试类路径：`src/test/java/com/yiyi/ai_train_playground/service/jd/impl`

### 2.3 测试框架要求
- 必须使用`@SpringBootTest`注解
- 禁止使用`@WebMvcTest`
- 使用真实的数据库连接进行测试

### 2.4 Maven测试运行规范
- **运行测试时必须指定profile为home**
- 测试命令格式：`mvn test -Dtest=测试类名 -Dspring.profiles.active=home`
- 示例：
  ```bash
  # 运行单个测试类
  mvn test -Dtest=TrainReceptionChatroomServiceImplTest -Dspring.profiles.active=home
  
  # 运行单个测试方法
  mvn test -Dtest=TrainReceptionChatroomServiceImplTest#testCreateChatroomWithTasks -Dspring.profiles.active=home
  
  # 运行所有测试
  mvn test -Dspring.profiles.active=home
  ```
- **不指定profile会导致ApplicationContext加载失败**

### 2.5 测试数据库脚本
- DDL语句放在`src/test/resources/db`目录下的同包名路径
- 示例：类路径`com.yiyi.ai_train_playground.service.jd.impl`
- DDL路径：`src/test/resources/db/com/yiyi/ai_train_playground/service/jd/impl/`

## 3. 数据库规范

### 3.1 SQL编写规范
- 所有SQL必须写在XML文件中
- 必须使用MyBatis动态SQL
- 禁止在Java类中编写SQL语句

#### 3.1.1 动态SQL条件判断规范
- **必须使用`<if test="条件">`标签进行条件判断**，防止空值或未传参数导致的SQL错误
- **字符串非空判断**：`<if test="name != null and name != ''">`
- **数值非空判断**：`<if test="status != null">`
- **集合非空判断**：`<if test="ids != null and ids.size() > 0">`

**动态SQL示例**：
```xml
<!-- 员工查询示例 -->
<select id="selectStaffList" resultType="com.yiyi.ai_train_playground.entity.staff.TrainStaff">
    SELECT * FROM train_staff
    WHERE team_id = #{teamId}
    <if test="name != null and name != ''">
        AND name LIKE CONCAT('%', #{name}, '%')
    </if>
    <if test="status != null">
        AND status = #{status}
    </if>
    <if test="createTimeStart != null">
        AND create_time &gt;= #{createTimeStart}
    </if>
    <if test="createTimeEnd != null">
        AND create_time &lt;= #{createTimeEnd}
    </if>
    <if test="roleIds != null and roleIds.size() > 0">
        AND id IN (
            SELECT staff_id FROM train_staff_role 
            WHERE role_id IN
            <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
                #{roleId}
            </foreach>
        )
    </if>
    ORDER BY create_time DESC
</select>

<!-- 更新示例 -->
<update id="updateStaff">
    UPDATE train_staff
    <set>
        update_time = NOW(),
        <if test="name != null and name != ''">
            name = #{name},
        </if>
        <if test="phone != null and phone != ''">
            phone = #{phone},
        </if>
        <if test="status != null">
            status = #{status},
        </if>
        <if test="updater != null and updater != ''">
            updater = #{updater},
        </if>
    </set>
    WHERE id = #{id} AND team_id = #{teamId}
</update>
```

**避免的错误写法**：
```xml
<!-- 错误：不判断参数是否为空，会导致SQL语法错误 -->
<select id="badExample">
    SELECT * FROM train_staff 
    WHERE name LIKE CONCAT('%', #{name}, '%')
    AND status = #{status}
</select>
```

### 3.2 数据库表设计规范
- 所有表必须包含以下标准字段：
  ```sql
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）'
  ```

### 3.3 数据库表命名规范
- 所有表名必须加`train_`前缀
- 表名使用小写字母和下划线命名
- 示例：`train_user`、`train_product`、`train_order_detail`

### 3.4 数据库约束规范
- 禁止使用物理外键约束
- 使用逻辑外键关联
- 所有建表语句必须是MySQL兼容的DDL

### 3.5 建表语句示例
```sql
CREATE TABLE `train_example_table` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='示例表';
```

### 3.6 数据库直连操作规范
- **如果需要涉及操作数据库的，直接使用mysql client连接上去进行操作**
- 连接命令：`mysql -h www.yiyiailocal.com -P 3306 -u root -p123456`
- 适用场景：
  - 创建表、修改表结构等DDL操作
  - 插入测试数据、清理数据等DML操作
  - 查询验证数据、调试SQL语句
  - 数据库维护和调试工作
- 优势：直接高效，避免通过应用程序的复杂性
- 数据库名称是:yiyi_ai_db

## 4. 文档和API规范

### 4.1 文档生成规范
- 不需要生成Markdown说明文档
- 不要生成HTML测试文件
- 使用`springdoc-openapi-starter-webmvc-ui`管理API文档

### 4.2 API文档要求
- 所有Controller方法必须添加Swagger注解
- 提供完整的请求/响应示例
- 包含详细的参数说明

## 5. 应用测试规范

### 5.1 应用启动测试
- 使用curl命令测试应用启动状态
- **注意端口配置**：需要从`application.yml`中获取正确的服务端口
- 当前项目端口配置：`server.port: 8081`



#### 5.1.2 测试命令示例
  ```bash
  # 健康检查端点（通常不需要认证）
  curl -X GET http://localhost:8081/actuator/health

  # 需要认证的API接口测试
  curl -X GET http://localhost:8081/api/some-protected-endpoint 

  # Swagger UI访问
  curl -X GET http://localhost:8081/swagger-ui/index.html
  ```
- 如果端口配置发生变化，需要相应更新测试命令中的端口号

### 5.2 端口配置管理
- 服务端口在`src/main/resources/application.yml`中配置
- 当前配置：
  ```yaml
  server:
    port: 8081
  ```
- 测试时必须使用配置文件中的实际端口
- 不同环境可能使用不同端口，测试前需确认当前环境的端口配置

### 5.3 健康检查端点
- 确保应用提供健康检查端点
- 返回应用运行状态信息
- 使用Spring Boot Actuator提供的`/actuator/health`端点

## 6. 代码质量要求

### 6.1 代码组织
- 遵循单一职责原则
- 保持代码简洁和可读性
- 适当添加注释说明复杂逻辑

### 6.2 异常处理
- 统一异常处理机制
- 提供有意义的错误信息
- 记录必要的日志信息

### 6.3 性能考虑
- 合理使用数据库索引
- 避免N+1查询问题
- 适当使用缓存机制

## 7. 版本控制规范

### 7.1 提交规范
- 提交信息要清晰明确
- 一次提交只包含一个功能或修复
- 包含相关的测试代码

### 7.2 分支管理
- 功能开发使用feature分支
- 及时合并到主分支
- 保持分支历史清晰

## 8. 部署和运维

### 8.1 配置管理
- 环境相关配置外部化
- 敏感信息使用环境变量
- 提供不同环境的配置文件

### 8.2 监控和日志
- 添加必要的业务监控
- 合理设置日志级别
- 保留关键操作的审计日志

## 9. Controller层规范

### 9.1 团队ID获取规范
- Controller中获取teamId一律使用：`Long teamId = SecurityUtil.getCurrentTeamId();`
- **禁止使用JwtUtil来获取teamId**
- 确保团队隔离的一致性和安全性

### 9.2 分页查询字段名规范
- 如果是分页查询，字段名统一为：
  - `page` (Integer类型) - 页码，从1开始
  - `pageSize` (Integer类型) - 每页大小
- **分页参数必须封装在请求DTO中**，不要使用单独的`@RequestParam`
- 示例：
  ```java
  // 请求DTO中的分页字段
  @Data
  @Schema(description = "查询请求")
  public class TaskQueryRequest {
      @Schema(description = "页码，从1开始", example = "1")
      private Integer page = 1;

      @Schema(description = "每页大小", example = "10")
      private Integer pageSize = 10;

      // 其他查询条件...
  }

  // Controller方法
  @GetMapping("/list")
  public Result<PageResult<TaskDTO>> getTaskList(
      @Parameter(description = "查询条件") TaskQueryRequest request) {
      // 实现逻辑
  }
  ```
- **禁止使用其他变体**，如：`pageNum`、`size`、`limit`、`offset` 等

### 9.3 Creator字段获取规范
- **Controller中获取creator字段统一使用**：`String creator = SecurityUtil.getCurrentUsername();`
- **禁止使用`String.valueOf(SecurityUtil.getCurrentUserId())`的方式获取creator**
- **适用场景**：所有创建(create)操作和更新(update)操作中的creator/updater字段设置
- **示例**：
  ```java
  @PostMapping("/create")
  public Result<String> create(@RequestBody CreateRequest request) {
      Long teamId = SecurityUtil.getCurrentTeamId();
      String creator = SecurityUtil.getCurrentUsername();  // 统一使用此方式
      
      service.create(entity, teamId, creator);
      return Result.success("创建成功");
  }
  
  @PutMapping("/{id}")
  public Result<String> update(@PathVariable Long id, @RequestBody UpdateRequest request) {
      Long teamId = SecurityUtil.getCurrentTeamId();
      String updater = SecurityUtil.getCurrentUsername();  // 更新操作也使用此方式
      
      service.update(id, entity, teamId, updater);
      return Result.success("更新成功");
  }
  ```
- **确保数据一致性**：creator字段统一存储用户名而非用户ID

### 9.4 Controller API文档规范
- **所有Controller在新增或者改动后，都必须在docs/同名包路径下新增或者修改curl调用示例**
- 文件路径规范：`docs/{子包名}/{Controller名}_curl调用示例.md`
- 示例：
  - 如果修改了`com.yiyi.ai_train_playground.controller.task.TrainReceptionChatroomController`
  - 则需要在`docs/task/TrainReceptionChatroomController_curl调用示例.md`中更新对应的curl示例
- curl文件命名规范：`{Controller名}_curl调用示例.md`
- curl文档内容要求：
  - 包含所有API接口的完整curl命令
  - 提供详细的参数说明和响应示例
  - 使用实际的请求/响应数据格式
  - 便于前端开发人员参考调用

