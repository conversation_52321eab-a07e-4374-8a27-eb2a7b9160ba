# 剧本分组名称冲突友好提示修复

## 问题描述

在ScriptGroupController中，当新建分组时如果名称冲突，系统会直接抛出数据库异常，用户体验不好。

### 原始错误信息
```
系统内部错误： ### Error updating database. Cause: 
java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '唐僧' for key 
'train_script_group.uniq_group_title' ### The error may exist in file 
[C:\projects\myai\yiyi_ai_playground\target\classes\mapper\ScriptGroupMapper.xml] ### 
The error occurred while setting parameters ### SQL: INSERT INTO train_script_group ( 
group_title, parent_id, team_id, is_official, create_time, update_time ) VALUES ( ?, ?, ?, ?, 
NOW(), NOW() ) ### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 
'唐僧' for key 'train_script_group.uniq_group_title'; Duplicate entry '唐僧' for key 
'train_script_group.uniq_group_title'
```

## 修复方案

### 1. Controller层异常处理

在`ScriptGroupController`的`save`和`update`方法中添加了异常捕获和友好提示：

```java
@PostMapping
public Result<Void> save(@RequestBody ScriptGroup scriptGroup,
                       @RequestHeader("Authorization") String authorization) {
    try {
        // 原有逻辑...
        if (scriptGroupService.save(scriptGroup)) {
            return Result.success(null);
        }
        return Result.error("添加失败");
    } catch (Exception e) {
        // 检查是否是分组名称重复的异常
        if (e.getMessage() != null && e.getMessage().contains("Duplicate entry") && 
            (e.getMessage().contains("uniq_group_title") || e.getMessage().contains("uniq_team_group_title"))) {
            return Result.error("该团队下分组名称已存在，请使用其他名称");
        }
        // 其他异常
        return Result.error("添加失败：" + e.getMessage());
    }
}
```

### 2. 数据库约束优化

发现原有的唯一约束设计有问题：
- **原约束**：`UNIQUE KEY uniq_group_title (group_title)` - 全局唯一
- **新约束**：`UNIQUE KEY uniq_team_group_title (team_id, group_title)` - 团队级别唯一

这样不同团队可以创建相同名称的分组，更符合业务需求。

### 3. 数据库迁移脚本

创建了迁移脚本 `src/main/resources/db/migration/fix_script_group_unique_constraint.sql`：

```sql
-- 删除原有的全局唯一约束
ALTER TABLE `train_script_group` DROP INDEX `uniq_group_title`;

-- 添加新的团队级别唯一约束
ALTER TABLE `train_script_group` ADD UNIQUE KEY `uniq_team_group_title` (`team_id`, `group_title`);
```

### 4. 更新相关文件

- 更新了 `src/main/resources/db/init.sql` 中的表结构定义
- 更新了 `src/test/resources/schema-test.sql` 中的测试表结构
- 兼容处理新旧约束名称，确保平滑过渡

## 测试验证

创建了完整的单元测试 `ScriptGroupControllerTest`，覆盖以下场景：

1. **正常创建分组** - 验证成功场景
2. **分组名称重复** - 验证友好错误提示
3. **兼容旧约束名称** - 确保向后兼容
4. **其他异常情况** - 验证通用异常处理
5. **正常更新分组** - 验证更新成功场景
6. **更新时名称重复** - 验证更新时的友好提示
7. **权限验证** - 验证团队隔离

所有测试用例均通过验证。

## 修复效果

### 修复前
- 用户看到复杂的数据库异常信息
- 不同团队无法创建相同名称的分组

### 修复后
- 用户看到友好的错误提示："该团队下分组名称已存在，请使用其他名称"
- 不同团队可以创建相同名称的分组
- 保持团队内分组名称唯一性

## 相关文件

- `src/main/java/com/yiyi/ai_train_playground/controller/script/ScriptGroupController.java`
- `src/main/resources/db/init.sql`
- `src/main/resources/db/migration/fix_script_group_unique_constraint.sql`
- `src/test/resources/schema-test.sql`
- `src/test/java/com/yiyi/ai_train_playground/controller/script/ScriptGroupControllerTest.java`
