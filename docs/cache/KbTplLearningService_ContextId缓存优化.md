# KbTplLearningService ContextId缓存优化

## 📋 概述

为了优化知识库模板学习服务的性能，我们为`KbTplLearningServiceImpl.processKbTplLearning`方法中的ContextId生成添加了缓存机制。

## 🎯 优化目标

- **减少API调用**：避免重复调用豆包大模型生成相同的ContextId
- **提升性能**：从缓存中快速获取ContextId，减少响应时间
- **降低成本**：减少对外部API的调用次数，降低token消耗

## 🔧 技术实现

### 核心修改

#### 1. 添加缓存依赖注入
```java
private final CacheManager cacheManager;
```

#### 2. 添加缓存常量
```java
// 缓存相关常量
private static final String CONTEXT_CACHE_PREFIX = "kb:context:";
private static final long CONTEXT_CACHE_TTL_HOURS = 24; // 缓存24小时
```

#### 3. 修改ContextId获取逻辑
**修改前：**
```java
ContextResult contextResult = doubaoBigModelServiceImpl.generateContextId(systemPrompt);
```

**修改后：**
```java
ContextResult contextResult = getOrCreateContextId(systemPrompt);
```

### 新增私有方法

#### getOrCreateContextId方法
```java
/**
 * 获取或创建ContextId（带缓存）
 * 先从缓存中获取，如果没有则调用豆包大模型生成，然后存入缓存
 */
private ContextResult getOrCreateContextId(String systemPrompt) {
    try {
        // 1. 生成缓存key（基于系统提示词的hash）
        String cacheKey = generateCacheKey(systemPrompt);
        
        // 2. 先从缓存中获取
        var cachedResult = cacheManager.get(cacheKey, ContextResult.class);
        if (cachedResult.isPresent()) {
            log.info("从缓存中获取到ContextId: {}", cachedResult.get().getId());
            return cachedResult.get();
        }
        
        // 3. 缓存中没有，调用豆包大模型生成
        log.info("缓存中未找到ContextId，调用豆包大模型生成");
        ContextResult contextResult = doubaoBigModelServiceImpl.generateContextId(systemPrompt);
        
        // 4. 将结果存入缓存
        cacheManager.put(cacheKey, contextResult, CONTEXT_CACHE_TTL_HOURS, TimeUnit.HOURS);
        log.info("ContextId已存入缓存: {}, cacheKey: {}", contextResult.getId(), cacheKey);
        
        return contextResult;
        
    } catch (Exception e) {
        log.error("获取或创建ContextId失败", e);
        // 如果缓存操作失败，直接调用豆包大模型
        log.warn("缓存操作失败，直接调用豆包大模型生成ContextId");
        return doubaoBigModelServiceImpl.generateContextId(systemPrompt);
    }
}
```

#### generateCacheKey方法
```java
/**
 * 生成缓存key
 * 基于系统提示词内容生成MD5哈希作为缓存key
 */
private String generateCacheKey(String systemPrompt) {
    try {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hashBytes = md.digest(systemPrompt.getBytes(StandardCharsets.UTF_8));
        
        // 转换为16进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }
        
        String hash = sb.toString();
        String cacheKey = CONTEXT_CACHE_PREFIX + hash;
        log.debug("生成缓存key: {}, 系统提示词长度: {}", cacheKey, systemPrompt.length());
        return cacheKey;
        
    } catch (Exception e) {
        log.error("生成缓存key失败", e);
        // 如果生成hash失败，使用系统提示词长度作为简单的key
        return CONTEXT_CACHE_PREFIX + "len_" + systemPrompt.length();
    }
}
```

## 🔄 工作流程

### 缓存命中流程
1. **生成缓存key**：基于系统提示词内容的MD5哈希
2. **查询缓存**：使用CacheManager从Redis中查询
3. **返回结果**：如果缓存命中，直接返回ContextResult

### 缓存未命中流程
1. **调用豆包大模型**：使用原有逻辑生成ContextId
2. **存入缓存**：将结果存入Redis，设置24小时过期时间
3. **返回结果**：返回新生成的ContextResult

### 异常处理流程
1. **缓存操作异常**：如果缓存读写失败，降级到直接调用豆包大模型
2. **哈希生成异常**：如果MD5生成失败，使用系统提示词长度作为简单key

## 📊 性能优化效果

### 预期收益
- **响应时间**：缓存命中时响应时间从秒级降低到毫秒级
- **API调用次数**：相同系统提示词的重复调用减少到0
- **Token消耗**：避免重复生成相同ContextId的token消耗

### 缓存策略
- **缓存时间**：24小时（可配置）
- **缓存key格式**：`kb:context:{md5_hash}`
- **缓存内容**：完整的ContextResult对象

## 🧪 测试验证

### 单元测试
- **缓存key生成测试**：验证相同内容生成相同key，不同内容生成不同key
- **缓存功能测试**：验证缓存的存储和读取功能
- **异常处理测试**：验证缓存失败时的降级处理

### 测试结果
```
✅ 缓存key生成正常工作
✅ 相同系统提示词生成相同缓存key: kb:context:b5ac1436de1bf4d65d3dfc04a560362a
✅ 不同系统提示词生成不同缓存key: kb:context:1ddd190f241536d0fbd8c5ef38432292
✅ MD5哈希算法正常工作
✅ 异常处理机制正常
```

## 🔧 配置说明

### 缓存配置
- 使用项目现有的`CacheManager`接口
- 基于Redis实现缓存存储
- 支持TTL（生存时间）设置

### 日志配置
- **DEBUG级别**：缓存key生成详情
- **INFO级别**：缓存命中/未命中信息
- **WARN级别**：缓存操作失败警告
- **ERROR级别**：异常详情

## 🚀 部署说明

### 依赖要求
- Redis服务正常运行
- CacheManager Bean正确配置
- 网络连接稳定

### 监控建议
- 监控缓存命中率
- 监控Redis连接状态
- 监控ContextId生成频率

## 📈 后续优化

### 可能的改进
1. **缓存预热**：在系统启动时预生成常用的ContextId
2. **缓存分层**：本地缓存+Redis缓存的多级缓存策略
3. **智能过期**：基于使用频率的动态TTL调整
4. **缓存统计**：添加缓存命中率等监控指标

### 配置优化
1. **TTL可配置化**：通过配置文件设置缓存过期时间
2. **缓存大小限制**：设置最大缓存条目数
3. **缓存清理策略**：定期清理过期或低频使用的缓存
