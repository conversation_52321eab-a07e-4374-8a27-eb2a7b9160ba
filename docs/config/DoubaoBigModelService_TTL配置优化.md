# DoubaoBigModelService TTL配置优化

## 📋 概述

为了更好地控制豆包大模型ContextId的生存时间，我们为`DoubaoBigModelServiceImpl.generateContextId`方法添加了可配置的TTL（Time To Live）参数。

## 🎯 优化目标

- **可配置化**：通过配置文件控制ContextId的过期时间
- **灵活性**：支持不同环境使用不同的TTL值
- **成本控制**：合理设置过期时间，避免资源浪费
- **性能优化**：根据业务需求调整上下文保持时间

## 🔧 技术实现

### 核心修改

#### 1. DoubaoConfig配置类添加ctxExpire字段
```java
private Integer ctxExpire = 604800; // 上下文过期时间，默认604800秒（7天）
```

#### 2. application.yml配置文件添加ctx-expire配置
```yaml
my:
  doubao:
    ctx-expire: ${DOUBAO_CTX_EXPIRE:604800}  # 上下文过期时间（秒），默认604800秒（7天）
```

#### 3. 修改CreateContextRequest构建逻辑
**修改前：**
```java
CreateContextRequest req = CreateContextRequest.builder()
        .model(config.getNormal().getEndpoint().getName())
        .mode("common_prefix")
        .messages(messagesForReqList)
        .build();
```

**修改后：**
```java
CreateContextRequest req = CreateContextRequest.builder()
        .model(config.getNormal().getEndpoint().getName())
        .mode("common_prefix")
        .messages(messagesForReqList)
        .ttl(config.getCtxExpire())
        .build();
```

## 📊 配置说明

### 默认配置
- **默认TTL值**：604800秒（7天）
- **配置路径**：`my.doubao.ctx-expire`
- **环境变量**：`DOUBAO_CTX_EXPIRE`

### 配置示例

#### 开发环境（短期测试）
```yaml
my:
  doubao:
    ctx-expire: 3600  # 1小时
```

#### 生产环境（长期使用）
```yaml
my:
  doubao:
    ctx-expire: 604800  # 7天（默认值）
```

#### 高频使用场景
```yaml
my:
  doubao:
    ctx-expire: 1209600  # 14天
```

### 环境变量配置
```bash
# 设置为1天
export DOUBAO_CTX_EXPIRE=86400

# 设置为3天
export DOUBAO_CTX_EXPIRE=259200

# 设置为7天（默认）
export DOUBAO_CTX_EXPIRE=604800
```

## 🔄 工作流程

### TTL参数传递流程
1. **配置加载**：Spring Boot启动时从配置文件加载`ctx-expire`值
2. **依赖注入**：DoubaoConfig Bean包含ctxExpire字段
3. **服务调用**：DoubaoBigModelServiceImpl使用config.getCtxExpire()获取TTL值
4. **API请求**：将TTL值传递给豆包大模型API的CreateContextRequest
5. **上下文管理**：豆包API根据TTL值管理ContextId的生存周期

### 配置优先级
1. **环境变量**：`DOUBAO_CTX_EXPIRE`（最高优先级）
2. **配置文件**：`my.doubao.ctx-expire`
3. **默认值**：604800秒（7天）

## 📈 时间单位对照表

| 时间描述 | 秒数 | 配置值 |
|---------|------|--------|
| 1小时 | 3,600 | 3600 |
| 6小时 | 21,600 | 21600 |
| 12小时 | 43,200 | 43200 |
| 1天 | 86,400 | 86400 |
| 3天 | 259,200 | 259200 |
| 7天（默认） | 604,800 | 604800 |
| 14天 | 1,209,600 | 1209600 |
| 30天 | 2,592,000 | 2592000 |

## 🧪 测试验证

### 单元测试
- **配置加载测试**：验证ctx-expire配置正确加载
- **默认值测试**：验证默认值604800秒正确设置
- **配置结构测试**：验证DoubaoConfig配置结构完整性

### 测试结果
```
✅ 配置文件中的ctx-expire值: 604800
✅ ctx-expire配置验证通过，值为: 604800 秒（7 天）
✅ DoubaoConfig配置结构验证通过
✅ Normal Endpoint: ep-20250629195408-gtv9c
✅ Ctx Expire: 604800 秒
```

## 🔧 使用建议

### 根据业务场景选择TTL值

#### 短期测试场景
- **推荐TTL**：1-6小时（3600-21600秒）
- **适用场景**：开发测试、功能验证
- **优点**：快速释放资源，避免测试数据积累

#### 日常业务场景
- **推荐TTL**：1-3天（86400-259200秒）
- **适用场景**：常规客服对话、知识库查询
- **优点**：平衡性能和资源消耗

#### 长期会话场景
- **推荐TTL**：7-14天（604800-1209600秒）
- **适用场景**：复杂业务流程、长期项目跟踪
- **优点**：保持上下文连续性，提升用户体验

### 监控建议
1. **监控ContextId使用率**：观察实际使用时长
2. **监控API调用成本**：评估TTL设置对成本的影响
3. **监控性能指标**：关注响应时间和成功率

## 🚀 部署说明

### 配置更新步骤
1. **修改配置文件**：更新application.yml中的ctx-expire值
2. **设置环境变量**：（可选）通过环境变量覆盖配置
3. **重启应用**：使新配置生效
4. **验证配置**：通过日志确认新TTL值生效

### 注意事项
- TTL值过小可能导致频繁重新创建ContextId
- TTL值过大可能增加资源消耗和成本
- 建议根据实际业务需求和成本预算合理设置

## 📝 配置模板

### 完整配置示例
```yaml
my:
  doubao:
    think:
      model:
        name: doubao-1.5-thinking-pro-250415
    normal:
      model:
        name: doubao-1-5-pro-32k-250115
      endpoint:
        name: ep-20250629195408-gtv9c
    url: https://ark.cn-beijing.volces.com/api/v3/
    ctx-expire: ${DOUBAO_CTX_EXPIRE:604800}  # 上下文过期时间（秒）
    connection-pool:
      max-idle: 50
      max-requests: 200
      max-requests-per-host: 100
      keep-alive-duration: 3
```

### Docker环境变量配置
```dockerfile
ENV DOUBAO_CTX_EXPIRE=604800
```

### Kubernetes ConfigMap配置
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: doubao-config
data:
  DOUBAO_CTX_EXPIRE: "604800"
```

## 🔄 后续优化

### 可能的改进
1. **动态配置**：支持运行时修改TTL值
2. **智能TTL**：根据使用频率自动调整TTL
3. **分级TTL**：不同类型的上下文使用不同TTL
4. **TTL监控**：添加TTL使用情况的监控指标

### 配置增强
1. **TTL范围限制**：设置最小和最大TTL值
2. **TTL验证**：配置值合法性检查
3. **TTL预警**：TTL即将过期时的提醒机制
