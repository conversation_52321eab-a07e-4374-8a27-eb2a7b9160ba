# 文件导入Controller API调用示例

## 接口概述

文件导入Controller提供文件上传、验证、切割和存储功能，支持Excel、CSV、TXT格式的文件导入。

## 1. 上传并导入文件

### 请求示例

```bash
curl -X POST "http://localhost:8081/api/file-import/upload" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/conversation_data.xlsx" \
  -F "description=客服对话记录文件"
```

### 参数说明

- `file`: 上传的文件（必填，支持xlsx、xls、csv、txt格式，最大1MB）
- `description`: 文件描述（可选）

### 功能说明

系统会自动执行以下步骤：
1. **创建模板记录**：在`train_kb_tpl_main`表中自动创建一条记录
   - `name`：使用上传的文件名
   - `file_type`：根据文件扩展名自动识别
   - `learn_status`：设置为`file_uploading`（文件上传中）
2. **文件处理**：读取、验证和切割文件内容
3. **数据存储**：将切割后的内容块存储到`train_kb_tpl_pre`表
4. **状态更新**：处理完成后将`learn_status`更新为`un_learn`（未学习）

### 成功响应示例

```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "tplId": 123,
    "originalFileName": "conversation_data.xlsx",
    "fileSize": 102400,
    "fileType": "xlsx",
    "totalChunks": 15,
    "successChunks": 15,
    "failedChunks": 0,
    "startTime": "2025-08-08T10:00:00",
    "endTime": "2025-08-08T10:00:02",
    "processingTimeMs": 2000,
    "errorMessages": [],
    "chunkInfos": [
      {
        "index": 0,
        "size": 20480,
        "preview": "以下为一通会话\n客服：您好，有什么可以帮助您的吗？\n客户：我想了解一下这个产品的功能...",
        "hasCompleteConversation": true
      },
      {
        "index": 1,
        "size": 18560,
        "preview": "以下为一通会话\n客服：欢迎咨询\n客户：我有一个问题需要解决...",
        "hasCompleteConversation": true
      }
    ]
  }
}
```

**注意**：`tplId`字段是系统自动生成的模板ID，前端可以使用这个ID进行后续的操作（如查询模板详情、更新学习状态等）。
```

### 错误响应示例

```json
{
  "code": 0,
  "message": "文件导入失败: 文件大小超过限制，最大允许 1048576 字节",
  "data": null
}
```

## 2. 验证文件

### 请求示例

```bash
curl -X POST "http://localhost:8081/api/file-import/validate" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/test_file.csv"
```

### 参数说明

- `file`: 要验证的文件（必填）

### 成功响应示例

```json
{
  "code": 1,
  "message": "操作成功",
  "data": "文件验证通过"
}
```

### 错误响应示例

```json
{
  "code": 0,
  "message": "不支持的文件格式: pdf，支持的格式: xlsx,xls,csv,txt",
  "data": null
}
```

## 3. 预览文件切割结果

### 请求示例

```bash
curl -X GET "http://localhost:8081/api/file-import/preview/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 参数说明

- `tplId`: 模板ID（路径参数，必填）

### 成功响应示例

```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "tplId": 1,
    "originalFileName": null,
    "fileSize": null,
    "fileType": null,
    "totalChunks": null,
    "successChunks": null,
    "failedChunks": null,
    "startTime": null,
    "endTime": null,
    "processingTimeMs": null,
    "errorMessages": null,
    "chunkInfos": null
  }
}
```

## 4. 清空模板的所有块

### 请求示例

```bash
curl -X DELETE "http://localhost:8081/api/file-import/chunks/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 参数说明

- `tplId`: 模板ID（路径参数，必填）

### 成功响应示例

```json
{
  "code": 1,
  "message": "操作成功",
  "data": 15
}
```

## 支持的文件格式

### Excel文件 (.xlsx, .xls)
- 读取第一个工作表的所有内容
- 自动处理各种数据类型（文本、数字、日期、布尔值）
- 单元格内容用制表符分隔，行用换行符分隔

### CSV文件 (.csv)
- 按文本文件处理
- 保持原有的逗号分隔格式
- 支持UTF-8编码

### 文本文件 (.txt)
- 直接读取文本内容
- 支持UTF-8编码
- 保持原有的换行格式

## 文件切割规则

### 切割大小
- 默认每块20KB（可在配置文件中调整）
- 配置项：`my.file-processing.chunk-size`

### 会话完整性保护
- **会话开始标志**：`以下为一通会话`
- **会话结束标志**：`会话结束_`
- 切割时确保不会在会话中间截断
- 如果在预定位置截断会破坏会话完整性，会自动延伸到会话结束位置

### 切割示例

原始内容：
```
一些前置内容
以下为一通会话
客服：您好，有什么可以帮助您的吗？
客户：我想了解产品功能
客服：好的，我来为您详细介绍...
会话结束_2024-01-01 10:00:00
以下为一通会话
客服：欢迎咨询
...
```

切割后：
- 块1：包含完整的第一通会话
- 块2：包含完整的第二通会话
- 每块尽量接近20KB，但保证会话完整性

## 数据存储

### 存储位置
- 表名：`train_kb_tpl_pre`
- 每个块作为一条记录存储

### 字段映射
- `tpl_id`：模板ID
- `content`：块内容
- `index`：块顺序（从0开始）
- `learn_status`：学习状态（默认为'un_learn'）
  - `file_uploading`：文件上传中
  - `un_learn`：未学习
  - `learning`：学习中
  - `learned`：已学习
- `team_id`：团队ID（数据隔离）

### 存储策略
- 导入新文件时，先删除该模板的所有现有块
- 然后批量插入新的块数据
- 确保数据的一致性和完整性

## 错误处理

### 常见错误类型
1. **文件格式不支持**：返回支持的格式列表
2. **文件大小超限**：返回最大允许大小
3. **文件内容为空**：提示文件内容不能为空
4. **读取文件失败**：返回具体的错误信息
5. **数据库操作失败**：返回数据库相关错误

### 错误响应格式
```json
{
  "code": 0,
  "message": "具体的错误信息",
  "data": null
}
```

## 性能优化

### 文件处理
- 使用流式读取，避免大文件内存溢出
- 支持多种Excel格式的高效解析
- 智能的文本编码检测

### 数据库操作
- 批量插入提高性能
- 事务保证数据一致性
- 合理的索引设计支持快速查询

### 内存管理
- 及时释放文件流资源
- 分块处理避免内存占用过大
- 垃圾回收友好的对象设计

## 配置说明

### application.yml配置项
```yaml
my:
  file-processing:
    chunk-size: 20480  # 文件切割大小，默认20KB
    max-file-size: 1048576  # 最大文件大小，默认1MB
    supported-formats: "xlsx,xls,csv,txt"  # 支持的文件格式
    conversation-start-marker: "以下为一通会话"  # 会话开始标志
    conversation-end-marker: "会话结束_"  # 会话结束标志
```

### Spring文件上传配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 10MB  # Spring最大文件大小
      max-request-size: 100MB  # 最大请求大小
```

## 使用建议

1. **文件准备**：确保文件格式正确，内容包含明确的会话标志
2. **大小控制**：单个文件建议不超过1MB，以确保处理效率
3. **内容格式**：会话内容应包含明确的开始和结束标志
4. **错误处理**：根据返回的错误信息调整文件格式或内容
5. **进度监控**：通过响应中的块信息了解处理结果
