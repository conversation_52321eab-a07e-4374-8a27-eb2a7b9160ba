# QaImportController API 调用示例

## 接口概述

问答导入Controller提供Excel文件导入功能，支持问答数据的批量导入和格式验证，并提供OSS下载链接。

## 1. 验证问答Excel格式

### 接口信息
- **URL**: `POST /api/qa-import/validate`
- **描述**: 验证上传的Excel文件格式是否正确
- **认证**: 需要JWT Token

### 请求示例
```bash
curl -X POST "http://localhost:8081/api/qa-import/validate" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@your_file.xlsx"
```

### 成功响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": "文件格式验证通过"
}
```

### 失败响应示例
```json
{
  "code": 0,
  "message": "表头格式不正确，应为：问题、答案",
  "data": null
}
```

## 2. 导入问答Excel

### 接口信息
- **URL**: `POST /api/qa-import/excel`
- **描述**: 上传Excel文件导入问答数据到数据库
- **认证**: 需要JWT Token

### 请求示例
```bash
curl -X POST "http://localhost:8081/api/qa-import/excel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@your_file.xlsx"
```

### Excel文件格式要求

Excel文件必须包含以下表头（第一行）：
| 问题 | 答案 |
|------|------|

### 数据格式要求
- **问题**: 不能为空，长度不超过500字符，同一团队内不能重复
- **答案**: 不能为空，支持长文本和表情符号
- **文件格式**: 支持 `.xlsx` 和 `.xls` 格式

### Excel文件示例
| 问题 | 答案 |
|------|------|
| 防晒干是水 | 亲爱的～防晒到货水分的高的状态下是哦～利给用刀搭配防水喷雾合即可的哦～这个是正常现象～并不影响的效果哦～ |
| 有试用吗 | 亲爱的，若有赠送款，可以用1个哦。 |

系统会自动执行以下步骤：
1. **文件格式验证**：检查文件格式和表头是否正确
2. **数据验证**：验证问题和答案是否为空，问题长度是否超限，问题是否重复
3. **数据存储**：将验证通过的数据存储到`train_qa_import`表
4. **错误处理**：生成失败明细Excel文件并上传到OSS

### 成功响应示例

```json
{
  "code": 1,
  "message": "导入完成",
  "data": {
    "totalCount": 10,
    "successCount": 8,
    "failCount": 2,
    "successItems": [
      {
        "question": "防晒干是水",
        "answer": "亲爱的～防晒到货水分的高的状态下是哦～利给用刀搭配防水喷雾合即可的哦～这个是正常现象～并不影响的效果哦～"
      },
      {
        "question": "有试用吗",
        "answer": "亲爱的，若有赠送款，可以用1个哦。"
      }
    ],
    "failedItems": [
      {
        "row": 3,
        "question": "重复问题",
        "reason": "问题已存在：重复问题"
      },
      {
        "row": 5,
        "question": "缺失",
        "reason": "问题和答案不能为空"
      }
    ],
    "successExcelUrl": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_1756223694215_qa_import_success_1756223694213.xlsx",
    "failedExcelUrl": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_1756223694655_qa_import_failed_1756223694654.xlsx"
  }
}
```

### 错误响应示例

```json
{
  "code": 0,
  "message": "表头格式不正确，应为：问题、答案",
  "data": null
}
```

## 2. 验证问答Excel格式

### 请求示例

```bash
curl -X POST "http://localhost:8081/api/qa-import/validate" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/qa_data.xlsx"
```

### 参数说明

- `file`: 要验证的Excel文件（必填）

### 功能说明

验证Excel文件格式是否符合要求，包括：
- 文件格式是否为Excel（.xlsx或.xls）
- 是否包含数据
- 表头是否正确（问题、答案）

### 成功响应示例

```json
{
  "code": 1,
  "message": "文件格式验证通过",
  "data": "文件格式验证通过"
}
```

### 错误响应示例

```json
{
  "code": 0,
  "message": "文件格式不正确，请上传Excel文件（.xlsx或.xls）",
  "data": null
}
```

## 数据库表结构

### train_qa_import 表

```sql
CREATE TABLE `train_qa_import` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `question` varchar(500) NOT NULL COMMENT '问题',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '答案（支持表情存储）',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_question` (`question`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答导入表';
```

## 注意事项

1. **文件格式**：只支持Excel格式（.xlsx和.xls）
2. **表头要求**：第一行必须是"问题"和"答案"
3. **字段限制**：
   - 问题最大长度500个字符
   - 答案支持表情符号存储
   - 问题在同一团队内不能重复
4. **错误处理**：导入失败的数据会生成Excel文件并上传到OSS
5. **团队隔离**：数据按团队ID隔离存储

## 测试用例

### 正常导入测试

```bash
# 先登录获取token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "staff001", "password": "123456", "rememberMe": false}'

# 使用返回的token进行导入
curl -X POST "http://localhost:8081/api/qa-import/excel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@qa_test_data.xlsx"
```

### 格式验证测试

```bash
curl -X POST "http://localhost:8081/api/qa-import/validate" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@qa_test_data.xlsx"
```

## OSS下载链接说明

### 成功导入Excel (successExcelUrl)
- **内容**: 包含所有成功导入的问答数据
- **格式**: Excel文件，包含"问题"和"答案"两列
- **路径**: `temp.md/excel/` 目录下
- **有效期**: 24小时
- **示例**: `https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_1756223694215_qa_import_success_1756223694213.xlsx`

### 失败明细Excel (failedExcelUrl)
- **内容**: 包含所有导入失败的数据及失败原因
- **格式**: Excel文件，包含"行号"、"问题"、"答案"、"失败原因"四列
- **路径**: `temp.md/excel/` 目录下
- **有效期**: 24小时
- **示例**: `https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_1756223694655_qa_import_failed_1756223694654.xlsx`

### 下载链接验证
可以使用curl命令验证下载链接是否有效：

```bash
# 检查成功导入Excel文件
curl -I "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_1756223694215_qa_import_success_1756223694213.xlsx"

# 检查失败明细Excel文件
curl -I "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_1756223694655_qa_import_failed_1756223694654.xlsx"
```

### 响应头示例
```
HTTP/1.1 200 OK
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Length: 8234
Expires: Wed, 27 Aug 2025 15:54:54 GMT
```

## 功能特性

1. **自动生成Excel文件**: 导入完成后自动生成成功和失败的Excel文件
2. **OSS存储**: 文件上传到阿里云OSS，提供24小时有效的下载链接
3. **团队隔离**: 文件按团队ID隔离存储
4. **UTF-8支持**: 完美支持中文和表情符号
5. **详细错误信息**: 失败明细包含具体的行号和失败原因
6. **安全认证**: 所有接口都需要JWT Token认证
