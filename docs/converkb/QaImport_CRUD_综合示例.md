# 问答导入系统 CRUD 综合示例

## 概述

问答导入系统采用主表+详情表的设计模式，提供完整的增删改查功能。

## 表关系

```
train_qa_import_main (主表)
├── id (主键)
├── batch_no (批次号)
├── qa_im_name (知识库名称)
├── qa_im_desc (知识库描述)
└── team_id (团队ID)

train_qa_import_dtl (详情表)
├── id (主键)
├── qa_main_id (外键 -> train_qa_import_main.id)
├── question (问题)
├── answer (答案)
└── team_id (团队ID)
```

## 完整业务流程示例

### 1. 创建知识库（主表）

```java
@Service
public class QaImportBusinessService {
    
    @Autowired
    private TrainQaImportMainMapper mainMapper;
    
    @Autowired
    private TrainQaImportDtlMapper dtlMapper;
    
    /**
     * 创建新的知识库
     */
    @Transactional
    public Long createKnowledgeBase(String fileName, Long teamId, String userId) {
        // 生成批次号
        String batchNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        
        // 创建主表记录
        TrainQaImportMain mainRecord = new TrainQaImportMain();
        mainRecord.setBatchNo(batchNo);
        mainRecord.setQaImName(fileName);
        mainRecord.setQaImDesc(fileName);
        mainRecord.setTeamId(teamId);
        mainRecord.setCreator(userId);
        mainRecord.setUpdater(userId);
        
        mainMapper.insert(mainRecord);
        return mainRecord.getId();
    }
}
```

### 2. 添加问答详情

```java
/**
 * 批量添加问答详情
 */
@Transactional
public void addQaDetails(Long qaMainId, List<QaItem> qaItems, Long teamId, String userId) {
    for (QaItem item : qaItems) {
        // 检查问题是否已存在
        if (dtlMapper.existsByQuestion(item.getQuestion(), teamId) > 0) {
            throw new BusinessException("问题已存在：" + item.getQuestion());
        }
        
        // 插入详情记录
        TrainQaImportDtl dtlRecord = new TrainQaImportDtl();
        dtlRecord.setQaMainId(qaMainId);
        dtlRecord.setQuestion(item.getQuestion());
        dtlRecord.setAnswer(item.getAnswer());
        dtlRecord.setTeamId(teamId);
        dtlRecord.setCreator(userId);
        dtlRecord.setUpdater(userId);
        
        dtlMapper.insert(dtlRecord);
    }
}
```

### 3. 查询知识库列表

```java
/**
 * 分页查询知识库列表
 */
public PageResult<KnowledgeBaseVO> getKnowledgeBaseList(Long teamId, Integer page, Integer pageSize) {
    int offset = (page - 1) * pageSize;
    
    // 查询主表列表
    List<TrainQaImportMain> mainList = mainMapper.selectByTeamId(teamId, offset, pageSize);
    int total = mainMapper.countByTeamId(teamId);
    
    // 转换为VO并统计详情数量
    List<KnowledgeBaseVO> voList = mainList.stream().map(main -> {
        KnowledgeBaseVO vo = new KnowledgeBaseVO();
        vo.setId(main.getId());
        vo.setBatchNo(main.getBatchNo());
        vo.setQaImName(main.getQaImName());
        vo.setQaImDesc(main.getQaImDesc());
        vo.setCreateTime(main.getCreateTime());
        
        // 统计详情数量
        int dtlCount = dtlMapper.countByQaMainId(main.getId());
        vo.setQaCount(dtlCount);
        
        return vo;
    }).collect(Collectors.toList());
    
    return new PageResult<>(voList, total, page, pageSize);
}
```

### 4. 查询问答详情

```java
/**
 * 分页查询问答详情
 */
public PageResult<QaDetailVO> getQaDetailList(Long qaMainId, Integer page, Integer pageSize) {
    int offset = (page - 1) * pageSize;
    
    // 查询详情列表
    List<TrainQaImportDtl> dtlList = dtlMapper.selectByQaMainId(qaMainId, offset, pageSize);
    int total = dtlMapper.countByQaMainId(qaMainId);
    
    // 转换为VO
    List<QaDetailVO> voList = dtlList.stream().map(dtl -> {
        QaDetailVO vo = new QaDetailVO();
        vo.setId(dtl.getId());
        vo.setQuestion(dtl.getQuestion());
        vo.setAnswer(dtl.getAnswer());
        vo.setCreateTime(dtl.getCreateTime());
        return vo;
    }).collect(Collectors.toList());
    
    return new PageResult<>(voList, total, page, pageSize);
}
```

### 5. 搜索问答

```java
/**
 * 根据问题关键词搜索
 */
public PageResult<QaDetailVO> searchQaByQuestion(String keyword, Long teamId, Integer page, Integer pageSize) {
    int offset = (page - 1) * pageSize;
    
    // 模糊查询
    List<TrainQaImportDtl> dtlList = dtlMapper.selectByQuestionLike(keyword, teamId, offset, pageSize);
    int total = dtlMapper.countByQuestionLike(keyword, teamId);
    
    // 转换为VO
    List<QaDetailVO> voList = dtlList.stream().map(dtl -> {
        QaDetailVO vo = new QaDetailVO();
        vo.setId(dtl.getId());
        vo.setQaMainId(dtl.getQaMainId());
        vo.setQuestion(dtl.getQuestion());
        vo.setAnswer(dtl.getAnswer());
        vo.setCreateTime(dtl.getCreateTime());
        return vo;
    }).collect(Collectors.toList());
    
    return new PageResult<>(voList, total, page, pageSize);
}
```

### 6. 更新问答

```java
/**
 * 更新问答详情
 */
@Transactional
public void updateQaDetail(Long id, String question, String answer, Long teamId, String userId) {
    // 查询原记录
    TrainQaImportDtl original = dtlMapper.selectById(id);
    if (original == null || !original.getTeamId().equals(teamId)) {
        throw new BusinessException("记录不存在或无权限");
    }
    
    // 如果问题发生变化，检查新问题是否重复
    if (!original.getQuestion().equals(question)) {
        if (dtlMapper.existsByQuestion(question, teamId) > 0) {
            throw new BusinessException("问题已存在：" + question);
        }
    }
    
    // 更新记录
    TrainQaImportDtl updateRecord = new TrainQaImportDtl();
    updateRecord.setId(id);
    updateRecord.setQuestion(question);
    updateRecord.setAnswer(answer);
    updateRecord.setTeamId(teamId);
    updateRecord.setUpdater(userId);
    
    dtlMapper.updateById(updateRecord);
}
```

### 7. 删除操作

```java
/**
 * 删除知识库（级联删除）
 */
@Transactional
public void deleteKnowledgeBase(Long qaMainId, Long teamId) {
    // 验证权限
    TrainQaImportMain mainRecord = mainMapper.selectById(qaMainId);
    if (mainRecord == null || !mainRecord.getTeamId().equals(teamId)) {
        throw new BusinessException("知识库不存在或无权限");
    }
    
    // 先删除详情记录
    dtlMapper.deleteByQaMainId(qaMainId);
    
    // 再删除主表记录
    mainMapper.deleteById(qaMainId);
}

/**
 * 删除单个问答
 */
@Transactional
public void deleteQaDetail(Long id, Long teamId) {
    // 验证权限
    TrainQaImportDtl dtlRecord = dtlMapper.selectById(id);
    if (dtlRecord == null || !dtlRecord.getTeamId().equals(teamId)) {
        throw new BusinessException("记录不存在或无权限");
    }
    
    dtlMapper.deleteById(id);
}

/**
 * 批量删除问答
 */
@Transactional
public void batchDeleteQaDetails(List<Long> ids, Long teamId) {
    // 验证所有记录的权限
    for (Long id : ids) {
        TrainQaImportDtl dtlRecord = dtlMapper.selectById(id);
        if (dtlRecord == null || !dtlRecord.getTeamId().equals(teamId)) {
            throw new BusinessException("记录ID " + id + " 不存在或无权限");
        }
    }
    
    // 批量删除
    dtlMapper.deleteByIds(ids);
}
```

## 数据统计示例

```java
/**
 * 获取团队问答统计信息
 */
public QaStatisticsVO getQaStatistics(Long teamId) {
    QaStatisticsVO statistics = new QaStatisticsVO();
    
    // 知识库总数
    int kbCount = mainMapper.countByTeamId(teamId);
    statistics.setKnowledgeBaseCount(kbCount);
    
    // 问答总数
    int qaCount = dtlMapper.countByTeamId(teamId);
    statistics.setQaCount(qaCount);
    
    // 最近7天新增的知识库
    LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
    // 这里需要在Mapper中添加按时间范围查询的方法
    
    return statistics;
}
```

## 性能优化建议

### 1. 索引优化
```sql
-- 主表索引
CREATE INDEX idx_main_team_create ON train_qa_import_main(team_id, create_time);
CREATE INDEX idx_main_batch_team ON train_qa_import_main(batch_no, team_id);

-- 详情表索引
CREATE INDEX idx_dtl_main_create ON train_qa_import_dtl(qa_main_id, create_time);
CREATE INDEX idx_dtl_team_question ON train_qa_import_dtl(team_id, question);
CREATE INDEX idx_dtl_team_create ON train_qa_import_dtl(team_id, create_time);
```

### 2. 分页优化
```java
// 使用游标分页替代offset分页（大数据量时）
public List<TrainQaImportDtl> selectByTeamIdWithCursor(Long teamId, Long lastId, Integer limit) {
    // 在Mapper中实现基于ID的游标分页
    return dtlMapper.selectByTeamIdWithCursor(teamId, lastId, limit);
}
```

### 3. 缓存策略
```java
@Cacheable(value = "qa_statistics", key = "#teamId")
public QaStatisticsVO getQaStatistics(Long teamId) {
    // 缓存统计信息
}

@CacheEvict(value = "qa_statistics", key = "#teamId")
public void evictQaStatistics(Long teamId) {
    // 数据变更时清除缓存
}
```

## 注意事项

1. **事务管理**: 涉及多表操作时必须使用事务
2. **权限控制**: 所有操作都要验证团队ID权限
3. **数据一致性**: 删除主表时必须先删除详情表
4. **性能考虑**: 大数据量时使用分页查询
5. **索引优化**: 根据查询模式建立合适的索引
6. **缓存策略**: 对频繁查询的统计数据进行缓存
