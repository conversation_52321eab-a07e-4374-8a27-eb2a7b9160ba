# QaManagementController curl调用示例

## 概述

问答管理控制器的完整curl调用示例，包含所有增删改查接口的详细调用方法。

## 接口总览

### 知识库管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 查询知识库列表 | GET | `/api/qa-management/main/list` | 分页查询知识库列表，支持按名称模糊搜索 |
| 查询知识库详情 | GET | `/api/qa-management/main/{id}` | 根据ID查询知识库详情 |
| 编辑知识库 | PUT | `/api/qa-management/main/{id}` | 编辑知识库名称和描述 |
| 删除知识库 | DELETE | `/api/qa-management/main/{id}` | 删除知识库（级联删除明细表） |
| 批量删除知识库 | DELETE | `/api/qa-management/main/batch` | 批量删除知识库（级联删除明细表） |

### 问答详情管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 查询问答详情列表 | GET | `/api/qa-management/detail/list` | 分页查询问答详情，支持搜索 |
| 查询问答详情 | GET | `/api/qa-management/detail/{id}` | 根据ID查询问答详情 |
| 创建问答详情 | POST | `/api/qa-management/detail` | 创建新的问答详情 |
| 更新问答详情 | PUT | `/api/qa-management/detail/{id}` | 更新问答详情 |
| 删除问答详情 | DELETE | `/api/qa-management/detail/{id}` | 删除单个问答详情 |
| 批量删除问答详情 | DELETE | `/api/qa-management/detail/batch` | 批量删除问答详情 |

## 通用参数说明

### 分页参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小，建议不超过100 |

### 响应格式

所有接口都遵循统一的响应格式：

```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "操作成功",         // 响应消息
  "data": {}                    // 响应数据，具体结构见各接口说明
}
```

### 错误码说明

| 错误码 | 说明 | 常见原因 |
|--------|------|----------|
| 1 | 成功 | 操作成功 |
| 0 | 失败 | 业务逻辑错误 |
| 400 | 请求参数错误 | 参数格式不正确或缺少必填参数 |
| 401 | 未授权 | JWT token无效或过期 |
| 403 | 无权限 | 没有访问该资源的权限 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 409 | 数据冲突 | 数据重复或冲突 |
| 500 | 服务器内部错误 | 系统异常 |

## 前置条件

### 1. 获取认证Token

```bash
# 登录获取token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "staffId": 1,
    "username": "staff001",
    "displayName": "张三",
    "teamId": 1,
    "status": 1,
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
  }
}
```

### 2. 设置Token变量

```bash
# 提取token并设置为环境变量
TOKEN="eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

## 知识库管理接口

### 1. 查询知识库列表

**接口**: `GET /api/qa-management/main/list`

**入参**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小，建议不超过100 |
| qaImName | String | 否 | - | 知识库名称（模糊搜索） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "list": [                   // 知识库列表
      {
        "id": 1,                // 知识库ID
        "batchNo": "20250827012536578",  // 批次号
        "qaImName": "测试知识库",         // 知识库名称
        "qaImDesc": "测试知识库描述",     // 知识库描述
        "teamId": 1,            // 团队ID
        "createTime": "2025-08-27T01:25:36",  // 创建时间
        "updateTime": "2025-08-27T01:25:36",  // 更新时间
        "creator": "93",        // 创建人
        "updater": "93",        // 更新人
        "version": 0            // 版本号
      }
    ],
    "total": 1,                 // 总记录数
    "page": 1,                  // 当前页码
    "pageSize": 10              // 每页大小
  }
}
```

**curl示例**:
```bash
# 基本查询
curl -X GET "http://localhost:8081/api/qa-management/main/list" \
  -H "Authorization: Bearer $TOKEN"

# 分页查询
curl -X GET "http://localhost:8081/api/qa-management/main/list?page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"

# 查询第2页
curl -X GET "http://localhost:8081/api/qa-management/main/list?page=2&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 按知识库名称模糊搜索
curl -X GET "http://localhost:8081/api/qa-management/main/list?qaImName=客服" \
  -H "Authorization: Bearer $TOKEN"

# 按知识库名称搜索并分页
curl -X GET "http://localhost:8081/api/qa-management/main/list?qaImName=测试&page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"

# 模糊搜索示例：搜索名称包含"知识库"的记录
curl -X GET "http://localhost:8081/api/qa-management/main/list?qaImName=知识库&page=1&pageSize=20" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. 查询知识库主表详情

**接口**: `GET /api/qa-management/main/{id}`

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 知识库ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "id": 1,                    // 知识库ID
    "batchNo": "20250827012536578",      // 批次号
    "qaImName": "测试知识库",             // 知识库名称
    "qaImDesc": "测试知识库描述",         // 知识库描述
    "teamId": 1,                // 团队ID
    "createTime": "2025-08-27T01:25:36", // 创建时间
    "updateTime": "2025-08-27T01:25:36", // 更新时间
    "creator": "93",            // 创建人
    "updater": "93",            // 更新人
    "version": 0                // 版本号
  }
}
```

**curl示例**:
```bash
# 查询ID为1的知识库详情
curl -X GET "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN"

# 查询ID为2的知识库详情
curl -X GET "http://localhost:8081/api/qa-management/main/2" \
  -H "Authorization: Bearer $TOKEN"
```

### 3. 编辑知识库

**接口**: `PUT /api/qa-management/main/{id}`

**功能说明**: 编辑知识库的名称和描述，其他字段（如批次号、创建时间等）不可修改

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 知识库ID（路径参数） |
| qaImName | String | 是 | 知识库名称，长度不超过100个字符 |
| qaImDesc | String | 否 | 知识库描述，长度不超过500个字符 |

**请求体示例**:
```json
{
  "qaImName": "更新后的知识库名称",       // 必填：知识库名称
  "qaImDesc": "更新后的知识库描述信息"   // 可选：知识库描述
}
```

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "更新成功",         // 响应消息
  "data": "更新成功"            // 响应数据
}
```

**curl示例**:
```bash
# 更新知识库名称和描述
curl -X PUT "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaImName": "客服专用知识库",
    "qaImDesc": "用于客服人员回答客户常见问题的标准答案库"
  }'

# 只更新知识库名称
curl -X PUT "http://localhost:8081/api/qa-management/main/2" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaImName": "产品FAQ知识库"
  }'

# 更新知识库名称（清空描述）
curl -X PUT "http://localhost:8081/api/qa-management/main/3" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaImName": "销售支持知识库",
    "qaImDesc": ""
  }'

# 错误示例：知识库名称不能为空
curl -X PUT "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaImName": "",
    "qaImDesc": "描述信息"
  }'
# 返回: {"code": 0, "message": "知识库名称不能为空", "data": null}
```

### 4. 删除知识库

**接口**: `DELETE /api/qa-management/main/{id}`

**功能说明**: 删除知识库主表记录，同时级联删除该知识库下的所有问答详情记录（train_qa_import_dtl表中相关数据）

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 知识库ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "删除成功",         // 响应消息
  "data": "删除成功"            // 响应数据
}
```

**curl示例**:
```bash
# 删除ID为1的知识库（级联删除所有问答详情）
curl -X DELETE "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN"

# 删除前可以先查看该知识库下有多少问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1" \
  -H "Authorization: Bearer $TOKEN"

# 然后再删除知识库（包括所有问答详情）
curl -X DELETE "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN"
```

### 5. 批量删除知识库

**接口**: `DELETE /api/qa-management/main/batch`

**功能说明**: 批量删除知识库主表记录，同时级联删除所有相关的问答详情记录（train_qa_import_dtl表中相关数据）

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 是 | 逗号分隔的ID列表，如"1,2,3" |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "批量删除成功",     // 响应消息
  "data": "批量删除成功"        // 响应数据
}
```

**curl示例**:
```bash
# 批量删除ID为1,2,3的知识库
curl -X DELETE "http://localhost:8081/api/qa-management/main/batch?ids=1,2,3" \
  -H "Authorization: Bearer $TOKEN"

# 批量删除ID为4,5的知识库
curl -X DELETE "http://localhost:8081/api/qa-management/main/batch?ids=4,5" \
  -H "Authorization: Bearer $TOKEN"
```

## 问答详情管理接口

### 1. 查询问答详情列表

**接口**: `GET /api/qa-management/detail/list`

**入参**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小，建议不超过100 |
| qaMainId | Long | 否 | - | 主表ID，查询指定知识库下的问答 |
| question | String | 否 | - | 问题关键词，模糊查询 |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "list": [                   // 问答详情列表
      {
        "id": 233,              // 问答详情ID
        "qaMainId": 1,          // 主表ID
        "question": "防晒干是水", // 问题
        "answer": "亲爱的～防晒到货水分的高的状态下是哦～", // 答案
        "teamId": 1,            // 团队ID
        "createTime": "2025-08-27T01:25:36",  // 创建时间
        "updateTime": "2025-08-27T01:25:36",  // 更新时间
        "creator": "93",        // 创建人
        "updater": "93",        // 更新人
        "version": 0            // 版本号
      }
    ],
    "total": 1,                 // 总记录数
    "page": 1,                  // 当前页码
    "pageSize": 10              // 每页大小
  }
}
```

**curl示例**:
```bash
# 查询所有问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list" \
  -H "Authorization: Bearer $TOKEN"

# 分页查询
curl -X GET "http://localhost:8081/api/qa-management/detail/list?page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 查询指定知识库下的问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 模糊搜索问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?question=防晒&page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 在指定知识库中搜索问题（本次修复的重点功能）
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&question=使用&page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. 查询问答详情

**接口**: `GET /api/qa-management/detail/{id}`

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 问答详情ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "id": 233,                  // 问答详情ID
    "qaMainId": 1,              // 主表ID
    "question": "防晒干是水",    // 问题
    "answer": "亲爱的～防晒到货水分的高的状态下是哦～", // 答案
    "teamId": 1,                // 团队ID
    "createTime": "2025-08-27T01:25:36",  // 创建时间
    "updateTime": "2025-08-27T01:25:36",  // 更新时间
    "creator": "93",            // 创建人
    "updater": "93",            // 更新人
    "version": 0                // 版本号
  }
}
```

**curl示例**:
```bash
# 查询ID为233的问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer $TOKEN"

# 查询ID为234的问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/234" \
  -H "Authorization: Bearer $TOKEN"
```

### 3. 创建问答详情

**接口**: `POST /api/qa-management/detail`

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| qaMainId | Long | 是 | 主表ID，关联的知识库ID |
| question | String | 是 | 问题，长度不超过500字符 |
| answer | String | 是 | 答案，支持表情符号 |

**请求体示例**:
```json
{
  "qaMainId": 1,                // 主表ID
  "question": "防晒使用方法",    // 问题
  "answer": "每天早上出门前30分钟涂抹防晒霜"  // 答案
}
```

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "创建成功",         // 响应消息
  "data": "创建成功"            // 响应数据
}
```

**curl示例**:
```bash
# 创建基本问答
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "防晒使用方法",
    "answer": "每天早上出门前30分钟涂抹防晒霜"
  }'

# 创建包含表情的问答
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "产品效果如何",
    "answer": "亲爱的～这款产品效果很好哦😊，很多客户都反馈不错呢💕"
  }'

# 创建长答案问答
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "产品成分介绍",
    "answer": "本产品主要成分包括：\n1. 透明质酸：深层补水保湿\n2. 维生素C：美白抗氧化\n3. 烟酰胺：控油收缩毛孔\n4. 神经酰胺：修护肌肤屏障\n所有成分均经过安全性测试，敏感肌也可放心使用。"
  }'
```

### 4. 更新问答详情

**接口**: `PUT /api/qa-management/detail/{id}`

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 问答详情ID（路径参数） |
| question | String | 是 | 问题，长度不超过500字符 |
| answer | String | 是 | 答案，支持表情符号 |

**请求体示例**:
```json
{
  "question": "防晒使用方法（更新版）",  // 更新后的问题
  "answer": "每天早上出门前30分钟涂抹防晒霜，注意补涂，每2-3小时补涂一次"  // 更新后的答案
}
```

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "更新成功",         // 响应消息
  "data": "更新成功"            // 响应数据
}
```

**curl示例**:
```bash
# 更新基本信息
curl -X PUT "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "防晒使用方法（更新版）",
    "answer": "每天早上出门前30分钟涂抹防晒霜，注意补涂，每2-3小时补涂一次"
  }'

# 更新包含表情的答案
curl -X PUT "http://localhost:8081/api/qa-management/detail/234" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "产品保质期",
    "answer": "亲爱的～产品保质期是3年哦😊，开封后建议12个月内用完💕"
  }'
```

### 5. 删除问答详情

**接口**: `DELETE /api/qa-management/detail/{id}`

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 问答详情ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "删除成功",         // 响应消息
  "data": "删除成功"            // 响应数据
}
```

**curl示例**:
```bash
# 删除单个问答详情
curl -X DELETE "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer $TOKEN"

# 删除另一个问答详情
curl -X DELETE "http://localhost:8081/api/qa-management/detail/234" \
  -H "Authorization: Bearer $TOKEN"
```

### 6. 批量删除问答详情

**接口**: `DELETE /api/qa-management/detail/batch`

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 是 | 逗号分隔的ID列表，如"233,234,235" |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "批量删除成功",     // 响应消息
  "data": "批量删除成功"        // 响应数据
}
```

**curl示例**:
```bash
# 批量删除多个问答详情
curl -X DELETE "http://localhost:8081/api/qa-management/detail/batch?ids=233,234,235" \
  -H "Authorization: Bearer $TOKEN"

# 批量删除两个问答详情
curl -X DELETE "http://localhost:8081/api/qa-management/detail/batch?ids=236,237" \
  -H "Authorization: Bearer $TOKEN"
```

## 完整业务流程示例

### 场景1：新建知识库并添加问答

```bash
# 1. 先通过Excel导入创建知识库
curl -X POST "http://localhost:8081/api/qa-import/excel" \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@qa_data.xlsx"

# 2. 查询新创建的知识库
curl -X GET "http://localhost:8081/api/qa-management/main/list?page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"

# 3. 在知识库中添加新的问答
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "发货时间",
    "answer": "一般情况下，我们会在24小时内发货，节假日可能会延迟"
  }'
```

### 场景2：搜索和管理问答

```bash
# 1. 搜索包含"发货"的问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?question=发货" \
  -H "Authorization: Bearer $TOKEN"

# 2. 查看具体问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/238" \
  -H "Authorization: Bearer $TOKEN"

# 3. 更新问答内容
curl -X PUT "http://localhost:8081/api/qa-management/detail/238" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "发货时间说明",
    "answer": "工作日下单当天发货，周末及节假日顺延至下一工作日发货"
  }'
```

### 场景3：清理和维护

```bash
# 1. 查询某个知识库下的所有问答
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1" \
  -H "Authorization: Bearer $TOKEN"

# 2. 批量删除不需要的问答
curl -X DELETE "http://localhost:8081/api/qa-management/detail/batch?ids=239,240,241" \
  -H "Authorization: Bearer $TOKEN"

# 3. 删除整个知识库
curl -X DELETE "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN"
```

## 错误处理示例

### 1. 处理认证错误

```bash
# 使用无效token
curl -X GET "http://localhost:8081/api/qa-management/main/list" \
  -H "Authorization: Bearer invalid_token"

# 响应: {"code": 0, "message": "未授权", "data": null}
```

### 2. 处理参数错误

```bash
# 创建问答时缺少必填字段
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": ""
  }'

# 响应: {"code": 0, "message": "问题不能为空", "data": null}
```

### 3. 处理重复问题

```bash
# 创建重复问题
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "防晒干是水",
    "answer": "重复的问题"
  }'

# 响应: {"code": 0, "message": "问题已存在：防晒干是水", "data": null}
```

## 性能优化建议

### 1. 分页查询

```bash
# 推荐：使用合理的分页大小
curl -X GET "http://localhost:8081/api/qa-management/detail/list?page=1&pageSize=20" \
  -H "Authorization: Bearer $TOKEN"

# 不推荐：一次查询过多数据
curl -X GET "http://localhost:8081/api/qa-management/detail/list?page=1&pageSize=1000" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. 精确查询

```bash
# 推荐：在指定知识库中搜索
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&question=防晒" \
  -H "Authorization: Bearer $TOKEN"

# 可用但性能较差：全局搜索
curl -X GET "http://localhost:8081/api/qa-management/detail/list?question=防晒" \
  -H "Authorization: Bearer $TOKEN"

## 重要修复说明

### 问题描述
在2025年9月5日之前，`getDetailList`方法存在一个逻辑缺陷：
- 当`qaMainId`有值时，完全忽略`question`参数
- 但实际业务需求是：`qaMainId`是必传的，如果`question`也有值，应该在指定知识库范围内进行问题的模糊查询

### 修复内容
1. **修改Mapper层**：`TrainQaImportDtlMapper.selectByQaMainId`和`countByQaMainId`方法增加`question`参数
2. **修改XML实现**：在SQL中增加动态条件，当`question`不为空时增加`LIKE`查询
3. **修改Service层**：更新方法签名以支持新的参数
4. **修改Controller层**：优化查询逻辑，支持同时按`qaMainId`和`question`查询

### 修复后的查询逻辑
- **qaMainId有值 + question有值**：在指定知识库范围内进行问题模糊查询 ✅
- **仅qaMainId有值**：查询指定知识库下的所有问答
- **仅question有值**：跨所有知识库进行问题模糊查询
- **都没有值**：查询团队下所有问答

### 测试验证
已通过完整的单元测试验证修复功能正常工作，包括：
- 不带问题过滤的查询
- 带问题过滤的查询
- 问题过滤无结果的查询

### 关键示例
```bash
# 修复前：这个调用会忽略question参数，只按qaMainId查询
# 修复后：这个调用会在知识库1中搜索包含"防晒"的问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&question=防晒" \
  -H "Authorization: Bearer $TOKEN"
```
```
