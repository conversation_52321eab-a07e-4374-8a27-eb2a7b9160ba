# 会话知识库模板管理API - curl调用示例

## 接口概览

会话知识库模板管理模块提供完整的模板增删改查功能，支持对话明细的管理和token统计。

**基础URL**: `http://localhost:8081/api/kb-templates`

**认证方式**: JWT Bearer Token

## 接口列表

### 1. 创建会话知识库模板

**接口地址**: `POST /api/kb-templates`

**描述**: 创建新的会话知识库模板，包含主表信息和对话明细

**请求示例**:
```bash
curl -X POST http://localhost:8081/api/kb-templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "客服售前标准话术",
    "description": "用于售前咨询的标准话术模板，包含常见问答",
    "fileType": "csv",
    "learnStatus": "un_learn",
    "details": [
      {
        "content": "您好，有什么可以帮助您的吗？",
        "tplType": "pre_sales"
      },
      {
        "content": "我想了解一下这个产品的功能",
        "tplType": "pre_sales"
      },
      {
        "content": "好的，我来为您详细介绍一下产品功能...",
        "tplType": "pre_sales"
      }
    ]
  }'
```

**成功响应**:
```json
{
  "code": 1,
  "message": "创建成功",
  "data": 1
}
```

**参数说明**:
- `name`: 模板名称（必填，最长100字符）
- `description`: 模板描述（可选，最长500字符，如果为空则使用name值填充）
- `fileType`: 文件类型（可选，枚举值：csv/excel/txt）
- `learnStatus`: 学习状态（可选，枚举值：file_uploading/un_learn/learning/learned）
  - `file_uploading`: 文件上传中
  - `un_learn`: 未学习
  - `learning`: 学习中
  - `learned`: 已学习
- `details`: 对话明细列表
  - `content`: 对话内容（必填）
  - `tplType`: 模板类型（必填，枚举值：pre_sales/saling/after_sale/other）

---

### 2. 获取模板详情

**接口地址**: `GET /api/kb-templates/{id}`

**描述**: 根据ID获取会话知识库模板的详细信息，支持对话明细分页查询

**请求示例**:
```bash
# 基础分页查询明细
curl -X GET "http://localhost:8081/api/kb-templates/1?page=1&pageSize=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 按模板类型筛选明细
curl -X GET "http://localhost:8081/api/kb-templates/1?page=1&pageSize=5&tplType=pre_sales" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**请求参数**:
- `page`: 页码，从1开始（可选，默认1）
- `pageSize`: 每页大小（可选，默认10）
- `tplType`: 模板类型筛选（可选，枚举值：pre_sales/saling/after_sale/other）

**成功响应**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "name": "客服售前标准话术",
    "description": "专为售前客服设计的标准话术模板，包含常见问答和处理流程",
    "tokens": 1500,
    "fileType": "csv",
    "learnStatus": "un_learn",
    "createTime": "2025-08-07T10:00:00",
    "updateTime": "2025-08-07T10:30:00",
    "version": 0,
    "details": {
      "records": [
        {
          "id": 1,
          "content": "您好，有什么可以帮助您的吗？",
          "tplTypeCode": "pre_sales",
          "tplTypeValue": "售前"
        },
        {
          "id": 2,
          "content": "我想了解一下这个产品的功能",
          "tplTypeCode": "pre_sales",
          "tplTypeValue": "售前"
        },
        {
          "id": 3,
          "content": "好的，我来为您详细介绍一下产品功能...",
          "tplTypeCode": "pre_sales",
          "tplTypeValue": "售前"
        }
      ],
      "total": 8,
      "page": 1,
      "size": 5,
      "pages": 2
    }
  }
}
```

**响应字段说明**:
- `details`: 对话明细分页结果
  - `records`: 明细记录列表
    - `tplTypeCode`: 模板类型代码（pre_sales/saling/after_sale/other）
    - `tplTypeValue`: 模板类型描述（售前/销售中/售后/其他）
  - `total`: 总记录数
  - `page`: 当前页码
  - `size`: 每页大小
  - `pages`: 总页数

---

### 3. 分页查询模板列表

**接口地址**: `GET /api/kb-templates`

**描述**: 分页查询会话知识库模板列表，支持按名称和模板类型筛选

**请求示例**:
```bash
# 基础分页查询
curl -X GET "http://localhost:8081/api/kb-templates?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 带筛选条件的查询
curl -X GET "http://localhost:8081/api/kb-templates?page=1&pageSize=10&name=客服&tplType=pre_sales" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**成功响应**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "客服售前标准话术",
        "description": "专为售前客服设计的标准话术模板",
        "tokens": 1500,
        "detailCount": 8,
        "fileType": "csv",
        "learnStatusCode": "un_learn",
        "learnStatusValue": "未学习",
        "createTime": "2025-08-07T10:00:00",
        "updateTime": "2025-08-07T10:30:00"
      },
      {
        "id": 2,
        "name": "售后服务话术",
        "description": "售后客服专用话术模板",
        "tokens": 1200,
        "detailCount": 6,
        "fileType": "excel",
        "learnStatusCode": "learned",
        "learnStatusValue": "已学习",
        "createTime": "2025-08-07T09:00:00",
        "updateTime": "2025-08-07T09:30:00"
      }
    ],
    "total": 25,
    "page": 1,
    "size": 10,
    "pages": 3
  }
}
```

**查询参数**:
- `page`: 页码，从1开始（可选，默认1）
- `pageSize`: 每页大小（可选，默认10）
- `name`: 模板名称，支持模糊查询（可选）
- `tplType`: 模板类型筛选（可选，枚举值：pre_sales/saling/after_sale/other）

**响应字段说明**:
- `learnStatusCode`: 学习状态代码（枚举值：file_uploading/un_learn/learning/learned）
- `learnStatusValue`: 学习状态描述（对应的中文描述）
  - `file_uploading`: 文件上传中
  - `un_learn`: 未学习
  - `learning`: 学习中
  - `learned`: 已学习

---

### 4. 更新模板

**接口地址**: `PUT /api/kb-templates/{id}`

**描述**: 更新会话知识库模板的名称和对话明细

**请求示例**:
```bash
curl -X PUT http://localhost:8081/api/kb-templates/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "更新后的客服话术",
    "description": "更新后的客服话术模板，包含更多场景",
    "fileType": "excel",
    "learnStatus": "learning",
    "details": [
      {
        "content": "您好，欢迎咨询！",
        "tplType": "pre_sales"
      },
      {
        "content": "我想了解产品价格",
        "tplType": "pre_sales"
      }
    ]
  }'
```

**成功响应**:
```json
{
  "code": 1,
  "message": "更新成功",
  "data": true
}
```

**错误响应**:
```json
{
  "code": 500,
  "message": "模板名称已存在",
  "data": null
}
```

---

### 5. 删除模板

**接口地址**: `DELETE /api/kb-templates/{id}`

**描述**: 删除指定的会话知识库模板及其所有对话明细

**请求示例**:
```bash
curl -X DELETE http://localhost:8081/api/kb-templates/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**成功响应**:
```json
{
  "code": 1,
  "message": "删除成功",
  "data": true
}
```

**错误响应**:
```json
{
  "code": 500,
  "message": "模板不存在或删除失败",
  "data": null
}
```

---

### 6. 检查名称是否存在

**接口地址**: `GET /api/kb-templates/check-name`

**描述**: 检查指定的模板名称是否已存在

**请求示例**:
```bash
# 新增时检查
curl -X GET "http://localhost:8081/api/kb-templates/check-name?name=客服售前标准话术" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 更新时检查（排除当前记录）
curl -X GET "http://localhost:8081/api/kb-templates/check-name?name=客服售前标准话术&excludeId=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**成功响应**:
```json
{
  "code": 1,
  "message": "检查完成",
  "data": false
}
```

**参数说明**:
- `name`: 要检查的模板名称（必填）
- `excludeId`: 排除的ID，用于更新时检查（可选）
- 返回值：`true`表示名称已存在，`false`表示名称可用

---

### 7. 更新模板token数量

**接口地址**: `PUT /api/kb-templates/{id}/tokens`

**描述**: 更新指定模板的累计token消耗数量

**请求示例**:
```bash
curl -X PUT "http://localhost:8081/api/kb-templates/1/tokens?tokens=2000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**成功响应**:
```json
{
  "code": 1,
  "message": "更新成功",
  "data": true
}
```

**参数说明**:
- `tokens`: 新的token数量（必填，整数类型）

---

## 通用说明

### 认证说明
所有接口都需要在请求头中携带JWT Token：
```
Authorization: Bearer YOUR_JWT_TOKEN
```

### 团队隔离
所有操作都基于当前用户的团队ID进行数据隔离，用户只能操作自己团队的模板。

### 错误码说明
- `code = 1`: 操作成功
- `code = 500`: 操作失败
- HTTP状态码均为200，具体成功失败通过响应中的`code`字段判断

### 数据库表关系
- `train_kb_tpl_main`: 模板主表，存储模板基本信息
- `train_kb_tpl_detail`: 模板明细表，存储对话内容
- 两表通过`tpl_id`关联，支持一对多关系

### 功能特性
1. **完整CRUD操作**: 支持模板的增删改查
2. **分页查询**: 支持大数据量的分页查询
3. **模糊搜索**: 支持按名称模糊查询
4. **类型筛选**: 支持按模板类型筛选
5. **名称唯一性检查**: 防止重复名称
6. **Token统计**: 支持模板使用的token数量统计
7. **文件类型管理**: 支持csv、excel、txt三种文件类型标识
8. **学习状态跟踪**: 支持un_learn、learning、learned三种学习状态
9. **团队隔离**: 确保数据安全性
10. **事务支持**: 保证数据一致性
11. **乐观锁**: 防止并发更新冲突

### 前端集成建议
1. 创建模板时建议先调用名称检查接口
2. 更新模板时注意处理乐观锁冲突
3. 删除操作建议添加确认提示
4. 分页查询支持搜索和筛选功能
5. Token统计可用于成本分析和使用统计