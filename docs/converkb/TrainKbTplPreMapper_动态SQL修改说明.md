# TrainKbTplPreMapper 动态SQL修改说明

## 修改概述

将 `TrainKbTplPreMapper.xml` 中的 `updateById` 方法改为动态SQL，支持忽略空字段的更新。

## 修改前后对比

### 修改前（静态SQL）
```xml
<update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre">
    UPDATE train_kb_tpl_pre
    SET content = #{content},
        `index` = #{index},
        learn_status = #{learnStatus},
        updater = #{updater},
        version = version + 1
    WHERE id = #{id} 
      AND team_id = #{teamId}
      AND version = #{version}
</update>
```

### 修改后（动态SQL）
```xml
<update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre">
    UPDATE train_kb_tpl_pre
    <set>
        <if test="content != null and content != ''">content = #{content},</if>
        <if test="index != null">`index` = #{index},</if>
        <if test="learnStatus != null and learnStatus != ''">learn_status = #{learnStatus},</if>
        <if test="updater != null and updater != ''">updater = #{updater},</if>
        version = version + 1
    </set>
    WHERE id = #{id} 
      AND team_id = #{teamId}
      AND version = #{version}
</update>
```

## 动态SQL规则

1. **content字段**：当 `content != null and content != ''` 时才更新
2. **index字段**：当 `index != null` 时才更新（数字类型，只判断null）
3. **learnStatus字段**：当 `learnStatus != null and learnStatus != ''` 时才更新
4. **updater字段**：当 `updater != null and updater != ''` 时才更新
5. **version字段**：始终更新（乐观锁机制）

## Service层修改

同时修改了 `TrainKbTplPreServiceImpl.updatePreRecord` 方法，移除了Service层的null值处理逻辑，直接将参数传递给Mapper，让动态SQL处理null值。

### 修改前
```java
// Service层处理null值
record.setContent(content != null ? content : original.getContent());
record.setIndex(index != null ? index : original.getIndex());
record.setLearnStatus(learnStatus != null ? learnStatus : original.getLearnStatus());
```

### 修改后
```java
// 直接传递参数，让动态SQL处理null
record.setContent(content);
record.setIndex(index);
record.setLearnStatus(learnStatus);
```

## 测试验证

添加了 `testUpdatePreRecordDynamicSQL` 测试方法，验证以下场景：

1. **只更新content字段**：传入content值，其他字段为null
2. **只更新index字段**：传入index值，其他字段为null
3. **只更新learnStatus字段**：传入learnStatus值，其他字段为null
4. **空字符串忽略**：传入空字符串，应该被动态SQL忽略

## 实际SQL执行示例

### 场景1：只更新content
```sql
UPDATE train_kb_tpl_pre SET content = ?, updater = ?, version = version + 1 
WHERE id = ? AND team_id = ? AND version = ?
```

### 场景2：只更新index
```sql
UPDATE train_kb_tpl_pre SET `index` = ?, updater = ?, version = version + 1 
WHERE id = ? AND team_id = ? AND version = ?
```

### 场景3：只更新learnStatus
```sql
UPDATE train_kb_tpl_pre SET learn_status = ?, updater = ?, version = version + 1 
WHERE id = ? AND team_id = ? AND version = ?
```

### 场景4：更新所有字段
```sql
UPDATE train_kb_tpl_pre SET content = ?, `index` = ?, learn_status = ?, updater = ?, version = version + 1 
WHERE id = ? AND team_id = ? AND version = ?
```

### 场景5：空字符串被忽略
```sql
UPDATE train_kb_tpl_pre SET updater = ?, version = version + 1 
WHERE id = ? AND team_id = ? AND version = ?
```

## 优势

1. **灵活性**：支持部分字段更新，避免不必要的字段覆盖
2. **性能**：减少不必要的字段更新操作
3. **安全性**：避免意外覆盖重要数据
4. **维护性**：动态SQL更容易维护和扩展

## 注意事项

1. 空字符串会被忽略，如果需要清空字段，需要传入特殊值或修改判断条件
2. version字段始终会更新，确保乐观锁机制正常工作
3. 至少会更新version字段，确保SQL语法正确
