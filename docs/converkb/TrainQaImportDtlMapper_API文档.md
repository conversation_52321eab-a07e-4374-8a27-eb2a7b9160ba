# 问答详情管理 API 文档

## 概述

问答详情管理接口，提供问答详情的增删改查功能，基于HTTP RESTful API。

## 数据库表结构

### train_qa_import_dtl 表字段

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | NOT NULL | AUTO_INCREMENT | 主键ID |
| qa_main_id | bigint | - | NOT NULL | - | 主表ID（关联train_qa_import_main.id） |
| question | varchar | 500 | NOT NULL | - | 问题 |
| answer | text | - | NOT NULL | - | 答案（支持表情存储） |
| team_id | bigint | - | NOT NULL | - | 团队ID |
| create_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| creator | varchar | 64 | NOT NULL | '0' | 创建人 |
| updater | varchar | 64 | NOT NULL | '0' | 更新人 |
| version | bigint | - | NOT NULL | 0 | 版本号（乐观锁） |

## 认证说明

所有API接口都需要JWT认证，请先登录获取token：

```bash
# 登录获取token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

登录成功后，在后续请求中添加Authorization头：
```bash
-H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## API 接口列表

### 1. 查询问答详情列表

**接口**: `GET /api/qa-management/detail/list`

**描述**: 分页查询问答详情列表，支持按主表ID查询和模糊搜索

**入参**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小，建议不超过100 |
| qaMainId | Long | 否 | - | 主表ID，查询指定知识库下的问答 |
| question | String | 否 | - | 问题关键词，模糊查询 |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "list": [                   // 问答详情列表
      {
        "id": 233,              // 问答详情ID
        "qaMainId": 1,          // 主表ID（关联train_qa_import_main.id）
        "question": "防晒干是水", // 问题
        "answer": "亲爱的～防晒到货水分的高的状态下是哦～", // 答案（支持表情存储）
        "teamId": 1,            // 团队ID
        "createTime": "2025-08-27T01:25:36",  // 创建时间
        "updateTime": "2025-08-27T01:25:36",  // 更新时间
        "creator": "93",        // 创建人
        "updater": "93",        // 更新人
        "version": 0            // 版本号（乐观锁）
      }
    ],
    "total": 1,                 // 总记录数
    "page": 1,                  // 当前页码
    "pageSize": 10              // 每页大小
  }
}
```

**curl示例**:

```bash
# 查询所有问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 查询指定知识库下的问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 模糊搜索问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?question=防晒&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 2. 查询问答详情

**接口**: `GET /api/qa-management/detail/{id}`

**描述**: 根据ID查询问答详情

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 问答详情ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "id": 233,                  // 问答详情ID
    "qaMainId": 1,              // 主表ID（关联train_qa_import_main.id）
    "question": "防晒干是水",    // 问题
    "answer": "亲爱的～防晒到货水分的高的状态下是哦～", // 答案（支持表情存储）
    "teamId": 1,                // 团队ID
    "createTime": "2025-08-27T01:25:36",  // 创建时间
    "updateTime": "2025-08-27T01:25:36",  // 更新时间
    "creator": "93",            // 创建人
    "updater": "93",            // 更新人
    "version": 0                // 版本号（乐观锁）
  }
}
```

**curl示例**:
```bash
curl -X GET "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 创建问答详情

**接口**: `POST /api/qa-management/detail`

**描述**: 创建新的问答详情

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| qaMainId | Long | 是 | 主表ID，关联的知识库ID |
| question | String | 是 | 问题，长度不超过500字符，同一团队内不能重复 |
| answer | String | 是 | 答案，支持表情符号存储 |

**请求体示例**:
```json
{
  "qaMainId": 1,                // 主表ID
  "question": "防晒使用方法",    // 问题
  "answer": "每天早上出门前30分钟涂抹防晒霜"  // 答案
}
```

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "创建成功",         // 响应消息
  "data": "创建成功"            // 响应数据
}
```

**curl示例**:
```bash
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "防晒使用方法",
    "answer": "每天早上出门前30分钟涂抹防晒霜"
  }'
```

### 4. 更新问答详情

**接口**: `PUT /api/qa-management/detail/{id}`

**描述**: 更新问答详情

**路径参数**:
- `id` (Long): 问答详情ID

**请求体**:
```json
{
  "question": "更新后的问题",
  "answer": "更新后的答案"
}
```

**curl示例**:
```bash
curl -X PUT "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "防晒使用方法（更新）",
    "answer": "每天早上出门前30分钟涂抹防晒霜，注意补涂"
  }'
```

**响应示例**:
```json
{
  "code": 1,
  "message": "更新成功",
  "data": "更新成功"
}
```

### 5. 删除问答详情

**接口**: `DELETE /api/qa-management/detail/{id}`

**描述**: 删除单个问答详情

**路径参数**:
- `id` (Long): 问答详情ID

**curl示例**:
```bash
curl -X DELETE "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "删除成功",
  "data": "删除成功"
}
```

### 6. 批量删除问答详情

**接口**: `DELETE /api/qa-management/detail/batch`

**描述**: 批量删除问答详情

**请求参数**:
- `ids` (String): 逗号分隔的ID列表，如"233,234,235"

**curl示例**:
```bash
curl -X DELETE "http://localhost:8081/api/qa-management/detail/batch?ids=233,234,235" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "批量删除成功",
  "data": "批量删除成功"
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查JWT token是否有效 |
| 403 | 无权限 | 检查是否有访问该资源的权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 409 | 数据冲突 | 问题已存在，请修改问题内容 |
| 500 | 服务器内部错误 | 联系系统管理员 |

### 错误响应示例

```json
{
  "code": 0,
  "message": "问题已存在：防晒干是水",
  "data": null
}
```

## 数据验证规则

### 创建问答详情验证

1. **qaMainId**: 必填，必须是有效的知识库ID
2. **question**: 必填，长度不超过500字符，同一团队内不能重复
3. **answer**: 必填，支持表情符号存储

### 更新问答详情验证

1. **question**: 必填，长度不超过500字符，如果修改问题需检查重复性
2. **answer**: 必填，支持表情符号存储
3. **权限验证**: 只能更新本团队的问答详情

## 使用示例

### 完整流程示例

```bash
# 1. 登录获取token
TOKEN=$(curl -s -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "staff001", "password": "123456", "rememberMe": false}' | \
  jq -r '.data.token')

# 2. 查询所有问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list?page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"

# 3. 查询指定知识库下的问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 4. 模糊搜索问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?question=防晒&page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 5. 创建新的问答详情
curl -X POST "http://localhost:8081/api/qa-management/detail" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "qaMainId": 1,
    "question": "产品保质期",
    "answer": "本产品保质期为3年，请在有效期内使用"
  }'

# 6. 更新问答详情
curl -X PUT "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "产品保质期（更新）",
    "answer": "本产品保质期为3年，开封后请在12个月内使用完毕"
  }'

# 7. 查询单个问答详情
curl -X GET "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer $TOKEN"

# 8. 删除单个问答详情
curl -X DELETE "http://localhost:8081/api/qa-management/detail/233" \
  -H "Authorization: Bearer $TOKEN"

# 9. 批量删除问答详情
curl -X DELETE "http://localhost:8081/api/qa-management/detail/batch?ids=234,235,236" \
  -H "Authorization: Bearer $TOKEN"
```

### 高级查询示例

```bash
# 分页查询第2页，每页20条
curl -X GET "http://localhost:8081/api/qa-management/detail/list?page=2&pageSize=20" \
  -H "Authorization: Bearer $TOKEN"

# 在指定知识库中搜索包含"使用"的问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?qaMainId=1&question=使用&page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN"

# 搜索所有包含"价格"的问题
curl -X GET "http://localhost:8081/api/qa-management/detail/list?question=价格" \
  -H "Authorization: Bearer $TOKEN"
```

## 注意事项

1. **外键关联**: qa_main_id字段关联train_qa_import_main表的id字段
2. **团队隔离**: 所有操作都会自动按团队ID隔离，确保数据安全
3. **问题唯一性**: 同一团队内问题不能重复，创建和更新时会自动检查
4. **UTF-8支持**: answer字段支持表情符号存储，适合客服场景
5. **权限验证**: 只能操作本团队的问答详情，确保数据安全
6. **模糊查询**: 支持问题关键词模糊搜索，便于快速定位
7. **分页查询**: 建议使用分页查询避免大数据量影响性能
8. **批量操作**: 支持批量删除，提高操作效率
