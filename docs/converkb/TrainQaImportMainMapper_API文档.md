# 问答知识库管理 API 文档

## 概述

问答知识库管理接口，提供知识库的增删改查功能，基于HTTP RESTful API。

## 数据库表结构

### train_qa_import_main 表字段

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | NOT NULL | AUTO_INCREMENT | 主键ID |
| batch_no | varchar | 50 | NOT NULL | - | 批次号（年月日时分秒毫秒格式） |
| qa_im_name | varchar | 200 | NOT NULL | - | 知识库名称 |
| qa_im_desc | varchar | 200 | NOT NULL | - | 知识库描述 |
| team_id | bigint | - | NOT NULL | - | 团队ID |
| create_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| creator | varchar | 64 | NOT NULL | '0' | 创建人 |
| updater | varchar | 64 | NOT NULL | '0' | 更新人 |
| version | bigint | - | NOT NULL | 0 | 版本号（乐观锁） |

## 认证说明

所有API接口都需要JWT认证，请先登录获取token：

```bash
# 登录获取token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

登录成功后，在后续请求中添加Authorization头：
```bash
-H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## API 接口列表

### 1. 查询知识库列表

**接口**: `GET /api/qa-management/main/list`

**描述**: 分页查询问答知识库主表列表

**入参**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小，建议不超过100 |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "list": [                   // 知识库列表
      {
        "id": 1,                // 知识库ID
        "batchNo": "20250827012536578",  // 批次号（年月日时分秒毫秒格式）
        "qaImName": "测试知识库",         // 知识库名称
        "qaImDesc": "测试知识库描述",     // 知识库描述
        "teamId": 1,            // 团队ID
        "createTime": "2025-08-27T01:25:36",  // 创建时间
        "updateTime": "2025-08-27T01:25:36",  // 更新时间
        "creator": "93",        // 创建人
        "updater": "93",        // 更新人
        "version": 0            // 版本号（乐观锁）
      }
    ],
    "total": 1,                 // 总记录数
    "page": 1,                  // 当前页码
    "pageSize": 10              // 每页大小
  }
}
```

**curl示例**:
```bash
curl -X GET "http://localhost:8081/api/qa-management/main/list?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 2. 查询知识库详情

**接口**: `GET /api/qa-management/main/{id}`

**描述**: 根据ID查询知识库详情

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 知识库ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "查询成功",         // 响应消息
  "data": {
    "id": 1,                    // 知识库ID
    "batchNo": "20250827012536578",      // 批次号（年月日时分秒毫秒格式）
    "qaImName": "测试知识库",             // 知识库名称
    "qaImDesc": "测试知识库描述",         // 知识库描述
    "teamId": 1,                // 团队ID
    "createTime": "2025-08-27T01:25:36", // 创建时间
    "updateTime": "2025-08-27T01:25:36", // 更新时间
    "creator": "93",            // 创建人
    "updater": "93",            // 更新人
    "version": 0                // 版本号（乐观锁）
  }
}
```

**curl示例**:
```bash
curl -X GET "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 删除知识库

**接口**: `DELETE /api/qa-management/main/{id}`

**描述**: 删除知识库及其所有问答详情（级联删除）

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 知识库ID（路径参数） |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "删除成功",         // 响应消息
  "data": "删除成功"            // 响应数据
}
```

**curl示例**:
```bash
curl -X DELETE "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 4. 批量删除知识库

**接口**: `DELETE /api/qa-management/main/batch`

**描述**: 批量删除知识库及其所有问答详情

**入参**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 是 | 逗号分隔的ID列表，如"1,2,3" |

**出参**:
```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "message": "批量删除成功",     // 响应消息
  "data": "批量删除成功"        // 响应数据
}
```

**curl示例**:
```bash
curl -X DELETE "http://localhost:8081/api/qa-management/main/batch?ids=1,2,3" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 问答导入相关接口

### 5. 导入问答Excel

**接口**: `POST /api/qa-import/excel`

**描述**: 上传Excel文件导入问答数据到数据库

**请求参数**:
- `file` (MultipartFile): Excel文件

**curl示例**:
```bash
curl -X POST "http://localhost:8081/api/qa-import/excel" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -F "file=@/path/to/your/qa_data.xlsx"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "导入完成",
  "data": {
    "totalCount": 2,
    "successCount": 2,
    "failCount": 0,
    "successItems": [
      {
        "question": "防晒干是水",
        "answer": "亲爱的～防晒到货水分的高的状态下是哦～"
      },
      {
        "question": "有试用吗",
        "answer": "亲爱的，若有赠送款，可以用1个哦。"
      }
    ],
    "failedItems": [],
    "successExcelUrl": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/success_20250827012536578.xlsx",
    "failedExcelUrl": null
  }
}
```

### 6. 验证问答Excel格式

**接口**: `POST /api/qa-import/validate`

**描述**: 验证上传的Excel文件格式是否正确

**请求参数**:
- `file` (MultipartFile): 要验证的Excel文件

**curl示例**:
```bash
curl -X POST "http://localhost:8081/api/qa-import/validate" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -F "file=@/path/to/your/qa_data.xlsx"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "文件格式验证通过",
  "data": "文件格式验证通过"
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查JWT token是否有效 |
| 403 | 无权限 | 检查是否有访问该资源的权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系系统管理员 |

### 错误响应示例

```json
{
  "code": 0,
  "message": "知识库不存在或无权限",
  "data": null
}
```

## 使用示例

### 完整流程示例

```bash
# 1. 登录获取token
TOKEN=$(curl -s -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "staff001", "password": "123456", "rememberMe": false}' | \
  jq -r '.data.token')

# 2. 查询知识库列表
curl -X GET "http://localhost:8081/api/qa-management/main/list?page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"

# 3. 查询指定知识库详情
curl -X GET "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN"

# 4. 导入Excel文件
curl -X POST "http://localhost:8081/api/qa-import/excel" \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@qa_data.xlsx"

# 5. 删除知识库
curl -X DELETE "http://localhost:8081/api/qa-management/main/1" \
  -H "Authorization: Bearer $TOKEN"
```

## 注意事项

1. **团队隔离**: 所有操作都会自动按团队ID隔离，确保数据安全
2. **JWT认证**: 所有接口都需要有效的JWT token
3. **级联删除**: 删除知识库会同时删除其下所有问答详情
4. **文件格式**: Excel导入要求标准格式（问题、答案两列）
5. **分页查询**: 建议使用分页查询避免大数据量影响性能
6. **批量操作**: 批量删除时会验证每个ID的权限
