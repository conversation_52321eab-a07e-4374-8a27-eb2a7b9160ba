# ExternalTeamShopsController API 调用示例

外部团队店铺接口调用控制器的curl调用示例，包含完整的请求和响应格式，供前端Vue开发参考。

## 基础配置

- **服务端口**: 8081
- **基础路径**: `/api/external/team-shops`
- **认证方式**: JWT Token (在Header中添加 `Authorization: Bearer <token>`)
- **外部服务地址**: http://localhost:7081 (可通过配置 `external.team-shops.base-url` 修改)

## API 接口总览

| 序号 | 接口名称 | HTTP方法 | 路径 | 功能描述 |
|------|----------|----------|------|----------|
| 1 | 获取有效店铺列表(GET) | GET | `/api/external/team-shops/valid-list` | 通过GET方式调用外部接口获取有效店铺列表 |
| 2 | 获取有效店铺列表(POST) | POST | `/api/external/team-shops/valid-list` | 通过POST方式调用外部接口获取有效店铺列表 |

## API 接口详细说明

### 1. 获取有效店铺列表(GET方式)

#### 请求方式
```bash
curl -X GET "http://localhost:8081/api/external/team-shops/valid-list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 功能说明
- 通过GET方式调用外部团队店铺服务接口
- 自动从JWT Token中获取当前用户的teamId
- 将teamId作为查询参数传递给外部接口
- 外部接口地址: `{baseUrl}/api/team-shops/valid-list?teamId={teamId}`

#### 请求参数
无需手动传递参数，teamId会自动从JWT Token中获取

#### 响应示例

**成功响应**
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "total": 3,
    "records": [
      {
         "teamId": "1",
         "shopId": "619356",
         "shopType": "0",
         "shopTypeName": "京东",
         "createTime": "2025-07-20T23:16:31",
         "updateTime": "2025-09-10T09:22:41",
         "isAuthorize": true,
         "authorizeStatusName": "已授权",
         "isSyncComplete": true,
         "syncStatusName": "已完成",
         "accessToken": "4aa132eae0814fda8882f1e5fd423265hmtm",
         "expiresTime": "2025-08-21T15:49:43",
         "isTokenExpired": true,
         "shopName": "",
         "deadLine": "2025-09-09T18:00:52",
         "isExpired": null,
         "expiredStatusName": "未知"
      },
      {
         "teamId": "1",
         "shopId": "619356",
         "shopType": "0",
         "shopTypeName": "京东",
         "createTime": "2025-07-20T23:16:31",
         "updateTime": "2025-09-10T09:22:41",
         "isAuthorize": true,
         "authorizeStatusName": "已授权",
         "isSyncComplete": true,
         "syncStatusName": "已完成",
         "accessToken": "4aa132eae0814fda8882f1e5fd423265hmtm",
         "expiresTime": "2025-08-21T15:49:43",
         "isTokenExpired": true,
         "shopName": "",
         "deadLine": "2025-09-09T18:00:52",
         "isExpired": null,
         "expiredStatusName": "未知"
      },
      {
         "teamId": "1",
         "shopId": "619356",
         "shopType": "0",
         "shopTypeName": "京东",
         "createTime": "2025-07-20T23:16:31",
         "updateTime": "2025-09-10T09:22:41",
         "isAuthorize": true,
         "authorizeStatusName": "已授权",
         "isSyncComplete": true,
         "syncStatusName": "已完成",
         "accessToken": "4aa132eae0814fda8882f1e5fd423265hmtm",
         "expiresTime": "2025-08-21T15:49:43",
         "isTokenExpired": true,
         "shopName": "",
         "deadLine": "2025-09-09T18:00:52",
         "isExpired": null,
         "expiredStatusName": "未知"
      }
    ]
  }
}
```

**失败响应(认证失败)**
```json
{
  "code": 500,
  "message": "无法获取团队信息",
  "data": null
}
```

**失败响应(外部接口调用失败)**
```json
{
  "code": 500,
  "message": "调用外部接口失败",
  "data": null
}
```

**失败响应(外部接口异常)**
```json
{
  "code": 500,
  "message": "调用外部接口异常: Connection refused",
  "data": null
}
```

### 2. 获取有效店铺列表(POST方式)

#### 请求方式
```bash
curl -X POST "http://localhost:8081/api/external/team-shops/valid-list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 功能说明
- 通过POST方式调用外部团队店铺服务接口
- 自动从JWT Token中获取当前用户的teamId
- 将teamId作为请求体参数传递给外部接口
- 外部接口地址: `{baseUrl}/api/team-shops/valid-list`

#### 请求参数
无需手动传递参数，teamId会自动从JWT Token中获取并作为请求体发送

#### 内部请求体格式
```json
{
  "teamId": 1
}
```

#### 响应示例

**成功响应**
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "total": 3,
    "records": [
      {
        "id": 1,
        "shopName": "旗舰店A",
        "shopCode": "SHOP001",
        "platform": "淘宝",
        "status": "active",
        "createTime": "2025-01-01T10:00:00",
        "teamId": 1
      },
      {
        "id": 2,
        "shopName": "专营店B",
        "shopCode": "SHOP002",
        "platform": "京东", 
        "status": "active",
        "createTime": "2025-01-02T10:00:00",
        "teamId": 1
      },
      {
        "id": 3,
        "shopName": "直营店C",
        "shopCode": "SHOP003",
        "platform": "天猫",
        "status": "active",
        "createTime": "2025-01-03T10:00:00",
        "teamId": 1
      }
    ]
  }
}
```

**失败响应格式与GET方式相同**

## 错误响应格式

当接口调用失败时，统一返回以下格式：

```json
{
  "code": 500,
  "message": "具体的错误信息",
  "data": null
}
```

### 常见错误码
- `500`: 服务器内部错误
- `401`: 认证失败，JWT Token无效或过期
- `400`: 请求参数错误
- `404`: 资源不存在

## 技术实现说明

### 外部接口调用流程
1. **认证验证**: 从JWT Token中提取teamId
2. **参数构建**: 
   - GET方式: 将teamId作为URL查询参数
   - POST方式: 将teamId作为请求体参数
3. **接口调用**: 使用RestTemplate调用外部服务
4. **响应处理**: 将外部接口响应包装为统一的Result格式返回

### 配置说明
- **外部服务地址**: 通过 `external.team-shops.base-url` 配置项设置
- **默认地址**: http://localhost:7081
- **环境变量**: 可通过环境变量覆盖配置

### 团队隔离
- 所有接口都基于JWT Token中的teamId进行团队数据隔离
- 确保用户只能访问自己团队的店铺数据
- 如果无法获取teamId，接口会返回认证失败错误

## 注意事项

1. **认证要求**: 所有接口都需要在Header中携带有效的JWT Token
2. **团队隔离**: 接口自动根据JWT Token中的teamId进行数据隔离
3. **响应格式**: 所有成功响应的code都是1，失败响应的code是500
4. **外部依赖**: 接口依赖外部团队店铺服务，需确保外部服务可用
5. **超时处理**: 如果外部服务响应超时，会返回相应的错误信息
6. **日志记录**: 所有接口调用都会记录详细的日志信息，便于问题排查

## 前端集成建议

1. **统一请求封装**: 建议封装axios请求拦截器自动添加JWT Token
2. **错误处理**: 统一处理401错误，引导用户重新登录
3. **Loading状态**: 由于涉及外部接口调用，建议显示Loading状态
4. **重试机制**: 对于网络异常可考虑实现重试机制
5. **缓存策略**: 店铺列表数据相对稳定，可考虑适当缓存
6. **接口选择**: 根据外部服务的实际要求选择GET或POST方式调用

## 外部接口规范

### 外部接口地址
- **GET方式**: `{baseUrl}/api/team-shops/valid-list?teamId={teamId}`
- **POST方式**: `{baseUrl}/api/team-shops/valid-list`

### 外部接口预期响应格式
外部接口应返回标准的HTTP响应，响应体格式由外部服务定义。本接口会将外部服务的响应直接包装在Result.data中返回。

### 外部服务要求
- 支持GET和POST两种调用方式
- 能够根据teamId返回对应团队的有效店铺列表
- 返回标准的HTTP状态码
- 响应体格式应为JSON格式
