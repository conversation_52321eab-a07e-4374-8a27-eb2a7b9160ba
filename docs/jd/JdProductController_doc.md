# JdProductController API 文档

## 接口概述
JdProductController 提供京东商品相关的API接口，包括商品列表查询和商品详情查询功能。

## 认证方式
所有接口都需要JWT认证，请在请求头中添加：
```
Authorization: Bearer {your_jwt_token}
```

---

## 1. 分页查询京东商品列表

**接口地址：** `GET /api/jd-prd-list`

**功能描述：** 支持按商品标题、SKU ID、品牌名称查询的分页接口

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| searchKeyword | String | 否 | - | 搜索关键词（同时匹配商品标题、SKU ID、品牌名称） |
| page | Integer | 否 | 1 | 页号，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小 |

### curl 调用示例

#### 1.1 查询所有商品（无搜索条件）
```bash
curl -X GET "http://localhost:8081/api/jd-prd-list?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 1.2 按品牌名称搜索
```bash
curl -X GET "http://localhost:8081/api/jd-prd-list?searchKeyword=小米&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 1.3 按商品标题搜索
```bash
curl -X GET "http://localhost:8081/api/jd-prd-list?searchKeyword=手环&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 1.4 按SKU ID搜索
```bash
curl -X GET "http://localhost:8081/api/jd-prd-list?searchKeyword=12345&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "total": 72,
    "shopId": 619356,
    "isAuthorize": true,
    "isSyncComplete": 2,
    "isSyncCompleteDesc": "已同步",
    "rows": [
      {
        "id": 510,
        "brandId": 18374,
        "wareId": 10030237827895,
        "brandName": "小米（MI）",
        "logo": "https://img11.360buyimg.com/devfe/jfs/t1/303463/26/17646/50554/685d2a5fFf334debc/36005576825bcf3b.jpg",
        "title": "小米（MI）手环10NFC版 全新升级 运动手环 智能手环",
        "status": "自主下架",
        "onlineTime": "2025-06-26 20:26:34",
        "offLineTime": "2025-06-26 21:17:40"
      }
    ]
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，1表示成功 |
| message | String | 响应消息 |
| data.total | Long | 总记录数 |
| data.shopId | Long | 店铺ID |
| data.isAuthorize | Boolean | 是否授权 |
| data.isSyncComplete | Integer | 同步状态（1=同步中，2=已同步） |
| data.isSyncCompleteDesc | String | 同步状态描述 |
| data.rows | Array | 商品列表 |
| rows[].id | Long | 京东商品表主键 |
| rows[].brandId | Long | 品牌ID |
| rows[].wareId | Long | 商品ID |
| rows[].brandName | String | 品牌名称 |
| rows[].logo | String | 商品LOGO完整URL |
| rows[].title | String | 商品标题 |
| rows[].status | String | 商品状态 |
| rows[].onlineTime | String | 上线时间 |
| rows[].offLineTime | String | 下线时间 |

---

## 2. 查询京东商品详情

**接口地址：** `GET /api/jd-prd-dtl/{wareId}`

**功能描述：** 根据wareId查询京东商品详情信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| wareId | Long | 是 | 京东商品ID（路径参数） |

### curl 调用示例
```bash
curl -X GET "http://localhost:8081/api/jd-prd-dtl/10030237827895" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "id": 510,
    "wareId": 10030237827895,
    "jdProdDtl": "商品详细描述内容...",
    "jdProdImgList": "图片URL1,图片URL2,图片URL3"
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，1表示成功 |
| message | String | 响应消息 |
| data.id | Long | 京东商品表主键 |
| data.wareId | Long | 京东商品ID |
| data.jdProdDtl | String | 商品详细描述 |
| data.jdProdImgList | String | 商品图片列表（逗号分隔） |

---

## 错误响应格式
```json
{
  "code": 0,
  "message": "错误信息描述",
  "data": null
}
```

## 注意事项
1. **认证要求**：所有接口都需要有效的JWT token
2. **搜索功能**：`searchKeyword` 参数支持同时匹配商品标题、SKU ID、品牌名称
3. **分页参数**：page从1开始，pageSize建议不超过100
4. **数据去重**：查询结果已自动去重，避免重复记录
5. **状态码**：成功返回code=1，失败返回code=0或其他错误码
6. **时间格式**：时间字段采用 "yyyy-MM-dd HH:mm:ss" 格式

## 前端集成参考
1. **请求封装**：建议封装统一的HTTP请求方法，自动添加Authorization头
2. **分页组件**：可以基于total字段实现分页组件
3. **搜索防抖**：建议对搜索输入框添加防抖处理，避免频繁请求
4. **错误处理**：根据code字段判断请求是否成功，并显示相应的错误信息
5. **图片处理**：logo和jdProdImgList字段包含完整的图片URL，可直接使用
