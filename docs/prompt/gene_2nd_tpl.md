```markdown
# 电商聊天记录修改专家

## 核心任务
1. **替换买家身份** → 将原始聊天记录<<chatLogPart>>中的买家身份替换为 `targetRolePart`
2. **替换商品信息** → 将原始聊天记录<<chatLogPart>>中的商品信息替换为 `targetProductTitlePart`
3. **更新商品知识** → 将原始聊天记录<<chatLogPart>>中商品知识内容替换为 `targetProKnowledgePart`
4. **保持原始风格** → 严格保留原始聊天记录<<chatLogPart>>的表达风格（语气/句式/用词习惯）

## 输入数据
### 原始聊天记录<<chatLogPart>>
```
{{chat_log}}

```

### 目标商品知识库<<targetProKnowledgePart>>
```
{{targetProKnowledge}}
```

### 目标买家身份<<targetRolePart>>
```
{{targetRole}}
```

### 目标商品标题<<targetProductTitlePart>>
```
{{targetProductTitle}}
```

## 输出要求
✅ **直接输出修改后的完整聊天记录**（不包含任何额外标志或说明文字）  
✅ 保持原始对话逻辑和时间线  
✅ 将指定身份/商品/知识库自然融入对话  
❌ 禁止修改时间戳、售后流程等非指定内容  
❌ 禁止添加任何标记（如【生成 **全新聊天记录**】）  
❌ 输出结果中禁止用飘号`

## 输出格式示例
一位年轻时尚的小伙子 2025-08-06 10:10:10
[消息内容]

小米自营-热情 2025-08-06 10:10:10
[消息内容]

[身份] [时间]
[消息内容]
...
```
（严格遵循此纯文本格式，包含身份+时间戳+消息内容的三段式结构）
```
