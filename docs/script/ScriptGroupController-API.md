# ScriptGroupController API 调用文档

## 概述

本文档提供ScriptGroupController的完整API调用示例，包括详细的入参和出参格式，供前端Vue开发参考。ScriptGroupController主要用于管理剧本分组的增删改查操作。

## API基本信息

- **基础路径**: `/api/script-groups`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token (Bearer Token)
- **功能**: 剧本分组管理，包括查询分组树、创建分组、更新分组、删除分组

## API接口列表

### 1. 查询分组树 (GET)

#### curl调用示例
```bash
curl -X GET "http://localhost:8081/api/script-groups?groupTitle=测试分组" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1c2VyMTIzIiwidGVhbUlkIjoxLCJpYXQiOjE2OTk5OTk5OTksImV4cCI6MTY5OTk5OTk5OX0.example_jwt_token"
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `groupTitle` | String | 否 | 分组标题过滤条件 | `"测试分组"` |

#### 成功响应 (HTTP 200)
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "groups": [
      {
        "id": -1,
        "parentId": null,
        "groupTitle": "全部剧本",
        "isOfficial": 0,
        "subGroups": [
          {
            "id": 1,
            "parentId": null,
            "groupTitle": "官方分组",
            "isOfficial": 1,
            "subGroups": [
              {
                "id": 13,
                "parentId": 1,
                "groupTitle": "数码产品",
                "isOfficial": 1,
                "subGroups": []
              }
            ]
          },
          {
            "id": 25,
            "parentId": null,
            "groupTitle": "自定义分组",
            "isOfficial": 0,
            "subGroups": []
          }
        ]
      }
    ]
  }
}
```

### 2. 创建分组 (POST)

#### curl调用示例
```bash
curl -X POST "http://localhost:8081/api/script-groups" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here" \
  -d '{
    "groupTitle": "新建分组",
    "parentId": 1,
    "isOfficial": false,
    "sortOrder": 1
  }'
```

#### 请求体参数
```json
{
  "groupTitle": "新建分组",
  "parentId": 1,
  "isOfficial": false,
  "sortOrder": 1
}
```

#### 请求体字段说明
| 字段 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| `groupTitle` | String | 是 | 分组标题 | `"新建分组"` |
| `parentId` | Long | 否 | 父分组ID，null表示顶级分组 | `1` |
| `isOfficial` | Boolean | 否 | 是否为官方分组 | `false` |
| `sortOrder` | Integer | 否 | 排序顺序 | `1` |

#### 成功响应 (HTTP 200)
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

### 3. 更新分组 (PUT)

#### curl调用示例
```bash
curl -X PUT "http://localhost:8081/api/script-groups" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here" \
  -d '{
    "id": 25,
    "teamId": 1,
    "groupTitle": "更新后的分组名称",
    "parentId": 1,
    "isOfficial": false,
    "sortOrder": 2
  }'
```

#### 请求体参数
```json
{
  "id": 25,
  "teamId": 1,
  "groupTitle": "更新后的分组名称",
  "parentId": 1,
  "isOfficial": false,
  "sortOrder": 2
}
```

#### 请求体字段说明
| 字段 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| `id` | Long | 是 | 分组ID | `25` |
| `teamId` | Long | 是 | 团队ID | `1` |
| `groupTitle` | String | 是 | 分组标题 | `"更新后的分组名称"` |
| `parentId` | Long | 否 | 父分组ID | `1` |
| `isOfficial` | Boolean | 否 | 是否为官方分组 | `false` |
| `sortOrder` | Integer | 否 | 排序顺序 | `2` |

#### 成功响应 (HTTP 200)
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

### 4. 删除分组 (DELETE)

#### curl调用示例
```bash
# 删除单个分组
curl -X DELETE "http://localhost:8081/api/script-groups?ids=25" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here"

# 删除多个分组
curl -X DELETE "http://localhost:8081/api/script-groups?ids=25,26,27" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here"
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `ids` | String | 是 | 分组ID列表，多个用逗号分隔 | `"25,26,27"` |

#### 成功响应 (HTTP 200)
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

## 错误响应

### 401 未授权
```json
{
  "code": 0,
  "message": "用户认证信息无效，请重新登录",
  "data": null
}
```

### 403 权限不足
```json
{
  "code": 0,
  "message": "无权修改其他团队的分组",
  "data": null
}
```

### 500 服务器错误
```json
{
  "code": 0,
  "message": "添加失败",
  "data": null
}
```

## 响应字段说明

### 分组对象字段
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `id` | Long | 分组ID | `25` |
| `parentId` | Long | 父分组ID，null表示顶级分组 | `1` |
| `groupTitle` | String | 分组标题 | `"数码产品"` |
| `isOfficial` | Integer | 是否为官方分组，1=是，0=否 | `1` |
| `subGroups` | Array | 子分组列表 | `[]` |

### isOfficial字段说明
| 值 | 含义 |
|----|------|
| `1` | 官方分组 |
| `0` | 用户自定义分组 |

## Vue前端调用示例

### 使用axios

```javascript
// 安装: npm install axios

import axios from 'axios'

// 配置axios实例
const api = axios.create({
  baseURL: 'http://localhost:8081',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加JWT token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('jwt_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 1. 查询分组树
async function getScriptGroups(groupTitle = '') {
  try {
    const response = await api.get('/api/script-groups', {
      params: { groupTitle }
    })

    if (response.data.code === 1) {
      return {
        success: true,
        data: response.data.data
      }
    } else {
      return {
        success: false,
        message: response.data.message
      }
    }
  } catch (error) {
    console.error('查询分组树失败:', error)
    return {
      success: false,
      message: error.response?.data?.message || '网络请求失败'
    }
  }
}

// 2. 创建分组
async function createScriptGroup(groupData) {
  try {
    const response = await api.post('/api/script-groups', groupData)

    if (response.data.code === 1) {
      return {
        success: true,
        message: '创建成功'
      }
    } else {
      return {
        success: false,
        message: response.data.message
      }
    }
  } catch (error) {
    console.error('创建分组失败:', error)
    return {
      success: false,
      message: error.response?.data?.message || '网络请求失败'
    }
  }
}

// 3. 更新分组
async function updateScriptGroup(groupData) {
  try {
    const response = await api.put('/api/script-groups', groupData)

    if (response.data.code === 1) {
      return {
        success: true,
        message: '更新成功'
      }
    } else {
      return {
        success: false,
        message: response.data.message
      }
    }
  } catch (error) {
    console.error('更新分组失败:', error)
    return {
      success: false,
      message: error.response?.data?.message || '网络请求失败'
    }
  }
}

// 4. 删除分组
async function deleteScriptGroups(ids) {
  try {
    const response = await api.delete('/api/script-groups', {
      params: { ids }
    })

    if (response.data.code === 1) {
      return {
        success: true,
        message: '删除成功'
      }
    } else {
      return {
        success: false,
        message: response.data.message
      }
    }
  } catch (error) {
    console.error('删除分组失败:', error)
    return {
      success: false,
      message: error.response?.data?.message || '网络请求失败'
    }
  }
}

// Vue组件使用示例
export default {
  data() {
    return {
      groups: [],
      loading: false,
      searchTitle: '',
      selectedGroup: null,
      showCreateDialog: false,
      showEditDialog: false,
      createForm: {
        groupTitle: '',
        parentId: null,
        isOfficial: false,
        sortOrder: 1
      },
      editForm: {
        id: null,
        teamId: null,
        groupTitle: '',
        parentId: null,
        isOfficial: false,
        sortOrder: 1
      }
    }
  },
  mounted() {
    this.loadGroups()
  },
  methods: {
    // 加载分组树
    async loadGroups() {
      this.loading = true
      try {
        const result = await getScriptGroups(this.searchTitle)
        if (result.success) {
          this.groups = result.data.groups || []
        } else {
          this.$message.error(result.message)
        }
      } finally {
        this.loading = false
      }
    },

    // 搜索分组
    async searchGroups() {
      await this.loadGroups()
    },

    // 创建分组
    async handleCreate() {
      const result = await createScriptGroup(this.createForm)
      if (result.success) {
        this.$message.success(result.message)
        this.showCreateDialog = false
        this.resetCreateForm()
        await this.loadGroups()
      } else {
        this.$message.error(result.message)
      }
    },

    // 编辑分组
    async handleEdit() {
      const result = await updateScriptGroup(this.editForm)
      if (result.success) {
        this.$message.success(result.message)
        this.showEditDialog = false
        this.resetEditForm()
        await this.loadGroups()
      } else {
        this.$message.error(result.message)
      }
    },

    // 删除分组
    async handleDelete(group) {
      try {
        await this.$confirm('确定要删除这个分组吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const result = await deleteScriptGroups(group.id.toString())
        if (result.success) {
          this.$message.success(result.message)
          await this.loadGroups()
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        // 用户取消删除
      }
    },

    // 批量删除分组
    async handleBatchDelete(selectedGroups) {
      if (!selectedGroups || selectedGroups.length === 0) {
        this.$message.warning('请选择要删除的分组')
        return
      }

      try {
        await this.$confirm(`确定要删除选中的 ${selectedGroups.length} 个分组吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const ids = selectedGroups.map(group => group.id).join(',')
        const result = await deleteScriptGroups(ids)
        if (result.success) {
          this.$message.success(result.message)
          await this.loadGroups()
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        // 用户取消删除
      }
    },

    // 重置创建表单
    resetCreateForm() {
      this.createForm = {
        groupTitle: '',
        parentId: null,
        isOfficial: false,
        sortOrder: 1
      }
    },

    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        id: null,
        teamId: null,
        groupTitle: '',
        parentId: null,
        isOfficial: false,
        sortOrder: 1
      }
    },

    // 打开编辑对话框
    openEditDialog(group) {
      this.editForm = {
        id: group.id,
        teamId: group.teamId,
        groupTitle: group.groupTitle,
        parentId: group.parentId,
        isOfficial: group.isOfficial === 1,
        sortOrder: group.sortOrder || 1
      }
      this.showEditDialog = true
    }
  }
}
```

### 使用fetch API

```javascript
// 原生fetch调用示例

// 1. 查询分组树
async function getScriptGroupsFetch(groupTitle = '') {
  const token = localStorage.getItem('jwt_token')
  const queryParams = new URLSearchParams()
  if (groupTitle) {
    queryParams.append('groupTitle', groupTitle)
  }

  try {
    const response = await fetch(`http://localhost:8081/api/script-groups?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}

// 2. 创建分组
async function createScriptGroupFetch(groupData) {
  const token = localStorage.getItem('jwt_token')

  try {
    const response = await fetch('http://localhost:8081/api/script-groups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(groupData)
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}

// 3. 更新分组
async function updateScriptGroupFetch(groupData) {
  const token = localStorage.getItem('jwt_token')

  try {
    const response = await fetch('http://localhost:8081/api/script-groups', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(groupData)
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}

// 4. 删除分组
async function deleteScriptGroupsFetch(ids) {
  const token = localStorage.getItem('jwt_token')
  const queryParams = new URLSearchParams({ ids })

  try {
    const response = await fetch(`http://localhost:8081/api/script-groups?${queryParams}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}
```

## 注意事项

1. **认证**: 所有请求都需要在Header中携带有效的JWT Token
2. **团队隔离**: 后端会自动根据JWT Token中的teamId进行数据隔离
3. **权限验证**: 更新和删除操作会验证用户是否有权限操作对应的分组
4. **错误处理**: 前端需要根据code字段判断请求是否成功
5. **分组层级**: 支持多级分组结构，通过parentId建立父子关系
6. **官方分组**: isOfficial字段区分官方分组和用户自定义分组
7. **批量删除**: 删除接口支持传入多个ID，用逗号分隔

## 使用场景

- **分组管理页面**: 展示分组树结构，支持增删改查操作
- **剧本分类**: 为剧本提供分组分类功能
- **权限控制**: 区分官方分组和用户自定义分组
- **层级管理**: 支持多级分组的层级结构管理

## 数据结构说明

### 分组树结构
分组数据采用树形结构，根节点为"全部剧本"（id=-1），下面包含各级分组。每个分组可以有多个子分组，形成层级结构。

### 分组类型
- **官方分组**: isOfficial=1，通常为系统预设的分组
- **用户分组**: isOfficial=0，用户自定义创建的分组

---

*本文档基于AI Train Playground项目，版本：v0.2.0-SNAPSHOT*
```
```
