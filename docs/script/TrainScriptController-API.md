# TrainScriptController API 调用示例

本文档提供 TrainScriptController 所有 API 接口的 curl 命令调用示例，包括详细的入参和出参说明。

## 基础信息

- 服务端口: 8081
- 基础路径: `/api`
- 认证方式: JWT Token (Authorization: Bearer {token})

## API 接口列表

### 1. 分页查询剧本列表

**接口描述:** 根据条件分页查询剧本列表

**HTTP 方法:** `GET`  
**请求路径:** `/api/scripts`

#### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| name | String | 否 | - | 剧本昵称 |
| creator | String | 否 | - | 创建人 |
| generationTypeCode | Integer | 否 | - | 生成方式代码：0-商品知识训练；1-实战能力进阶；2-自定义内容 |
| createTimeStart | String | 否 | - | 创建时间起始点 |
| createTimeEnd | String | 否 | - | 创建时间结束点 |
| updateTimeStart | String | 否 | - | 更新时间起始点 |
| updateTimeEnd | String | 否 | - | 更新时间结束点 |
| groupId | Long | 否 | - | 分组编号 |
| page | Integer | 否 | 1 | 页号 |
| pageSize | Integer | 否 | 10 | 每页大小 |

#### curl 命令示例
```bash
# 基础查询
curl -X GET "http://localhost:8081/api/scripts?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 带条件查询
curl -X GET "http://localhost:8081/api/scripts?name=测试剧本&creator=admin&generationTypeCode=0&page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 时间范围查询
curl -X GET "http://localhost:8081/api/scripts?createTimeStart=2025-01-01&createTimeEnd=2025-12-31&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 响应示例
```json
{
  "code": 1,
  "message": "success",
  "data": {
    "total": 25,
    "rows": [
      {
        "id": 1,
        "name": "测试剧本1",
        "buyerRequirement": "买家需求描述",
        "intentId": 10,
        "intentName": "销售意图",
        "generationTypeCode": 0,
        "generationType": "商品知识训练",
        "groupId": 5,
        "creator": "admin",
        "createTime": "2025-07-26T10:30:00",
        "updateTime": "2025-07-26T10:30:00"
      }
    ]
  }
}
```

---

### 2. 根据ID查询剧本详情

**接口描述:** 根据剧本ID查询详细信息

**HTTP 方法:** `GET`  
**请求路径:** `/api/scripts/{id}`

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 是 | 剧本ID |

#### curl 命令示例
```bash
curl -X GET "http://localhost:8081/api/scripts/123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 响应示例
```json
{
  "code": 1,
  "message": "success",
  "data": {
    "id": 123,
    "name": "测试剧本",
    "generationTypeCode": 0,
    "generationType": "商品知识训练",
    "groupId": 5,
    "intentId": 10,
    "intentName": "销售意图",
    "parentIntentId": 1,
    "parentIntentName": "父意图",
    "evaluationPlanId": 20,
    "evaluationPlanName": "评估方案",
    "evaluationPlanGroupName": "评估分组",
    "buyerRequirement": "买家需求描述",
    "orderIsRemarked": 1,
    "orderPriority": 2,
    "orderRemark": "订单备注",
    "retryFlowNodeCounts": 3,
    "retryBuyerRequirementCounts": 2,
    "simulationTool": "模拟工具",
    "createTime": "2025-07-26T10:30:00",
    "updateTime": "2025-07-26T10:30:00",
    "version": 1,
    "prodType": 0,
    "productList": [
      {
        "id": 1,
        "externalProductId": "PROD001",
        "externalProductName": "测试商品",
        "externalProductLink": "http://example.com/product/1",
        "externalProductImage": "http://example.com/image.jpg"
      }
    ],
    "relateImgs": [
      {
        "id": 1,
        "mediaType": 1,
        "uploadType": 1,
        "recognizedText": "识别文本",
        "url": "http://example.com/image.jpg"
      }
    ],
    "flowNodes": [
      {
        "id": 1,
        "nodeName": "节点1",
        "nodeBuyerRequirement": "节点买家需求"
      }
    ]
  }
}
```

---

### 3. 创建剧本

**接口描述:** 创建新的剧本及相关数据

**HTTP 方法:** `POST`  
**请求路径:** `/api/scripts`

#### 请求体参数
```json
{
  "name": "剧本名称",
  "generationTypeCode": 0,
  "groupId": 5,
  "intentId": 10,
  "evaluationPlanId": 20,
  "buyerRequirement": "买家需求描述",
  "orderIsRemarked": 1,
  "orderPriority": 2,
  "orderRemark": "订单备注",
  "simulationTool": "模拟工具",
  "prodType": 0,
  "productList": [
    {
      "externalProductId": "PROD001",
      "externalProductName": "商品名称",
      "externalProductLink": "http://example.com/product/1",
      "externalProductImage": "http://example.com/image.jpg"
    }
  ],
  "relateImgs": [
    {
      "mediaType": 1,
      "uploadType": 1,
      "recognizedText": "识别文本",
      "url": "http://example.com/image.jpg"
    }
  ],
  "flowNodes": [
    {
      "nodeName": "节点名称",
      "nodeBuyerRequirement": "节点买家需求"
    }
  ]
}
```

#### curl 命令示例
```bash
curl -X POST "http://localhost:8081/api/scripts" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "新建剧本",
    "generationTypeCode": 0,
    "groupId": 5,
    "intentId": 10,
    "evaluationPlanId": 20,
    "buyerRequirement": "测试买家需求",
    "orderIsRemarked": 1,
    "orderPriority": 2,
    "orderRemark": "测试订单备注",
    "simulationTool": "测试模拟工具",
    "prodType": 0,
    "productList": [
      {
        "externalProductId": "TEST001",
        "externalProductName": "测试商品",
        "externalProductLink": "http://example.com/product/test",
        "externalProductImage": "http://example.com/test.jpg"
      }
    ],
    "relateImgs": [
      {
        "mediaType": 1,
        "uploadType": 1,
        "recognizedText": "测试图片",
        "url": "http://example.com/test.jpg"
      }
    ],
    "flowNodes": [
      {
        "nodeName": "测试节点",
        "nodeBuyerRequirement": "测试节点需求"
      }
    ]
  }'
```

#### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": 456
}
```

---

### 4. 更新剧本

**接口描述:** 更新剧本及相关数据

**HTTP 方法:** `PUT`  
**请求路径:** `/api/scripts/{id}`

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 是 | 剧本ID |

#### 请求体参数
```json
{
  "id": 123,
  "name": "更新后的剧本名称",
  "generationTypeCode": 1,
  "groupId": 6,
  "intentId": 11,
  "evaluationPlanId": 21,
  "buyerRequirement": "更新的买家需求",
  "orderIsRemarked": 0,
  "orderPriority": 3,
  "orderRemark": "更新的订单备注",
  "retryBuyerRequirementCounts": 2,
  "retryFlowNodeCounts": 3,
  "simulationTool": "更新的模拟工具",
  "version": 1,
  "isOfficial": true,
  "prodType": 1,
  "productList": [
    {
      "id": 1,
      "externalProductId": "PROD002",
      "externalProductName": "更新的商品名称",
      "externalProductLink": "http://example.com/product/updated",
      "externalProductImage": "http://example.com/updated.jpg"
    }
  ],
  "relateImgs": [
    {
      "id": 1,
      "mediaType": 2,
      "uploadType": 2,
      "recognizedText": "更新的识别文本",
      "url": "http://example.com/updated.jpg"
    }
  ],
  "flowNodes": [
    {
      "id": 1,
      "nodeName": "更新的节点名称",
      "nodeBuyerRequirement": "更新的节点需求"
    }
  ]
}
```

#### curl 命令示例
```bash
curl -X PUT "http://localhost:8081/api/scripts/123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 123,
    "name": "更新剧本",
    "generationTypeCode": 1,
    "groupId": 6,
    "intentId": 11,
    "evaluationPlanId": 21,
    "buyerRequirement": "更新买家需求",
    "orderIsRemarked": 0,
    "orderPriority": 3,
    "orderRemark": "更新订单备注",
    "retryBuyerRequirementCounts": 2,
    "retryFlowNodeCounts": 3,
    "simulationTool": "更新模拟工具",
    "version": 1,
    "isOfficial": true,
    "prodType": 1,
    "productList": [
      {
        "id": 1,
        "externalProductId": "UPD001",
        "externalProductName": "更新商品",
        "externalProductLink": "http://example.com/updated",
        "externalProductImage": "http://example.com/updated.jpg"
      }
    ]
  }'
```

#### 响应示例
```json
{
  "code": 1,
  "message": "剧本更新成功",
  "data": null
}
```

---

### 5. 批量删除剧本

**接口描述:** 根据ID列表批量删除剧本

**HTTP 方法:** `DELETE`  
**请求路径:** `/api/scripts`

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| ids | String | 是 | 剧本ID列表，逗号分隔，如："1,2,3" |

#### curl 命令示例
```bash
# 删除单个剧本
curl -X DELETE "http://localhost:8081/api/scripts?ids=123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 批量删除多个剧本
curl -X DELETE "http://localhost:8081/api/scripts?ids=123,456,789" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

---

### 6. 根据分组ID查询剧本列表

**接口描述:** 根据剧本分组ID分页查询剧本列表，支持多个分组ID

**HTTP 方法:** `GET`  
**请求路径:** `/api/scripts/group`

#### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| scriptGroupIds | String | 否 | - | 剧本分组ID，支持多个值用逗号分隔。-1表示查询所有分组，1表示查询默认分组(NULL)，其他值查询对应分组 |
| page | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页大小 |

#### curl 命令示例
```bash
# 查询所有分组
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=-1&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 查询默认分组（未分组）
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=1&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 查询指定分组
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=13&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 查询多个分组
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=1,13,25&page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# 基础查询（不指定分组）
curl -X GET "http://localhost:8081/api/scripts/group?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "total": 15,
    "records": [
      {
        "id": 1,
        "name": "分组剧本1",
        "buyerRequirement": "买家需求描述",
        "intentId": 10,
        "intentName": "销售意图",
        "generationTypeCode": 0,
        "generationType": "商品知识训练",
        "groupId": 13,
        "creator": "admin",
        "createTime": "2025-07-26T10:30:00",
        "updateTime": "2025-07-26T10:30:00"
      }
    ]
  }
}
```

---

## 错误响应示例

### 认证失败
```json
{
  "code": 401,
  "message": "未找到有效的认证token",
  "data": null
}
```

### 团队信息错误
```json
{
  "code": 400,
  "message": "无法获取团队信息",
  "data": null
}
```

### 服务器内部错误
```json
{
  "code": 500,
  "message": "查询剧本列表失败",
  "data": null
}
```

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的JWT Token
2. **团队隔离**: 接口会根据JWT中的teamId进行数据隔离
3. **分页参数**: 页码从1开始，默认每页10条记录
4. **时间格式**: 时间参数支持标准日期格式，如："2025-01-01"
5. **分组查询规则**:
   - `-1`: 查询所有分组的剧本
   - `1`: 查询未分组的剧本（group_id为NULL）
   - 其他数字: 查询对应group_id的剧本
   - 支持多个分组ID，用逗号分隔