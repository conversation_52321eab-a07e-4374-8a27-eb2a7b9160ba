# TrainScriptController.getScriptsByGroup API 调用文档

## 概述

本文档提供TrainScriptController.getScriptsByGroup方法的完整调用示例，包括详细的入参和出参格式，供前端Vue开发参考。

## API基本信息

- **请求方法**: GET
- **请求路径**: `/api/scripts/group`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token (Bearer Token)
- **功能**: 根据剧本分组ID分页查询剧本列表，支持多个分组ID用逗号分隔

## curl调用示例

### 1. 查询所有分组
```bash
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=-1&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1c2VyMTIzIiwidGVhbUlkIjoxLCJpYXQiOjE2OTk5OTk5OTksImV4cCI6MTY5OTk5OTk5OX0.example_jwt_token"
```

### 2. 查询默认分组（group_id为NULL）
```bash
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=1&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here"
```

### 3. 查询指定分组
```bash
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=13&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here"
```

### 4. 查询多个分组
```bash
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=13,25&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here"
```

### 5. 查询包含默认分组的多个分组
```bash
curl -X GET "http://localhost:8081/api/scripts/group?scriptGroupIds=1,13,25&page=1&pageSize=20" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token_here"
```

## 请求参数详解

### Query Parameters

| 参数名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| `scriptGroupIds` | String | 是 | - | 剧本分组ID，支持多个值用逗号分隔 | `"1,13,25"` |
| `page` | Integer | 否 | 1 | 页码，从1开始 | `1` |
| `pageSize` | Integer | 否 | 10 | 每页大小，建议1-100 | `10` |

### scriptGroupIds参数说明

| 值 | 含义 | 查询条件 |
|----|------|----------|
| `-1` | 查询所有分组 | 不添加group_id条件 |
| `1` | 查询默认分组 | `group_id IS NULL` |
| `13` | 查询分组13 | `group_id = 13` |
| `13,25` | 查询分组13和25 | `group_id IN (13,25)` |
| `1,13` | 查询默认分组和分组13 | `group_id IS NULL OR group_id = 13` |

## 响应格式

### 成功响应 (HTTP 200)

```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 225,
        "name": "分组测试-分组25",
        "buyerRequirement": "分组25测试需求",
        "intentId": 5,
        "intentName": "咨询产品",
        "generationTypeCode": 0,
        "generationType": "商品知识训练",
        "groupId": 25,
        "creator": "test_user",
        "createTime": "2025-07-25T08:22:09",
        "updateTime": "2025-07-25T08:22:09"
      },
      {
        "id": 224,
        "name": "分组测试-分组13",
        "buyerRequirement": "分组13测试需求",
        "intentId": 5,
        "intentName": "咨询产品",
        "generationTypeCode": 0,
        "generationType": "商品知识训练",
        "groupId": 13,
        "creator": "test_user",
        "createTime": "2025-07-25T08:22:09",
        "updateTime": "2025-07-25T08:22:09"
      }
    ],
    "total": 4,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 错误响应

#### 401 未授权
```json
{
  "code": 0,
  "message": "用户认证信息无效，请重新登录",
  "data": null
}
```

#### 500 服务器错误
```json
{
  "code": 0,
  "message": "查询剧本列表失败：数据库连接异常",
  "data": null
}
```

## 响应字段说明

### 外层结构
| 字段 | 类型 | 说明 |
|------|------|------|
| `code` | Integer | 响应码，1表示成功，0表示失败 |
| `message` | String | 响应消息 |
| `data` | Object | 分页数据对象 |

### data字段结构
| 字段 | 类型 | 说明 |
|------|------|------|
| `records` | Array | 剧本列表数据 |
| `total` | Long | 总记录数 |
| `page` | Integer | 当前页码 |
| `pageSize` | Integer | 每页大小 |
| `totalPages` | Integer | 总页数 |

### records数组中每个剧本对象的字段
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `id` | Long | 剧本ID | `225` |
| `name` | String | 剧本名称 | `"分组测试-分组25"` |
| `buyerRequirement` | String | 买家需求 | `"分组25测试需求"` |
| `intentId` | Long | 意图ID | `5` |
| `intentName` | String | 意图名称 | `"咨询产品"` |
| `generationTypeCode` | Integer | 生成类型代码 | `0` |
| `generationType` | String | 生成类型描述 | `"商品知识训练"` |
| `groupId` | Long | 分组ID | `25` |
| `creator` | String | 创建人 | `"test_user"` |
| `createTime` | String | 创建时间 | `"2025-07-25T08:22:09"` |
| `updateTime` | String | 更新时间 | `"2025-07-25T08:22:09"` |

### generationTypeCode枚举值
| 代码 | 描述 |
|------|------|
| `0` | 商品知识训练 |
| `1` | 实战能力进阶 |
| `2` | 自定义内容 |

## Vue前端调用示例

### 使用axios

```javascript
// 安装: npm install axios

import axios from 'axios'

// 配置axios实例
const api = axios.create({
  baseURL: 'http://localhost:8081',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加JWT token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('jwt_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 查询剧本分组列表的方法
async function getScriptsByGroup(params) {
  try {
    const response = await api.get('/api/scripts/group', {
      params: {
        scriptGroupIds: params.scriptGroupIds, // "1,13,25"
        page: params.page || 1,
        pageSize: params.pageSize || 10
      }
    })

    if (response.data.code === 1) {
      return {
        success: true,
        data: response.data.data
      }
    } else {
      return {
        success: false,
        message: response.data.message
      }
    }
  } catch (error) {
    console.error('查询剧本列表失败:', error)
    return {
      success: false,
      message: error.response?.data?.message || '网络请求失败'
    }
  }
}

// Vue组件使用示例
export default {
  data() {
    return {
      scripts: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      loading: false
    }
  },
  methods: {
    async loadScripts(groupIds = '-1') {
      this.loading = true
      try {
        const result = await getScriptsByGroup({
          scriptGroupIds: groupIds,
          page: this.currentPage,
          pageSize: this.pageSize
        })

        if (result.success) {
          this.scripts = result.data.records
          this.total = result.data.total
        } else {
          this.$message.error(result.message)
        }
      } finally {
        this.loading = false
      }
    },

    // 查询所有分组
    loadAllGroups() {
      this.loadScripts('-1')
    },

    // 查询默认分组
    loadDefaultGroup() {
      this.loadScripts('1')
    },

    // 查询多个分组
    loadMultipleGroups() {
      this.loadScripts('1,13,25')
    }
  }
}
```

### 使用fetch API

```javascript
// 原生fetch调用示例
async function getScriptsByGroupFetch(params) {
  const token = localStorage.getItem('jwt_token')
  const queryParams = new URLSearchParams({
    scriptGroupIds: params.scriptGroupIds,
    page: params.page || 1,
    pageSize: params.pageSize || 10
  })

  try {
    const response = await fetch(`http://localhost:8081/api/scripts/group?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}
```

## 注意事项

1. **认证**: 所有请求都需要在Header中携带有效的JWT Token
2. **URL编码**: 当scriptGroupIds包含特殊字符时，需要进行URL编码
3. **分页**: page从1开始，pageSize建议设置合理范围(1-100)
4. **错误处理**: 前端需要根据code字段判断请求是否成功
5. **超时设置**: 建议设置合理的请求超时时间
6. **团队隔离**: 后端会自动根据JWT Token中的teamId进行数据隔离

## 使用场景

- **剧本管理页面**: 根据分组筛选显示剧本列表
- **分组切换**: 支持单个或多个分组的快速切换查看
- **数据统计**: 统计不同分组下的剧本数量
- **批量操作**: 基于分组进行批量管理操作

---

*本文档基于AI Train Playground项目，版本：v0.2.0-SNAPSHOT*
```
```
