# GeneTempJwtController API调用示例

## 接口概述

`GeneTempJwtController` 提供临时JWT令牌生成功能，用于测试和开发环境。

### 基础信息

- **控制器路径**: `/api/staff`
- **Swagger标签**: `员工认证管理`
- **创建日期**: 2025-09-01

---

## API接口详情

### 1. 生成临时JWT令牌

#### 基本信息

- **接口路径**: `POST /api/staff/generate-temp-jwt`
- **接口描述**: 生成一个临时的JWT令牌，使用随机用户ID和UUID用户名
- **请求方式**: POST
- **无需入参**: 该接口无需任何请求参数

#### 业务逻辑

1. 从 `SecurityUtil.getCurrentTeamId()` 获取当前团队ID
2. 生成随机负数作为 `userId`（范围：-1 到 -1000000）
3. 使用 `UUID.randomUUID()` 生成随机用户名
4. 调用 `jwtUtil.generateToken(userId, username, teamId, false)` 生成JWT令牌
5. 返回令牌及相关信息

#### 请求示例

```bash
# 基础请求
curl -X POST http://localhost:8081/api/staff/generate-temp-jwt \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 使用jq格式化输出
curl -X POST http://localhost:8081/api/staff/generate-temp-jwt \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq '.'
```

#### 响应示例

##### 成功响应 (200)

```json
{
  "success": true,
  "code": 1,
  "message": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOi0xMjM0NTYsInRlYW1JZCI6MTAwMSwic3ViIjoiZjQ3YWMxMGItNThmMS00YjcyLTlkYjEtYzM5M2Q5OThhYjUyIiwiaWF0IjoxNzI1MTk2ODAwLCJleHAiOjE3MjUyMDA0MDB9.Xx1n2_Abc3D4e5F6g7H8i9J0k1L2m3N4o5P6q7R8s9T0",
    "userId": -123456,
    "username": "f47ac10b-58f1-4b72-9db1-c393d998ab52",
    "teamId": 1001,
    "rememberMe": false
  },
  "timestamp": "2025-09-01T10:30:00"
}
```

##### 失败响应 (500)

```json
{
  "success": false,
  "code": 500,
  "message": "生成临时JWT令牌失败: Unable to get team ID from security context",
  "data": null,
  "timestamp": "2025-09-01T10:30:00"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `token` | String | 生成的JWT令牌 |
| `userId` | Long | 随机生成的负数用户ID |
| `username` | String | UUID格式的随机用户名 |
| `teamId` | Long | 从SecurityUtil获取的团队ID |
| `rememberMe` | Boolean | 固定值false |

---

## 使用场景

### 开发测试
```bash
# 获取临时令牌用于接口测试
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/staff/generate-temp-jwt \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $EXISTING_TOKEN")

TEMP_TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.data.token')
echo "临时令牌: $TEMP_TOKEN"

# 使用临时令牌测试其他接口
curl -X GET http://localhost:8081/api/some-protected-endpoint \
  -H "Authorization: Bearer $TEMP_TOKEN"
```

### 批量生成
```bash
# 生成多个临时令牌
for i in {1..5}; do
  echo "=== 生成第 $i 个临时令牌 ==="
  curl -X POST http://localhost:8081/api/staff/generate-temp-jwt \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq '.data.token'
  echo ""
done
```

---

## 注意事项

1. **权限要求**: 需要有效的JWT令牌才能访问此接口
2. **团队隔离**: 生成的令牌会继承当前用户的teamId
3. **随机性**: 每次调用都会生成不同的userId和username
4. **有效期**: 生成的令牌遵循系统默认的过期时间配置
5. **用途限制**: 建议仅在开发和测试环境中使用

---

## 错误处理

### 常见错误及解决方案

| 错误描述 | 可能原因 | 解决方案 |
|----------|----------|----------|
| `Unable to get team ID` | 当前用户未登录或JWT令牌无效 | 检查Authorization头中的JWT令牌 |
| `Internal Server Error` | JwtUtil配置问题 | 检查JWT相关配置项 |
| `403 Forbidden` | 无权限访问 | 确认用户具有相应的访问权限 |

---

## 技术实现

### 核心依赖
- `JwtUtil`: JWT令牌生成工具
- `SecurityUtil`: 安全上下文工具类
- `Random`: 随机数生成
- `UUID`: 唯一标识符生成

### 关键代码逻辑
```java
// 获取当前团队ID
Long teamId = SecurityUtil.getCurrentTeamId();

// 生成随机负数userId
Long userId = (long) (random.nextInt(1000000) + 1) * -1;

// 生成UUID用户名
String username = UUID.randomUUID().toString();

// 生成JWT令牌
String token = jwtUtil.generateToken(userId, username, teamId, false);
```

---

## 版本历史

| 版本 | 日期 | 变更说明 |
|------|------|----------|
| 1.0.0 | 2025-09-01 | 初始版本，支持临时JWT令牌生成 |