# TrainRoleController API 调用示例

## 接口基础信息
- **服务端口**: 8081
- **基础路径**: `/api/roles`
- **认证方式**: JWT Token (请在请求头中添加 `Authorization: Bearer <token>`)

## API 接口列表

### 1. 创建角色

**接口**: `POST /api/roles`

**描述**: 新增角色信息

**请求示例**:
```bash
curl -X POST "http://localhost:8081/api/roles" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "客服",
    "roleCode": "CUSTOMER_SERVICE",
    "description": "负责客户服务相关工作"
  }'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| roleName | String | 是 | 角色名称，最大64字符 | "客服" |
| roleCode | String | 是 | 角色编码，最大64字符 | "CUSTOMER_SERVICE" |
| description | String | 否 | 角色描述，最大255字符 | "负责客户服务相关工作" |

**响应示例**:
```json
{
  "code": 1,
  "message": "角色创建成功",
  "data": 1
}
```

---

### 2. 更新角色信息

**接口**: `PUT /api/roles`

**描述**: 修改角色基本信息

**请求示例**:
```bash
curl -X PUT "http://localhost:8081/api/roles" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "roleName": "高级客服",
    "description": "负责高级客户服务相关工作",
    "version": 0
  }'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 角色ID | 1 |
| roleName | String | 否 | 角色名称，最大64字符 | "高级客服" |
| description | String | 否 | 角色描述，最大255字符 | "负责高级客户服务相关工作" |
| version | Long | 是 | 版本号（乐观锁） | 0 |

**响应示例**:
```json
{
  "code": 1,
  "message": "角色更新成功",
  "data": null
}
```

---

### 3. 删除角色

**接口**: `DELETE /api/roles/{id}`

**描述**: 根据ID删除单个角色

**请求示例**:
```bash
curl -X DELETE "http://localhost:8081/api/roles/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 角色ID | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "角色删除成功",
  "data": null
}
```

---

### 4. 批量删除角色

**接口**: `DELETE /api/roles/batch`

**描述**: 根据ID列表批量删除角色

**请求示例**:
```bash
curl -X DELETE "http://localhost:8081/api/roles/batch" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '[1, 2, 3]'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| ids | List<Long> | 是 | 角色ID列表 | [1, 2, 3] |

**响应示例**:
```json
{
  "code": 1,
  "message": "角色批量删除成功",
  "data": null
}
```

---

### 5. 查询角色详情

**接口**: `GET /api/roles/{id}`

**描述**: 根据ID查询角色详细信息

**请求示例**:
```bash
curl -X GET "http://localhost:8081/api/roles/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 角色ID | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "roleName": "客服",
    "roleCode": "CUSTOMER_SERVICE",
    "description": "负责客户服务相关工作",
    "teamId": 100,
    "createTime": "2025-07-27T09:00:00",
    "updateTime": "2025-07-27T10:30:00",
    "creator": "admin",
    "updater": "admin",
    "version": 0
  }
}
```

---

### 6. 分页查询角色列表

**接口**: `GET /api/roles/list`

**描述**: 根据条件分页查询角色列表

**请求示例**:
```bash
# 基础分页查询
curl -X GET "http://localhost:8081/api/roles/list?page=1&pageSize=10" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 带筛选条件的查询
curl -X GET "http://localhost:8081/api/roles/list?page=1&pageSize=10&roleName=客服&roleCode=CUSTOMER" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**查询参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| page | Integer | 否 | 页码，从1开始，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认10 | 10 |
| roleName | String | 否 | 角色名称（模糊搜索） | "客服" |
| roleCode | String | 否 | 角色编码（模糊搜索） | "CUSTOMER" |

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "roleName": "客服",
        "roleCode": "CUSTOMER_SERVICE",
        "description": "负责客户服务相关工作",
        "teamId": 100,
        "createTime": "2025-07-27T09:00:00",
        "updateTime": "2025-07-27T10:30:00",
        "creator": "admin",
        "updater": "admin",
        "version": 0
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

---

### 7. 查询所有角色

**接口**: `GET /api/roles/all`

**描述**: 查询团队所有角色（不分页）

**请求示例**:
```bash
curl -X GET "http://localhost:8081/api/roles/all" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "roleName": "客服",
      "roleCode": "CUSTOMER_SERVICE",
      "description": "负责客户服务相关工作",
      "teamId": 100,
      "createTime": "2025-07-27T09:00:00",
      "updateTime": "2025-07-27T10:30:00",
      "creator": "admin",
      "updater": "admin",
      "version": 0
    },
    {
      "id": 2,
      "roleName": "管理员",
      "roleCode": "ADMIN",
      "description": "系统管理员",
      "teamId": 100,
      "createTime": "2025-07-27T09:00:00",
      "updateTime": "2025-07-27T10:30:00",
      "creator": "admin",
      "updater": "admin",
      "version": 0
    }
  ]
}
```

---

### 8. 检查角色名称是否存在

**接口**: `GET /api/roles/check-name`

**描述**: 验证角色名称是否已被使用

**请求示例**:
```bash
# 创建时检查
curl -X GET "http://localhost:8081/api/roles/check-name?roleName=客服" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 编辑时检查（排除当前角色）
curl -X GET "http://localhost:8081/api/roles/check-name?roleName=客服&excludeId=1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**查询参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| roleName | String | 是 | 要检查的角色名称 | "客服" |
| excludeId | Long | 否 | 排除的角色ID（编辑时使用） | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "检查完成",
  "data": true
}
```

---

### 9. 检查角色编码是否存在

**接口**: `GET /api/roles/check-code`

**描述**: 验证角色编码是否已被使用

**请求示例**:
```bash
# 创建时检查
curl -X GET "http://localhost:8081/api/roles/check-code?roleCode=CUSTOMER_SERVICE" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 编辑时检查（排除当前角色）
curl -X GET "http://localhost:8081/api/roles/check-code?roleCode=CUSTOMER_SERVICE&excludeId=1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**查询参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| roleCode | String | 是 | 要检查的角色编码 | "CUSTOMER_SERVICE" |
| excludeId | Long | 否 | 排除的角色ID（编辑时使用） | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "检查完成",
  "data": false
}
```

---

### 10. 查询员工角色

**接口**: `GET /api/roles/by-staff/{staffId}`

**描述**: 根据员工ID查询其拥有的角色

**请求示例**:
```bash
curl -X GET "http://localhost:8081/api/roles/by-staff/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| staffId | Long | 是 | 员工ID | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "roleName": "客服",
      "roleCode": "CUSTOMER_SERVICE",
      "description": "负责客户服务相关工作",
      "teamId": 100,
      "createTime": "2025-07-27T09:00:00",
      "updateTime": "2025-07-27T10:30:00",
      "creator": "admin",
      "updater": "admin",
      "version": 0
    }
  ]
}
```

---

## 使用说明

1. **认证**: 所有接口都需要在请求头中携带有效的JWT Token
2. **团队隔离**: 系统会自动根据Token中的团队信息进行数据隔离
3. **乐观锁**: 更新操作使用乐观锁机制，需要传递正确的版本号
4. **参数校验**: 创建和更新操作会进行严格的参数校验
5. **编码唯一性**: 角色编码在团队内必须唯一，不可修改
6. **名称唯一性**: 角色名称在团队内必须唯一
7. **响应格式**: 所有接口统一使用 `Result` 包装器，成功时 `code=1`，失败时 `code=500`
8. **数据库关联**: 
   - 角色与员工是多对多关系，通过 `train_staff_role` 表关联
   - 删除角色时会同时删除相关的员工角色关联关系
   - 角色编码一旦创建不可修改，只能修改角色名称和描述

## 字段说明

### 角色状态字段
- **id**: 角色唯一标识，系统自动生成
- **roleName**: 角色显示名称，支持中文，用于界面展示
- **roleCode**: 角色编码，英文大写+下划线，用于程序逻辑判断
- **description**: 角色描述，详细说明角色职责
- **teamId**: 团队ID，系统自动设置，确保数据隔离
- **version**: 版本号，用于乐观锁控制并发更新

### 布尔值说明
- **true**: 表示存在/是/启用
- **false**: 表示不存在/否/禁用

## 错误码说明

- **code=1**: 操作成功
- **code=500**: 操作失败，具体错误信息在message字段中
- 常见错误：
  - "角色名称已存在"
  - "角色编码已存在" 
  - "角色不存在或无权限操作"
  - "版本号不匹配（数据已被其他用户修改）"

## 前端开发建议

1. **表单验证**: 前端应实现客户端验证，与后端验证保持一致
2. **重复检查**: 在用户输入角色名称/编码时，实时调用检查接口
3. **乐观锁处理**: 更新失败时提示用户刷新数据后重试
4. **分页查询**: 建议默认每页显示10-20条记录
5. **筛选功能**: 支持按角色名称和编码进行模糊搜索
6. **角色选择**: 使用下拉列表时可调用 `/all` 接口获取所有角色