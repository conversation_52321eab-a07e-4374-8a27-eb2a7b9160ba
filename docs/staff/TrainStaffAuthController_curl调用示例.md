# TrainStaffAuthController API调用示例

## 接口说明
TrainStaffAuthController 提供员工认证相关接口，支持员工登录认证并返回JWT令牌。

## 基础信息
- **基础路径**: `/api/staff/auth`
- **认证方式**: 员工登录接口不需要认证，其他受保护接口需要JWT令牌
- **响应格式**: 统一使用Result包装，成功状态码为1

---

## 1. 员工登录

### 接口描述
员工登录认证接口，验证员工用户名和密码，返回JWT令牌用于后续API调用。

### 请求信息
- **请求路径**: `POST /api/staff/auth/login`
- **Content-Type**: `application/json`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| username | String | 是 | 员工用户名 | "staff001" |
| password | String | 是 | 员工密码 | "123456" |
| rememberMe | Boolean | 否 | 是否记住登录状态，默认false | false |

### 请求示例
```bash
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

### 响应示例

#### 登录成功
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "staffId": 1,
    "username": "staff001",
    "displayName": "张三",
    "teamId": 1,
    "status": 1,
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
  }
}
```

#### 登录失败
```json
{
  "code": 500,
  "message": "用户名或密码错误",
  "data": null
}
```

#### 账户被锁定
```json
{
  "code": 500,
  "message": "账号已被锁定，请30分钟后再试",
  "data": null
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| staffId | Long | 员工ID |
| username | String | 员工用户名 |
| displayName | String | 员工显示名称 |
| teamId | Long | 团队ID |
| status | Integer | 员工状态（固定为1，表示正常） |
| token | String | JWT访问令牌，用于后续API调用 |

---

## 功能特性

### 1. 密码验证
- 使用BCrypt加密算法验证密码
- 支持强密码策略

### 2. 账户安全
- **失败次数限制**: 连续5次密码错误后账户被锁定
- **自动解锁**: 锁定30分钟后自动解锁
- **锁定状态检查**: 登录时检查账户是否被锁定

### 3. JWT令牌管理
- **令牌生成**: 成功登录后生成JWT令牌
- **记住我功能**: 支持长期令牌（通过rememberMe参数控制）
- **令牌信息**: 令牌包含员工ID、用户名、团队ID等信息

### 4. 数据库关联
- **员工表**: 基于train_staff表进行认证
- **团队隔离**: 支持多团队环境
- **登录记录**: 记录最后登录时间和失败尝试次数

---

## 错误处理

### 常见错误码
| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 用户名或密码错误 | 用户名不存在或密码不匹配 | 检查用户名和密码是否正确 |
| 账号已被锁定，请30分钟后再试 | 连续失败5次导致账户锁定 | 等待30分钟后重试或联系管理员 |

### 技术错误处理
- **参数校验**: 用户名和密码不能为空
- **数据库连接**: 数据库异常时返回系统错误
- **JWT生成**: 令牌生成失败时返回认证错误

---

## 使用说明

### 1. 登录流程
1. 发送POST请求到登录接口
2. 验证返回的JWT令牌
3. 在后续API调用中携带令牌

### 2. JWT令牌使用
```bash
# 在请求头中携带令牌
curl -X GET "http://localhost:8081/api/staff/xxx" \
  -H "Authorization: Bearer {token}"
```

### 3. 前端集成建议
- 将JWT令牌存储在localStorage或sessionStorage
- 设置请求拦截器自动添加Authorization头
- 监听401状态码自动跳转到登录页

---

## 开发测试

### 测试用户
```sql
-- 创建测试员工数据
INSERT INTO train_staff (username, password_hash, display_name, team_id, creator, updater) 
VALUES ('teststaff', '$2a$10$example_bcrypt_hash', '测试员工', 1, 'system', 'system');
```

### 测试脚本
```bash
# 测试登录成功
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "teststaff", "password": "123456", "rememberMe": false}'

# 测试密码错误
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "teststaff", "password": "wrongpassword", "rememberMe": false}'
```

---

## 安全注意事项

1. **HTTPS传输**: 生产环境必须使用HTTPS
2. **密码强度**: 建议实施强密码策略
3. **令牌管理**: 客户端应安全存储JWT令牌
4. **会话管理**: 定期更新令牌，实现令牌刷新机制
5. **日志记录**: 记录登录尝试和安全事件