# TrainStaffController API 调用示例

## 接口基础信息
- **服务端口**: 8081
- **基础路径**: `/api/staff`
- **认证方式**: JWT Token (请在请求头中添加 `Authorization: Bearer <token>`)

## API 接口列表

### 1. 创建员工

**接口**: `POST /api/staff/create`

**描述**: 新增员工信息

**请求示例**:
```bash
curl -X POST "http://localhost:8081/api/staff/create" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "password": "123456",
    "displayName": "约翰·多伊",
    "roleId": 1,
    "tagIds": [1, 2, 3]
  }'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| username | String | 是 | 用户名（登录名），3-50字符 | "john_doe" |
| password | String | 是 | 密码，6-50字符 | "123456" |
| displayName | String | 是 | 显示名称，最大100字符 | "约翰·多伊" |
| roleId | Long | 否 | 角色ID，指定员工的角色权限 | 1 |
| tagIds | List<Long> | 否 | 标签ID列表，指定员工的标签 | [1, 2, 3] |

**响应示例**:
```json
{
  "code": 1,
  "message": "员工创建成功",
  "data": null
}
```

---

### 2. 更新员工信息

**接口**: `PUT /api/staff/update`

**描述**: 修改员工基本信息

**请求示例**:
```bash
curl -X PUT "http://localhost:8081/api/staff/update" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "displayName": "约翰·多伊（高级）",
    "version": 0,
    "roleId": 2,
    "tagIds": [2, 3, 4]
  }'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 员工ID | 1 |
| displayName | String | 是 | 显示名称，最大100字符 | "约翰·多伊（高级）" |
| version | Long | 是 | 版本号（乐观锁） | 0 |
| roleId | Long | 否 | 角色ID，指定员工的角色权限 | 2 |
| tagIds | List<Long> | 否 | 标签ID列表，指定员工的标签 | [2, 3, 4] |

**响应示例**:
```json
{
  "code": 1,
  "message": "员工信息更新成功",
  "data": null
}
```

---

### 3. 删除员工

**接口**: `DELETE /api/staff/delete/{id}`

**描述**: 根据ID删除员工

**请求示例**:
```bash
curl -X DELETE "http://localhost:8081/api/staff/delete/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "员工删除成功",
  "data": null
}
```

---

### 4. 批量删除员工

**接口**: `DELETE /api/staff/batch-delete`

**描述**: 批量删除多个员工

**请求示例**:
```bash
curl -X DELETE "http://localhost:8081/api/staff/batch-delete" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '[1, 2, 3]'
```

**请求参数**: 员工ID数组
```json
[1, 2, 3]
```

**响应示例**:
```json
{
  "code": 1,
  "message": "成功删除 3 个员工",
  "data": null
}
```

---

### 5. 查询员工详情

**接口**: `GET /api/staff/detail/{id}`

**描述**: 根据ID查询员工详细信息

**请求示例**:
```bash
curl -X GET "http://localhost:8081/api/staff/detail/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "userId": 1001,
    "username": "john_doe",
    "displayName": "约翰·多伊",
    "isLocked": false,
    "failedAttempts": 0,
    "lockTime": null,
    "lastLoginTime": "2025-07-27T10:30:00",
    "lastLoginIp": "*************",
    "teamId": 1,
    "createTime": "2025-07-27T09:00:00",
    "updateTime": "2025-07-27T10:30:00",
    "creator": "admin",
    "updater": "admin",
    "version": 0,
    "roleId": 1,
    "tagIds": [1, 2, 3]
  }
}
```

---

### 6. 分页查询员工列表

**接口**: `GET /api/staff/list`

**描述**: 根据条件分页查询员工列表

**请求示例**:
```bash
# 基础分页查询
curl -X GET "http://localhost:8081/api/staff/list?page=1&pageSize=10" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 带条件的分页查询
curl -X GET "http://localhost:8081/api/staff/list?page=1&pageSize=10&username=john&status=1&isLocked=false" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 更复杂的查询
curl -X GET "http://localhost:8081/api/staff/list?page=1&pageSize=20&displayName=管理&email=admin" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| page | Integer | 否 | 页码，从1开始，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认10 | 10 |
| username | String | 否 | 用户名（模糊查询） | "john" |
| displayName | String | 否 | 显示名称（模糊查询） | "管理员" |
| email | String | 否 | 邮箱（模糊查询） | "admin" |
| status | Integer | 否 | 员工状态：1-正常，2-停用 | 1 |
| isLocked | Boolean | 否 | 是否锁定：true-已锁定，false-未锁定 | false |

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "userId": 1001,
        "username": "john_doe",
        "displayName": "约翰·多伊",
        "isLocked": false,
        "failedAttempts": 0,
        "lockTime": null,
        "lastLoginTime": "2025-07-27T10:30:00",
        "lastLoginIp": "*************",
        "teamId": 1,
        "createTime": "2025-07-27T09:00:00",
        "updateTime": "2025-07-27T10:30:00",
        "creator": "admin",
        "updater": "admin",
        "version": 0,
        "roleId": 1,
        "tagIds": [1, 2, 3]
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

---

### 7. 检查用户名是否存在

**接口**: `GET /api/staff/check-username`

**描述**: 检查用户名是否已被使用

**请求示例**:
```bash
# 检查新用户名
curl -X GET "http://localhost:8081/api/staff/check-username?username=new_user" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 更新时检查（排除自己）
curl -X GET "http://localhost:8081/api/staff/check-username?username=john_doe&excludeId=1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| username | String | 是 | 要检查的用户名 | "new_user" |
| excludeId | Long | 否 | 排除的员工ID（更新时使用） | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": true
}
```

---

### 8. 重置员工密码

**接口**: `PUT /api/staff/reset-password`

**描述**: 管理员重置员工密码

**请求示例**:
```bash
curl -X PUT "http://localhost:8081/api/staff/reset-password?id=1&newPassword=newpass123" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 员工ID | 1 |
| newPassword | String | 是 | 新密码 | "newpass123" |

**响应示例**:
```json
{
  "code": 1,
  "message": "密码重置成功",
  "data": null
}
```

---

### 9. 锁定/解锁账户

**接口**: `PUT /api/staff/toggle-lock`

**描述**: 切换员工账户的锁定状态

**请求示例**:
```bash
# 锁定账户
curl -X PUT "http://localhost:8081/api/staff/toggle-lock?id=1&isLocked=true" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 解锁账户
curl -X PUT "http://localhost:8081/api/staff/toggle-lock?id=1&isLocked=false" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 员工ID | 1 |
| isLocked | Boolean | 是 | 是否锁定 | true |

**响应示例**:
```json
{
  "code": 1,
  "message": "账户锁定成功",
  "data": null
}
```

---

## 错误响应示例

### 参数校验错误
```json
{
  "code": 500,
  "message": "用户名不能为空",
  "data": null
}
```

### 业务逻辑错误
```json
{
  "code": 500,
  "message": "用户名已存在",
  "data": null
}
```

### 权限错误
```json
{
  "code": 500,
  "message": "员工不存在",
  "data": null
}
```

### 乐观锁冲突
```json
{
  "code": 500,
  "message": "员工信息更新失败，可能数据已被修改",
  "data": null
}
```

---

## 使用说明

1. **认证**: 所有接口都需要在请求头中携带有效的JWT Token
2. **团队隔离**: 系统会自动根据Token中的团队信息进行数据隔离
3. **乐观锁**: 更新操作使用乐观锁机制，需要传递正确的版本号
4. **参数校验**: 创建和更新操作会进行严格的参数校验
5. **密码加密**: 系统使用BCrypt对密码进行加密存储
6. **账户锁定**: 支持手动锁定/解锁账户功能
7. **登录信息**: 系统会记录员工的登录时间、IP和失败次数
8. **响应格式**: 所有接口统一使用 `Result` 包装器，成功时 `code=1`，失败时 `code=500`
9. **数据库关联**: 
   - 员工与角色是多对多关系，通过 `train_staff_role` 表关联
   - 员工与标签是多对多关系，通过 `train_staff_tag_relation` 表关联
   - 删除员工时会自动删除其角色和标签关联关系
   - 更新员工时会先删除原有关联再创建新的关联关系

## 字段说明

### 员工状态 (status)
- `1`: 正常状态，可以正常使用
- `2`: 停用状态，账户被禁用

### 账户锁定 (isLocked)
- `true`: 账户已锁定，无法登录
- `false`: 账户未锁定，可以正常登录

### 版本号 (version)
- 用于乐观锁控制，每次更新会自动递增
- 更新时必须传递当前正确的版本号

### 用户ID (userId)
- 系统自动生成的用户ID，用于关联其他业务数据
- 由SecurityUtil获取当前用户信息后自动设置

### 角色ID (roleId)
- 员工的角色权限ID，与train_staff_role表关联
- 创建员工时可指定角色，更新时可修改角色
- 删除员工时会自动删除相关的角色关联
- 查询员工时会返回其当前的角色ID

## 数据库关联说明

### 员工角色关系
- **主表**: `train_staff` - 员工基本信息
- **关联表**: `train_staff_role` - 员工角色关联关系
- **关系**: 一个员工可以有一个主要角色（当前实现为一对一）
- **操作说明**:
  - 创建员工时，如提供roleId，会自动创建角色关联
  - 更新员工时，如提供roleId，会先删除旧关联再创建新关联
  - 删除员工时，会自动删除所有相关的角色关联
  - 查询员工时，会自动获取其角色信息