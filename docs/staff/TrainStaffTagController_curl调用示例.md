# TrainStaffTagController API 调用示例

## 接口基础信息
- **服务端口**: 8081
- **基础路径**: `/api/staff/tag`
- **认证方式**: JWT <PERSON>ken (请在请求头中添加 `Authorization: Bearer <token>`)

## API 接口列表

### 1. 分页查询员工标签

**接口**: `GET /api/staff/tag/page`

**描述**: 根据条件分页查询员工标签列表

**请求示例**:
```bash
# 基础分页查询
curl -X GET "http://localhost:8081/api/staff/tag/page?page=1&pageSize=10" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 带名称模糊查询的分页
curl -X GET "http://localhost:8081/api/staff/tag/page?page=1&pageSize=10&name=技术" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "技术专家",
        "teamId": 1,
        "createTime": "2025-07-27T10:00:00",
        "updateTime": "2025-07-27T10:00:00",
        "creator": "admin",
        "updater": "admin",
        "version": 0
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

---

### 2. 获取所有员工标签

**接口**: `GET /api/staff/tag/list`

**描述**: 获取当前团队的所有员工标签（不分页）

**请求示例**:
```bash
curl -X GET "http://localhost:8081/api/staff/tag/list" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "技术专家",
      "teamId": 1,
      "createTime": "2025-07-27T10:00:00",
      "updateTime": "2025-07-27T10:00:00",
      "creator": "admin",
      "updater": "admin",
      "version": 0
    },
    {
      "id": 2,
      "name": "产品经理",
      "teamId": 1,
      "createTime": "2025-07-27T10:05:00",
      "updateTime": "2025-07-27T10:05:00",
      "creator": "admin",
      "updater": "admin",
      "version": 0
    }
  ]
}
```

---

### 3. 获取员工标签详情

**接口**: `GET /api/staff/tag/{id}`

**描述**: 根据ID获取员工标签详细信息

**请求示例**:
```bash
curl -X GET "http://localhost:8081/api/staff/tag/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "技术专家",
    "teamId": 1,
    "createTime": "2025-07-27T10:00:00",
    "updateTime": "2025-07-27T10:00:00",
    "creator": "admin",
    "updater": "admin",
    "version": 0
  }
}
```

---

### 4. 创建员工标签

**接口**: `POST /api/staff/tag`

**描述**: 创建新的员工标签

**请求示例**:
```bash
curl -X POST "http://localhost:8081/api/staff/tag" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "高级开发工程师"
  }'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| name | String | 是 | 标签名称，最大长度50字符 | "高级开发工程师" |

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": 3
}
```

---

### 5. 更新员工标签

**接口**: `PUT /api/staff/tag`

**描述**: 更新员工标签信息

**请求示例**:
```bash
curl -X PUT "http://localhost:8081/api/staff/tag" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "资深技术专家",
    "version": 0
  }'
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 标签ID | 1 |
| name | String | 是 | 标签名称，最大长度50字符 | "资深技术专家" |
| version | Long | 是 | 版本号（乐观锁） | 0 |

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

---

### 6. 删除员工标签

**接口**: `DELETE /api/staff/tag/{id}`

**描述**: 根据ID删除员工标签

**请求示例**:
```bash
curl -X DELETE "http://localhost:8081/api/staff/tag/1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

---

### 7. 批量删除员工标签

**接口**: `DELETE /api/staff/tag/batch`

**描述**: 根据ID列表批量删除员工标签

**请求示例**:
```bash
curl -X DELETE "http://localhost:8081/api/staff/tag/batch" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '[1, 2, 3]'
```

**请求参数**: ID数组
```json
[1, 2, 3]
```

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": null
}
```

---

### 8. 检查标签名称是否重复

**接口**: `GET /api/staff/tag/check-name`

**描述**: 检查标签名称在当前团队是否已存在

**请求示例**:
```bash
# 检查新标签名称
curl -X GET "http://localhost:8081/api/staff/tag/check-name?name=技术专家" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"

# 更新时检查（排除自己）
curl -X GET "http://localhost:8081/api/staff/tag/check-name?name=技术专家&excludeId=1" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| name | String | 是 | 要检查的标签名称 | "技术专家" |
| excludeId | Long | 否 | 排除的标签ID（更新时使用） | 1 |

**响应示例**:
```json
{
  "code": 1,
  "message": "操作成功",
  "data": true
}
```

---

## 错误响应示例

### 参数校验错误
```json
{
  "code": 500,
  "message": "标签名称不能为空",
  "data": null
}
```

### 业务逻辑错误
```json
{
  "code": 500,
  "message": "标签名称已存在",
  "data": null
}
```

### 权限错误
```json
{
  "code": 500,
  "message": "员工标签不存在或无权限访问",
  "data": null
}
```

### 乐观锁冲突
```json
{
  "code": 500,
  "message": "数据已被其他用户修改，请刷新后重试",
  "data": null
}
```

---

## 使用说明

1. **认证**: 所有接口都需要在请求头中携带有效的JWT Token
2. **团队隔离**: 系统会自动根据Token中的团队信息进行数据隔离
3. **乐观锁**: 更新操作使用乐观锁机制，需要传递正确的版本号
4. **参数校验**: 创建和更新操作会进行严格的参数校验
5. **重名检查**: 标签名称在同一团队内必须唯一
6. **响应格式**: 所有接口统一使用 `Result` 包装器，成功时 `code=1`，失败时 `code=500`