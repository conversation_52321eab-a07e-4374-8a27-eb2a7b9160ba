# SystemConfigController API 调用示例

## 接口概述
系统配置管理接口，提供系统配置的增删改查功能。

## 认证说明
所有接口都需要JWT认证，请在请求头中添加：
```
Authorization: Bearer <your_jwt_token>
```

## 1. 分页查询系统配置列表

### 接口信息
- **URL**: `GET /api/system-configs`
- **描述**: 分页查询系统配置列表，支持按命名空间、配置键、描述等条件查询

### 请求示例
```bash
# 基础查询
curl -X GET "http://localhost:8081/api/system-configs?page=1&pageSize=10" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"

# 按命名空间查询
curl -X GET "http://localhost:8081/api/system-configs?page=1&pageSize=10&namespace=default" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"

# 按配置键模糊查询
curl -X GET "http://localhost:8081/api/system-configs?page=1&pageSize=10&configKey=timeout" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

### 响应示例
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "teamId": 1,
        "namespace": "default",
        "configKey": "system.timeout",
        "configValue": "30000",
        "description": "系统超时配置，单位毫秒",
        "version": 0,
        "creator": "admin",
        "updater": "admin",
        "createTime": "2025-08-20 10:00:00",
        "updateTime": "2025-08-20 10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

## 2. 查询系统配置详情

### 接口信息
- **URL**: `GET /api/system-configs/{id}`
- **描述**: 根据配置ID查询详细信息

### 请求示例
```bash
curl -X GET "http://localhost:8081/api/system-configs/1" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

### 响应示例
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "teamId": 1,
    "namespace": "default",
    "configKey": "system.timeout",
    "configValue": "30000",
    "description": "系统超时配置，单位毫秒",
    "version": 0,
    "creator": "admin",
    "updater": "admin",
    "createTime": "2025-08-20 10:00:00",
    "updateTime": "2025-08-20 10:00:00"
  }
}
```

## 3. 创建系统配置

### 接口信息
- **URL**: `POST /api/system-configs`
- **描述**: 创建新的系统配置项

### 请求示例
```bash
curl -X POST "http://localhost:8081/api/system-configs" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ" \
  -d '{
    "namespace": "default",
    "configKey": "system.timeout",
    "configValue": "30000",
    "description": "系统超时配置，单位毫秒"
  }'
```

### 响应示例
```json
{
  "code": 1,
  "message": "创建成功",
  "data": 1
}
```

## 4. 更新系统配置

### 接口信息
- **URL**: `PUT /api/system-configs/{id}`
- **描述**: 更新指定的系统配置项

### 请求示例
```bash
curl -X PUT "http://localhost:8081/api/system-configs/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ" \
  -d '{
    "id": 1,
    "namespace": "default",
    "configKey": "system.timeout",
    "configValue": "60000",
    "description": "系统超时配置，单位毫秒（已更新）",
    "version": 0
  }'
```

### 响应示例
```json
{
  "code": 1,
  "message": "更新成功",
  "data": null
}
```

## 5. 删除系统配置

### 接口信息
- **URL**: `DELETE /api/system-configs/{id}`
- **描述**: 删除指定的系统配置项

### 请求示例
```bash
curl -X DELETE "http://localhost:8081/api/system-configs/1" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

### 响应示例
```json
{
  "code": 1,
  "message": "删除成功",
  "data": null
}
```

## 6. 批量删除系统配置

### 接口信息
- **URL**: `DELETE /api/system-configs?ids={ids}`
- **描述**: 批量删除多个系统配置项

### 请求示例
```bash
curl -X DELETE "http://localhost:8081/api/system-configs?ids=1,2,3" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

### 响应示例
```json
{
  "code": 1,
  "message": "批量删除成功",
  "data": null
}
```

## 7. 检查配置键是否存在

### 接口信息
- **URL**: `GET /api/system-configs/check-config-key`
- **描述**: 检查指定的配置键是否已存在

### 请求示例
```bash
# 检查新配置键
curl -X GET "http://localhost:8081/api/system-configs/check-config-key?namespace=default&configKey=system.timeout" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"

# 检查更新时排除当前记录
curl -X GET "http://localhost:8081/api/system-configs/check-config-key?namespace=default&configKey=system.timeout&excludeId=1" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

### 响应示例
```json
{
  "code": 1,
  "message": "检查完成",
  "data": true
}
```

## 8. 获取配置值

### 接口信息
- **URL**: `GET /api/system-configs/config-value`
- **描述**: 根据命名空间和配置键获取配置值

### 请求示例
```bash
# 获取配置值
curl -X GET "http://localhost:8081/api/system-configs/config-value?namespace=default&configKey=system.timeout" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"

# 获取配置值（带默认值）
curl -X GET "http://localhost:8081/api/system-configs/config-value?namespace=default&configKey=system.timeout&defaultValue=30000" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

### 响应示例
```json
{
  "code": 1,
  "message": "获取成功",
  "data": "30000"
}
```

## 错误响应示例

### 参数错误
```json
{
  "code": 0,
  "message": "创建失败: 配置键已存在: default.system.timeout",
  "data": null
}
```

### 记录不存在
```json
{
  "code": 0,
  "message": "查询失败: 系统配置不存在，ID: 999",
  "data": null
}
```

### 版本冲突
```json
{
  "code": 0,
  "message": "更新失败: 更新系统配置失败，可能是版本冲突或记录不存在",
  "data": null
}
```

## 注意事项

1. **团队隔离**: 所有操作都基于当前用户的团队ID，确保数据隔离
2. **乐观锁**: 更新操作使用版本号进行乐观锁控制，防止并发冲突
3. **唯一性约束**: 同一团队下，命名空间+配置键的组合必须唯一
4. **分页参数**: page从1开始，pageSize建议不超过100
5. **模糊查询**: configKey和description支持模糊查询
6. **JWT认证**: 所有接口都需要有效的JWT token
