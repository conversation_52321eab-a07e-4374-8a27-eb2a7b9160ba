# FakeRobotController curl调用示例

## 接口说明
虚拟机器人服务，用于模拟聊天记录中的买家消息，按客服回复分组并依次返回。

## 1. 获取下一个买家消息

### 接口信息
- **URL**: `/api/fake-robot/next-message`
- **方法**: `POST`
- **描述**: 解析聊天记录并返回下一个买家消息块

### 请求示例

```bash
curl -X POST "http://localhost:8081/api/fake-robot/next-message" \
  -H "Content-Type: text/plain" \
  -d '一位年轻时尚的小伙子 2025-09-01 10:30:00
https://item.jd.com/10160508281189.html?sdx=ehi-lLxFuZiE6JnJZ4NZjM4iuDGQABwrsmlNsKZFZdqPPe_RLJhY73rloEjhX2WZ
这个小米（MI）手环10NFC版 新品 运动手环 智能手环能测血压吗
小米手机通讯新顾问 2025-09-01 10:30:06
在的
小米手机通讯新顾问 2025-09-01 10:30:12
亲爱的，请您稍等几分钟，这边查询到给您回复
小米手机通讯新顾问 2025-09-01 10:31:00
这款小米（MI）手环10NFC版 新品 运动手环 智能手环支持压力监测功能，能够实时记录您的血压数据哦 
一位年轻时尚的小伙子 2025-09-01 10:31:10
续航时间多久
小米手机通讯新顾问 2025-09-01 10:31:16
一般NFC版续航时间在18天左右（典型模式），AOD模式续航8天，重载模式续航7天，具体以实际使用情况为准哦
一位年轻时尚的小伙子 2025-09-01 10:31:25
有粉色表带吗
小米手机通讯新顾问 2025-09-01 10:31:32
目前NFC版只有TPU腕带，没有粉色表带哦 
一位年轻时尚的小伙子 2025-09-01 10:31:40
带充电器吗
小米手机通讯新顾问 2025-09-01 10:31:46
包装清单里有充电线，但没有充电头，需要您自己购买哦
一位年轻时尚的小伙子 2025-09-01 10:31:52
那个订单下面3块多的就是充电器吧
小米手机通讯新顾问 2025-09-01 10:31:58
以您的页面显示为准哦
一位年轻时尚的小伙子 2025-09-01 10:32:05
好的，谢谢
小米手机通讯新顾问 2025-09-01 10:32:10
不客气，祝您购物愉快！'
```

### 响应示例

**第一次调用响应**：
```json
{
  "result": [
    "https://item.jd.com/10160508281189.html?sdx=ehi-lLxFuZiE6JnJZ4NZjM4iuDGQABwrsmlNsKZFZdqPPe_RLJhY73rloEjhX2WZ"
  ]
}
```

**第二次调用响应**：
```json
{
  "result": [
    "续航时间多久"
  ]
}
```

**第三次调用响应**：
```json
{
  "result": [
    "有粉色表带吗"
  ]
}
```

**最后调用响应**：
```json
{
  "result": [
    "已无更多问题，会话结束"
  ]
}
```

## 2. 清除会话

### 接口信息
- **URL**: `/api/fake-robot/session/{sessionId}`
- **方法**: `DELETE`
- **描述**: 清除指定会话的栈数据

### 请求示例

```bash
curl -X DELETE "http://localhost:8081/api/fake-robot/session/91020476"
```

### 响应示例

**成功响应**：
```json
{
  "message": "会话清除成功"
}
```

**失败响应**：
```json
{
  "error": "清除会话失败: 错误信息"
}
```

## 3. 检查是否有更多消息

### 接口信息
- **URL**: `/api/fake-robot/session/{sessionId}/has-more`
- **方法**: `GET`
- **描述**: 检查指定会话是否还有更多消息

### 请求示例

```bash
curl -X GET "http://localhost:8081/api/fake-robot/session/91020476/has-more"
```

### 响应示例

**有更多消息**：
```json
{
  "hasMore": true
}
```

**没有更多消息**：
```json
{
  "hasMore": false
}
```

**失败响应**：
```json
{
  "error": "检查失败: 错误信息"
}
```

## 使用说明

### 会话ID生成规则
会话ID是根据聊天记录内容的hashCode生成的，相同的聊天记录会产生相同的会话ID。

### 消息分组逻辑
1. 识别买家（聊天记录中第一个发送人）
2. 按客服回复将买家消息分组
3. 每次调用返回一个买家消息组
4. 消息组按时间顺序依次返回

### 栈数据管理
- 栈数据存储在Redis中，键格式：`fake_robot_stack:{sessionId}`
- 栈数据24小时后自动过期
- 可以手动清除会话数据

### 错误处理
- 聊天记录解析失败时返回错误信息
- 空聊天记录返回结束消息
- 系统异常时返回错误响应

## 注意事项

1. **聊天记录格式**：每行格式为 `[发送人] [时间] [内容]`，内容可能在下一行
2. **时间格式**：`yyyy-MM-dd HH:mm:ss`
3. **买家识别**：第一个发送人被识别为买家
4. **消息分组**：客服回复作为分组分隔符
5. **栈操作**：使用Redis List实现栈功能，支持并发访问
