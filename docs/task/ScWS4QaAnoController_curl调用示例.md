# ScWS4QaAnoController curl调用示例

## 概述
ScWS4QaAnoController 是专门用于问答匿名的模拟聊天WebSocket控制器，基于STOMP协议提供WebSocket接口，用于创建和管理问答匿名场景下的模拟聊天会话。

**🔥 重要更新**：从 2025-09-04 开始，该控制器已实施**WebSocket消息隔离机制**，解决了多用户同时访问时的消息串台问题。每个用户现在使用独立的订阅topic，确保消息安全隔离。

## WebSocket连接信息
- **连接地址**: `ws://localhost:8081/ws`
- **协议**: STOMP over WebSocket

## API端点

### 1. 初始化会话（已更新 - 消息隔离）
**WebSocket端点**: `/app/scws4qa/init`
**订阅响应**: `/topic/scws4qa/init/{tempSubscribeId}` ⚡ **新增隔离机制**

#### 请求示例（已更新）
```bash
# 注意：这是WebSocket STOMP消息，不是HTTP请求
# 需要通过WebSocket客户端发送

# 消息内容示例（新增tempSubscribeId参数）：
{
  "receChatRoomId": 1,
  "sceneName": "问答匿名",
  "token": "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6OTMsInN1YiI6InN0YWZmMDAxIiwiaWF0IjoxNzU2NTY4NDEyLCJleHAiOjE3NTY2NTQ4MTJ9.6mTuVhccNypE5-ls0q6dFQ4J1qPLyzgmDptOCdNEiU4",
  "isThinking": "true",
  "isStreaming": "true",
  "tempSubscribeId": "550e8400-e29b-41d4-a716-************",  // ⚡ 新增必填参数
  "ownerUserName": "testuser",     // 匿名场景用户名
  "ownerId": "1",                  // 匿名场景用户ID
  "ownerTeamId": "1",             // 匿名场景团队ID
  "anoRealName": "张三",          // 匿名用户真实姓名
  "anoRealNo": "TEST001"          // 匿名用户编号
}
```

#### 参数说明（已更新）
| 参数名 | 类型 | 必填 | 说明 | 新增/更新 |
|--------|------|------|------|-----------|
| receChatRoomId | Long | ✅ | 接待聊天室ID | - |
| sceneName | String | ✅ | 场景名称 | - |
| token | String | ✅ | JWT认证令牌 | - |
| isThinking | String | ✅ | 是否启用思考模式 | - |
| isStreaming | String | ✅ | 是否启用流式响应 | - |
| **tempSubscribeId** | **String** | **✅** | **临时订阅ID，用于消息隔离** | **⚡ 新增** |
| ownerUserName | String | ✅ | 匿名场景用户名 | - |
| ownerId | String | ✅ | 匿名场景用户ID | - |
| ownerTeamId | String | ✅ | 匿名场景团队ID | - |
| anoRealName | String | ✅ | 匿名用户真实姓名 | - |
| anoRealNo | String | ✅ | 匿名用户编号 | - |

#### 响应示例
```json
{
  "success": true,
  "message": "模拟聊天会话初始化成功（知识库转换用）",
  "chatroomId": 1,
  "sessionCount": 1,
  "sessions": [
    {
      "sessionId": "qa-1756568412345-abc12345",
      "robotName": "小智",
      "firstMessage": "您好，我想咨询一下产品相关的问题。",
      "firstMessageWithoutJson": "您好，我想咨询一下产品相关的问题。",
      "systemPrompt": "你是一个专业的客服助手...",
      "teamName": "测试团队",
      "msgId": "uuid-12345",
      "isFirstFreq": true
    }
  ],
  "staffName": "张三",
  "taskInfo": {
    "taskId": 1,
    "taskName": "问答匿名测试任务",
    "taskPurposeTag": 0
  },
  "chatRoomInfo": {
    "chatroomId": 1,
    "entryFreqMin": 1,
    "entryFreqMax": 5,
    "receptionDuration": 30
  }
}
```

### 2. 发送消息（未变更）
**WebSocket端点**: `/app/scws4qa/send`

#### 2.1 发送聊天消息
**订阅地址**: `/topic/scws4qa/send/{sessionId}`

#### 2.2 订阅聊天响应
**订阅地址**: `/topic/scws4qa/chat/{sessionId}`

#### 请求示例
```bash
# WebSocket STOMP消息内容：
{
  "sessionId": "qa-1756568412345-abc12345",
  "message": "我想了解一下这个产品的价格",
  "msgId": "uuid-12345"
}
```

#### 响应示例

##### 普通回复格式
```json
{
  "resp": {
    "results": ["根据您的咨询，这个产品的价格是..."],
    "msgId": "uuid-12345"
  }
}
```

##### 评判结果格式（包含考察点）
```json
{
  "resp": {
    "results": ["{\"分析\": \"匹配度分析描述\", \"得分\": 85, \"扣分点\": [\"扣分点1\", \"扣分点2\"], \"正确答案\": \"正确答案参考\", \"下一题\": \"丰富后的下一问题\", \"考察点\": \"退货政策\"}"],
    "msgId": "uuid-12345"
  }
}
```

### 3. 关闭会话（未变更）
**WebSocket端点**: `/app/scws4qa/close`
**订阅响应**: `/topic/scws4qa/close/{sessionId}`

#### 请求示例
```bash
# WebSocket STOMP消息内容：
{
  "sessionId": "qa-1756568412345-abc12345"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "会话已关闭（问答匿名用）",
  "sessionId": "qa-1756568412345-abc12345"
}
```

## 🔒 WebSocket消息隔离机制（2025-09-04 新增）

### 隔离原理
为解决多用户同时访问时的消息串台问题，系统实施了基于`tempSubscribeId`的消息隔离机制：

1. **前端生成UUID**：每个用户生成唯一的`tempSubscribeId`
2. **隔离订阅**：订阅用户专属的topic：`/topic/scws4qa/init/{tempSubscribeId}`
3. **消息隔离**：服务端根据`tempSubscribeId`发送到对应的隔离topic
4. **延时消息隔离**：延时创建的会话消息也发送到隔离topic

### 隔离效果
- **问题前**：用户A、B、C都会收到所有初始化响应，导致多个聊天TAB出现
- **问题后**：每个用户只收到自己的初始化响应，完全隔离

### tempSubscribeId生成建议
```javascript
// 方式1：使用crypto.randomUUID()（推荐）
const tempSubscribeId = crypto.randomUUID();
// 示例：550e8400-e29b-41d4-a716-************

// 方式2：使用时间戳 + 随机数
const tempSubscribeId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
// 示例：1725469966123-k2j8x9p4q

// 方式3：使用用户ID + 时间戳（确保唯一性）
const tempSubscribeId = userId + '-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
// 示例：user123-1725469966123-a5b8c2
```

## 使用流程（已更新）

### 1. 建立WebSocket连接（已更新）
```javascript
// 使用SockJS和STOMP建立连接
const socket = new SockJS('http://localhost:8081/ws');
const stompClient = Stomp.over(socket);

// ⚡ 生成临时订阅ID
const tempSubscribeId = crypto.randomUUID();

stompClient.connect({}, function(frame) {
    console.log('连接成功:', frame);
    
    // ⚡ 订阅隔离后的响应topic
    stompClient.subscribe('/topic/scws4qa/init/' + tempSubscribeId, function(response) {
        const result = JSON.parse(response.body);
        console.log('初始化响应:', result);
    });
});
```

### 2. 初始化会话（已更新）
```javascript
const initRequest = {
    receChatRoomId: 1,
    sceneName: "问答匿名",
    token: "your-jwt-token-here",
    isThinking: "true",
    isStreaming: "true",
    tempSubscribeId: tempSubscribeId,    // ⚡ 新增必填参数
    ownerUserName: "testuser",           // 匿名场景用户名
    ownerId: "1",                        // 匿名场景用户ID  
    ownerTeamId: "1",                    // 匿名场景团队ID
    anoRealName: "张三",                 // 匿名用户真实姓名
    anoRealNo: "TEST001"                 // 匿名用户编号
};

stompClient.send('/app/scws4qa/init', {}, JSON.stringify(initRequest));
```

### 3. 发送消息（未变更）
```javascript
// 订阅聊天消息
stompClient.subscribe('/topic/scws4qa/chat/' + sessionId, function(response) {
    const result = JSON.parse(response.body);
    console.log('收到回复:', result);
});

// 发送消息
const messageRequest = {
    sessionId: sessionId,
    message: "我想了解产品信息",
    msgId: msgId  // 从初始化响应中获取
};

stompClient.send('/app/scws4qa/send', {}, JSON.stringify(messageRequest));
```

### 4. 关闭会话（未变更）
```javascript
// 订阅关闭确认
stompClient.subscribe('/topic/scws4qa/close/' + sessionId, function(response) {
    const result = JSON.parse(response.body);
    console.log('会话关闭确认:', result);
});

// 发送关闭请求
const closeRequest = {
    sessionId: sessionId
};

stompClient.send('/app/scws4qa/close', {}, JSON.stringify(closeRequest));
```

## 认证要求

### JWT Token获取
```bash
# 首先获取JWT token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "staffId": 93,
    "username": "staff001",
    "displayName": "张三",
    "teamId": 1,
    "status": 1,
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6OTMsInN1YiI6InN0YWZmMDAxIiwiaWF0IjoxNzU2NTY4NDEyLCJleHAiOjE3NTY2NTQ4MTJ9.6mTuVhccNypE5-ls0q6dFQ4J1qPLyzgmDptOCdNEiU4"
  }
}
```

## 错误处理

### 常见错误响应
```json
{
  "error": true,
  "message": "错误描述信息"
}
```

### 错误类型（已更新）
1. **认证失败**: JWT token无效或过期
2. **参数错误**: 必要参数缺失或格式错误
3. **⚡ 隔离参数错误**: tempSubscribeId不能为空，用于消息隔离 **（新增）**
4. **权限不足**: 员工无权限访问指定聊天室
5. **会话不存在**: 指定的sessionId不存在或已过期
6. **系统错误**: 服务器内部错误

### 隔离相关错误示例（新增）
```json
{
  "error": true,
  "message": "tempSubscribeId不能为空，用于消息隔离"
}
```

## 注意事项（已更新）

1. **WebSocket协议**: 所有接口都是基于WebSocket的STOMP消息，不是HTTP请求
2. **认证要求**: 所有操作都需要有效的JWT token
3. **🔒 消息隔离**: 必须提供`tempSubscribeId`参数，确保消息不串台 **（新增重要）**
4. **会话管理**: sessionId用于标识唯一会话，需要妥善保存
5. **消息格式**: 所有消息都是JSON格式
6. **订阅机制**: 需要先订阅相应的隔离topic才能接收响应
7. **msgId字段**: 发送消息时必须包含msgId字段，从初始化响应中获取
8. **⚡ UUID唯一性**: tempSubscribeId必须全局唯一，建议使用crypto.randomUUID() **（新增）**

## 完整示例（已更新）

### HTML + JavaScript示例（已更新消息隔离）
```html
<!DOCTYPE html>
<html>
<head>
    <title>问答匿名WebSocket测试（消息隔离版本）</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
</head>
<body>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="输入消息">
    <button onclick="sendMessage()">发送</button>
    <button onclick="closeSession()">关闭会话</button>

    <script>
        let stompClient = null;
        let sessionId = null;
        let msgId = null;
        
        // ⚡ 生成临时订阅ID，确保消息隔离
        const tempSubscribeId = crypto.randomUUID();
        console.log('生成的临时订阅ID:', tempSubscribeId);

        function connect() {
            const socket = new SockJS('http://localhost:8081/ws');
            stompClient = Stomp.over(socket);

            stompClient.connect({}, function(frame) {
                console.log('连接成功:', frame);

                // ⚡ 订阅隔离后的初始化响应
                stompClient.subscribe('/topic/scws4qa/init/' + tempSubscribeId, function(response) {
                    const result = JSON.parse(response.body);
                    if (result.success && result.sessions.length > 0) {
                        sessionId = result.sessions[0].sessionId;
                        msgId = result.sessions[0].msgId;
                        
                        // 订阅聊天消息（未变更）
                        stompClient.subscribe('/topic/scws4qa/chat/' + sessionId, function(chatResponse) {
                            const chatResult = JSON.parse(chatResponse.body);
                            displayMessage('助手: ' + chatResult.resp.results[0]);
                        });

                        // 订阅关闭确认（未变更）
                        stompClient.subscribe('/topic/scws4qa/close/' + sessionId, function(closeResponse) {
                            const closeResult = JSON.parse(closeResponse.body);
                            displayMessage('系统: ' + closeResult.message);
                        });

                        displayMessage('✅ 会话已初始化，sessionId: ' + sessionId);
                        displayMessage('🔒 使用隔离订阅ID: ' + tempSubscribeId);
                    } else if (result.error) {
                        displayMessage('❌ 初始化失败: ' + result.message);
                    }
                });

                // 初始化会话
                initSession();
            });
        }

        function initSession() {
            const request = {
                receChatRoomId: 1,
                sceneName: "问答匿名",
                token: "your-jwt-token-here",
                isThinking: "true",
                isStreaming: "true",
                tempSubscribeId: tempSubscribeId,    // ⚡ 新增必填参数
                ownerUserName: "testuser",           // 匿名场景用户名
                ownerId: "1",                        // 匿名场景用户ID  
                ownerTeamId: "1",                    // 匿名场景团队ID
                anoRealName: "张三",                 // 匿名用户真实姓名
                anoRealNo: "TEST001"                 // 匿名用户编号
            };

            stompClient.send('/app/scws4qa/init', {}, JSON.stringify(request));
            displayMessage('🚀 正在初始化会话...');
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value;

            if (message && sessionId) {
                const request = {
                    sessionId: sessionId,
                    message: message,
                    msgId: msgId
                };

                stompClient.send('/app/scws4qa/send', {}, JSON.stringify(request));
                displayMessage('用户: ' + message);
                messageInput.value = '';
            }
        }

        function closeSession() {
            if (sessionId) {
                const request = {
                    sessionId: sessionId
                };

                stompClient.send('/app/scws4qa/close', {}, JSON.stringify(request));
            }
        }

        function displayMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const timestamp = new Date().toLocaleTimeString();
            messagesDiv.innerHTML += '<div>[' + timestamp + '] ' + message + '</div>';
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 页面加载时自动连接
        window.onload = connect;
    </script>
</body>
</html>
```

## 🆚 升级对比：修改前后

### 修改前（有串台问题）
```javascript
// 订阅全局topic（所有用户共享）
stompClient.subscribe('/topic/scws4qa/init', function(response) {
    // 会收到所有用户的初始化响应！❌
});

// 发送请求（无隔离参数）
const request = {
    receChatRoomId: 1,
    sceneName: "问答匿名",
    // 缺少tempSubscribeId参数
};
```

### 修改后（消息隔离）
```javascript
// 生成唯一标识符
const tempSubscribeId = crypto.randomUUID();

// 订阅隔离topic（用户专属）
stompClient.subscribe('/topic/scws4qa/init/' + tempSubscribeId, function(response) {
    // 只会收到自己的初始化响应！✅
});

// 发送请求（包含隔离参数）
const request = {
    receChatRoomId: 1,
    sceneName: "问答匿名",
    tempSubscribeId: tempSubscribeId,    // ⚡ 新增隔离参数
    // ... 其他参数
};
```

## 与知识库转换控制器的区别

| 特性 | ScWS4QaAnoController | SimuChatWS4ConvController |
|------|---------------------|---------------------------|
| 用途 | 问答匿名场景 | 知识库转换场景 |
| 端点前缀 | `/scws4qa/` | `/smc4conv/` |
| 服务层 | BmForQaService | BmForKbService |
| 主要场景 | 匿名问答处理 | 知识库内容转换 |
| 日志标识 | "问答匿名用" | "知识库转换用" |
| **消息隔离** | **✅ 已实施（2025-09-04）** | **需要同步更新** |

## 📅 更新历史

- **2025-09-04**: 🔒 实施WebSocket消息隔离机制，解决多用户消息串台问题
  - 新增`tempSubscribeId`必填参数
  - 修改订阅topic为`/topic/scws4qa/init/{tempSubscribeId}`
  - 更新前端示例代码
  - 添加错误处理和注意事项

这个控制器专门用于问答匿名场景，提供了完整的WebSocket接口支持，包括会话初始化、消息发送和会话关闭功能。**现在支持多用户安全访问，消息完全隔离！** 🚀

#### 响应示例
```json
{
  "success": true,
  "message": "模拟聊天会话初始化成功（问答匿名用）",
  "chatroomId": 1,
  "sessionCount": 1,
  "sessions": [
    {
      "sessionId": "qa-1756568412345-abc12345",
      "robotName": "小智",
      "firstMessage": "您好，我想咨询一下产品相关的问题。",
      "firstMessageWithoutJson": "您好，我想咨询一下产品相关的问题。",
      "systemPrompt": "你是一个专业的客服助手...",
      "teamName": "测试团队",
      "msgId": "uuid-12345",
      "isFirstFreq": true
    }
  ],
  "staffName": "张三",
  "taskInfo": {
    "taskId": 1,
    "taskName": "问答匿名测试任务",
    "taskPurposeTag": 0
  },
  "chatRoomInfo": {
    "chatroomId": 1,
    "entryFreqMin": 1,
    "entryFreqMax": 5,
    "receptionDuration": 30
  }
}
```

### 2. 发送消息
**WebSocket端点**: `/app/scws4qa/send`

#### 2.1 发送聊天消息
**订阅地址**: `/topic/scws4qa/send/{sessionId}`

#### 2.2 订阅聊天响应
**订阅地址**: `/topic/scws4qa/chat/{sessionId}`

#### 请求示例
```bash
# WebSocket STOMP消息内容：
{
  "sessionId": "qa-1756568412345-abc12345",
  "message": "我想了解一下这个产品的价格",
  "msgId": "uuid-12345"
}
```

#### 响应示例

##### 普通回复格式
```json
{
  "resp": {
    "results": ["根据您的咨询，这个产品的价格是..."],
    "msgId": "uuid-12345"
  }
}
```

##### 评判结果格式（包含考察点）
```json
{
  "resp": {
    "results": ["{\"分析\": \"匹配度分析描述\", \"得分\": 85, \"扣分点\": [\"扣分点1\", \"扣分点2\"], \"正确答案\": \"正确答案参考\", \"下一题\": \"丰富后的下一问题\", \"考察点\": \"退货政策\"}"],
    "msgId": "uuid-12345"
  }
}
```

### 3. 关闭会话
**WebSocket端点**: `/app/scws4qa/close`
**订阅响应**: `/topic/scws4qa/close/{sessionId}`

#### 请求示例
```bash
# WebSocket STOMP消息内容：
{
  "sessionId": "qa-1756568412345-abc12345"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "会话已关闭（问答匿名用）",
  "sessionId": "qa-1756568412345-abc12345"
}
```

## 使用流程

### 1. 建立WebSocket连接
```javascript
// 使用SockJS和STOMP建立连接
const socket = new SockJS('http://localhost:8081/ws');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function(frame) {
    console.log('连接成功:', frame);
    
    // 订阅响应
    stompClient.subscribe('/topic/scws4qa/init', function(response) {
        const result = JSON.parse(response.body);
        console.log('初始化响应:', result);
    });
});
```

### 2. 初始化会话
```javascript
const initRequest = {
    receChatRoomId: 1,
    sceneName: "问答匿名",
    token: "your-jwt-token-here",
    isThinking: "true",
    isStreaming: "true"
};

stompClient.send('/app/scws4qa/init', {}, JSON.stringify(initRequest));
```

### 3. 发送消息
```javascript
// 订阅聊天消息
stompClient.subscribe('/topic/scws4qa/chat/' + sessionId, function(response) {
    const result = JSON.parse(response.body);
    console.log('收到回复:', result);
});

// 发送消息
const messageRequest = {
    sessionId: sessionId,
    message: "我想了解产品信息",
    msgId: msgId  // 从初始化响应中获取
};

stompClient.send('/app/scws4qa/send', {}, JSON.stringify(messageRequest));
```

### 4. 关闭会话
```javascript
// 订阅关闭确认
stompClient.subscribe('/topic/scws4qa/close/' + sessionId, function(response) {
    const result = JSON.parse(response.body);
    console.log('会话关闭确认:', result);
});

// 发送关闭请求
const closeRequest = {
    sessionId: sessionId
};

stompClient.send('/app/scws4qa/close', {}, JSON.stringify(closeRequest));
```

## 认证要求

### JWT Token获取
```bash
# 首先获取JWT token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "staffId": 93,
    "username": "staff001",
    "displayName": "张三",
    "teamId": 1,
    "status": 1,
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6OTMsInN1YiI6InN0YWZmMDAxIiwiaWF0IjoxNzU2NTY4NDEyLCJleHAiOjE3NTY2NTQ4MTJ9.6mTuVhccNypE5-ls0q6dFQ4J1qPLyzgmDptOCdNEiU4"
  }
}
```

## 错误处理

### 常见错误响应
```json
{
  "error": true,
  "message": "错误描述信息"
}
```

### 错误类型
1. **认证失败**: JWT token无效或过期
2. **参数错误**: 必要参数缺失或格式错误
3. **权限不足**: 员工无权限访问指定聊天室
4. **会话不存在**: 指定的sessionId不存在或已过期
5. **系统错误**: 服务器内部错误

## 注意事项

1. **WebSocket协议**: 所有接口都是基于WebSocket的STOMP消息，不是HTTP请求
2. **认证要求**: 所有操作都需要有效的JWT token
3. **会话管理**: sessionId用于标识唯一会话，需要妥善保存
4. **消息格式**: 所有消息都是JSON格式
5. **订阅机制**: 需要先订阅相应的topic才能接收响应
6. **msgId字段**: 发送消息时必须包含msgId字段，从初始化响应中获取

## 完整示例

### HTML + JavaScript示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>问答匿名WebSocket测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
</head>
<body>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="输入消息">
    <button onclick="sendMessage()">发送</button>
    <button onclick="closeSession()">关闭会话</button>

    <script>
        let stompClient = null;
        let sessionId = null;
        let msgId = null;

        function connect() {
            const socket = new SockJS('http://localhost:8081/ws');
            stompClient = Stomp.over(socket);

            stompClient.connect({}, function(frame) {
                console.log('连接成功:', frame);

                // 订阅初始化响应
                stompClient.subscribe('/topic/scws4qa/init', function(response) {
                    const result = JSON.parse(response.body);
                    if (result.success && result.sessions.length > 0) {
                        sessionId = result.sessions[0].sessionId;
                        msgId = result.sessions[0].msgId;
                        
                        // 订阅聊天消息
                        stompClient.subscribe('/topic/scws4qa/chat/' + sessionId, function(chatResponse) {
                            const chatResult = JSON.parse(chatResponse.body);
                            displayMessage('助手: ' + chatResult.resp.results[0]);
                        });

                        // 订阅关闭确认
                        stompClient.subscribe('/topic/scws4qa/close/' + sessionId, function(closeResponse) {
                            const closeResult = JSON.parse(closeResponse.body);
                            displayMessage('系统: ' + closeResult.message);
                        });

                        displayMessage('会话已初始化，sessionId: ' + sessionId);
                    }
                });

                // 初始化会话
                initSession();
            });
        }

        function initSession() {
            const request = {
                receChatRoomId: 1,
                sceneName: "问答匿名",
                token: "your-jwt-token-here",
                isThinking: "true",
                isStreaming: "true"
            };

            stompClient.send('/app/scws4qa/init', {}, JSON.stringify(request));
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value;

            if (message && sessionId) {
                const request = {
                    sessionId: sessionId,
                    message: message,
                    msgId: msgId
                };

                stompClient.send('/app/scws4qa/send', {}, JSON.stringify(request));
                displayMessage('用户: ' + message);
                messageInput.value = '';
            }
        }

        function closeSession() {
            if (sessionId) {
                const request = {
                    sessionId: sessionId
                };

                stompClient.send('/app/scws4qa/close', {}, JSON.stringify(request));
            }
        }

        function displayMessage(message) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML += '<div>' + message + '</div>';
        }

        // 页面加载时自动连接
        window.onload = connect;
    </script>
</body>
</html>
```

## 与知识库转换控制器的区别

| 特性 | ScWS4QaAnoController | SimuChatWS4ConvController |
|------|---------------------|---------------------------|
| 用途 | 问答匿名场景 | 知识库转换场景 |
| 端点前缀 | `/scws4qa/` | `/smc4conv/` |
| 服务层 | BmForQaService | BmForKbService |
| 主要场景 | 匿名问答处理 | 知识库内容转换 |
| 日志标识 | "问答匿名用" | "知识库转换用" |

这个控制器专门用于问答匿名场景，提供了完整的WebSocket接口支持，包括会话初始化、消息发送和会话关闭功能。
