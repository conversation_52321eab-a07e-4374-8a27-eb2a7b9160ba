# SimuChatWS4ConvController 使用说明

## 概述
SimuChatWS4ConvController 是专门用于知识库转换的模拟聊天WebSocket控制器，基于STOMP协议提供WebSocket接口，用于创建和管理知识库转换场景下的模拟聊天会话。该控制器与普通模拟聊天控制器独立，专门服务于知识库相关的对话测试和验证场景。

## 核心特性
- **知识库专用**: 专门用于知识库转换测试，使用 `bmForKbService` 服务
- **双重逻辑**: 支持高频知识库逻辑（taskPurposeTag=0）和传统知识库模板逻辑
- **问答栈管理**: 新逻辑使用问答栈式机器人，支持问答数据的出栈和入栈操作
- **智能提示词**: 根据逻辑类型动态生成系统提示词
- **延时会话**: 支持根据进线频率配置自动创建延时会话
- **会话管理**: 完整的会话生命周期管理，包括创建、消息处理、资源清理
- **团队隔离**: 基于JWT token进行团队数据隔离
- **实时通信**: 基于WebSocket的实时双向通信

## STOMP客户端连接配置

### 1. WebSocket连接地址
```
ws://localhost:8081/ws
```

### 2. STOMP端点配置
- **初始化会话**: `/app/smc4conv/init`
- **发送消息**: `/app/smc4conv/send`
- **关闭会话**: `/app/smc4conv/close`
- **订阅初始化响应**: `/topic/smc4conv/init`
- **订阅聊天消息**: `/topic/smc4conv/chat/{sessionId}`
- **订阅关闭确认**: `/topic/smc4conv/close/{sessionId}`

## STOMP接口详细说明

### 1. 初始化会话接口

#### 1.1 发送消息到服务器
**目标地址**: `/app/smc4conv/init`

**请求参数**:
```json
{
  "receChatRoomId": 1,
  "sceneName": "知识库转换",
  "token": "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ",
  "isThinking": "true",
  "isStreaming": "true"
}
```

**参数说明**:
- `receChatRoomId` (Long, 必填): 接待聊天室ID
- `sceneName` (String, 可选): 场景名称，默认为"知识库转换"
- `token` (String, 必填): JWT认证令牌
- `isThinking` (String, 可选): 是否使用思考模型，"true"/"false"，默认"false"
- `isStreaming` (String, 可选): 是否使用流式响应，"true"/"false"，默认"false"

#### 1.2 订阅响应消息
**订阅地址**: `/topic/smc4conv/init`

**说明**: 该接口会返回两种类型的响应：
1. **立即响应**: 初始化成功后立即返回第一个会话
2. **延时响应**: 根据进线频率配置，延时创建其余会话

#### 1.3 立即响应结构（初始化成功）
```json
{
  "success": true,
  "message": "模拟聊天会话初始化成功（知识库转换用）",
  "chatroomId": 1,
  "sessionCount": 1,
  "staffName": "客服张晓",
  "taskInfo": {
    "id": 99,
    "taskId": 128,
    "taskName": "任务-128",
    "scriptId": 241,
    "scriptName": "小米手环0801演示",
    "intentName": "咨询产品",
    "parentIntentName": "售前",
    "trainingRecycleCnt": 1
  },
  "chatRoomInfo": {
    "chatroomId": 1,
    "entryFreqMin": 1,
    "entryFreqMax": 5,
    "receptionDuration": 30,
    "receptionSkin": "default_skin",
    "sceneMode": 0,
    "timerDisplay": false,
    "winChatMainId": 1001,
    "scriptList": [],
    "chatRoomTaskList": [],
    "chatRoomTaskList4Conv": []
  },
  "sessions": [
    {
      "sessionId": "ctx-20250821181017-abc123",
      "robotName": "智能助手",
      "serviceName": "客服张晓",
      "firstMessage": "您好！我是您的专属客服，很高兴为您服务。",
      "firstMessageWithoutJson": "您好！我是您的专属客服，很高兴为您服务。",
      "isFirstFreq": true,
      "msgId": "qa-uuid-123",
      "winchatDtlId_ctx-20250821181017-abc123": 100,
      "productList": [
        {
          "id": 1,
          "externalProductId": "product_001",
          "externalProductName": "小米手环8",
          "externalProductLink": "https://www.mi.com/miband8",
          "externalProductImage": "https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/product.jpg",
          "status": "上架中",
          "category": "智能穿戴",
          "platform": "小米商城",
          "shopName": "小米官方旗舰店"
        }
      ]
    }
  ]
}
```

#### 1.4 延时响应结构（延时会话创建）
```json
{
  "success": true,
  "message": "延时会话创建成功（知识库转换用）",
  "sessionCount": 1,
  "isDelayed": true,
  "taskInfo": {
    "id": 99,
    "taskId": 128,
    "taskName": "任务-128",
    "scriptId": 241,
    "scriptName": "小米手环0801演示",
    "intentName": "咨询产品",
    "parentIntentName": "售前",
    "trainingRecycleCnt": 1
  },
  "sessions": [
    {
      "sessionId": "ctx-20250821181245-def456",
      "robotName": "智能助手",
      "serviceName": "客服张晓",
      "firstMessage": "您好！我是您的专属客服，很高兴为您服务。",
      "firstMessageWithoutJson": "您好！我是您的专属客服，很高兴为您服务。",
      "isFirstFreq": true,
      "msgId": "qa-uuid-456",
      "winchatDtlId_ctx-20250821181245-def456": 101,
      "productList": [
        {
          "id": 1,
          "externalProductId": "product_001",
          "externalProductName": "小米手环8",
          "externalProductLink": "https://www.mi.com/miband8",
          "externalProductImage": "https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/product.jpg",
          "status": "上架中",
          "category": "智能穿戴",
          "platform": "小米商城",
          "shopName": "小米官方旗舰店"
        }
      ]
    }
  ]
}
```

#### 1.5 响应字段详细说明

**基础响应字段**：
- `success` (Boolean): 操作是否成功
- `message` (String): 操作结果消息
- `chatroomId` (Long): 聊天室ID（仅立即响应包含）
- `sessionCount` (Integer): 本次创建的会话数量
- `staffName` (String): 客服姓名（仅立即响应包含）
- `isDelayed` (Boolean): 是否为延时创建的会话（仅延时响应包含）
- `sessions` (Array): 会话列表

**taskInfo字段说明**:
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | Long | 聊天室任务关联ID | 99 |
| taskId | Long | 任务ID | 128 |
| taskName | String | 任务名称 | "任务-128" |
| scriptId | Long | 剧本ID | 241 |
| scriptName | String | 剧本名称 | "小米手环0801演示" |
| intentName | String | 意图名称 | "咨询产品" |
| parentIntentName | String | 父级意图名称 | "售前" |
| trainingRecycleCnt | Integer | 训练循环次数 | 1 |
| taskPurposeTag | String | 任务目的标签："0"=高频知识库，其他=传统模板 | "0" |
| qaMainId | Long | 问答主表ID（仅taskPurposeTag=0时有效） | 123 |

**chatRoomInfo字段说明**:
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| chatroomId | Long | 聊天室ID | 1 |
| entryFreqMin | Integer | 进线频率下限（分钟） | 1 |
| entryFreqMax | Integer | 进线频率上限（分钟） | 5 |
| receptionDuration | Integer | 接待时长（分钟） | 30 |
| receptionSkin | String | 接待皮肤：支持字符串值如"default_skin"、"qianniu_skin"等 | "default_skin" |
| sceneMode | Integer | 场景模式：0-萌新友好，1-压力考核，2-自定义 | 0 |
| timerDisplay | Boolean | 定时器显示：是否显示倒计时 | false |
| winChatMainId | Long | 模拟聊天室窗口主表ID | 1001 |
| scriptList | Array | 剧本列表 | [] |
| chatRoomTaskList | Array | 聊天室任务列表（兼容老版本） | [] |
| chatRoomTaskList4Conv | Array | 知识库转换专用任务列表 | [] |

**sessions数组中每个会话对象的字段说明**:
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| sessionId | String | 会话唯一标识符 | "ctx-20250821181017-abc123" |
| robotName | String | 机器人名称 | "智能助手" |
| serviceName | String | 客服名称 | "客服张晓" |
| firstMessage | String | 首条消息（可能包含JSON格式） | "您好！我是您的专属客服..." |
| firstMessageWithoutJson | String | 首条消息纯文本版本 | "您好！我是您的专属客服..." |
| isFirstFreq | Boolean | 是否为首次高频问答（新增字段） | true |
| msgId | String | 消息ID，来自问答数据UUID（新增字段） | "qa-uuid-123" |
| winchatDtlId_{sessionId} | Long | 聊天窗口明细记录ID | 100 |
| productList | Array | 关联的商品列表 | 见下方商品字段说明 |

**productList数组中每个商品对象的字段说明**:
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | Long | 商品ID | 1 |
| externalProductId | String | 外部商品ID | "product_001" |
| externalProductName | String | 商品名称 | "小米手环8" |
| externalProductLink | String | 商品链接 | "https://www.mi.com/miband8" |
| externalProductImage | String | 商品图片URL | "https://cdn.cnbj1.fds.api.mi-img.com/..." |
| status | String | 商品状态 | "上架中" |
| category | String | 商品分类 | "智能穿戴" |
| platform | String | 销售平台 | "小米商城" |
| shopName | String | 店铺名称 | "小米官方旗舰店" |

**延时会话机制说明**:
- **触发条件**: 当聊天室配置的总会话数大于1时，会创建延时会话
- **延时计算**: 在进线频率范围内随机生成延时时间（entryFreqMin到entryFreqMax分钟）
- **创建时机**: 根据任务的trainingRecycleCnt配置，为每个任务创建对应数量的会话
- **通知机制**: 延时会话创建成功后，会通过同一个订阅地址通知前端
- **识别方式**: 通过`isDelayed: true`字段识别延时创建的会话

## 双重逻辑机制详解

### 1. 逻辑判断机制
控制器根据任务的`taskPurposeTag`字段决定使用哪种逻辑：

#### 1.1 高频知识库逻辑（新逻辑）
- **触发条件**: `taskPurposeTag = "0"`
- **数据来源**: 使用`qaMainId`从问答导入明细表获取数据
- **处理方式**: 问答栈式机器人管理
- **系统提示词**: 基于问答数据动态生成

#### 1.2 传统知识库模板逻辑（旧逻辑）
- **触发条件**: `taskPurposeTag != "0"`
- **数据来源**: 使用知识库模板数据
- **处理方式**: 传统的假机器人服务
- **系统提示词**: 使用空字符串

### 2. 高频知识库逻辑流程

#### 2.1 初始化阶段
```
1. 检查 taskPurposeTag = "0"
2. 获取 qaMainId 对应的问答列表
3. 生成问答栈式机器人令牌
4. 出栈第一条问答数据
5. 基于问题生成系统提示词
```

#### 2.2 问答数据结构
```json
{
  "uuid": "qa-uuid-123",
  "question": "这个产品有什么特点？",
  "answer": "该产品具有以下特点：1. 高性能处理器..."
}
```

#### 2.3 系统提示词生成
- **模板来源**: `CONSTS.DEFAULT_CONVERT_FREQ_TITLE_KEYWORD`
- **变量替换**: 将模板中的`{{ques}}`替换为问答数据的问题
- **用途**: 指导大模型如何处理用户输入

### 3. 传统知识库模板逻辑流程

#### 3.1 初始化阶段
```
1. 检查 taskPurposeTag != "0"
2. 获取知识库模板数据
3. 生成传统假机器人令牌
4. 系统提示词设为空字符串
```

#### 3.2 数据来源
- **模板数据**: 从`train_task_conv_kb_dtl`表获取
- **处理方式**: 使用`FakeRobotService`管理
- **提示词**: 不使用动态生成的系统提示词

### 4. 逻辑对比表

| 特性 | 高频知识库逻辑 | 传统模板逻辑 |
|------|----------------|--------------|
| 触发条件 | taskPurposeTag = "0" | taskPurposeTag != "0" |
| 数据来源 | 问答导入明细表 | 知识库模板表 |
| 机器人类型 | 问答栈式机器人 | 传统假机器人 |
| 系统提示词 | 动态生成（基于问题） | 空字符串 |
| 数据管理 | 栈式出入栈操作 | 随机模板选择 |
| 适用场景 | 高频问答训练 | 通用知识库测试 |

### 5. 问答栈管理机制

#### 5.1 栈操作说明
- **入栈**: 初始化时将所有问答数据压入栈中
- **出栈**: 每次需要新问题时从栈顶取出数据
- **栈空处理**: 栈为空时返回默认提示信息

#### 5.2 令牌管理
- **生成**: `qa2StackService.generateFakeRobotToken(qaSimpDtoList)`
- **使用**: `qa2StackService.getNextBuyerMessageAsQa(fakeRobotToken)`
- **生命周期**: 与会话绑定，会话结束时自动清理

#### 5.3 数据格式
```java
// 问答简单DTO
QaSimpleDto {
    String question;    // 问题内容
    String answer;      // 答案内容
}

// 带UUID的问答DTO
QaSimplelDtoWithUUID {
    String uuid;        // 唯一标识
    String question;    // 问题内容
    String answer;      // 答案内容
}
```

### 6. 新增响应字段详解

#### 6.1 isFirstFreq字段
- **类型**: Boolean
- **含义**: 标识是否为首次高频问答
- **取值**: 固定为`true`（在初始化响应中）
- **用途**: 前端可以根据此字段判断会话的初始状态
- **添加时间**: @20250827 by germmy

#### 6.2 msgId字段
- **类型**: String
- **含义**: 消息ID，来自问答数据的UUID
- **取值**:
  - 高频知识库逻辑（taskPurposeTag=0）: 问答数据的UUID，如"qa-uuid-123"
  - 传统模板逻辑（taskPurposeTag≠0）: 空字符串或null
- **用途**:
  - 关联问答数据，便于追踪和调试
  - 在后续的消息响应中也会包含此字段
  - 可用于前端显示问答来源信息
- **添加时间**: @20250827 by germmy

#### 6.3 字段使用示例
```javascript
// 处理初始化响应中的新字段
function handleInitResponse(result) {
  if (result.success && result.sessions) {
    result.sessions.forEach(session => {
      console.log('会话ID:', session.sessionId);
      console.log('是否首次高频:', session.isFirstFreq);
      console.log('消息ID:', session.msgId);

      // 根据msgId判断逻辑类型
      const logicType = session.msgId ? '高频知识库' : '传统模板';
      console.log('逻辑类型:', logicType);

      // 存储msgId供后续使用
      sessionManager.setSessionMsgId(session.sessionId, session.msgId);
    });
  }
}
```

### 2. 发送消息接口

#### 2.1 发送消息到服务器
**目标地址**: `/app/smc4conv/send`

**请求参数**:
```json
{
  "sessionId": "session_uuid_12345",
  "message": "请帮我分析这个产品的知识库内容",
  "msgId": "qa-uuid-123"
}
```

**参数说明**:
- `sessionId` (String, 必填): 会话ID，从初始化响应中获取
- `message` (String, 必填): 用户发送的消息内容
- `msgId` (String, 必填): 消息ID，从初始化响应的session.msgId中获取

**msgId参数详解**:
- **新增时间**: @20250827 by germmy
- **用途**: 强制要求前端传递msgId，用于关联问答数据和逻辑处理
- **取值来源**: 从初始化响应中的`sessions[].msgId`字段获取
- **高频知识库逻辑**: msgId为问答数据的UUID，如"qa-uuid-123"
- **传统模板逻辑**: msgId为空字符串""，但仍需传递
- **重要性**: 该参数为必填项，缺失会导致请求失败

**msgId使用示例**:
```javascript
// 从会话信息中获取msgId
const session = chatManager.sessions.get(sessionId);
const msgId = session.msgId || '';

// 发送消息时必须包含msgId
const request = {
  sessionId: sessionId,
  message: userMessage,
  msgId: msgId  // 必填参数
};
```

#### 2.2 订阅聊天响应
**订阅地址**: `/topic/smc4conv/chat/{sessionId}`

**响应格式说明**:
当前实现中，无论 `isStreaming` 参数设置为何值，实际都返回非流式响应格式。

**标准响应结构**:
```json
{
  "resp": {
    "results": ["根据您的问题，我为您提供以下信息：该产品具有优秀的性能和良好的用户体验..."],
    "msgId": "qa-uuid-123"
  }
}
```

**评判结果响应结构**（当modelResponse包含评判结果时）:

**英文评判格式**（包含analysis、score等字段）:
```json
{
  "resp": {
    "results": ["评判结果"],
    "judgeResult": {
      "analysis": "部分匹配，提及棉成分但数据不准确",
      "score": 60,
      "deductionPoints": ["棉含量数据错误（80% vs 100%）"],
      "correctAnswer": "应准确说明'材质是100%纯棉'",
      "nextQuestion": "我想搭配不同穿搭，能详细介绍下现有的颜色款式吗？"
    },
    "msgId": "qa-uuid-123"
  }
}
```

**中文评判格式**（包含分析、得分等中文字段）:
```json
{
  "resp": {
    "results": ["{\"分析\":\"匹配度分析描述\",\"得分\":85,\"扣分点\":[\"扣分点1\",\"扣分点2\"],\"正确答案\":\"正确答案参考\",\"下一题\":\"丰富后的下一问题\"}", "2", "3"],
    "msgId": "123456"
  }
}
```

**响应格式特点**:
- 嵌套的JSON对象格式，包含`resp`根对象
- `resp.results`字段：字符串数组，包含大模型的回复内容或评判结果
- `resp.msgId`字段：消息ID，来自问答数据的UUID（高频知识库逻辑）或空字符串（传统逻辑）
- **双重评判格式支持**：
  - **英文格式**：使用`judgeResult`字段包含完整的评判信息
    - `analysis`：分析结果描述
    - `score`：评分（0-100）
    - `deductionPoints`：扣分点数组
    - `correctAnswer`：正确答案建议
    - `nextQuestion`：下一个问题建议
  - **中文格式**：将完整的中文评判JSON作为字符串放入`results`数组第一个元素
    - `分析`：匹配度分析描述
    - `得分`：评分数值
    - `扣分点`：扣分点数组
    - `正确答案`：正确答案参考
    - `下一题`：丰富后的下一问题
- **自动格式识别**：系统自动识别大模型返回的是英文字段（analysis、score）还是中文字段（分析、得分）
- 内容由 `bmForKbService` 服务生成，支持双重逻辑处理和智能评判结果格式识别
- 响应格式统一，无论使用哪种逻辑都返回相同的JSON结构

**重要说明**:
- 当前实现中，`isStreaming` 参数实际上不影响响应格式
- 所有响应都是非流式的，使用 `{"resp": {"results": ["内容"], "msgId": "uuid"}}` 格式
- 前端需要解析JSON并提取 `resp.results[0]` 来获取实际的消息内容
- `resp.msgId` 字段在高频知识库逻辑中包含问答UUID，在传统逻辑中为空字符串
- 响应格式已统一，支持双重逻辑机制

**前端处理示例**:
```javascript
// 正确的消息处理方式（包含中英文评判结果处理）
function handleMessage(sessionId, responseString) {
  try {
    const data = JSON.parse(responseString);
    if (data.resp && data.resp.results && Array.isArray(data.resp.results)) {
      const actualContent = data.resp.results[0];
      const msgId = data.resp.msgId || '';
      
      // 检查是否包含英文评判结果
      if (data.resp.judgeResult) {
        const judgeResult = data.resp.judgeResult;
        console.log('收到英文评判结果:', {
          analysis: judgeResult.analysis,
          score: judgeResult.score,
          deductionPoints: judgeResult.deductionPoints,
          correctAnswer: judgeResult.correctAnswer,
          nextQuestion: judgeResult.nextQuestion
        });
        
        // 显示英文评判结果
        displayEnglishJudgeResult(sessionId, judgeResult, msgId);
      } else {
        // 检查是否为中文评判格式（results[0]是JSON字符串）
        try {
          const chineseJudgeResult = JSON.parse(actualContent);
          if (chineseJudgeResult.分析 && chineseJudgeResult.得分 !== undefined) {
            console.log('收到中文评判结果:', {
              分析: chineseJudgeResult.分析,
              得分: chineseJudgeResult.得分,
              扣分点: chineseJudgeResult.扣分点,
              正确答案: chineseJudgeResult.正确答案,
              下一题: chineseJudgeResult.下一题
            });
            
            // 显示中文评判结果
            displayChineseJudgeResult(sessionId, chineseJudgeResult, msgId);
          } else {
            // 普通消息
            console.log('收到普通消息:', actualContent);
            displayMessage(sessionId, actualContent, msgId);
          }
        } catch (parseError) {
          // 不是JSON格式，作为普通消息处理
          console.log('收到普通消息:', actualContent);
          displayMessage(sessionId, actualContent, msgId);
        }
      }
    }
  } catch (error) {
    console.error('解析消息失败:', error);
  }
}

// 显示英文评判结果的函数
function displayEnglishJudgeResult(sessionId, judgeResult, msgId) {
  const resultHtml = `
    <div class="judge-result english-format" data-session="${sessionId}" data-msg-id="${msgId}">
      <div class="judge-header">
        <h4>评判结果 (English Format)</h4>
        <span class="score ${getScoreClass(judgeResult.score)}">${judgeResult.score}分</span>
      </div>
      <div class="analysis">
        <strong>分析：</strong>${judgeResult.analysis}
      </div>
      ${judgeResult.deductionPoints && judgeResult.deductionPoints.length > 0 ? `
      <div class="deduction-points">
        <strong>扣分点：</strong>
        <ul>
          ${judgeResult.deductionPoints.map(point => `<li>${point}</li>`).join('')}
        </ul>
      </div>
      ` : ''}
      ${judgeResult.correctAnswer ? `
      <div class="correct-answer">
        <strong>建议答案：</strong>${judgeResult.correctAnswer}
      </div>
      ` : ''}
      ${judgeResult.nextQuestion ? `
      <div class="next-question">
        <strong>下一个问题：</strong>${judgeResult.nextQuestion}
      </div>
      ` : ''}
    </div>
  `;
  
  // 将HTML添加到消息容器中
  document.getElementById(`messages-${sessionId}`).innerHTML += resultHtml;
}

// 显示中文评判结果的函数
function displayChineseJudgeResult(sessionId, judgeResult, msgId) {
  const resultHtml = `
    <div class="judge-result chinese-format" data-session="${sessionId}" data-msg-id="${msgId}">
      <div class="judge-header">
        <h4>评判结果 (中文格式)</h4>
        <span class="score ${getScoreClass(judgeResult.得分)}">${judgeResult.得分}分</span>
      </div>
      <div class="analysis">
        <strong>分析：</strong>${judgeResult.分析}
      </div>
      ${judgeResult.扣分点 && judgeResult.扣分点.length > 0 ? `
      <div class="deduction-points">
        <strong>扣分点：</strong>
        <ul>
          ${judgeResult.扣分点.map(point => `<li>${point}</li>`).join('')}
        </ul>
      </div>
      ` : ''}
      ${judgeResult.正确答案 ? `
      <div class="correct-answer">
        <strong>正确答案：</strong>${judgeResult.正确答案}
      </div>
      ` : ''}
      ${judgeResult.下一题 ? `
      <div class="next-question">
        <strong>下一题：</strong>${judgeResult.下一题}
      </div>
      ` : ''}
    </div>
  `;
  
  // 将HTML添加到消息容器中
  document.getElementById(`messages-${sessionId}`).innerHTML += resultHtml;
}

// 显示评判结果的函数（兼容旧版本）
function displayJudgeResult(sessionId, judgeResult, msgId) {
  // 自动检测是英文还是中文格式，优先使用对应的专门函数
  if (judgeResult.analysis !== undefined) {
    displayEnglishJudgeResult(sessionId, judgeResult, msgId);
  } else if (judgeResult.分析 !== undefined) {
    displayChineseJudgeResult(sessionId, judgeResult, msgId);
  } else {
    // 兼容处理，使用英文格式
    displayEnglishJudgeResult(sessionId, judgeResult, msgId);
  }
}

// 根据分数返回CSS样式类
function getScoreClass(score) {
  if (score >= 80) return 'score-excellent';
  if (score >= 60) return 'score-good';
  return 'score-poor';
}
```

**双重逻辑响应差异**:
```javascript
// 高频知识库逻辑响应（taskPurposeTag = "0"）
// 标准回复格式
{
  "resp": {
    "results": ["基于问答数据生成的回复内容"],
    "msgId": "qa-uuid-123"  // 包含问答数据的UUID
  }
}

// 高频知识库逻辑 - 包含评判结果的回复
{
  "resp": {
    "results": ["评判结果"],
    "judgeResult": {
      "analysis": "回答完全正确，准确描述了产品特性",
      "score": 85,
      "deductionPoints": [],
      "correctAnswer": "保持当前回答方式",
      "nextQuestion": "客户可能会询问价格和优惠信息"
    },
    "msgId": "qa-uuid-123"
  }
}

// 传统模板逻辑响应（taskPurposeTag != "0"）
{
  "resp": {
    "results": ["基于知识库模板生成的回复内容"],
    "msgId": ""  // 空字符串
  }
}
```

#### 2.3 错误响应结构
```json
{
  "error": true,
  "message": "会话不存在: session_uuid_12345"
}
```

**常见错误类型**:
- `"参数不完整"`: sessionId、message 或 msgId 参数缺失
- `"会话不存在: {sessionId}"`: 指定的会话ID不存在或已过期
- `"发送消息失败"`: 服务器内部处理错误

**参数验证说明**:
- 当前实现中，虽然代码只检查`sessionId`和`message`是否为null，但`msgId`参数是必需的
- 建议前端在发送前验证所有三个参数都不为空
- `msgId`参数即使为空字符串也必须传递

### 3. 关闭会话接口

#### 3.1 发送关闭请求到服务器
**目标地址**: `/app/smc4conv/close`

**请求参数**:
```json
{
  "sessionId": "session_uuid_12345"
}
```

**参数说明**:
- `sessionId` (String, 必填): 要关闭的会话ID

#### 3.2 订阅关闭确认响应
**订阅地址**: `/topic/smc4conv/close/{sessionId}`

**成功响应结构**:
```json
{
  "success": true,
  "message": "会话已关闭（知识库转换用）",
  "sessionId": "ctx-20250821181017-abc123"
}
```

**失败响应结构**:
```json
{
  "success": false,
  "message": "关闭会话失败: 具体错误信息",
  "sessionId": "ctx-20250821181017-abc123"
}
```

**功能说明**:
- **资源清理**: 自动清除Redis中的会话数据，包括：
  - 聊天记录：`chatlog:{sessionId}`
  - 会话配置：`session:{sessionId}`
- **安全关闭**: 确保会话资源完全释放，避免内存泄漏
- **状态同步**: 向客户端确认会话已成功关闭

#### 3.3 Redis资源清理详情
关闭会话时，服务器会自动清理以下Redis键：

1. **聊天记录键**: `chatlog:{sessionId}`
   - 存储类型：List
   - 内容：完整的聊天消息历史
   - 清理方式：完全删除

2. **会话配置键**: `session:{sessionId}`
   - 存储类型：Hash
   - 内容：会话配置信息（系统提示词、机器人名称、模型配置等）
   - 清理方式：完全删除

3. **上下文缓存**: 豆包模型的上下文缓存也会被清理

## STOMP客户端使用示例

### 1. JavaScript/TypeScript 示例

#### 1.1 基础连接和订阅
```javascript
import SockJS from 'sockjs-client';
import Stomp from 'stompjs';

// 建立WebSocket连接
const socket = new SockJS('http://localhost:8081/ws');
const stompClient = Stomp.over(socket);

// 连接成功后的处理
stompClient.connect({}, (frame) => {
  console.log('知识库转换WebSocket连接成功:', frame);

  // 订阅初始化响应（包括立即响应和延时响应）
  stompClient.subscribe('/topic/smc4conv/init', (response) => {
    const result = JSON.parse(response.body);
    handleInitResponse(result);
  });

  // 发送初始化请求
  initializeChat();
}, (error) => {
  console.error('WebSocket连接失败:', error);
});

// 初始化聊天会话
function initializeChat() {
  const request = {
    receChatRoomId: 1,
    sceneName: "知识库转换",
    token: "your-jwt-token-here",
    isThinking: "true",
    isStreaming: "true"
  };

  stompClient.send('/app/smc4conv/init', {}, JSON.stringify(request));
}

// 处理初始化响应（包括延时会话）
function handleInitResponse(result) {
  if (result.success) {
    console.log('任务信息:', result.taskInfo);
    console.log(`客服发送消息倒计时: ${result.taskInfo.srvSendCd}秒`);

    if (result.isDelayed) {
      // 处理延时创建的会话
      console.log(`延时会话创建成功，新增${result.sessionCount}个会话`);
      result.sessions.forEach(session => {
        processSessionInfo(session);
        addSession(session);
        subscribeToSession(session.sessionId);
      });
    } else {
      // 处理初始化会话
      console.log(`会话初始化成功，当前客服：${result.staffName}`);
      result.sessions.forEach(session => {
        processSessionInfo(session);
        addSession(session);
        subscribeToSession(session.sessionId);
      });
    }
  } else {
    console.error('会话初始化失败:', result.message);
  }
}

// 处理会话信息，包括新增的字段
function processSessionInfo(session) {
  console.log('=== 会话详细信息 ===');
  console.log('会话ID:', session.sessionId);
  console.log('机器人名称:', session.robotName);
  console.log('客服名称:', session.serviceName);
  console.log('是否首次高频:', session.isFirstFreq);
  console.log('消息ID:', session.msgId);

  // 根据msgId判断逻辑类型
  const logicType = session.msgId ? '高频知识库' : '传统模板';
  console.log('逻辑类型:', logicType);

  // 存储会话的msgId供后续使用
  if (session.msgId) {
    sessionManager.setSessionMsgId(session.sessionId, session.msgId);
  }

  // 如果是高频知识库逻辑，可以进行特殊处理
  if (session.msgId && session.isFirstFreq) {
    console.log('这是高频知识库的首次问答会话');
    // 可以在UI上显示特殊标识
    markSessionAsHighFreq(session.sessionId);
  }
}

// 订阅单个会话的消息
function subscribeToSession(sessionId) {
  // 订阅聊天消息
  stompClient.subscribe(`/topic/smc4conv/chat/${sessionId}`, (message) => {
    const data = JSON.parse(message.body);
    handleMessage(sessionId, data);
  });

  // 订阅会话关闭确认
  stompClient.subscribe(`/topic/smc4conv/close/${sessionId}`, (message) => {
    const data = JSON.parse(message.body);
    handleCloseResponse(sessionId, data);
  });
}
```

#### 1.2 发送消息示例
```javascript
// 发送消息到指定会话
function sendMessage(sessionId, message) {
  // 获取会话的msgId
  const msgId = chatManager.getSessionMsgId(sessionId) || '';

  const request = {
    sessionId: sessionId,
    message: message,
    msgId: msgId  // 必填参数，从会话信息中获取
  };

  console.log('发送消息请求:', request);
  stompClient.send('/app/smc4conv/send', {}, JSON.stringify(request));
}

// 安全的发送消息方法，包含参数验证
function sendMessageSafely(sessionId, message) {
  // 参数验证
  if (!sessionId || !message) {
    console.error('发送消息失败：sessionId和message不能为空');
    return false;
  }

  // 获取会话信息
  const session = chatManager.sessions.get(sessionId);
  if (!session) {
    console.error('发送消息失败：会话不存在', sessionId);
    return false;
  }

  // 获取msgId（即使为空字符串也要传递）
  const msgId = session.msgId || '';

  const request = {
    sessionId: sessionId,
    message: message,
    msgId: msgId
  };

  try {
    console.log('发送消息:', {
      sessionId: sessionId.substring(0, 8) + '...',
      message: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
      msgId: msgId ? msgId.substring(0, 8) + '...' : '(空)',
      logicType: msgId ? '高频知识库' : '传统模板'
    });

    stompClient.send('/app/smc4conv/send', {}, JSON.stringify(request));
    return true;
  } catch (error) {
    console.error('发送消息失败:', error);
    return false;
  }
}

// 处理接收到的消息
function handleMessage(sessionId, data) {
  if (data.error) {
    console.error(`会话${sessionId}错误:`, data.message);
    return;
  }

  // 处理实际的响应格式 {"resp": {"results": ["消息内容"], "msgId": "uuid"}}
  if (data.resp && data.resp.results && Array.isArray(data.resp.results)) {
    const content = data.resp.results[0]; // 获取数组中的第一个元素
    const msgId = data.resp.msgId || ''; // 获取消息ID
    console.log(`会话${sessionId}收到消息:`, content);
    console.log(`消息ID:`, msgId);

    // 根据msgId判断逻辑类型
    const logicType = msgId ? '高频知识库' : '传统模板';
    console.log(`逻辑类型: ${logicType}`);

    // 在界面上显示消息
    displayMessage(sessionId, content, 'robot', msgId);
  } else {
    console.warn(`会话${sessionId}收到未知格式消息:`, data);
  }
}
```

#### 1.3 关闭会话示例
```javascript
// 关闭指定会话
function closeSession(sessionId) {
  const request = {
    sessionId: sessionId
  };

  stompClient.send('/app/smc4conv/close', {}, JSON.stringify(request));
}

// 处理关闭会话响应
function handleCloseResponse(sessionId, data) {
  if (data.success) {
    console.log(`会话 ${sessionId} 已成功关闭:`, data.message);
    // 清理本地会话数据
    removeSession(sessionId);
  } else {
    console.error(`关闭会话 ${sessionId} 失败:`, data.message);
  }
}
```

## 使用注意事项

### 1. 独特功能特点
- **端点路径**: 使用 `smc4conv` 前缀，专门用于知识库转换功能
- **业务用途**: 专门用于知识库转换测试，支持专业的知识库管理场景
- **服务层**: 使用 `bmForKbService` 实现知识库相关的大模型服务
- **双重逻辑**: 支持高频知识库逻辑和传统模板逻辑两种处理方式
- **问答栈管理**: 新增问答栈式机器人，支持问答数据的栈式管理
- **智能提示词**: 根据逻辑类型动态生成系统提示词
- **数据存储**: 会话数据存储在知识库相关的数据表中（train_conv_winchat_main、train_conv_winchat_dtl、train_conv_winchat_log）
- **延时机制**: 支持延时会话创建，模拟真实的进线频率
- **响应格式**: 消息响应使用统一的 `{"resp": {"results": ["内容"], "msgId": "uuid"}}` 格式

### 2. 认证和权限
- **JWT认证**: 所有操作都需要有效的JWT token
- **团队隔离**: 基于teamId进行数据隔离
- **会话安全**: sessionId使用UUID，防止猜测和劫持

### 3. 参数要求

#### 3.1 初始化参数
- **必填参数**: `receChatRoomId`, `token`
- **可选参数**: `sceneName`, `isThinking`, `isStreaming`
- **依赖数据**: 聊天室任务配置、脚本详情、知识库数据
- **返回数据**: 包含完整的任务信息（taskInfo）和聊天室配置（chatRoomInfo）
- **会话标识**: sessionId 使用上下文ID格式，如 "ctx-20250821181017-abc123"

#### 3.2 发送消息参数（重要更新）
- **必填参数**: `sessionId`, `message`, `msgId`
- **msgId要求**:
  - 必须从初始化响应的`sessions[].msgId`中获取
  - 高频知识库逻辑：包含问答UUID
  - 传统模板逻辑：空字符串，但仍需传递
  - **新增时间**: @20250827 by germmy
- **参数验证**: 前端应确保所有三个参数都不为null
- **错误处理**: 缺失任何参数都可能导致请求失败

### 4. 延时会话处理
- **监听机制**: 需要持续监听`/topic/smc4conv/init`以接收延时会话
- **识别方式**: 通过`isDelayed: true`字段识别延时创建的会话
- **动态添加**: 延时会话创建后需要动态添加到会话列表并订阅消息
- **任务信息**: 每个延时会话都包含完整的任务信息

### 5. 连接管理
- **连接地址**: 确保WebSocket连接地址正确
- **重连机制**: 实现网络异常时的自动重连
- **资源清理**: 页面卸载时断开WebSocket连接并关闭所有活动会话

### 6. 消息处理
- **请求格式**: 发送消息必须包含`sessionId`、`message`、`msgId`三个参数
- **msgId管理**:
  - 从初始化响应中获取并存储每个会话的msgId
  - 发送消息时必须携带对应会话的msgId
  - 即使msgId为空字符串也必须传递
- **响应格式**: 所有消息响应都是 `{"result": ["内容"]}` 格式，需要提取 result[0] 获取实际内容
- **错误处理**: 监听和处理 `{"error": true, "message": "错误信息"}` 格式的错误消息
- **状态管理**: 维护会话状态和消息历史，包括msgId的管理
- **内容解析**: 正确解析JSON响应并提取消息内容
- **参数验证**: 发送前验证所有必需参数，避免"参数不完整"错误

### 7. 会话生命周期管理
- **主动关闭**: 用户可以主动关闭不需要的会话
- **自动清理**: 页面卸载时自动关闭所有会话
- **资源释放**: 关闭会话时确保Redis缓存被正确清理
- **状态同步**: 关闭会话后及时更新前端状态

## 常见问题和解决方案

### 1. 连接问题
**问题**: WebSocket连接失败
**解决方案**:
- 检查服务器地址和端口是否正确（默认8081）
- 确认服务器WebSocket配置是否启用
- 检查网络防火墙设置

### 2. 认证问题
**问题**: JWT token无效或过期
**解决方案**:
- 使用有效的JWT token
- 实现token刷新机制
- 处理token过期的重新登录逻辑

### 3. 延时会话问题
**问题**: 延时会话未正确接收
**解决方案**:
- 确保持续监听`/topic/smc4conv/init`订阅
- 检查`isDelayed`字段的处理逻辑
- 验证延时会话的动态添加机制
- 确认进线频率配置是否正确

**问题**: 延时会话创建时间不准确
**解决方案**:
- 检查聊天室的`entryFreqMin`和`entryFreqMax`配置
- 验证任务的`trainingRecycleCnt`设置
- 确认服务器时间配置正确

### 4. 消息处理问题
**问题**: "参数不完整"错误
**解决方案**:
- 确保发送消息时包含`sessionId`、`message`、`msgId`三个参数
- 检查msgId是否从初始化响应中正确获取和存储
- 即使msgId为空字符串也必须传递该参数
- 使用前端参数验证避免遗漏

**问题**: msgId获取和管理错误
**解决方案**:
- 在初始化响应处理中正确存储每个会话的msgId
- 使用状态管理器维护sessionId到msgId的映射关系
- 发送消息前验证msgId是否存在
- 实现msgId的生命周期管理（创建、使用、清理）

**问题**: 消息格式解析错误
**解决方案**:
- 正确解析 `{"resp": {"results": ["内容"], "msgId": "uuid"}}` 格式的响应
- 提取 `resp.results` 数组的第一个元素作为实际消息内容
- 处理可能的JSON解析异常

**问题**: 消息内容显示异常
**解决方案**:
- 检查是否正确提取了 `resp.results[0]` 的内容
- 验证消息内容的编码格式
- 确保前端正确显示长文本内容
- 检查响应中的msgId字段是否正确处理

### 5. 知识库相关问题
**问题**: 知识库转换响应异常
**解决方案**:
- 确保聊天室配置了正确的知识库数据
- 检查 `bmForKbService` 服务是否正常运行
- 验证知识库模板和提示词配置

### 6. 任务信息问题
**问题**: taskInfo字段缺失或不完整
**解决方案**:
- 确保聊天室关联了有效的任务配置
- 检查任务的srvSendCd字段是否正确设置
- 验证任务与脚本的关联关系

### 7. 会话关闭问题
**问题**: 会话关闭失败或资源未清理
**解决方案**:
- 确保WebSocket连接正常
- 检查sessionId是否有效
- 验证服务器端Redis连接状态
- 实现关闭失败的重试机制

**问题**: 页面刷新后会话丢失
**解决方案**:
- 实现会话状态持久化（localStorage）
- 页面加载时恢复会话状态
- 提供重新连接机制

**问题**: 多个会话同时关闭时出现异常
**解决方案**:
- 实现会话关闭队列，避免并发关闭
- 添加关闭状态标记，防止重复关闭
- 使用Promise.allSettled处理批量关闭

## 开发建议

### 1. 状态管理
建议使用合适的状态管理方案来管理复杂的会话状态：

```javascript
// 基础状态管理示例
class ConvChatManager {
  constructor() {
    this.sessions = new Map();
    this.activeSessionId = null;
    this.connectionStatus = 'disconnected';
    this.isInitializing = false;
    this.taskInfo = null; // 存储任务信息，包含srvSendCd等字段
    this.delayedSessions = new Set(); // 跟踪延时会话
    this.sessionMsgIds = new Map(); // 存储会话的msgId
    this.highFreqSessions = new Set(); // 跟踪高频知识库会话
  }

  setSessions(sessions) {
    sessions.forEach(session => {
      this.sessions.set(session.sessionId, session);
      // 处理新增字段
      this.processNewSessionFields(session);
    });
  }

  addDelayedSession(session) {
    this.sessions.set(session.sessionId, session);
    this.delayedSessions.add(session.sessionId);
    // 处理新增字段
    this.processNewSessionFields(session);
    console.log(`延时会话已添加: ${session.sessionId}`);
  }

  // 处理会话的新增字段
  processNewSessionFields(session) {
    // 存储msgId
    if (session.msgId) {
      this.sessionMsgIds.set(session.sessionId, session.msgId);
    }

    // 标记高频知识库会话
    if (session.msgId && session.isFirstFreq) {
      this.highFreqSessions.add(session.sessionId);
      console.log(`高频知识库会话: ${session.sessionId}, msgId: ${session.msgId}`);
    }
  }

  // 获取会话的msgId
  getSessionMsgId(sessionId) {
    return this.sessionMsgIds.get(sessionId) || '';
  }

  // 设置会话的msgId
  setSessionMsgId(sessionId, msgId) {
    if (msgId) {
      this.sessionMsgIds.set(sessionId, msgId);
    }
  }

  // 检查是否为高频知识库会话
  isHighFreqSession(sessionId) {
    return this.highFreqSessions.has(sessionId);
  }

  addMessage(sessionId, message) {
    const session = this.sessions.get(sessionId);
    if (session) {
      if (!session.messages) session.messages = [];
      session.messages.push(message);
    }
  }

  setConnectionStatus(status) {
    this.connectionStatus = status;
  }

  setTaskInfo(taskInfo) {
    this.taskInfo = taskInfo;
    console.log('任务信息已更新:', taskInfo);
    if (taskInfo.trainingRecycleCnt) {
      console.log(`训练循环次数: ${taskInfo.trainingRecycleCnt}`);
    }
  }

  // 关闭会话的状态管理
  closeSession(sessionId) {
    this.sessions.delete(sessionId);
    this.delayedSessions.delete(sessionId);
    this.sessionMsgIds.delete(sessionId); // 清理msgId
    this.highFreqSessions.delete(sessionId); // 清理高频会话标记

    if (this.activeSessionId === sessionId) {
      const remainingSessions = Array.from(this.sessions.keys());
      this.activeSessionId = remainingSessions.length > 0 ? remainingSessions[0] : null;
    }

    console.log(`会话 ${sessionId} 的所有状态已清理`);
  }

  // 检查是否为延时会话
  isDelayedSession(sessionId) {
    return this.delayedSessions.has(sessionId);
  }
}
```

### 2. 延时会话处理最佳实践
```javascript
// 延时会话处理管理器
class DelayedSessionManager {
  constructor(stompClient, chatManager) {
    this.stompClient = stompClient;
    this.chatManager = chatManager;
    this.pendingDelayedSessions = new Set();
  }

  // 处理初始化响应（包括延时会话）
  handleInitResponse(result) {
    if (!result.success) {
      console.error('会话初始化失败:', result.message);
      return;
    }

    // 存储任务信息
    if (result.taskInfo) {
      this.chatManager.setTaskInfo(result.taskInfo);
    }

    if (result.isDelayed) {
      // 处理延时创建的会话
      this.handleDelayedSession(result);
    } else {
      // 处理初始化会话
      this.handleInitialSession(result);
    }
  }

  handleInitialSession(result) {
    console.log(`会话初始化成功，当前客服：${result.staffName}`);
    console.log(`预期总会话数：${result.sessionCount}`);

    result.sessions.forEach(session => {
      this.chatManager.setSessions([session]);
      this.subscribeToSession(session.sessionId);
    });
  }

  handleDelayedSession(result) {
    console.log(`延时会话创建成功，新增${result.sessionCount}个会话`);
    console.log('延时会话任务信息:', result.taskInfo);

    result.sessions.forEach(session => {
      this.chatManager.addDelayedSession(session);
      this.subscribeToSession(session.sessionId);
      this.pendingDelayedSessions.delete(session.sessionId);
    });
  }

  subscribeToSession(sessionId) {
    // 订阅聊天消息
    this.stompClient.subscribe(`/topic/smc4conv/chat/${sessionId}`, (message) => {
      const data = JSON.parse(message.body);
      this.handleMessage(sessionId, data);
    });

    // 订阅会话关闭确认
    this.stompClient.subscribe(`/topic/smc4conv/close/${sessionId}`, (message) => {
      const data = JSON.parse(message.body);
      this.handleCloseResponse(sessionId, data);
    });
  }

  handleMessage(sessionId, data) {
    if (data.error) {
      console.error(`会话${sessionId}错误:`, data.message);
      return;
    }

    // 处理实际的响应格式 {"result": ["消息内容"]}
    if (data.result && Array.isArray(data.result)) {
      const content = data.result[0]; // 获取数组中的第一个元素
      this.chatManager.addMessage(sessionId, {
        content: content,
        timestamp: new Date().toISOString(),
        sender: 'robot'
      });
      console.log(`会话${sessionId}收到消息:`, content);
    } else {
      console.warn(`会话${sessionId}收到未知格式消息:`, data);
    }
  }

  handleCloseResponse(sessionId, data) {
    if (data.success) {
      console.log(`会话 ${sessionId} 已成功关闭`);
      this.chatManager.closeSession(sessionId);
    } else {
      console.error(`关闭会话 ${sessionId} 失败:`, data.message);
    }
  }
}
```

### 3. 会话关闭最佳实践
```javascript
// 安全的会话关闭实现
const closeSessionSafely = async (sessionId) => {
  try {
    // 1. 检查会话是否存在
    if (!chatManager.sessions.has(sessionId)) {
      console.warn(`会话 ${sessionId} 不存在，无需关闭`);
      return;
    }

    // 2. 标记会话为关闭中状态
    const session = chatManager.sessions.get(sessionId);
    if (session) {
      session.isClosing = true;
    }

    // 3. 发送关闭请求
    const request = { sessionId };
    stompClient.send('/app/smc4conv/close', {}, JSON.stringify(request));

    // 4. 设置超时处理
    const timeoutId = setTimeout(() => {
      console.warn(`会话 ${sessionId} 关闭超时，强制清理本地状态`);
      forceCloseSession(sessionId);
    }, 5000);

    // 5. 等待服务器确认
    return new Promise((resolve) => {
      const unsubscribe = stompClient.subscribe(`/topic/smc4conv/close/${sessionId}`, (response) => {
        clearTimeout(timeoutId);
        unsubscribe.unsubscribe();
        resolve(JSON.parse(response.body));
      });
    });
  } catch (error) {
    console.error('关闭会话失败:', error);
    // 发生错误时也要清理本地状态
    forceCloseSession(sessionId);
  }
};

// 强制关闭会话（本地清理）
const forceCloseSession = (sessionId) => {
  chatManager.closeSession(sessionId);
  console.log(`会话 ${sessionId} 已强制关闭`);
};

// 批量关闭会话
const closeAllSessions = async () => {
  const sessionIds = Array.from(chatManager.sessions.keys());
  const closePromises = sessionIds.map(sessionId =>
    closeSessionSafely(sessionId)
  );

  try {
    await Promise.allSettled(closePromises);
    console.log('所有会话已关闭');
  } catch (error) {
    console.error('批量关闭会话时发生错误:', error);
  }
};
```

### 4. 错误处理增强
```javascript
const handleError = (error, context) => {
  console.error(`知识库转换聊天错误 [${context}]:`, error);

  // 根据错误类型进行不同处理
  if (error.message.includes('token')) {
    // 处理认证错误
    console.log('JWT token无效，需要重新登录');
    // 可以触发重新登录逻辑
  } else if (error.message.includes('connection')) {
    // 处理连接错误
    console.log('连接错误，5秒后重试');
    setTimeout(() => connect(), 5000);
  } else if (error.message.includes('session')) {
    // 处理会话相关错误
    console.log('会话错误，清理无效会话');
    // 清理无效会话
  } else {
    // 显示通用错误消息
    console.log('操作失败，请稍后重试');
  }
};

// 延时会话错误处理
const handleDelayedSessionError = (error, taskId) => {
  console.error(`延时会话创建失败 [taskId: ${taskId}]:`, error);

  // 可以实现重试机制
  const retryCount = 3;
  let currentRetry = 0;

  const retryCreateSession = () => {
    if (currentRetry < retryCount) {
      currentRetry++;
      console.log(`重试创建延时会话，第${currentRetry}次尝试`);
      // 重试逻辑
    } else {
      console.error(`延时会话创建失败，已达到最大重试次数`);
    }
  };

  setTimeout(retryCreateSession, 2000);
};
```

### 5. 性能优化
```javascript
// 消息防抖发送
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const debouncedSendMessage = debounce(sendMessage, 300);

// 会话消息缓存管理
class MessageCache {
  constructor(maxSize = 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  addMessage(sessionId, message) {
    if (!this.cache.has(sessionId)) {
      this.cache.set(sessionId, []);
    }

    const messages = this.cache.get(sessionId);
    messages.push(message);

    // 限制消息数量，避免内存溢出
    if (messages.length > this.maxSize) {
      messages.splice(0, messages.length - this.maxSize);
    }
  }

  getMessages(sessionId) {
    return this.cache.get(sessionId) || [];
  }

  clearSession(sessionId) {
    this.cache.delete(sessionId);
  }
}
```

## 快速开始示例

### 1. 基础HTML页面示例
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库转换聊天测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
</head>
<body>
    <div id="app">
        <h1>知识库转换聊天测试</h1>
        <div id="status">连接状态: 未连接</div>
        <div id="sessions"></div>
        <div id="messages"></div>
        <input type="text" id="messageInput" placeholder="输入消息...">
        <button onclick="sendMessage()">发送</button>
        <button onclick="initChat()">初始化聊天</button>
    </div>

    <script>
        // 使用前面提供的JavaScript示例代码
        const chatManager = new ConvChatManager();
        let stompClient = null;
        let delayedSessionManager = null;

        function connect() {
            const socket = new SockJS('http://localhost:8081/ws');
            stompClient = Stomp.over(socket);

            stompClient.connect({}, function(frame) {
                console.log('连接成功:', frame);
                document.getElementById('status').textContent = '连接状态: 已连接';

                delayedSessionManager = new DelayedSessionManager(stompClient, chatManager);

                // 订阅初始化响应
                stompClient.subscribe('/topic/smc4conv/init', function(response) {
                    const result = JSON.parse(response.body);
                    delayedSessionManager.handleInitResponse(result);
                    updateUI();
                });
            });
        }

        function initChat() {
            const request = {
                receChatRoomId: 1,
                sceneName: "知识库转换",
                token: "your-jwt-token-here",
                isThinking: "true",
                isStreaming: "true"
            };

            stompClient.send('/app/smc4conv/init', {}, JSON.stringify(request));
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (message && chatManager.activeSessionId) {
                // 获取当前活动会话的msgId
                const msgId = chatManager.getSessionMsgId(chatManager.activeSessionId) || '';

                const request = {
                    sessionId: chatManager.activeSessionId,
                    message: message,
                    msgId: msgId  // 必填参数
                };

                console.log('发送消息:', {
                    sessionId: chatManager.activeSessionId.substring(0, 8) + '...',
                    message: message,
                    msgId: msgId ? msgId.substring(0, 8) + '...' : '(空)'
                });

                stompClient.send('/app/smc4conv/send', {}, JSON.stringify(request));
                input.value = '';
            } else {
                alert('请输入消息内容并选择一个活动会话');
            }
        }

        function updateUI() {
            // 更新会话列表
            const sessionsDiv = document.getElementById('sessions');
            sessionsDiv.innerHTML = '';

            chatManager.sessions.forEach((session, sessionId) => {
                const sessionDiv = document.createElement('div');
                const shortSessionId = sessionId.includes('ctx-') ?
                    sessionId.split('-')[2] : sessionId.substring(0, 8);

                // 获取会话的额外信息
                const msgId = chatManager.getSessionMsgId(sessionId);
                const isHighFreq = chatManager.isHighFreqSession(sessionId);
                const isDelayed = chatManager.isDelayedSession(sessionId);

                sessionDiv.innerHTML = `
                    <div style="border: 1px solid #ccc; padding: 10px; margin: 5px;">
                        <p><strong>会话:</strong> ${shortSessionId}...</p>
                        <p><strong>客服:</strong> ${session.serviceName}</p>
                        <p><strong>机器人:</strong> ${session.robotName}</p>
                        <p><strong>逻辑类型:</strong> ${isHighFreq ? '高频知识库' : '传统模板'}</p>
                        ${msgId ? `<p><strong>消息ID:</strong> ${msgId.substring(0, 8)}...</p>` : ''}
                        ${session.isFirstFreq ? '<span style="color: green;">[首次高频]</span>' : ''}
                        ${isDelayed ? '<span style="color: orange;">[延时会话]</span>' : ''}
                        ${isHighFreq ? '<span style="color: blue;">[高频知识库]</span>' : '<span style="color: gray;">[传统模板]</span>'}
                        <br>
                        <button onclick="setActiveSession('${sessionId}')">选择</button>
                        <button onclick="closeSession('${sessionId}')">关闭</button>
                    </div>
                `;
                sessionsDiv.appendChild(sessionDiv);
            });
        }

        function setActiveSession(sessionId) {
            chatManager.activeSessionId = sessionId;
            console.log('当前活动会话:', sessionId);
        }

        function closeSession(sessionId) {
            closeSessionSafely(sessionId);
        }

        // 页面加载时连接
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
```

### 2. Node.js 示例
```javascript
// package.json dependencies
// "sockjs-client": "^1.6.1",
// "stompjs": "^2.3.3"

const SockJS = require('sockjs-client');
const Stomp = require('stompjs');

class ConvChatClient {
  constructor(chatroomId, token) {
    this.chatroomId = chatroomId;
    this.token = token;
    this.stompClient = null;
    this.chatManager = new ConvChatManager();
    this.delayedSessionManager = null;
  }

  connect() {
    const socket = new SockJS('http://localhost:8081/ws');
    this.stompClient = Stomp.over(socket);

    this.stompClient.connect({}, (frame) => {
      console.log('知识库转换WebSocket连接成功:', frame);

      this.delayedSessionManager = new DelayedSessionManager(this.stompClient, this.chatManager);

      // 订阅初始化响应
      this.stompClient.subscribe('/topic/smc4conv/init', (response) => {
        const result = JSON.parse(response.body);
        this.delayedSessionManager.handleInitResponse(result);
      });

      // 自动初始化聊天
      this.initializeChat();
    }, (error) => {
      console.error('WebSocket连接失败:', error);
    });
  }

  initializeChat() {
    const request = {
      receChatRoomId: this.chatroomId,
      sceneName: "知识库转换",
      token: this.token,
      isThinking: "true",
      isStreaming: "true"
    };

    this.stompClient.send('/app/smc4conv/init', {}, JSON.stringify(request));
  }

  sendMessage(sessionId, message) {
    // 获取会话的msgId
    const session = this.chatManager.sessions.get(sessionId);
    const msgId = session ? (session.msgId || '') : '';

    const request = {
      sessionId: sessionId,
      message: message,
      msgId: msgId  // 必填参数
    };

    console.log('Node.js发送消息:', {
      sessionId: sessionId.substring(0, 8) + '...',
      message: message,
      msgId: msgId ? msgId.substring(0, 8) + '...' : '(空)',
      logicType: msgId ? '高频知识库' : '传统模板'
    });

    this.stompClient.send('/app/smc4conv/send', {}, JSON.stringify(request));
  }

  closeSession(sessionId) {
    const request = {
      sessionId: sessionId
    };

    this.stompClient.send('/app/smc4conv/close', {}, JSON.stringify(request));
  }

  disconnect() {
    if (this.stompClient && this.stompClient.connected) {
      this.stompClient.disconnect();
    }
  }
}

// 使用示例
const client = new ConvChatClient(1, 'your-jwt-token-here');
client.connect();

// 5秒后发送消息（等待会话初始化完成）
setTimeout(() => {
  const sessions = Array.from(client.chatManager.sessions.keys());
  if (sessions.length > 0) {
    const sessionId = sessions[0];
    const session = client.chatManager.sessions.get(sessionId);
    console.log('准备发送测试消息到会话:', {
      sessionId: sessionId.substring(0, 8) + '...',
      msgId: session.msgId ? session.msgId.substring(0, 8) + '...' : '(空)',
      logicType: session.msgId ? '高频知识库' : '传统模板'
    });
    client.sendMessage(sessionId, '你好，这是一条测试消息');
  }
}, 5000);
```

## 总结

SimuChatWS4ConvController专门用于知识库转换的模拟聊天功能，与普通模拟聊天相比：

### 核心特点
1. **专业用途**: 专门用于知识库转换测试和验证
2. **独立端点**: 使用独立的WebSocket端点避免冲突
3. **知识库集成**: 与知识库服务深度集成，使用 `bmForKbService`
4. **双重逻辑**: 支持高频知识库逻辑（taskPurposeTag=0）和传统模板逻辑
5. **问答栈管理**: 问答栈式机器人，支持问答数据的出栈入栈操作
6. **智能提示词**: 根据逻辑类型动态生成系统提示词
7. **延时会话**: 支持延时会话创建，模拟真实进线频率
8. **资源管理**: 完善的会话生命周期管理和资源清理机制
9. **任务信息**: 提供完整的任务信息，包含训练循环次数等配置
10. **跨平台**: 支持Web浏览器、Node.js等多种环境
11. **统一格式**: 消息响应使用统一的 `{"resp": {"results": ["内容"], "msgId": "uuid"}}` 格式

### 技术要点
- **WebSocket连接**: `ws://localhost:8081/ws`
- **STOMP端点**: `/app/smc4conv/*` 和 `/topic/smc4conv/*`
- **JSON格式**: 所有消息均为JSON格式
- **响应格式**: 消息响应统一为 `{"resp": {"results": ["内容"], "msgId": "uuid"}}` 格式
- **逻辑判断**: 基于`taskPurposeTag`字段自动选择处理逻辑
- **问答管理**: 支持问答栈的出栈入栈操作
- **提示词生成**: 动态生成系统提示词，支持变量替换
- **会话管理**: 支持会话创建、消息发送和安全关闭
- **延时机制**: 根据进线频率配置自动创建延时会话
- **会话标识**: 使用上下文ID格式，如 "ctx-20250821181017-abc123"

### 开发优势
- **完整示例**: 提供HTML、JavaScript、Node.js等多种实现示例
- **错误处理**: 包含完善的错误处理机制
- **状态管理**: 支持复杂的会话状态管理，包括新增字段的处理
- **性能优化**: 包含性能优化建议
- **资源清理**: 自动清理Redis缓存，防止内存泄漏
- **任务配置**: 支持获取和使用任务配置信息，包括训练循环次数等设置
- **延时处理**: 专门的延时会话管理机制
- **格式统一**: 统一的消息响应格式，便于前端处理
- **字段扩展**: 新增`isFirstFreq`和`msgId`字段，增强会话信息的完整性
- **逻辑识别**: 通过`msgId`字段可以轻松识别高频知识库逻辑和传统模板逻辑

### 适用场景
- **高频知识库测试**: 基于问答数据的高频问题训练和验证
- **传统知识库测试**: 基于模板的知识库内容验证
- **AI对话质量评估**: 双重逻辑下的对话效果对比
- **问答栈管理**: 问答数据的栈式管理和出栈测试
- **系统提示词验证**: 动态生成提示词的效果测试
- **客服培训和模拟**: 不同逻辑下的客服培训场景
- **延时会话场景测试**: 模拟真实的进线频率和时间分布
- **进线频率模拟**: 测试不同进线频率下的系统表现

通过本文档的指导，开发者可以快速集成SimuChatWS4ConvController，构建专业的知识库转换测试界面，支持延时会话管理，提升开发效率和用户体验。

## 相关文档
- WebSocket配置文档
- JWT认证文档
- 知识库服务文档
