# TrainQaReportDtl 模拟聊天室报告明细表 API 调用示例

## 概述

模拟聊天室报告明细表的增删改查API接口，用于管理聊天室会话的报告明细记录。

## 基础信息

- **基础URL**: `http://localhost:8081`
- **认证方式**: JWT Token
- **Content-Type**: `application/json`

## 认证

所有API调用都需要先获取JWT Token：

```bash
# 登录获取Token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

响应示例：
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "staffId": 1,
    "username": "staff001",
    "displayName": "张三",
    "teamId": 1,
    "status": 1,
    "token": "eyJhbGciOiJIUzI1NiJ9..."
  }
}
```

## API 接口

### 1. 创建报告明细记录

```bash
curl -X POST "http://localhost:8081/api/qa-report-dtl" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "rpMainId": 1,
    "taskId": 2,
    "sessionId": "session-12345",
    "qaMainId": 3
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "创建成功",
  "data": {
    "id": 1,
    "rpMainId": 1,
    "taskId": 2,
    "sessionId": "session-12345",
    "qaMainId": 3,
    "teamId": 1,
    "createTime": "2025-08-31T18:00:00",
    "updateTime": "2025-08-31T18:00:00",
    "creator": "staff001",
    "updater": "0",
    "version": 0
  }
}
```

### 2. 根据ID查询记录

```bash
curl -X GET "http://localhost:8081/api/qa-report-dtl/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "rpMainId": 1,
    "taskId": 2,
    "sessionId": "session-12345",
    "qaMainId": 3,
    "teamId": 1,
    "createTime": "2025-08-31T18:00:00",
    "updateTime": "2025-08-31T18:00:00",
    "creator": "staff001",
    "updater": "0",
    "version": 0
  }
}
```

### 3. 更新记录

```bash
curl -X PUT "http://localhost:8081/api/qa-report-dtl/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "rpMainId": 2,
    "taskId": 3,
    "sessionId": "session-updated",
    "qaMainId": 4
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "更新成功",
  "data": true
}
```

### 4. 删除记录

```bash
curl -X DELETE "http://localhost:8081/api/qa-report-dtl/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "删除成功",
  "data": true
}
```

### 5. 根据sessionId查询记录

```bash
curl -X GET "http://localhost:8081/api/qa-report-dtl/session/session-12345" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "rpMainId": 1,
    "taskId": 2,
    "sessionId": "session-12345",
    "qaMainId": 3,
    "teamId": 1,
    "createTime": "2025-08-31T18:00:00",
    "updateTime": "2025-08-31T18:00:00",
    "creator": "staff001",
    "updater": "0",
    "version": 0
  }
}
```

### 6. 根据taskId查询记录列表

```bash
curl -X GET "http://localhost:8081/api/qa-report-dtl/task/2" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "rpMainId": 1,
      "taskId": 2,
      "sessionId": "session-12345",
      "qaMainId": 3,
      "teamId": 1,
      "createTime": "2025-08-31T18:00:00",
      "updateTime": "2025-08-31T18:00:00",
      "creator": "staff001",
      "updater": "0",
      "version": 0
    }
  ]
}
```

### 7. 分页查询记录

```bash
curl -X GET "http://localhost:8081/api/qa-report-dtl/page?page=1&pageSize=10&taskId=2&qaMainId=3" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "rpMainId": 1,
        "taskId": 2,
        "sessionId": "session-12345",
        "qaMainId": 3,
        "teamId": 1,
        "createTime": "2025-08-31T18:00:00",
        "updateTime": "2025-08-31T18:00:00",
        "creator": "staff001",
        "updater": "0",
        "version": 0
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

### 8. 批量创建记录

```bash
curl -X POST "http://localhost:8081/api/qa-report-dtl/batch" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "records": [
      {
        "rpMainId": 1,
        "taskId": 2,
        "sessionId": "session-001",
        "qaMainId": 3
      },
      {
        "rpMainId": 1,
        "taskId": 2,
        "sessionId": "session-002",
        "qaMainId": 3
      }
    ]
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "批量创建成功",
  "data": true
}
```

### 9. 检查sessionId是否存在

```bash
curl -X GET "http://localhost:8081/api/qa-report-dtl/exists/session-12345" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": true
}
```

### 10. 根据条件创建记录（便捷方法）

```bash
curl -X POST "http://localhost:8081/api/qa-report-dtl/create-by-conditions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "rpMainId": 1,
    "taskId": 2,
    "sessionId": "session-conditions",
    "qaMainId": 3
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "创建成功",
  "data": true
}
```

## 错误响应

当请求失败时，API会返回错误信息：

```json
{
  "code": 0,
  "message": "错误描述",
  "data": null
}
```

常见错误码：
- `401`: 未授权，Token无效或过期
- `403`: 禁止访问，权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项

1. 所有API都需要有效的JWT Token
2. 数据会自动按团队ID进行隔离
3. sessionId在同一团队内必须唯一
4. 分页查询的page从1开始
5. 创建时间和更新时间由系统自动设置
6. 版本号用于乐观锁控制
