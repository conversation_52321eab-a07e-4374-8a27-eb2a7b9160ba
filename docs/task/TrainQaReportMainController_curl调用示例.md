# TrainQaReportMain 问答报告主表 API 调用示例

## 概述

问答报告主表的增删改查API接口，用于管理聊天室考试报告的主要信息。

## 基础信息

- **基础URL**: `http://localhost:8081`
- **认证方式**: JWT Token
- **Content-Type**: `application/json`

## 认证

所有API调用都需要先获取JWT Token：

```bash
# 登录获取Token
curl -X POST "http://localhost:8081/api/staff/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "staff001",
    "password": "123456",
    "rememberMe": false
  }'
```

响应示例：
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "staffId": 1,
    "username": "staff001",
    "displayName": "张三",
    "teamId": 1,
    "status": 1,
    "token": "eyJhbGciOiJIUzI1NiJ9..."
  }
}
```

## API 接口

### 1. 创建报告主表记录

```bash
curl -X POST "http://localhost:8081/api/qa-report-main" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "chatroomId": 100,
    "staffId": 200,
    "examUserRealName": "张三",
    "examUserNo": "EXAM001",
    "examScore": 85.50
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "创建成功",
  "data": {
    "id": 1,
    "chatroomId": 100,
    "staffId": 200,
    "examUserRealName": "张三",
    "examUserNo": "EXAM001",
    "examScore": 85.50,
    "teamId": 1,
    "createTime": "2025-08-31T18:00:00",
    "updateTime": "2025-08-31T18:00:00",
    "creator": "staff001",
    "updater": "0",
    "version": 0
  }
}
```

### 2. 根据ID查询记录

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "id": 1,
    "chatroomId": 100,
    "staffId": 200,
    "examUserRealName": "张三",
    "examUserNo": "EXAM001",
    "examScore": 85.50,
    "teamId": 1,
    "createTime": "2025-08-31T18:00:00",
    "updateTime": "2025-08-31T18:00:00",
    "creator": "staff001",
    "updater": "0",
    "version": 0
  }
}
```

### 3. 更新记录

```bash
curl -X PUT "http://localhost:8081/api/qa-report-main/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "chatroomId": 101,
    "staffId": 201,
    "examUserRealName": "李四",
    "examUserNo": "EXAM002",
    "examScore": 92.00,
    "version": 0
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "更新成功",
  "data": true
}
```

### 4. 删除记录

```bash
curl -X DELETE "http://localhost:8081/api/qa-report-main/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "删除成功",
  "data": true
}
```

### 5. 分页查询记录（支持多条件过滤）

#### 5.1 基础分页查询

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.2 按聊天室ID和员工ID过滤

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10&chatroomId=100&staffId=200" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.3 按姓名模糊查询

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10&examUserRealName=张三" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.4 按编号模糊查询

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10&examUserNo=EXAM001" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.5 按最低分数过滤

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10&minScore=60.0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.6 按创建时间范围查询

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10&createTimeStart=2024-01-01T00:00:00&createTimeEnd=2024-12-31T23:59:59" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.7 多条件组合查询

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/page?page=1&pageSize=10&examUserRealName=张&minScore=80.0&createTimeStart=2024-06-01T00:00:00" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 支持的查询参数

| 参数名称 | 类型 | 是否必填 | 说明 | 示例 |
|---------|------|---------|------|------|
| page | Integer | 否 | 页码，从1开始，默认为1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认为10 | 10 |
| chatroomId | Long | 否 | 聊天室ID，精确匹配 | 100 |
| staffId | Long | 否 | 员工ID，精确匹配 | 200 |
| examUserRealName | String | 否 | 考试用户真实姓名，支持模糊查询 | 张三 |
| examUserNo | String | 否 | 考试用户编号，支持模糊查询 | EXAM001 |
| createTimeStart | LocalDateTime | 否 | 创建时间开始（包含） | 2024-01-01T00:00:00 |
| createTimeEnd | LocalDateTime | 否 | 创建时间结束（包含） | 2024-12-31T23:59:59 |
| minScore | BigDecimal | 否 | 最低分数（大于等于此分数） | 60.0 |

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "chatroomId": 100,
        "staffId": 200,
        "examUserRealName": "张三",
        "examUserNo": "EXAM001",
        "examScore": 85.50,
        "teamId": 1,
        "createTime": "2025-08-31T18:00:00",
        "updateTime": "2025-08-31T18:00:00",
        "creator": "staff001",
        "updater": "0",
        "version": 0
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 6. 根据聊天室ID查询记录列表

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/chatroom/100" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "chatroomId": 100,
      "staffId": 200,
      "examUserRealName": "张三",
      "examUserNo": "EXAM001",
      "examScore": 85.50,
      "teamId": 1,
      "createTime": "2025-08-31T18:00:00",
      "updateTime": "2025-08-31T18:00:00",
      "creator": "staff001",
      "updater": "0",
      "version": 0
    }
  ]
}
```

### 7. 根据员工ID查询记录列表

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/staff/200" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "chatroomId": 100,
      "staffId": 200,
      "examUserRealName": "张三",
      "examUserNo": "EXAM001",
      "examScore": 85.50,
      "teamId": 1,
      "createTime": "2025-08-31T18:00:00",
      "updateTime": "2025-08-31T18:00:00",
      "creator": "staff001",
      "updater": "0",
      "version": 0
    }
  ]
}
```

### 8. 批量创建记录

```bash
curl -X POST "http://localhost:8081/api/qa-report-main/batch" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "records": [
      {
        "chatroomId": 100,
        "staffId": 200,
        "examUserRealName": "张三",
        "examUserNo": "EXAM001",
        "examScore": 85.50
      },
      {
        "chatroomId": 101,
        "staffId": 201,
        "examUserRealName": "李四",
        "examUserNo": "EXAM002",
        "examScore": 92.00
      }
    ]
  }'
```

**响应示例**：
```json
{
  "code": 1,
  "message": "批量创建成功",
  "data": true
}
```

### 9. 根据条件统计记录数（支持多条件过滤）

#### 9.1 基础统计

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/count" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 9.2 按聊天室ID和员工ID统计

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/count?chatroomId=100&staffId=200" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 9.3 按姓名模糊统计

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/count?examUserRealName=张" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 9.4 按最低分数统计

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/count?minScore=80.0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 9.5 按创建时间范围统计

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/count?createTimeStart=2024-01-01T00:00:00&createTimeEnd=2024-12-31T23:59:59" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 9.6 多条件组合统计

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/count?examUserNo=E&minScore=60.0&createTimeStart=2024-06-01T00:00:00" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**支持的查询参数**（与分页查询相同）：

| 参数名称 | 类型 | 是否必填 | 说明 | 示例 |
|---------|------|---------|------|------|
| chatroomId | Long | 否 | 聊天室ID，精确匹配 | 100 |
| staffId | Long | 否 | 员工ID，精确匹配 | 200 |
| examUserRealName | String | 否 | 考试用户真实姓名，支持模糊查询 | 张三 |
| examUserNo | String | 否 | 考试用户编号，支持模糊查询 | EXAM001 |
| createTimeStart | LocalDateTime | 否 | 创建时间开始（包含） | 2024-01-01T00:00:00 |
| createTimeEnd | LocalDateTime | 否 | 创建时间结束（包含） | 2024-12-31T23:59:59 |
| minScore | BigDecimal | 否 | 最低分数（大于等于此分数） | 60.0 |

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": 15
}
```

### 10. 展示考试结果详情

#### 10.1 默认模式（自动计算和更新分数）

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/show-exam-result/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

或者明确指定更新分数：

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/show-exam-result/1?isNeedUpdate=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 10.2 只读模式（使用已有分数，不重新计算）

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/show-exam-result/1?isNeedUpdate=false" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 10.3 指定团队ID（当SecurityContext中无teamId时使用）

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/show-exam-result/1?teamId=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 10.4 组合参数使用

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/show-exam-result/1?isNeedUpdate=false&teamId=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "qaReportMainId": 1,
    "examUserRealName": "张三",
    "examUserNo": "EXAM001",
    "examScore": 85.67,
    "totalQuestions": 5,
    "qaRdmList": [
      {
        "id": 101,
        "uuid": "uuid-12345-67890",
        "quesNo": "1",
        "question": "请问什么是Java？",
        "answer": "Java是一种编程语言",
        "actualQuestion": "什么是Java编程语言的主要特点？",
        "actualAnswer": "Java具有面向对象、跨平台、安全性高等特点",
        "resolve": "{\"analysis\":\"回答基本正确\",\"score\":85}",
        "sendTime": "2025-09-01T20:30:00",
        "score": 85
      },
      {
        "id": 102,
        "uuid": "uuid-12345-67891",
        "quesNo": "2",
        "question": "请解释Spring框架？",
        "answer": "Spring是Java开发框架",
        "actualQuestion": "Spring框架的核心特性有哪些？",
        "actualAnswer": "Spring提供依赖注入、面向切面编程、事务管理等功能",
        "resolve": "{\"analysis\":\"回答较为简单，需要更详细\",\"score\":75}",
        "sendTime": "2025-09-01T20:32:00",
        "score": 75
      },
      {
        "id": 103,
        "uuid": "uuid-12345-67892",
        "quesNo": "3",
        "question": "数据库索引的作用？",
        "answer": "索引可以提高查询效率",
        "actualQuestion": "数据库索引如何提升查询性能？",
        "actualAnswer": "索引通过B+树结构加速数据定位，减少磁盘IO次数",
        "resolve": "{\"analysis\":\"回答正确且详细\",\"score\":95}",
        "sendTime": "2025-09-01T20:35:00",
        "score": 95
      }
    ]
  }
}
```

**字段说明**：
- `qaReportMainId`: 报告主记录ID  
- `examUserRealName`: 考试用户真实姓名
- `examUserNo`: 考试用户编号
- `examScore`: 考试总分（自动计算平均分，保留2位小数）
- `totalQuestions`: 总题目数量
- `qaRdmList`: 答题记录列表
  - `id`: 答题记录ID
  - `uuid`: 唯一标识符
  - `quesNo`: 题目序号
  - `question`: 原始问题
  - `answer`: 原始答案
  - `actualQuestion`: 实际问题
  - `actualAnswer`: 实际答案  
  - `resolve`: 解析结果（JSON格式，包含analysis和score）
  - `sendTime`: 发送时间
  - `score`: 该题得分（从resolve中解析获得）

**参数说明**：
- `isNeedUpdate`（可选，默认为true）：
  - `true`：重新计算总分并更新到数据库（默认行为）
  - `false`：使用数据库中已有的examScore，不重新计算
- `teamId`（可选）：
  - 团队ID，用于数据权限控制
  - 优先使用SecurityContext中的teamId
  - 当SecurityContext中的teamId为null时，使用此参数值
  - 主要用于特殊场景下的团队ID指定

## 错误响应

当请求失败时，API会返回错误信息：

```json
{
  "code": 0,
  "message": "错误描述",
  "data": null
}
```

常见错误码：
- `401`: 未授权，Token无效或过期
- `403`: 禁止访问，权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项

1. 所有API都需要有效的JWT Token
2. 数据会自动按团队ID进行隔离
3. 分页查询的page从1开始
4. 创建时间和更新时间由系统自动设置
5. 版本号用于乐观锁控制
6. 考试分数使用decimal类型，支持小数
7. 聊天室ID和员工ID需要确保在相关表中存在
8. showExamResult接口支持isNeedUpdate参数控制计算模式
9. isNeedUpdate=true时会重新计算平均分并更新到数据库（默认行为）
10. isNeedUpdate=false时使用数据库中已有的examScore，提升查询性能
11. resolve字段包含JSON格式的评判结果，系统会自动解析其中的score
12. **新增查询功能**：分页查询和统计接口支持4种新的过滤条件
    - examUserRealName：姓名模糊查询，使用LIKE '%keyword%'匹配
    - examUserNo：编号模糊查询，使用LIKE '%keyword%'匹配
    - createTimeStart/createTimeEnd：创建时间范围查询，支持精确到秒
    - minScore：最低分数过滤，查询exam_score >= minScore的记录
13. **查询条件组合**：所有查询条件可任意组合，系统使用动态SQL确保查询的准确性和性能
14. **时间格式**：时间参数使用ISO 8601格式：YYYY-MM-DDTHH:mm:ss（如：2024-01-01T00:00:00）
15. **分数比较**：minScore参数使用BigDecimal类型，支持精确的小数计算和比较

### 8. 接待详情查看接口（废弃，见5. 分页查询记录（支持多条件过滤））

根据聊天室ID分页查询接待详情，支持按员工ID过滤。该接口使用了多租户过滤器，会自动根据当前登录用户的creator字段进行数据隔离。

#### 接口信息
- **URL**: `/api/qa-report-main/reception-details/{chatroomId}`
- **方法**: `GET`
- **认证**: 需要JWT Token

#### 请求参数

##### 路径参数
- `chatroomId` (Long, 必填): 聊天室ID

##### 查询参数
- `page` (Integer, 可选): 页码，从1开始，默认值为1
- `pageSize` (Integer, 可选): 每页大小，默认值为10
- `staffId` (Long, 可选): 员工ID，用于过滤特定员工的记录

#### 请求示例

##### 基础查询（默认分页）
```bash
curl -X GET "http://localhost:8081/api/qa-report-main/reception-details/1" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

##### 指定分页参数
```bash
curl -X GET "http://localhost:8081/api/qa-report-main/reception-details/1?page=2&pageSize=20" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

##### 按员工ID过滤
```bash
curl -X GET "http://localhost:8081/api/qa-report-main/reception-details/1?staffId=123&page=1&pageSize=10" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ"
```

#### 响应示例

##### 成功响应 (200 OK)
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "chatroomId": 1,
        "staffId": 123,
        "examUserRealName": "张三",
        "examUserNo": "EMP001",
        "examScore": 85.50,
        "teamId": 1,
        "createTime": "2025-01-17T10:30:00",
        "updateTime": "2025-01-17T11:00:00",
        "creator": "staff001",
        "updater": "staff001",
        "version": 1
      },
      {
        "id": 2,
        "chatroomId": 1,
        "staffId": 124,
        "examUserRealName": "李四",
        "examUserNo": "EMP002",
        "examScore": 92.00,
        "teamId": 1,
        "createTime": "2025-01-17T14:20:00",
        "updateTime": "2025-01-17T14:45:00",
        "creator": "staff001",
        "updater": "staff001",
        "version": 1
      }
    ],
    "total": 25,
    "page": 1,
    "pageSize": 10,
    "totalPages": 3
  }
}
```

#### 特殊说明

1. **多租户隔离**: 接口会自动根据当前登录用户的creator字段进行数据过滤，只返回该用户创建的记录
2. **分页参数**: page从1开始计数，pageSize建议不超过100
3. **权限控制**: 只能查看当前用户有权限的聊天室数据
4. **数据过滤**: teamId会被禁用，creator会被启用，确保数据安全隔离
5. **模糊查询**: examUserRealName和examUserNo支持模糊查询，会自动添加通配符
6. **分数过滤**: minScore参数会查询大于等于该分数的记录，支持小数
7. **时间范围**: createTimeStart和createTimeEnd支持精确到秒的时间范围查询
8. **多条件组合**: 所有查询条件可以任意组合使用，系统会自动构建相应的SQL条件

## 功能更新历史

- **2025-09-01**: 新增showExamResult接口，支持考试结果展示和自动分数计算
- **2025-09-02**: showExamResult接口新增isNeedUpdate参数，支持控制是否重新计算分数
- **2025-09-02**: 增强分页查询和统计接口，新增4个查询参数：
  - examUserRealName：支持姓名模糊查询
  - examUserNo：支持编号模糊查询  
  - createTimeStart/createTimeEnd：支持创建时间范围查询
  - minScore：支持最低分数过滤（大于等于指定分数）
- **2025-01-17**: 新增接待详情查看接口，支持按聊天室ID分页查询接待详情，集成多租户过滤器
- **2025-09-05**: 新增Excel导出接口，支持根据查询条件导出问答报告Excel文件，最多10万条记录

## Excel导出接口

### 11. 导出Excel文件

根据查询条件导出问答报告Excel文件，支持与分页查询相同的查询条件，最多导出10万条记录。

#### 11.1 基础导出（全部数据）

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-main.xlsx
```

#### 11.2 按聊天室ID导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?chatroomId=100" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-chatroom-100.xlsx
```

#### 11.3 按员工ID导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?staffId=200" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-staff-200.xlsx
```

#### 11.4 按姓名模糊查询导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?examUserRealName=张" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-name-zhang.xlsx
```

#### 11.5 按编号模糊查询导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?examUserNo=EXAM" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-no-exam.xlsx
```

#### 11.6 按分数范围导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?minScore=80.0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-score-80-above.xlsx
```

#### 11.7 按时间范围导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?createTimeStart=2024-01-01T00:00:00&createTimeEnd=2024-12-31T23:59:59" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-2024.xlsx
```

#### 11.8 多条件组合导出

```bash
curl -X GET "http://localhost:8081/api/qa-report-main/export-excel?examUserRealName=张&minScore=80.0&createTimeStart=2024-06-01T00:00:00" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o qa-report-custom.xlsx
```

#### 支持的查询参数（与分页查询相同）

| 参数名称 | 类型 | 是否必填 | 说明 | 示例 |
|---------|------|---------|------|------|
| chatroomId | Long | 否 | 聊天室ID，精确匹配 | 100 |
| staffId | Long | 否 | 员工ID，精确匹配 | 200 |
| examUserRealName | String | 否 | 考试用户真实姓名，支持模糊查询 | 张三 |
| examUserNo | String | 否 | 考试用户编号，支持模糊查询 | EXAM001 |
| createTimeStart | LocalDateTime | 否 | 创建时间开始（包含） | 2024-01-01T00:00:00 |
| createTimeEnd | LocalDateTime | 否 | 创建时间结束（包含） | 2024-12-31T23:59:59 |
| minScore | BigDecimal | 否 | 最低分数（大于等于此分数） | 60.0 |

#### Excel文件说明

- **文件格式**: .xlsx格式
- **工作表名称**: "问答报告"
- **文件列**: 包含以下字段
  - ID: 记录主键ID
  - 聊天室ID: 关联的聊天室ID
  - 员工ID: 关联的员工ID  
  - 考试用户姓名: 考试用户真实姓名
  - 考试用户编号: 考试用户编号
  - 考试分数: 考试得分
  - 团队ID: 所属团队ID
  - 创建时间: 记录创建时间
  - 更新时间: 记录最后更新时间
  - 创建人: 记录创建人
  - 更新人: 记录最后更新人
  - 版本号: 乐观锁版本号

#### Excel导出特性

1. **数据量限制**: 最多导出10万条记录，超过的数据会被截断
2. **查询一致性**: 使用与分页查询相同的查询逻辑，确保数据一致性
3. **权限控制**: 自动应用团队级数据隔离，只导出当前团队的数据
4. **样式美化**: 包含标题行样式、数据对齐等基础格式化
5. **自动列宽**: 自动调整列宽以适应内容
6. **时间格式**: 时间字段使用"yyyy-MM-dd HH:mm:ss"格式显示

#### 响应说明

- **Content-Type**: `application/octet-stream`
- **Content-Disposition**: `attachment; filename="qa-report-main.xlsx"`
- **响应体**: Excel文件的二进制数据

#### 使用建议

1. **大数据量导出**: 对于大量数据，建议使用时间范围或其他条件进行分批导出
2. **文件命名**: 使用`-o`参数指定有意义的文件名，便于区分不同的导出结果
3. **网络超时**: 大数据量导出可能需要较长时间，注意设置合适的网络超时时间
4. **存储空间**: 确保本地有足够的存储空间保存Excel文件

#### 错误情况处理

- **数据为空**: 如果查询条件没有匹配到任何数据，会返回只有标题行的Excel文件
- **查询超时**: 复杂查询可能超时，建议优化查询条件或分批导出
- **权限不足**: 没有数据访问权限时会返回空Excel文件
