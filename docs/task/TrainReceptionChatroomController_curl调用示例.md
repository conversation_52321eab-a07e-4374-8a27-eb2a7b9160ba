# TrainReceptionChatroomController API 调用示例

接待聊天室管理API的curl调用示例，包含完整的请求和响应格式，供前端Vue开发参考。

## ⚠️ 重要验证规则

**任务数量验证**: 创建和更新聊天室时，任务列表(taskList)必须包含至少1个任务，否则会返回错误："聊天室必须关联至少1个任务"

## 🔄 字段更新历史

**字段更新记录**:

1. **reception_skin字段类型变更** (2025-08-21更新):
   - **原类型**: Integer (0, 1, 2)
   - **新类型**: String ("0", "1", "2")
   - **影响**: 所有涉及reception_skin字段的请求参数和响应数据都需要使用字符串格式
   - **兼容性**: 前端需要相应调整，将数字类型改为字符串类型

2. **room_name字段新增** (2025-09-02更新):
   - **新增字段**: roomName (String, 必填)
   - **数据库字段**: room_name varchar(64) NOT NULL
   - **说明**: 聊天室名称，用于标识和展示聊天室
   - **影响**: 所有CRUD接口都包含此字段，创建和更新时必填
   - **校验规则**: @NotBlank，不能为空或仅包含空格

3. **roomName查询条件新增** (2025-09-03更新):
   - **新增查询条件**: roomName (String, 可选)
   - **功能**: 支持按聊天室名称模糊查询
   - **实现**: 使用LIKE '%keyword%'进行模糊匹配
   - **影响**: 分页查询接口新增roomName查询参数
   - **地位**: 与receptionSkin、sceneMode等查询条件平级，可选填

## 基础配置

- **服务端口**: 8081
- **基础路径**: `/api`
- **认证方式**: JWT Token (在Header中添加 `Authorization: Bearer <token>`)

## API 接口列表

### 1. 分页查询聊天室列表

#### 请求方式
```bash
curl -X GET "http://localhost:8081/api/chatrooms?receptionSkin=\"0\"&sceneMode=0&roomName=电商&quickPhrasesId=1&createTimeStart=2025-01-01&createTimeEnd=2025-12-31&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 查询参数说明
| 参数名             | 类型      | 必填 | 说明                       | 示例         |
|-----------------|---------|----|--------------------------|------------|
| receptionSkin   | String  | 否  | 接待皮肤："0"-千牛，"1"-咚咚，"2"-抖音 | "0"        |
| sceneMode       | Integer | 否  | 场景模式：0-萌新友好，1-压力考核，2-自定义 | 0          |
| roomName        | String  | 否  | 聊天室名称模糊查询                | 电商         |
| quickPhrasesId  | Long    | 否  | 快捷短语ID                   | 1          |
| createTimeStart | String  | 否  | 创建时间起始点                  | 2025-01-01 |
| createTimeEnd   | String  | 否  | 创建时间结束点                  | 2025-12-31 |
| page            | Integer | 否  | 页码，从1开始                  | 1          |
| pageSize        | Integer | 否  | 每页大小                     | 10         |

#### 响应示例
```json
{
  "code": 1,
  "message": "成功",
  "data": {
    "total": 25,
    "records": [
      {
        "id": 1,
        "roomName": "客服培训聊天室",
        "receptionSkin": "0",
        "receptionSkinName": "干牛",
        "sceneMode": 0,
        "sceneModeName": "萌新友好",
        "quickPhrasesId": 1,
        "quickPhrasesName": "标准短语",
        "receptionDuration": 30,
        "timerDisplay": false,
        "entryFreqMin": 1,
        "entryFreqMax": 5,
        "taskCount": 3,
        "staffCount": 2,
        "createTime": "2025-07-26T10:30:00",
        "updateTime": "2025-07-26T10:30:00",
        "creator": "admin"
      }
    ],
    "page": 1,
    "pageSize": 10,
    "totalPages": 3
  }
}
```

### 2. 查询聊天室详情

#### 请求方式
```bash
curl -X GET "http://localhost:8081/api/chatrooms/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 聊天室ID |

#### 响应示例
```json
{
  "code": 1,
  "message": "成功",
  "data": {
    "id": 1,
    "roomName": "高级客服培训室",
    "receptionSkin": "0",
    "receptionSkinName": "干牛",
    "sceneMode": 0,
    "sceneModeName": "萌新友好",
    "quickPhrasesId": 1,
    "quickPhrasesName": "标准短语",
    "receptionDuration": 30,
    "timerDisplay": false,
    "entryFreqMin": 1,
    "entryFreqMax": 5,
    "createTime": "2025-07-26T10:30:00",
    "updateTime": "2025-07-26T10:30:00",
    "creator": "admin",
    "version": 0,
    "taskList": [
      {
        "id": 1,
        "taskId": 1,
        "taskName": "商品知识训练",
        "trainingRecycleCnt": 0
      },
      {
        "id": 2,
        "taskId": 2,
        "taskName": "销售技巧训练",
        "trainingRecycleCnt": 1
      }
    ],
    "staffList": [1, 2, 3]
  }
}
```

### 3. 创建聊天室

#### 请求方式
```bash
curl -X POST "http://localhost:8081/api/chatrooms" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "roomName": "新客服培训聊天室",
    "receptionSkin": "0",
    "sceneMode": 0,
    "quickPhrasesId": 1,
    "receptionDuration": 30,
    "timerDisplay": false,
    "entryFreqMin": 2,
    "entryFreqMax": 8,
    "taskList": [
      {
        "taskId": 1,
        "trainingRecycleCnt": 0
      },
      {
        "taskId": 2,
        "trainingRecycleCnt": 1
      }
    ],
    "staffList": [1, 2, 3]
  }'
```

#### 请求体字段说明
| 字段名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| roomName | String | 是 | 聊天室名称，不能为空 | - |
| receptionSkin | String | 否 | 接待皮肤："0"-干牛，"1"-咩咩，"2"-抖音 | "0" |
| sceneMode | Integer | 否 | 场景模式：0-萌新友好，1-压力考核，2-自定义 | 0 |
| quickPhrasesId | Long | 否 | 快捷短语ID | null |
| receptionDuration | Integer | 否 | 接待时长（分钟） | 30 |
| timerDisplay | Boolean | 否 | 读秒显示 | false |
| entryFreqMin | Integer | 否 | 进线频率下限（分钟） | 1 |
| entryFreqMax | Integer | 否 | 进线频率上限（分钟） | 5 |
| taskList | Array | 否 | 关联任务列表 | [] |
| taskList[].taskId | Long | 是 | 任务ID | - |
| taskList[].trainingRecycleCnt | Integer | 否 | 此任务循环次数 | 0 |
| staffList | Array | 否 | 关联员工ID列表 | [] |

#### 成功响应示例
```json
{
  "code": 1,
  "message": "成功",
  "data": 1
}
```

#### 失败响应示例（任务数量不足）
```json
{
  "code": 0,
  "message": "聊天室必须关联至少1个任务",
  "data": null
}
```

### 4. 更新聊天室

#### 请求方式
```bash
curl -X PUT "http://localhost:8081/api/chatrooms/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "roomName": "更新后的聊天室名称",
    "receptionSkin": "1",
    "sceneMode": 1,
    "quickPhrasesId": 2,
    "receptionDuration": 45,
    "timerDisplay": true,
    "entryFreqMin": 3,
    "entryFreqMax": 12,
    "version": 0,
    "taskList": [
      {
        "id": 1,
        "taskId": 1,
        "trainingRecycleCnt": 2
      },
      {
        "taskId": 3,
        "trainingRecycleCnt": 0
      }
    ],
    "staffList": [2, 4]
  }'
```

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 聊天室ID |

#### 请求体字段说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| receptionSkin | String | 否 | 接待皮肤："0"-干牛，"1"-咩咩，"2"-抖音 |
| sceneMode | Integer | 否 | 场景模式：0-萌新友好，1-压力考核，2-自定义 |
| quickPhrasesId | Long | 否 | 快捷短语ID |
| receptionDuration | Integer | 否 | 接待时长（分钟） |
| timerDisplay | Boolean | 否 | 读秒显示 |
| entryFreqMin | Integer | 否 | 进线频率下限（分钟） |
| entryFreqMax | Integer | 否 | 进线频率上限（分钟） |
| version | Long | 是 | 版本号（用于乐观锁） |
| taskList | Array | 否 | 关联任务列表 |
| taskList[].id | Long | 否 | 任务关联ID（更新时需要） |
| taskList[].taskId | Long | 是 | 任务ID |
| taskList[].trainingRecycleCnt | Integer | 否 | 此任务循环次数 |
| staffList | Array | 否 | 关联员工ID列表 |

#### 成功响应示例
```json
{
  "code": 1,
  "message": "聊天室更新成功",
  "data": "聊天室更新成功"
}
```

#### 失败响应示例（任务数量不足）
```json
{
  "code": 0,
  "message": "聊天室必须关联至少1个任务",
  "data": null
}
```

### 5. 删除聊天室

#### 请求方式
```bash
curl -X DELETE "http://localhost:8081/api/chatrooms/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 聊天室ID |

#### 响应示例
```json
{
  "code": 1,
  "message": "聊天室删除成功",
  "data": "聊天室删除成功"
}
```

### 6. 批量删除聊天室

#### 请求方式
```bash
curl -X DELETE "http://localhost:8081/api/chatrooms?ids=1,2,3" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 查询参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| ids | String | 是 | 聊天室ID列表，逗号分隔 | 1,2,3 |

#### 响应示例
```json
{
  "code": 1,
  "message": "聊天室批量删除成功",
  "data": "聊天室批量删除成功"
}
```

### 7. 我的接待任务（按员工ID查询）

#### 请求方式
```bash
curl -X GET "http://localhost:8081/api/chatrooms/my-tasks?receptionSkin=\"1\"&sceneMode=1&roomName=电商&quickPhrasesId=1&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 查询参数说明
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| receptionSkin | String | 否 | 接待皮肤："0"-千牛，"1"-咚咚，"2"-抖音 | "1" |
| sceneMode | Integer | 否 | 场景模式：0-萌新友好，1-压力考核，2-自定义 | 1 |
| roomName | String | 否 | 聊天室名称模糊查询 | 电商 |
| quickPhrasesId | Long | 否 | 快捷短语ID | 1 |
| createTimeStart | String | 否 | 创建时间起始点 | 2025-01-01 |
| createTimeEnd | String | 否 | 创建时间结束点 | 2025-12-31 |
| page | Integer | 否 | 页码，从1开始 | 1 |
| pageSize | Integer | 否 | 每页大小 | 10 |

#### 功能说明
- 查询当前登录员工被分配的接待聊天室任务
- 通过JWT Token获取当前用户ID，再查询对应的员工信息
- 基于员工ID和聊天室关联表(train_chatroom_staff)查询相关聊天室
- 支持与普通聊天室列表相同的查询条件和分页功能

#### 响应示例
```json
{
  "code": 1,
  "message": "成功",
  "data": {
    "total": 3,
    "records": [
      {
        "id": 89,
        "receptionSkin": "1",
        "receptionSkinName": "咩咩",
        "sceneMode": 1,
        "sceneModeName": "压力考核",
        "quickPhrasesId": 1,
        "quickPhrasesName": "train_shortcut_phrases",
        "receptionDuration": 30,
        "timerDisplay": true,
        "entryFreqMin": 1,
        "entryFreqMax": 5,
        "createTime": "2025-07-27T22:56:19",
        "updateTime": "2025-07-27T22:56:19",
        "creator": "test_user",
        "version": 0,
        "staffCount": 1
      }
    ],
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

#### 业务逻辑
1. **员工身份验证**：通过JWT Token获取当前用户userId
2. **员工信息查询**：根据userId查询对应的员工信息（staff_id）
3. **聊天室过滤**：只返回在train_chatroom_staff表中与该员工关联的聊天室
4. **团队隔离**：确保只查询当前团队的数据
5. **分页支持**：支持标准分页查询，与其他接口保持一致

#### 错误处理
- 如果当前用户不存在对应的员工记录，返回空列表
- 如果员工没有被分配任何聊天室任务，返回空列表
- 保持与其他接口一致的错误响应格式

## 错误响应格式

当接口调用失败时，统一返回以下格式：

```json
{
  "code": 500,
  "message": "具体的错误信息",
  "data": null
}
```

### 常见错误码
- `500`: 服务器内部错误
- `401`: 认证失败，JWT Token无效或过期
- `400`: 请求参数错误
- `404`: 资源不存在

## 注意事项

1. **认证要求**: 所有接口都需要在Header中携带有效的JWT Token
2. **团队隔离**: 接口自动根据JWT Token中的teamId进行数据隔离
3. **响应格式**: 所有成功响应的code都是1，失败响应的code是500或其他错误码
4. **时间格式**: 时间字段采用ISO 8601格式（yyyy-MM-ddTHH:mm:ss）
5. **分页参数**: page从1开始，pageSize默认为10
6. **乐观锁**: 更新操作需要提供version字段防止并发冲突

## 新增功能说明

### 进线频率控制
- **entryFreqMin**: 进线频率下限（分钟），默认值为1
- **entryFreqMax**: 进线频率上限（分钟），默认值为5
- **功能说明**: 控制聊天室中人员进入的频率，系统会在设定的时间范围内随机安排人员进入，避免一次性涌入
- **使用示例**: 设置为3-12分钟，表示人员会在3-12分钟的随机时间间隔内进入聊天室
- **业务价值**: 提供更真实的客户进线体验，模拟实际业务场景中的客户流量分布

## 前端集成建议

1. **统一请求封装**: 建议封装axios请求拦截器自动添加JWT Token
2. **错误处理**: 统一处理401错误，引导用户重新登录
3. **Loading状态**: 长时间操作建议显示Loading状态
4. **数据验证**: 前端应进行基础的数据格式验证
5. **分页组件**: 可复用分页组件处理列表数据
6. **员工关联功能**: 新增支持聊天室与员工的多对多关联关系
   - `staffList`字段用于指定关联的员工ID列表
   - 支持在创建和更新聊天室时设置员工关联
   - 查询详情时返回关联的员工ID列表
   - 分页查询时返回关联员工数量统计
7. **进线频率设置**: 新增进线频率控制功能
   - 前端应提供友好的时间范围选择组件
   - 建议设置合理的默认值和范围限制
   - 可提供预设的常用时间范围选项（如：1-3分钟、3-8分钟、5-15分钟等）

## 数据库关联关系

### 聊天室员工关联表 (train_chatroom_staff)
- 实现聊天室与员工的多对多关联关系
- 关键字段：
  - `rece_chatroom_id`: 关联train_reception_chatroom表主键
  - `staff_id`: 关联train_staff表主键
  - `team_id`: 团队隔离
- 操作特性：
  - 创建/更新聊天室时自动维护员工关联
  - 删除聊天室时自动清理员工关联
  - 支持团队隔离和乐观锁机制

### 8. 检查聊天室任务可用性

#### 请求方式
```bash
curl -X GET "http://localhost:8081/api/chatrooms/{roomId}/check-task-available" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 路径参数说明
| 参数名   | 类型   | 必填 | 说明      | 示例 |
|---------|--------|------|-----------|------|
| roomId  | Long   | 是   | 聊天室ID  | 1    |

#### 功能说明
- 检查指定聊天室中的接待任务是否仍在学习中
- 如果有任务正在学习（learning_status为'un_learn'或'learning'且amt_has_learned=0），则拒绝使用
- 主要用于防止在任务学习过程中进行其他操作

#### 响应示例

**任务可用时（没有正在学习的任务）**
```json
{
  "code": 1,
  "message": "聊天室任务可用",
  "data": "聊天室任务可用"
}
```

**任务不可用时（有任务正在学习）**
```json
{
  "code": 0,
  "message": "123仍在学习中，请稍等。",
  "data": null
}
```

#### 业务逻辑
1. **团队隔离验证**：通过JWT Token获取当前用户teamId
2. **SQL查询执行**：使用以下逻辑查询正在学习的任务
   ```sql
   SELECT train_reception_task.id,train_reception_task.task_name,
          train_reception_task.amt_has_learned,train_reception_task.learning_status 
   FROM train_chatroom_task join train_reception_task
   ON train_chatroom_task.task_id=train_reception_task.id
   WHERE train_chatroom_task.chatroom_id={roomId} 
     AND train_reception_task.learning_status ='un_learn'
     OR (train_reception_task.learning_status ='learning' AND train_reception_task.amt_has_learned=0);
   ```
3. **结果判断**：
   - 如果查询结果 > 0，则返回拒绝信息（code=1，message包含任务ID）
   - 如果查询结果 = 0，则返回可用信息（code=1）

#### 使用场景
- 在启动聊天室任务前进行预检查
- 防止在任务学习过程中的并发操作

## 🔍 roomName查询功能详细说明

### 功能特性
- **查询类型**: 模糊查询，使用SQL LIKE '%keyword%'匹配
- **大小写**: 区分大小写（取决于数据库配置）
- **参数状态**: 可选参数，不传递时不影响查询结果
- **组合查询**: 可与其他查询条件（receptionSkin、sceneMode等）组合使用

### roomName查询示例

#### 1. 单独按名称查询
```bash
# 查询名称中包含"电商"的聊天室
curl -X GET "http://localhost:8081/api/chatrooms?roomName=电商&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 2. 组合查询示例
```bash
# 查询名称包含"电商"且皮肤为"千牛"的聊天室
curl -X GET "http://localhost:8081/api/chatrooms?roomName=电商&receptionSkin=qianniu_skin&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"

# 查询名称包含"客服"且场景模式为压力考核的聊天室
curl -X GET "http://localhost:8081/api/chatrooms?roomName=客服&sceneMode=1&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 3. 我的任务中按名称查询
```bash
# 在我的任务中查询名称包含"培训"的聊天室
curl -X GET "http://localhost:8081/api/chatrooms/my-tasks?roomName=培训&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token"
```

### 查询结果说明
- **匹配规则**: 名称中包含关键词的聊天室都会被返回
- **排序**: 按创建时间降序排列（最新创建的在前）
- **分页**: 支持标准分页，通过page和pageSize控制
- **空结果**: 如果没有匹配的聊天室，返回总数为0的分页结果

### Vue.js前端调用示例
```javascript
// 按名称查询聊天室
async getChatroomsByName(roomName, page = 1, pageSize = 10) {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString()
    });
    
    if (roomName && roomName.trim()) {
      params.append('roomName', roomName.trim());
    }
    
    const response = await axios.get(`/api/chatrooms?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('查询聊天室失败:', error);
    throw error;
  }
}

// 组合查询示例
async getChatroomsWithFilters(filters) {
  const params = new URLSearchParams({
    page: (filters.page || 1).toString(),
    pageSize: (filters.pageSize || 10).toString()
  });
  
  // 添加可选查询条件
  if (filters.roomName && filters.roomName.trim()) {
    params.append('roomName', filters.roomName.trim());
  }
  if (filters.receptionSkin) {
    params.append('receptionSkin', filters.receptionSkin);
  }
  if (filters.sceneMode !== null && filters.sceneMode !== undefined) {
    params.append('sceneMode', filters.sceneMode.toString());
  }
  
  const response = await axios.get(`/api/chatrooms?${params.toString()}`, {
    headers: {
      'Authorization': `Bearer ${this.getToken()}`,
      'Content-Type': 'application/json'
    }
  });
  
  return response.data;
}
```

### 注意事项
1. **空字符串处理**: 传递空字符串会被后端自动转换为null，不影响查询
2. **特殊字符**: 查询关键词中的特殊字符会被当作普通字符处理
3. **性能考虑**: 建议前端在用户停止输入后进行查询，避免频繁请求
4. **权限控制**: 查询结果会自动按团队ID隔离，确保数据安全
- 为前端提供友好的状态提示