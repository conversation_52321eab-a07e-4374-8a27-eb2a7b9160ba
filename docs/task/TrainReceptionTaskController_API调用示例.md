# TrainReceptionTaskController API 调用示例

本文档提供 `TrainReceptionTaskController` 所有接口的 curl 调用示例，供前端 Vue 开发参考。

## 基础信息

- **Base URL**: `http://localhost:8081/api/reception-tasks`
- **认证方式**: Bearer <PERSON> (JWT)
- **Content-Type**: `application/json`

## 通用请求头

所有请求都需要包含以下请求头：

```bash
-H "Content-Type: application/json" \
-H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 1. 分页查询接待任务列表

### 接口信息
- **方法**: GET
- **路径**: `/api/reception-tasks`
- **描述**: 支持按任务名称、任务模式、任务类型等条件查询

### 请求示例

#### 基础查询（无过滤条件）
```bash
curl -X GET "http://localhost:8081/api/reception-tasks?page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 带过滤条件的查询
```bash
curl -X GET "http://localhost:8081/api/reception-tasks?taskMode=1&taskType=0&taskName=商品知识&taskPurposeTag=0&judgeType=0&page=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 查询参数说明
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskMode | Integer | 否 | 任务模式：0-原版案例，1-AI智训，2-AI智训玩法 | 1 |
| taskType | Integer | 否 | 任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务 | 0 |
| taskName | String | 否 | 任务名称（模糊查询） | "商品知识" |
| scriptId | Long | 否 | 剧本ID | 1 |
| taskPurposeTag | Integer | 否 | 任务标签：0-训练，1-考核，2-面试，3-其他 | 0 |
| judgeType | Integer | 否 | 打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打 | 0 |
| page | Integer | 否 | 页码，从1开始 | 1 |
| pageSize | Integer | 否 | 每页大小 | 10 |

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "taskMode": 1,
        "taskModeName": "AI智训",
        "taskType": 0,
        "taskTypeName": "商品知识训练",
        "taskName": "商品知识训练任务",
        "taskDescription": "针对新员工的商品知识培训",
        "scriptId": 1,
        "scriptName": "商品咨询剧本",
        "receptionDuration": 30,
        "taskPurposeTag": 0,
        "taskPurposeTagName": "训练",
        "judgeType": 0,
        "judgeTypeName": "单条会话打分",
        "convKbId": 1,
        "convKbIdValue": "商品知识库模板",
        "qaMainId": 1,
        "qaMainName": "商品问答知识库",
        "learningStatus": "un_learn",
        "learningStatusCode": "un_learn",
        "learningStatusValue": "未学习",
        "learningProgressPercent": "0%",
        "amtToBeLearned": 100,
        "amtHasLearned": 0,
        "isShowResolve": false,
        "isShowCorrect": true,
        "freqAuesCnt": 15,
        "isShowInspect": false,
        "srvSendCd": 30,
        "createTime": "2025-07-24T10:30:00",
        "updateTime": "2025-07-24T10:30:00",
        "creator": "admin",
        "updater": "admin"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10
  }
}
```

### 响应字段说明
| 字段名                    | 类型      | 说明                  | 示例值                   |
|-----------------------|---------|--------------------|------------------------|
| id                    | Long    | 任务ID               | 1                      |
| taskMode              | Integer | 任务模式代码             | 1                      |
| taskModeName          | String  | 任务模式名称             | "AI智训"                 |
| taskType              | Integer | 任务类型代码             | 0                      |
| taskTypeName          | String  | 任务类型名称             | "商品知识训练"              |
| taskName              | String  | 任务名称               | "商品知识训练任务"            |
| taskDescription       | String  | 任务描述               | "针对新员工的商品知识培训"        |
| scriptId              | Long    | 剧本ID               | 1                      |
| scriptName            | String  | 剧本名称               | "商品咨询剧本"              |
| receptionDuration     | Integer | 接待时长（分钟）           | 30                     |
| taskPurposeTag        | Integer | 任务标签代码             | 0                      |
| taskPurposeTagName    | String  | 任务标签名称             | "训练"                   |
| judgeType             | Integer | 打分响应代码             | 0                      |
| judgeTypeName         | String  | 打分响应名称             | "单条会话打分"              |
| convKbId              | Long    | 关联的知识库模板ID         | 1                      |
| convKbIdValue         | String  | 关联的知识库模板名称         | "商品知识库模板"             |
| qaMainId              | Long    | 高频知识库ID            | 1                      |
| qaMainName            | String  | 高频知识库名称            | "商品问答知识库"             |
| learningStatus        | String  | 学习状态               | "un_learn"             |
| learningStatusCode    | String  | 学习状态代码             | "un_learn"             |
| learningStatusValue   | String  | 学习状态中文描述           | "未学习"                 |
| learningProgressPercent | String | 学习完成进度百分比（带%符号）          | "0%"                      |
| amtToBeLearned        | Long    | 待学习的总条数            | 100                    |
| amtHasLearned         | Long    | 目前已经学习条数           | 0                      |
| isShowResolve         | Boolean | 是否显示解析详情           | false                  |
| isShowCorrect         | Boolean | 是否显示正确答案           | true                   |
| freqAuesCnt           | Integer | 选取的知识库条数           | 15                     |
| srvSendCd             | Long    | 客服发送消息倒计时（秒）       | 30                     |
| createTime            | String  | 创建时间               | "2025-07-24T10:30:00"  |
| updateTime            | String  | 更新时间               | "2025-07-24T10:30:00"  |
| creator               | String  | 创建人                | "admin"                |
| updater               | String  | 更新人                | "admin"                |
```

## 2. 查询接待任务详情

### 接口信息
- **方法**: GET
- **路径**: `/api/reception-tasks/{id}`
- **描述**: 根据任务ID查询详细信息

### 请求示例
```bash
curl -X GET "http://localhost:8081/api/reception-tasks/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "id": 1,
    "taskMode": 1,
    "taskModeName": "AI智训",
    "taskType": 0,
    "taskTypeName": "商品知识训练",
    "taskName": "商品知识训练任务",
    "taskDescription": "针对新员工的商品知识培训",
    "scriptId": 1,
    "scriptName": "商品咨询剧本",
    "receptionDuration": 30,
    "questionIntervalType": 0,
    "questionIntervalTypeName": "随机",
    "questionIntervalSeconds": 3,
    "trainingLimitEnabled": false,
    "taskPurposeTag": 0,
    "taskPurposeTagName": "训练",
    "judgeType": 0,
    "judgeTypeName": "单条会话打分",
    "convKbId": 1,
    "qaMainId": 1,
    "qaMainName": "商品问答知识库",
    "learningStatus": "un_learn",
    "learningStatusCode": "un_learn",
    "learningStatusValue": "未学习",
    "learningProgressPercent": "0%",
    "amtToBeLearned": 100,
    "amtHasLearned": 0,
    "isShowResolve": false,
    "isShowCorrect": true,
    "freqAuesCnt": 15,
    "isShowInspect": false,
    "srvSendCd": 30,
    "teamId": 1,
    "createTime": "2025-07-24T10:30:00",
    "updateTime": "2025-07-24T10:30:00",
    "creator": "admin",
    "updater": "admin",
    "version": 1
  }
}
```

## 3. 创建接待任务

### 接口信息
- **方法**: POST
- **路径**: `/api/reception-tasks`
- **描述**: 创建新的接待任务，会自动创建知识库会话明细并启动异步模板学习

### 请求示例
```bash
curl -X POST "http://localhost:8081/api/reception-tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "taskMode": 1,
    "taskType": 0,
    "taskName": "商品知识训练任务",
    "taskDescription": "针对新员工的商品知识培训",
    "scriptId": 1,
    "receptionDuration": 30,
    "questionIntervalType": 0,
    "questionIntervalSeconds": 3,
    "trainingLimitEnabled": false,
    "taskPurposeTag": 0,
    "judgeType": 0,
    "convKbId": 1,
    "qaMainId": 1,
    "amtToBeLearned": 100,
    "amtHasLearned": 0,
    "isShowResolve": false,
    "isShowCorrect": true,
    "freqAuesCnt": 15,
    "isShowInspect": false,
    "learningStatus": "un_learn",
    "srvSendCd": 30
  }'
```

### 请求体字段说明
| 字段名                     | 类型      | 必填 | 说明                                | 示例值                  |
|-------------------------|---------|----|-----------------------------------|----------------------|
| taskMode                | Integer | 是  | 任务模式：0-原版案例，1-AI智训，2-AI智训玩法       | 1                    |
| taskType                | Integer | 是  | 任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务   | 0                    |
| taskName                | String  | 是  | 接待任务名称                            | "商品知识训练任务"           |
| taskDescription         | String  | 否  | 任务描述                              | "针对新员工的商品知识培训"       |
| scriptId                | Long    | 是  | 剧本ID，来自train_script表的主键              | 1                    |
| receptionDuration       | Integer | 是  | 接待时长（分钟）                          | 30                   |
| questionIntervalType    | Integer | 否  | 提问间隔类型：0-随机，1-固定                  | 0                    |
| questionIntervalSeconds | Integer | 否  | 提问间隔秒数                            | 3                    |
| trainingLimitEnabled    | Boolean | 否  | 训练次数限制：false-关，true-开             | false                |
| taskPurposeTag          | Integer | 否  | 任务标签：0-训练，1-考核，2-面试，3-其他          | 0                    |
| judgeType               | Integer | 否  | 打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打 | 0                    |
| convKbId                | Long    | 是  | 关联的知识库模板ID，必须大于0                  | 1                    |
| qaMainId                | Long    | 否  | 高频知识库ID，关联train_qa_import_main表主键    | 1                    |
| learningStatus          | String  | 否  | 学习状态：un_learn-未学习，learning-学习中，learned-已学习 | "un_learn"           |
| amtToBeLearned          | Long    | 否  | 待学习的总条数                           | 100                  |
| amtHasLearned           | Long    | 否  | 目前已经学习条数                          | 0                    |
| isShowResolve           | Boolean | 否  | 是否显示解析详情：false-不显示，true-显示        | false                |
| isShowCorrect           | Boolean | 否  | 是否显示正确答案：false-不显示，true-显示        | true                 |
| freqAuesCnt             | Integer | 否  | 选取的知识库条数，不能为负数                   | 15                   |
| isShowInspect           | Boolean | 否  | 是否显示考察点：false-不显示，true-显示          | false                |
| srvSendCd               | Long    | 否  | 客服发送消息倒计时（秒）                     | 30                   |

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": 1
}
```

## 4. 更新接待任务

### 接口信息
- **方法**: PUT
- **路径**: `/api/reception-tasks/{id}`
- **描述**: 更新指定ID的接待任务

### 请求示例
```bash
curl -X PUT "http://localhost:8081/api/reception-tasks/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "taskName": "更新后的任务名称",
    "taskDescription": "更新后的任务描述",
    "scriptId": 2,
    "receptionDuration": 45,
    "taskPurposeTag": 1,
    "convKbId": 5,
    "qaMainId": 2,
    "learningStatus": "learning",
    "amtHasLearned": 30,
    "isShowResolve": true,
    "isShowCorrect": false,
    "freqAuesCnt": 25,
    "isShowInspect": true,
    "srvSendCd": 45
  }'
```

### 请求体字段说明
更新请求的字段与创建请求相同。其中scriptId和convKbId是必填字段，其他字段都是可选的。只需要传递需要更新的字段。新增的学习状态相关字段（learningStatus、amtToBeLearned、amtHasLearned、srvSendCd）、高频知识库字段（qaMainId）以及新增的显示控制字段（isShowResolve、isShowCorrect、freqAuesCnt）也支持更新。

**新增字段说明**：
- **isShowResolve**：Boolean类型，控制是否显示解析详情，用于前端界面显示控制
- **isShowCorrect**：Boolean类型，控制是否显示正确答案，用于前端界面显示控制  
- **freqAuesCnt**：Integer类型，选取的知识库条数，用于控制从知识库中选取多少条数据进行训练，不能为负数

**srvSendCd字段说明**：
- 类型：Long
- 单位：秒
- 用途：设置客服发送消息的倒计时时间
- 应用场景：在模拟聊天中控制客服响应的延迟时间

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": true
}
```

## 5. 删除接待任务

### 接口信息
- **方法**: DELETE
- **路径**: `/api/reception-tasks/{id}`
- **描述**: 删除指定ID的接待任务

### 请求示例
```bash
curl -X DELETE "http://localhost:8081/api/reception-tasks/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": true
}
```

## 6. 批量删除接待任务

### 接口信息
- **方法**: DELETE
- **路径**: `/api/reception-tasks`
- **描述**: 批量删除指定ID的接待任务

### 请求示例
```bash
curl -X DELETE "http://localhost:8081/api/reception-tasks?ids=1,2,3" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 查询参数说明
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| ids | String | 是 | 任务ID列表，逗号分隔 | "1,2,3" |

### 响应示例
```json
{
  "code": 1,
  "message": "操作成功",
  "data": true
}
```

## 错误响应示例

### 认证失败
```json
{
  "code": 0,
  "message": "Authorization header is missing or invalid",
  "data": null
}
```

### 参数验证失败
```json
{
  "code": 0,
  "message": "任务名称不能为空",
  "data": null
}
```

### 资源不存在
```json
{
  "code": 0,
  "message": "任务不存在",
  "data": null
}
```

## Vue.js 前端调用示例

### 使用 axios 的示例代码

```javascript
// 1. 查询任务列表
const getTaskList = async (params) => {
  try {
    const response = await axios.get('/api/reception-tasks', {
      params: params,
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('查询任务列表失败:', error);
    throw error;
  }
};

// 2. 创建任务
const createTask = async (taskData) => {
  try {
    const response = await axios.post('/api/reception-tasks', taskData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('创建任务失败:', error);
    throw error;
  }
};

// 3. 更新任务
const updateTask = async (id, taskData) => {
  try {
    const response = await axios.put(`/api/reception-tasks/${id}`, taskData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('更新任务失败:', error);
    throw error;
  }
};

// 4. 删除任务
const deleteTask = async (id) => {
  try {
    const response = await axios.delete(`/api/reception-tasks/${id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('删除任务失败:', error);
    throw error;
  }
};

// 5. 创建带有高频知识库的任务示例
const createTaskWithQaMain = async () => {
  const taskData = {
    taskMode: 1,
    taskType: 0,
    taskName: "商品知识训练任务",
    taskDescription: "针对新员工的商品知识培训",
    scriptId: 1,
    receptionDuration: 30,
    questionIntervalType: 0,
    questionIntervalSeconds: 3,
    trainingLimitEnabled: false,
    taskPurposeTag: 0,
    judgeType: 0,
    convKbId: 1,
    qaMainId: 1, // 高频知识库ID
    amtToBeLearned: 100,
    amtHasLearned: 0,
    isShowResolve: false,
    isShowCorrect: true,
    freqAuesCnt: 15,
    isShowInspect: false,
    learningStatus: "un_learn",
    srvSendCd: 30
  };

  return await createTask(taskData);
};
```

## 注意事项

1. **认证**: 所有接口都需要在请求头中携带有效的 JWT Token
2. **分页**: 查询列表接口支持分页，页码从1开始
3. **过滤**: 查询接口支持多种过滤条件，可以组合使用
4. **验证**: 创建和更新接口会进行参数验证，必填字段不能为空
5. **团队隔离**: 所有操作都基于JWT中的团队ID进行数据隔离
6. **版本控制**: 更新操作使用乐观锁机制，避免并发冲突
7. **知识库关联**: convKbId字段现在是必填字段，必须关联一个有效的知识库模板ID（必须大于0）
8. **剧本关联**: scriptId字段现在是必填字段，必须关联一个有效的剧本ID
9. **学习状态管理**: 新增learningStatus、amtToBeLearned、amtHasLearned字段支持学习进度跟踪
10. **异步处理**: 创建任务时会自动批量插入知识库会话明细并启动异步模板学习
11. **自动化功能**: 创建任务后系统会自动创建train_task_conv_kb_dtl明细记录和启动多线程学习处理
12. **learningProgressPercent字段类型**: 该字段类型为String且包含%符号（如"75%"），前端可直接显示无需额外处理

## 数据库关联关系

### 接待任务表关联
- **train_reception_task**: 接待任务主表
- **train_script**: 剧本表（通过scriptId关联）
- **train_kb_tpl_main**: 知识库模板主表（通过convKbId关联）
- **train_qa_import_main**: 高频知识库主表（通过qaMainId关联）
- **train_task_conv_kb_dtl**: 任务知识库会话明细表（任务创建时自动生成）

### 字段更新历史
- **convKbId**: 2025-08-10新增字段，用于支持接待任务与知识库模板的关联功能，现为必填字段
- **convKbIdValue**: 2025-08-10新增字段，用于显示关联知识库模板的名称
- **learningStatus**: 2025-08-12新增字段，学习状态管理
- **amtToBeLearned**: 2025-08-12新增字段，待学习总条数
- **amtHasLearned**: 2025-08-12新增字段，已学习条数
- **learningStatusCode**: 2025-08-12新增字段，学习状态代码
- **learningStatusValue**: 2025-08-12新增字段，学习状态中文描述
- **learningProgressPercent**: 2025-08-12新增字段，学习进度百分比（带%符号）；2025-08-13修复字段类型从Integer改为String以支持%符号显示
- **srvSendCd**: 2025-08-18新增字段，客服发送消息倒计时（秒），用于控制模拟聊天中客服响应的延迟时间
- **qaMainId**: 2025-08-27新增字段，高频知识库ID，关联train_qa_import_main表主键
- **qaMainName**: 2025-08-27新增字段，高频知识库名称，对应train_qa_import_main.qa_im_name字段
- **isShowResolve**: 2025-08-29新增字段，是否显示解析详情，Boolean类型，用于前端界面显示控制
- **isShowCorrect**: 2025-08-29新增字段，是否显示正确答案，Boolean类型，用于前端界面显示控制
- **freqAuesCnt**: 2025-08-29新增字段，选取的知识库条数，Integer类型，用于控制从知识库中选取多少条数据进行训练
- **isShowInspect**: 2025-09-03新增字段，是否显示考察点，Boolean类型，用于前端界面显示控制

## 枚举值参考

### 任务模式 (taskMode)
- 0: 原版案例
- 1: AI智训
- 2: AI智训玩法

### 任务类型 (taskType)
- 0: 商品知识训练
- 1: 实战进阶任务
- 2: 综合训练任务

### 任务标签 (taskPurposeTag)
- 0: 训练
- 1: 考核
- 2: 面试
- 3: 其他

### 打分响应 (judgeType)
- 0: 单条会话打分
- 1: 会话结束打分
- 2: 单条、结束都要打

### 提问间隔类型 (questionIntervalType)
- 0: 随机
- 1: 固定

### 学习状态 (learningStatus)
- un_learn: 未学习
- learning: 学习中
- learned: 已学习

## 业务流程说明

### 创建任务的自动化流程
1. **创建任务记录**: 在train_reception_task表中创建任务记录
2. **批量生成明细**: 根据convKbId从train_kb_tpl_detail获取所有明细，批量插入到train_task_conv_kb_dtl表
3. **设置学习状态**: 所有明细初始状态为UN_LEARN，f1st_raw_chatlog字段填入知识库内容
4. **启动异步学习**: 调用templateLearnService.startTemplateLearning开始多线程学习处理
5. **更新学习进度**: 异步过程中会更新amtHasLearned字段和learningStatus状态

### 学习进度计算
- **learningProgressPercent** = "(train_task_conv_kb_dtl表中learning_status='learned'的数量 / amtToBeLearned) * 100%"
- 计算逻辑：先从train_task_conv_kb_dtl表中统计指定taskId下learning_status='learned'的记录数量，然后除以amtToBeLearned得出百分比
- 防止除零错误，当amtToBeLearned为0时返回"0%"
- SQL实现：`SELECT COUNT(*) FROM train_task_conv_kb_dtl WHERE task_id = ? AND learning_status = 'learned'`
