# 多租户拦截器使用文档

## 概述

基于MyBatis拦截器实现的多租户数据隔离功能，通过`@TenantFilter`注解控制，自动在SQL中添加`team_id`和`creator`条件，实现租户和用户级别的数据隔离。

## 功能特性

- ✅ **零侵入性**：不需要修改现有业务代码
- ✅ **灵活配置**：支持3种参数来源的任意组合
- ✅ **高性能**：方法元数据缓存，避免重复解析
- ✅ **异常安全**：拦截器异常不影响业务执行
- ✅ **可观测性**：完整的日志记录和监控

## 依赖配置

已在`pom.xml`中添加JSQLParser依赖：

```xml
<dependency>
    <groupId>com.github.jsqlparser</groupId>
    <artifactId>jsqlparser</artifactId>
    <version>5.0</version>
</dependency>
```

## 配置参数

在`application.yml`中配置：

```yaml
tenant:
  filter:
    enabled: true                    # 多租户拦截器开关，默认启用
    cache-method-metadata: true      # 缓存方法元数据提升性能，默认启用  
    log-sql: true                    # 是否记录修改后的SQL，默认启用
```

## 使用方式

### 1. 基础用法（从SecurityContext获取）

```java
@Mapper
public interface TrainStaffMapper {
    
    // 默认配置：从SecurityUtil获取teamId和userId
    @TenantFilter
    List<TrainStaff> selectPageList(@Param("username") String username, 
                                   @Param("offset") int offset, 
                                   @Param("limit") int limit);
}
```

**效果**：
- 原始SQL：`SELECT * FROM train_staff WHERE username LIKE '%test%'`
- 修改后SQL：`SELECT * FROM train_staff WHERE username LIKE '%test%' AND team_id = 123 AND creator = 'testuser'`

### 2. 从方法参数获取

```java
@TenantFilter(
    teamIdSource = TenantFilter.TeamIdSource.PARAM,
    teamIdParamName = "teamId",
    creatorSource = TenantFilter.CreatorSource.PARAM, 
    creatorParamName = "userId"
)
int insertStaff(@Param("staff") TrainStaff staff, 
               @Param("teamId") Long teamId,
               @Param("userId") Long userId);
```

### 3. 从实体对象获取

```java
@TenantFilter(
    teamIdSource = TenantFilter.TeamIdSource.ENTITY,
    creatorSource = TenantFilter.CreatorSource.ENTITY,
    creatorEntityField = "creator"
)
int updateStaff(TrainStaff staff);
```

### 4. 混合模式

```java
// teamId从SecurityUtil，creator从参数
@TenantFilter(
    creatorSource = TenantFilter.CreatorSource.PARAM,
    creatorParamName = "operatorId"
)
int deleteStaff(@Param("id") Long id, @Param("operatorId") Long operatorId);
```

### 5. 部分过滤

```java
// 只过滤team_id
@TenantFilter(includeCreator = false)
List<TrainStaff> selectByTeam();

// 只过滤creator
@TenantFilter(includeTeamId = false)
List<TrainStaff> selectByCreator();
```

### 6. 跳过拦截

```java
// 完全跳过拦截（如登录接口）
@TenantFilter(enable = false)
TrainStaff selectByUsernameForLogin(@Param("username") String username);
```

## 注解参数详解

### @TenantFilter注解参数

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| enable | boolean | true | 是否启用租户过滤 |
| includeTeamId | boolean | true | 是否包含team_id条件 |
| includeCreator | boolean | true | 是否包含creator条件 |
| teamIdSource | TeamIdSource | SECURITY_CONTEXT | team_id数据来源 |
| teamIdParamName | String | "teamId" | 参数来源时的参数名 |
| creatorSource | CreatorSource | SECURITY_CONTEXT | creator数据来源 |
| creatorParamName | String | "creator" | 参数来源时的参数名 |
| creatorEntityField | String | "creator" | 实体来源时的字段名 |

### 数据源枚举

**TeamIdSource / CreatorSource**：
- `SECURITY_CONTEXT`：从SecurityUtil获取
- `PARAM`：从方法参数获取
- `ENTITY`：从实体对象获取

## 实际应用示例

基于项目中的`TrainStaffMapper`，已添加的注解示例：

```java
// 删除员工 - 只按team_id过滤，不按creator过滤
@TenantFilter(includeCreator = false)
int deleteById(Long id);

// 批量删除 - teamId从参数获取
@TenantFilter(
    teamIdSource = TenantFilter.TeamIdSource.PARAM,
    teamIdParamName = "teamId", 
    includeCreator = false
)
int deleteBatch(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);

// 更新员工 - teamId从实体获取
@TenantFilter(
    teamIdSource = TenantFilter.TeamIdSource.ENTITY,
    includeCreator = false
)
int updateById(TrainStaff staff);

// 登录查询 - 跳过拦截
@TenantFilter(enable = false)
TrainStaff selectByUsernameForLogin(@Param("username") String username);
```

## 日志监控

启用`tenant.filter.log-sql=true`时，可在日志中看到：

```
2025-08-30 11:33:01.621 [main] INFO  c.y.a.i.TenantInterceptor - 租户拦截器生效 [com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper.selectPageList]: SELECT * FROM train_staff WHERE username LIKE '%test%' -> SELECT * FROM train_staff WHERE username LIKE '%test%' AND team_id = 123 AND creator = 'testuser'
```

## 支持的SQL类型

- ✅ SELECT语句
- ✅ UPDATE语句  
- ✅ DELETE语句
- ❌ INSERT语句（通常不需要过滤）
- ❌ DDL语句（CREATE、ALTER等）

## 最佳实践

### 1. 注解使用建议

- **查询操作**：通常只需要`team_id`过滤，设置`includeCreator = false`
- **删除操作**：建议只按`team_id`过滤，避免权限问题
- **更新操作**：如果实体包含`teamId`，使用`ENTITY`源更方便
- **登录相关**：必须设置`enable = false`跳过拦截

### 2. 性能优化

- 启用方法元数据缓存：`cache-method-metadata: true`
- 合理设计数据库索引，确保`team_id`和`creator`字段有索引
- 对于高频查询，考虑复合索引：`(team_id, creator, other_fields)`

### 3. 异常处理

拦截器设计为"异常安全"，即使SQL解析失败也不会影响业务：

```java
} catch (Exception e) {
    log.error("租户拦截器处理异常", e);
    return invocation.proceed(); // 继续执行原始SQL
}
```

### 4. 测试验证

运行单元测试验证功能：

```bash
mvn test -Dtest=TenantInterceptorTest -Dspring.profiles.active=home
```

## 注意事项

1. **数据库表要求**：被拦截的表必须包含`team_id`和`creator`字段
2. **参数名匹配**：使用`@Param`注解时，参数名必须与配置的参数名一致
3. **实体字段**：实体对象必须有对应的字段和getter方法
4. **SecurityUtil依赖**：确保`SecurityUtil.getCurrentTeamId()`和`getCurrentUsername()`方法正常工作
5. **性能影响**：SQL解析会有轻微性能开销，但通过缓存可以最小化影响

## 故障排除

### 1. 拦截器不生效

检查：
- 是否添加了`@TenantFilter`注解
- 配置文件中`tenant.filter.enabled`是否为`true`
- 方法参数和实体字段是否正确

### 2. SQL语法错误

检查：
- 表是否包含`team_id`和`creator`字段
- JSQLParser是否能正确解析原始SQL
- 查看日志中的错误信息

### 3. 参数获取失败

检查：
- `@Param`注解的参数名是否匹配
- 实体对象是否有对应的字段
- SecurityUtil方法是否返回有效值

通过以上配置和使用方式，可以实现灵活的多租户数据隔离功能。