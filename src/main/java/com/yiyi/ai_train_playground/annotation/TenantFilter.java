package com.yiyi.ai_train_playground.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 多租户数据过滤注解
 * 用于标记需要自动添加租户隔离条件的Mapper方法
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantFilter {
    
    /**
     * 是否启用租户过滤，默认启用
     */
    boolean enable() default true;
    
    /**
     * 是否包含team_id条件，默认包含
     */
    boolean includeTeamId() default true;
    
    /**
     * team_id的数据来源
     */
    TeamIdSource teamIdSource() default TeamIdSource.SECURITY_CONTEXT;
    
    /**
     * 当teamIdSource为PARAM时，指定参数名称
     */
    String teamIdParamName() default "teamId";
    
    /**
     * 是否包含creator条件，默认包含
     */
    boolean includeCreator() default true;
    
    /**
     * creator的数据来源
     */
    CreatorSource creatorSource() default CreatorSource.SECURITY_CONTEXT;
    
    /**
     * 当creatorSource为PARAM时，指定参数名称
     */
    String creatorParamName() default "creator";
    
    /**
     * 当creatorSource为ENTITY时，指定实体字段名称
     */
    String creatorEntityField() default "creator";
    
    /**
     * Team ID数据源枚举
     */
    enum TeamIdSource {
        /**
         * 从SecurityContext获取，调用SecurityUtil.getCurrentTeamId()
         */
        SECURITY_CONTEXT,
        
        /**
         * 从方法参数获取，需指定teamIdParamName
         */
        PARAM,
        
        /**
         * 从实体对象获取teamId字段
         */
        ENTITY
    }
    
    /**
     * Creator数据源枚举
     */
    enum CreatorSource {
        /**
         * 从SecurityContext获取，调用SecurityUtil.getCurrentUsername()
         */
        SECURITY_CONTEXT,
        
        /**
         * 从方法参数获取，需指定creatorParamName
         */
        PARAM,
        
        /**
         * 从实体对象获取，需指定creatorEntityField
         */
        ENTITY
    }
}