package com.yiyi.ai_train_playground.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
@Slf4j
public class LogAspect {
    private final ObjectMapper objectMapper;

    public LogAspect(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Around("execution(* com.yiyi.ai_train_playground.controller..*.*(..))")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();

        // 检查是否为WebSocket请求
        boolean isWebSocketRequest = className.contains("WebSocket");
        
        // 记录请求信息
        log.info("开始调用 - {}#{}", className, methodName);
        
        if (!isWebSocketRequest) {
            // 只有非WebSocket请求才获取HTTP请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                log.info("请求URL: {}", request.getRequestURL().toString());
                log.info("请求方法: {}", request.getMethod());
            }
        } else {
            log.info("WebSocket消息处理");
        }
        
        // 处理请求参数
        Object[] args = joinPoint.getArgs();
        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) args[i];
                params.put("file" + i, String.format("文件名: %s, 大小: %d bytes", file.getOriginalFilename(), file.getSize()));
            } else if (args[i] instanceof MultipartFile[]) {
                MultipartFile[] files = (MultipartFile[]) args[i];
                Map<String, String> fileInfos = new HashMap<>();
                for (int j = 0; j < files.length; j++) {
                    MultipartFile file = files[j];
                    fileInfos.put("file" + j, String.format("文件名: %s, 大小: %d bytes", file.getOriginalFilename(), file.getSize()));
                }
                params.put("files", fileInfos);
            } else if (args[i] instanceof HttpServletRequest) {
                params.put("request", "HttpServletRequest");
            } else {
                params.put("arg" + i, args[i]);
            }
        }
        log.info("请求参数: {}", objectMapper.writeValueAsString(params));

        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            log.info("结束调用 - {}#{}, 耗时: {}ms", className, methodName, (endTime - startTime));
            log.info("返回结果: {}", objectMapper.writeValueAsString(result));
            return result;
        } catch (Exception e) {
            log.error("调用异常 - {}#{}", className, methodName, e);
            throw e;
        }
    }
} 