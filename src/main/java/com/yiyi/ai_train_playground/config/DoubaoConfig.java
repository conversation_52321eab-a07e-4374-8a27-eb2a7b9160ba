package com.yiyi.ai_train_playground.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "my.doubao")
public class DoubaoConfig {
    private Think think;
    private Normal normal;
    private String url;
    private Image image;
    private Integer estimateToken;
    private Embed embed;
    private ConnectionPool connectionPool;
    private Db16Normal db16Normal;
    private Integer ctxExpire = 604800; // 上下文过期时间，默认604800秒（7天）
    private Double temperature = 0.3; // 模型温度，默认0.3
    private Double topP = 0.9; // top-p采样参数，默认0.9

    /**
     * 获取estimateToken，适配不同的字段名称
     * @return 估算的token数量
     */
    public Integer getEstimateToken() {
        return estimateToken;
    }

    @Data
    public static class Think {
        private Model model;
    }

    @Data
    public static class Normal {
        private Model model;
        private Endpoint endpoint;
    }

    @Data
    public static class Image {
        private Model model;
    }
    
    @Data
    public static class Embed {
        private String modelName;
    }

    @Data
    public static class Model {
        private String name;
    }

    @Data
    public static class Endpoint {
        private String name;
    }
    
    @Data
    public static class ConnectionPool {
        private Integer maxIdle = 100;
        private Integer maxRequests = 100;
        private Integer maxRequestsPerHost = 50;
        private Integer keepAliveDuration = 5; // 分钟
    }

    @Data
    public static class Db16Normal {
        private Model model;
    }
}