package com.yiyi.ai_train_playground.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 文件处理配置类
 */
@Component
@ConfigurationProperties(prefix = "my.file-processing")
public class FileProcessingConfig {
    
    /**
     * 文件切割大小（字节），默认20KB
     */
//    private Integer chunkSize = 20480;
    private Integer chunkSize = 12288;

    /**
     * 最大文件大小（字节），默认1MB
     */
    private Long maxFileSize = 1048576L;
    
    /**
     * 支持的文件格式
     */
    private String supportedFormats = "xlsx,xls,csv,txt";
    
    /**
     * 会话开始标志
     */
    private String conversationStartMarker = "以下为一通会话";
    
    /**
     * 会话结束标志
     */
    private String conversationEndMarker = "会话结束_";
    
    public Integer getChunkSize() {
        return chunkSize;
    }
    
    public void setChunkSize(Integer chunkSize) {
        this.chunkSize = chunkSize;
    }
    
    public Long getMaxFileSize() {
        return maxFileSize;
    }
    
    public void setMaxFileSize(Long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }
    
    public String getSupportedFormats() {
        return supportedFormats;
    }
    
    public void setSupportedFormats(String supportedFormats) {
        this.supportedFormats = supportedFormats;
    }
    
    public String getConversationStartMarker() {
        return conversationStartMarker;
    }
    
    public void setConversationStartMarker(String conversationStartMarker) {
        this.conversationStartMarker = conversationStartMarker;
    }
    
    public String getConversationEndMarker() {
        return conversationEndMarker;
    }
    
    public void setConversationEndMarker(String conversationEndMarker) {
        this.conversationEndMarker = conversationEndMarker;
    }
    
    /**
     * 获取支持的文件格式列表
     * @return 文件格式列表
     */
    public List<String> getSupportedFormatList() {
        return Arrays.asList(supportedFormats.split(","));
    }
    
    /**
     * 检查文件格式是否支持
     * @param fileExtension 文件扩展名
     * @return 是否支持
     */
    public boolean isSupportedFormat(String fileExtension) {
        if (fileExtension == null) {
            return false;
        }
        
        String extension = fileExtension.toLowerCase();
        if (extension.startsWith(".")) {
            extension = extension.substring(1);
        }
        
        return getSupportedFormatList().contains(extension);
    }
    
    /**
     * 检查文件大小是否超限
     * @param fileSize 文件大小
     * @return 是否超限
     */
    public boolean isFileSizeExceeded(long fileSize) {
        return fileSize > maxFileSize;
    }
    
    @Override
    public String toString() {
        return "FileProcessingConfig{" +
                "chunkSize=" + chunkSize +
                ", maxFileSize=" + maxFileSize +
                ", supportedFormats='" + supportedFormats + '\'' +
                ", conversationStartMarker='" + conversationStartMarker + '\'' +
                ", conversationEndMarker='" + conversationEndMarker + '\'' +
                '}';
    }
}
