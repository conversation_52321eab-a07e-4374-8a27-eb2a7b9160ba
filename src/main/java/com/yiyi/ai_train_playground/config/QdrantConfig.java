package com.yiyi.ai_train_playground.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.HttpHeaders;
import java.util.Base64;

@Slf4j
@Configuration
public class QdrantConfig {

    @Value("${qdrant.host:localhost}")
    private String host;

    @Value("${qdrant.http-port:6333}")
    private int httpPort;

    @Value("${qdrant.username:}")
    private String username;

    @Value("${qdrant.password:}")
    private String password;

    @Value("${qdrant.use-tls:false}")
    private boolean useTls;

    @Value("${qdrant.api-key:}")
    private String apiKey;

    @Bean
    public WebClient qdrantWebClient() {
        try {
            String protocol = useTls ? "https" : "http";
            String baseUrl = String.format("%s://%s:%d", protocol, host, httpPort);
            
            WebClient.Builder builder = WebClient.builder()
                    .baseUrl(baseUrl)
                    .defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            
            if (username != null && !username.trim().isEmpty() && 
                password != null && !password.trim().isEmpty()) {
                String auth = username + ":" + password;
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
                builder.defaultHeader(HttpHeaders.AUTHORIZATION, "Basic " + encodedAuth);
                log.info("Configured basic auth for user: {}", username);
            }
            
            if (apiKey != null && !apiKey.trim().isEmpty()) {
                builder.defaultHeader("api-key", apiKey);
                log.info("Configured API Key authentication");
            }
            
            WebClient client = builder.build();
            log.info("Qdrant WebClient initialized successfully: {}", baseUrl);
            return client;
        } catch (Exception e) {
            log.error("Failed to initialize Qdrant WebClient", e);
            throw new RuntimeException("Failed to initialize Qdrant WebClient", e);
        }
    }

    /**
     * Qdrant搜索配置类
     */
    @Data
    @Configuration
    @ConfigurationProperties(prefix = "qdrant.search")
    public static class QdrantSearchConfig {
        /**
         * 默认搜索数量限制
         */
        private int limit = 5;

        /**
         * 最小相似度得分阈值
         */
        private float minScore = 0.6f;
    }
} 