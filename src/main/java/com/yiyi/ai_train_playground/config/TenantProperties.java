package com.yiyi.ai_train_playground.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 多租户配置属性类
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "tenant")
public class TenantProperties {
    
    /**
     * 过滤器配置
     */
    private Filter filter = new Filter();
    
    @Data
    public static class Filter {
        /**
         * 是否启用多租户拦截器，默认启用
         */
        private boolean enabled = true;
        
        /**
         * 是否缓存方法元数据提升性能，默认启用
         */
        private boolean cacheMethodMetadata = true;
        
        /**
         * 是否记录修改后的SQL，默认启用
         */
        private boolean logSql = true;
    }
}