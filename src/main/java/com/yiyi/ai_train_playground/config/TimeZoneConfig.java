package com.yiyi.ai_train_playground.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;

import java.time.ZoneId;
import java.util.TimeZone;

/**
 * 时区配置类
 * 统一设置应用时区为Asia/Shanghai，解决时间显示问题
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-13
 */
@Slf4j
@Configuration
public class TimeZoneConfig implements ApplicationListener<ApplicationReadyEvent> {

    /**
     * 应用启动完成后设置默认时区
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 设置JVM默认时区为上海时区
        TimeZone.setDefault(TimeZone.getTimeZone(ZoneId.of("Asia/Shanghai")));
        
        log.info("✅ 应用时区设置完成");
        log.info("📍 JVM默认时区: {}", TimeZone.getDefault().getID());
        log.info("📍 系统默认时区: {}", ZoneId.systemDefault());
        log.info("📍 当前时间: {}", java.time.LocalDateTime.now());
    }
}