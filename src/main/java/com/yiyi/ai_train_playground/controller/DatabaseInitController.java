package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据库初始化控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/database")
@Tag(name = "数据库初始化", description = "数据库表初始化操作")
public class DatabaseInitController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 创建接待任务表
     */
    @PostMapping("/init-reception-task-table")
    @Operation(summary = "创建接待任务表", description = "初始化train_reception_task表")
    public Result<String> initTrainReceptionTaskTable() {
        try {
            log.info("开始创建接待任务表");
            
            String sql = """
                CREATE TABLE IF NOT EXISTS `train_reception_task` (
                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                  `task_mode` int NOT NULL DEFAULT '0' COMMENT '接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法',
                  `task_type` int NOT NULL DEFAULT '0' COMMENT '接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务',
                  `task_name` varchar(255) NOT NULL COMMENT '接待任务名称',
                  `task_description` text COMMENT '任务描述',
                  `script_id` bigint COMMENT '剧本ID，来自train_script表的主键，1对1映射',
                  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
                  `question_interval_type` int DEFAULT '0' COMMENT '顾客提问时间间隔：0-随机，1-固定',
                  `question_interval_seconds` int DEFAULT '3' COMMENT '提问间隔秒数',
                  `training_limit_enabled` tinyint(1) DEFAULT '0' COMMENT '训练次数限制：0-关，1-开',
                  `task_purpose_tag` int DEFAULT '0' COMMENT '任务标签：0-训练，1-考核，2-面试，3-其他',
                  `judge_type` int DEFAULT '0' COMMENT '打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打',
                  `team_id` bigint NOT NULL COMMENT '团队ID',
                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
                  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
                  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
                  PRIMARY KEY (`id`),
                  KEY `idx_team_id` (`team_id`),
                  KEY `idx_create_time` (`create_time`),
                  KEY `idx_task_type` (`task_type`),
                  KEY `idx_task_mode` (`task_mode`),
                  KEY `idx_script_id` (`script_id`),
                  KEY `idx_task_purpose_tag` (`task_purpose_tag`),
                  KEY `idx_judge_type` (`judge_type`),
                  CONSTRAINT `fk_reception_task_script` FOREIGN KEY (`script_id`) REFERENCES `train_script` (`id`) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待任务表'
                """;
            
            jdbcTemplate.execute(sql);
            
            log.info("接待任务表创建成功");
            return Result.success("接待任务表创建成功");
            
        } catch (Exception e) {
            log.error("创建接待任务表失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 创建快捷短语表
     */
    @PostMapping("/init-shortcut-phrases-table")
    @Operation(summary = "创建快捷短语表", description = "初始化train_shortcut_phrases表")
    public Result<String> initShortcutPhrasesTable() {
        try {
            log.info("开始创建快捷短语表");

            String sql = """
                CREATE TABLE IF NOT EXISTS `train_shortcut_phrases` (
                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                  `sp_group_id` bigint NOT NULL COMMENT '快捷短语分组表ID，来自于train_shortcut_phrases_group的主键',
                  `phrase_title` varchar(255) NOT NULL COMMENT '短语标题',
                  `phrase_content` text NOT NULL COMMENT '短语内容',
                  `phrase_type` int NOT NULL DEFAULT '0' COMMENT '短语类型：0-文本，1-图片，2-链接',
                  `usage_count` int NOT NULL DEFAULT '0' COMMENT '使用次数',
                  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
                  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序位置',
                  `tags` varchar(500) COMMENT '标签（逗号分隔）',
                  `team_id` bigint NOT NULL COMMENT '团队ID',
                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
                  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
                  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
                  PRIMARY KEY (`id`),
                  KEY `idx_sp_group_id` (`sp_group_id`),
                  KEY `idx_team_id` (`team_id`),
                  KEY `idx_create_time` (`create_time`),
                  KEY `idx_phrase_type` (`phrase_type`),
                  KEY `idx_is_active` (`is_active`),
                  KEY `idx_sort_order` (`sort_order`),
                  CONSTRAINT `fk_shortcut_phrases_group` FOREIGN KEY (`sp_group_id`) REFERENCES `train_shortcut_phrases_group` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷短语表'
                """;

            jdbcTemplate.execute(sql);

            log.info("快捷短语表创建成功");
            return Result.success("快捷短语表创建成功");

        } catch (Exception e) {
            log.error("创建快捷短语表失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }
}
