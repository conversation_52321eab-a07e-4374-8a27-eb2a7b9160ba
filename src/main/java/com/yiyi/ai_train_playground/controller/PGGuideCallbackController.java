package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.AiGuideUserResponse;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.service.staff.TrainStaffService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.ModelAndView;
import reactor.core.publisher.Mono;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * PG Guide项目对接控制器
 * 用于处理与ai_guide项目的用户认证和数据同步，跳转到Vue页面
 *
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@Controller
@RequestMapping("/pg-guide")
@RequiredArgsConstructor
@Tag(name = "PG Guide对接", description = "与ai_guide项目的用户认证和数据同步接口")
public class PGGuideCallbackController {
    
    private final TrainStaffService trainStaffService;
    private final JwtUtil jwtUtil;
    private final WebClient.Builder webClientBuilder;
    
    @Value("${ai-guide.base-url}")
    private String aiGuideBaseUrl;

    @Value("${ai-guide.callback-page-url}")
    private String callbackPageUrl;
    
    /**
     * 根据userId进行用户认证并跳转到Vue页面
     *
     * @param userId 用户ID
     * @return ModelAndView跳转到Vue页面
     */
    @GetMapping("/auth/{userId}")
    @Operation(summary = "用户认证", description = "根据userId查询员工信息并跳转到Vue页面")
    public ModelAndView authenticateUser(
            @PathVariable @Parameter(description = "用户ID") Long userId) {
        
        try {
            log.debug("开始处理用户认证: userId={}", userId);

            // 1. 先在train_staff表中查询
            TrainStaff existingStaff = trainStaffService.getStaffByUserId(userId);

            if (existingStaff != null) {
                // 1.1 如果存在，直接生成JWT并跳转到Vue页面
                log.debug("在train_staff表中找到员工: staffId={}, teamId={}",
                        existingStaff.getId(), existingStaff.getTeamId());

                String jwt = generateJwtToken(existingStaff);

                return createSuccessModelAndView(jwt, existingStaff.getId(), existingStaff.getTeamId(),
                        existingStaff.getUsername(), existingStaff.getDisplayName(), "existing_user");
            }
            
            // 1.2 如果不存在，调用ai_guide接口查询
            log.debug("在train_staff表中未找到员工，调用ai_guide接口查询: userId={}", userId);

            AiGuideUserResponse aiGuideResponse = callAiGuideUserApi(userId);

            if (aiGuideResponse == null || aiGuideResponse.getCode() != 1 || aiGuideResponse.getData() == null) {
                log.warn("ai_guide接口返回无效响应: userId={}, response={}", userId, aiGuideResponse);
                return createErrorModelAndView("用户不存在或ai_guide服务异常", "ai_guide_error");
            }

            // 创建新的train_staff记录
            TrainStaff newStaff = createStaffFromAiGuideResponse(aiGuideResponse.getData(), userId);
            int createResult = trainStaffService.createStaff(newStaff);

            if (createResult <= 0) {
                log.error("创建train_staff记录失败: userId={}", userId);
                return createErrorModelAndView("创建员工记录失败", "create_staff_error");
            }

            log.debug("成功创建train_staff记录: staffId={}, teamId={}",
                    newStaff.getId(), newStaff.getTeamId());

            // 生成JWT并跳转到Vue页面
            String jwt = generateJwtToken(newStaff);

            return createSuccessModelAndView(jwt, newStaff.getId(), newStaff.getTeamId(),
                    newStaff.getUsername(), newStaff.getDisplayName(), "new_user");

        } catch (Exception e) {
            log.error("用户认证失败: userId={}", userId, e);
            return createErrorModelAndView("认证失败: " + e.getMessage(), "system_error");
        }
    }
    
    /**
     * 调用ai_guide项目的用户查询接口
     */
    private AiGuideUserResponse callAiGuideUserApi(Long userId) {
        try {
            String url = aiGuideBaseUrl + "/api/user/" + userId;
            log.debug("调用ai_guide接口: {}", url);
            
            WebClient webClient = webClientBuilder.build();
            Mono<AiGuideUserResponse> responseMono = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(AiGuideUserResponse.class);
            
            AiGuideUserResponse response = responseMono.block();
            log.debug("ai_guide接口响应: userId={}, code={}", userId, 
                    response != null ? response.getCode() : null);
            
            return response;
            
        } catch (Exception e) {
            log.error("调用ai_guide接口失败: userId={}, url={}", userId, aiGuideBaseUrl, e);
            throw new RuntimeException("调用ai_guide接口失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据ai_guide响应创建TrainStaff对象
     */
    private TrainStaff createStaffFromAiGuideResponse(AiGuideUserResponse.UserData userData, Long userId) {
        TrainStaff staff = new TrainStaff();
        
        // 设置基本信息
        staff.setUsername(userData.getUsername());
        staff.setDisplayName(userData.getDisplayName() != null ? userData.getDisplayName() : userData.getUsername());
        staff.setTeamId(userData.getTeamId());
        staff.setFailedAttempts(userData.getFailedAttempts() != null ? userData.getFailedAttempts() : 0);
        staff.setIsLocked(userData.getIsLocked() != null ? userData.getIsLocked() : false);
        staff.setLockTime(userData.getLockTime());
        staff.setCreator(userData.getCreator() != null ? userData.getCreator() : "ai_guide_sync");
        staff.setUpdater(userData.getUpdater() != null ? userData.getUpdater() : "ai_guide_sync");
        
        // 重要：设置userId为ai_guide返回的id
        staff.setUserId(userData.getId());
        
        // 设置默认密码（由于是从ai_guide同步过来的，设置一个默认密码）
        staff.setPasswordHash("$2a$10$defaultPasswordHashFromAiGuideSync");
        
        log.debug("创建TrainStaff对象: username={}, teamId={}, userId={}", 
                staff.getUsername(), staff.getTeamId(), staff.getUserId());
        
        return staff;
    }
    
    /**
     * 生成JWT token
     */
    private String generateJwtToken(TrainStaff staff) {
        // 使用JwtUtil的方法生成包含staffId和teamId的JWT
        return jwtUtil.generateToken(staff.getId(), staff.getUsername(), staff.getTeamId(), false);
    }

    /**
     * 创建成功的ModelAndView，跳转到Vue页面
     */
    private ModelAndView createSuccessModelAndView(String token, Long staffId, Long teamId,
                                                   String username, String displayName, String userType) {
        try {
            // 构建跳转URL，将数据作为URL参数传递
            StringBuilder urlBuilder = new StringBuilder(callbackPageUrl);
            urlBuilder.append("?success=true");
            urlBuilder.append("&token=").append(URLEncoder.encode(token, StandardCharsets.UTF_8));
            urlBuilder.append("&staffId=").append(staffId);
            urlBuilder.append("&teamId=").append(teamId);
            urlBuilder.append("&username=").append(URLEncoder.encode(username, StandardCharsets.UTF_8));
            urlBuilder.append("&displayName=").append(URLEncoder.encode(displayName, StandardCharsets.UTF_8));
            urlBuilder.append("&userType=").append(userType);
            urlBuilder.append("&timestamp=").append(System.currentTimeMillis());

            log.debug("跳转到Vue页面: {}", urlBuilder.toString());

            // 使用redirect跳转到Vue页面
            ModelAndView modelAndView = new ModelAndView("redirect:" + urlBuilder.toString());
            return modelAndView;

        } catch (Exception e) {
            log.error("创建成功ModelAndView失败", e);
            return createErrorModelAndView("页面跳转失败", "redirect_error");
        }
    }

    /**
     * 创建错误的ModelAndView，跳转到Vue页面
     */
    private ModelAndView createErrorModelAndView(String errorMessage, String errorType) {
        try {
            // 构建错误跳转URL
            StringBuilder urlBuilder = new StringBuilder(callbackPageUrl);
            urlBuilder.append("?success=false");
            urlBuilder.append("&error=").append(URLEncoder.encode(errorMessage, StandardCharsets.UTF_8));
            urlBuilder.append("&errorType=").append(errorType);
            urlBuilder.append("&timestamp=").append(System.currentTimeMillis());

            log.debug("跳转到Vue错误页面: {}", urlBuilder.toString());

            // 使用redirect跳转到Vue页面
            ModelAndView modelAndView = new ModelAndView("redirect:" + urlBuilder.toString());
            return modelAndView;

        } catch (Exception e) {
            log.error("创建错误ModelAndView失败", e);
            // 如果连错误页面都无法跳转，返回一个简单的错误页面
            ModelAndView modelAndView = new ModelAndView("redirect:" + callbackPageUrl + "?success=false&error=system_error");
            return modelAndView;
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ResponseBody
    @Operation(summary = "健康检查", description = "检查PG Guide对接服务状态")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("aiGuideBaseUrl", aiGuideBaseUrl);
        health.put("callbackPageUrl", callbackPageUrl);
        health.put("timestamp", System.currentTimeMillis());

        return Result.success(health);
    }
}
