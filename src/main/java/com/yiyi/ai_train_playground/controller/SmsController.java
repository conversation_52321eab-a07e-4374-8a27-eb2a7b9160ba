package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.SmsCodeRequest;
import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.dto.SmsVerifyRequest;
import com.yiyi.ai_train_playground.service.SmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 短信验证码控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/sms")
@RequiredArgsConstructor
@Validated
@Tag(name = "短信验证码", description = "短信验证码相关接口")
public class SmsController {
    
    private final SmsService smsService;
    
    /**
     * 发送短信验证码
     */
    @PostMapping("/code")
    @Operation(summary = "发送短信验证码", description = "向指定手机号发送验证码")
    public Result<SmsCodeResponse> sendCode(@Valid @RequestBody SmsCodeRequest request) {
        try {
            // 额外的手动验证，确保phone参数存在
            if (request == null) {
                log.warn("请求体为空");
                return Result.error("请求体不能为空");
            }
            
            if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                log.warn("手机号参数为空");
                return Result.error("手机号不能为空");
            }
            
            String phone = request.getPhone().trim();
            
            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                log.warn("手机号格式不正确: {}", phone);
                return Result.error("手机号格式不正确，请输入正确的中国大陆手机号");
            }
            
            log.info("收到发送短信验证码请求，手机号: {}", phone);
            SmsCodeResponse response = smsService.sendVerificationCode(phone);
            log.info("短信验证码发送成功，verificationKey: {}", response.getVerificationKey());
            return Result.success("发送成功", response);
        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 验证短信验证码并登录
     */
    @PostMapping("/verify")
    @Operation(summary = "验证短信验证码并登录", description = "验证短信验证码，验证成功后自动创建团队和用户并返回登录信息")
    public Result<SmsLoginResponse> verifyAndLogin(@Valid @RequestBody SmsVerifyRequest request) {
        try {
            // 额外的手动验证
            if (request == null) {
                log.warn("请求体为空");
                return Result.error("请求体不能为空");
            }
            
            if (request.getVerificationKey() == null || request.getVerificationKey().trim().isEmpty()) {
                log.warn("验证密钥为空");
                return Result.error("验证密钥不能为空");
            }
            
            if (request.getVerificationCode() == null || request.getVerificationCode().trim().isEmpty()) {
                log.warn("验证码为空");
                return Result.error("验证码不能为空");
            }
            
            String verificationKey = request.getVerificationKey().trim();
            String verificationCode = request.getVerificationCode().trim();
            
            // 验证验证码格式
            if (!verificationCode.matches("^\\d{4}$")) {
                log.warn("验证码格式不正确: {}", verificationCode);
                return Result.error("验证码必须是4位数字");
            }
            
            log.info("收到短信验证码验证请求，verificationKey: {}", verificationKey);
            
            // 从verificationKey中获取手机号（需要从Redis中获取）
            // 这里我们需要修改验证逻辑，让smsLogin方法处理完整的验证和登录流程
            SmsLoginResponse response = smsService.smsLogin(verificationKey, verificationCode, null);
            
            log.info("短信验证码验证成功，userId: {}, username: {}", response.getUserId(), response.getUsername());
            return Result.success("登录成功", response);
        } catch (Exception e) {
            log.error("短信验证码验证失败", e);
            return Result.error(e.getMessage());
        }
    }
} 