package com.yiyi.ai_train_playground.controller.converkb;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.converkb.FileImportResponse;
import com.yiyi.ai_train_playground.service.converkb.FileImportService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件导入控制器
 */
@RestController
@RequestMapping("/api/file-import")
@Tag(name = "文件导入管理", description = "文件导入和切割相关接口")
public class FileImportController {
    
    private static final Logger log = LoggerFactory.getLogger(FileImportController.class);
    
    @Autowired
    private FileImportService fileImportService;
    
    @PostMapping("/upload")
    @Operation(summary = "上传并导入文件", description = "上传文件并自动切割成块存储到数据库")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "导入成功", 
                    content = @Content(schema = @Schema(implementation = FileImportResponse.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<FileImportResponse> uploadFile(
            @Parameter(description = "上传的文件", required = true)
            @RequestParam("file") MultipartFile file,

            @Parameter(description = "文件描述", example = "客服对话记录文件")
            @RequestParam(value = "description", required = false) String description) {
        
        try {
            // 获取当前用户信息
            Long teamId = SecurityUtil.getCurrentTeamId();
            String creator = SecurityUtil.getCurrentUsername();
            
            log.info("开始处理文件上传，fileName: {}, fileSize: {}, teamId: {}, 创建人: {}",
                    file.getOriginalFilename(), file.getSize(), teamId, creator);

            // 执行文件导入
            FileImportResponse response = fileImportService.importFile(file, teamId, creator);
            
            // 检查是否有错误
            if (response.getErrorMessages() != null && !response.getErrorMessages().isEmpty()) {
                log.warn("文件导入有错误: {}", response.getErrorMessages());
                return Result.error("文件导入失败: " + String.join(", ", response.getErrorMessages()));
            }
            
            log.info("文件导入成功，总块数: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                    response.getTotalChunks(), response.getSuccessChunks(), 
                    response.getFailedChunks(), response.getProcessingTimeMs());
            
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("文件上传处理失败", e);
            return Result.error("文件上传处理失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/validate")
    @Operation(summary = "验证文件", description = "验证文件格式和大小是否符合要求")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证完成"),
        @ApiResponse(responseCode = "400", description = "文件验证失败")
    })
    public Result<String> validateFile(
            @Parameter(description = "要验证的文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始验证文件，fileName: {}, fileSize: {}", 
                    file.getOriginalFilename(), file.getSize());
            
            String validationError = fileImportService.validateFile(file);
            
            if (validationError != null) {
                log.warn("文件验证失败: {}", validationError);
                return Result.error(validationError);
            }
            
            log.info("文件验证通过");
            return Result.success("文件验证通过");
            
        } catch (Exception e) {
            log.error("文件验证异常", e);
            return Result.error("文件验证异常: " + e.getMessage());
        }
    }
    
    @GetMapping("/preview/{tplId}")
    @Operation(summary = "预览文件切割结果", description = "预览指定模板的文件切割结果")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "预览成功"),
        @ApiResponse(responseCode = "404", description = "模板不存在")
    })
    public Result<FileImportResponse> previewChunks(
            @Parameter(description = "模板ID", required = true, example = "1")
            @PathVariable("tplId") Long tplId) {
        
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            log.info("预览文件切割结果，tplId: {}, teamId: {}", tplId, teamId);
            
            // 这里可以实现预览逻辑，比如获取已存储的块信息
            // 暂时返回简单响应
            FileImportResponse response = new FileImportResponse();
            response.setTplId(tplId);
            
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("预览文件切割结果失败", e);
            return Result.error("预览失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/chunks/{tplId}")
    @Operation(summary = "清空模板的所有块", description = "删除指定模板的所有预处理块")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "清空成功"),
        @ApiResponse(responseCode = "404", description = "模板不存在")
    })
    public Result<Integer> clearChunks(
            @Parameter(description = "模板ID", required = true, example = "1")
            @PathVariable("tplId") Long tplId) {
        
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            log.info("清空模板的所有块，tplId: {}, teamId: {}", tplId, teamId);
            
            // 这里需要调用相应的服务方法来删除块
            // int deletedCount = trainKbTplPreService.deleteByTplId(tplId, teamId);
            int deletedCount = 0; // 临时返回
            
            log.info("清空完成，删除块数: {}", deletedCount);
            return Result.success(deletedCount);
            
        } catch (Exception e) {
            log.error("清空模板块失败", e);
            return Result.error("清空失败: " + e.getMessage());
        }
    }
}
