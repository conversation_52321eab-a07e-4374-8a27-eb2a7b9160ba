package com.yiyi.ai_train_playground.controller.converkb;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.converkb.QaImportResponse;
import com.yiyi.ai_train_playground.service.converkb.QaImportService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 问答导入控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/qa-import")
@Tag(name = "问答导入管理", description = "问答Excel导入相关接口")
public class QaImportController {
    
    @Autowired
    private QaImportService qaImportService;
    
    @PostMapping("/excel")
    @Operation(summary = "导入问答Excel", description = "上传Excel文件导入问答数据到数据库")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "导入成功", 
                    content = @Content(schema = @Schema(implementation = QaImportResponse.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<QaImportResponse> importQaExcel(
            @Parameter(description = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            // 获取当前用户信息
            Long teamId = SecurityUtil.getCurrentTeamId();
            String creator = SecurityUtil.getCurrentUsername();
            
            log.info("开始导入问答Excel，文件名: {}, 文件大小: {}, 团队ID: {}, 创建人: {}",
                    file.getOriginalFilename(), file.getSize(), teamId, creator);
            
            // 验证文件格式
            String validationError = qaImportService.validateQaExcel(file);
            if (validationError != null) {
                log.warn("问答Excel验证失败: {}", validationError);
                return Result.error(validationError);
            }
            
            // 执行导入
            QaImportResponse response = qaImportService.importQaExcel(file, teamId, creator);
            
            log.info("问答Excel导入完成，总数: {}, 成功: {}, 失败: {}", 
                    response.getTotalCount(), response.getSuccessCount(), response.getFailCount());
            
            return Result.success("导入完成", response);
            
        } catch (Exception e) {
            log.error("导入问答Excel失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/validate")
    @Operation(summary = "验证问答Excel格式", description = "验证上传的Excel文件格式是否正确")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证成功"),
        @ApiResponse(responseCode = "400", description = "验证失败")
    })
    public Result<String> validateQaExcel(
            @Parameter(description = "要验证的Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始验证问答Excel，文件名: {}, 文件大小: {}", 
                    file.getOriginalFilename(), file.getSize());
            
            String validationError = qaImportService.validateQaExcel(file);
            
            if (validationError != null) {
                log.warn("问答Excel验证失败: {}", validationError);
                return Result.error(validationError);
            }
            
            log.info("问答Excel验证通过");
            return Result.success("文件格式验证通过");
            
        } catch (Exception e) {
            log.error("验证问答Excel异常", e);
            return Result.error("验证异常: " + e.getMessage());
        }
    }
}
