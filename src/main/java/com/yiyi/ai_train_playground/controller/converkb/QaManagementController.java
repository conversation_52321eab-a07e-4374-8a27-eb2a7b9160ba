package com.yiyi.ai_train_playground.controller.converkb;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.converkb.QaMainListRequest;
import com.yiyi.ai_train_playground.dto.converkb.QaMainListResponse;
import com.yiyi.ai_train_playground.dto.converkb.QaMainUpdateRequest;
import com.yiyi.ai_train_playground.dto.converkb.QaDtlListRequest;
import com.yiyi.ai_train_playground.dto.converkb.QaDtlListResponse;
import com.yiyi.ai_train_playground.dto.converkb.QaDtlCreateRequest;
import com.yiyi.ai_train_playground.dto.converkb.QaDtlUpdateRequest;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportMainService;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportDtlService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 问答管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/qa-management")
@Tag(name = "问答管理", description = "问答知识库增删改查接口")
public class QaManagementController {
    
    @Autowired
    private TrainQaImportMainService trainQaImportMainService;
    
    @Autowired
    private TrainQaImportDtlService trainQaImportDtlService;
    
    // ==================== 主表操作 ====================
    
    @GetMapping("/main/list")
    @Operation(summary = "查询知识库列表", description = "分页查询问答知识库主表列表，支持按名称模糊搜索")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功", 
                    content = @Content(schema = @Schema(implementation = QaMainListResponse.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<QaMainListResponse> getMainList(QaMainListRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            String creator = SecurityUtil.getCurrentUsername();
            
            int offset = (request.getPage() - 1) * request.getPageSize();
            
            // 使用新的条件查询方法，支持知识库名称模糊搜索
            List<TrainQaImportMain> mainList = trainQaImportMainService.getMainListByConditions(
                teamId, creator, request.getQaImName(), offset, request.getPageSize());
            int total = trainQaImportMainService.countByConditions(teamId, creator, request.getQaImName());
            
            QaMainListResponse response = new QaMainListResponse();
            response.setList(mainList);
            response.setTotal(total);
            response.setPage(request.getPage());
            response.setPageSize(request.getPageSize());
            
            return Result.success("查询成功", response);
        } catch (Exception e) {
            log.error("查询知识库列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/main/{id}")
    @Operation(summary = "查询知识库详情", description = "根据ID查询知识库详情")
    public Result<TrainQaImportMain> getMainById(@PathVariable Long id) {
        try {
            TrainQaImportMain main = trainQaImportMainService.getMainById(id);
            if (main == null) {
                return Result.error("知识库不存在");
            }
            return Result.success("查询成功", main);
        } catch (Exception e) {
            log.error("查询知识库详情失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/main/{id}")
    @Operation(summary = "编辑知识库", description = "编辑知识库名称和描述，其他字段不可修改")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "知识库不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<String> updateMain(@PathVariable Long id, @RequestBody QaMainUpdateRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            String updater = SecurityUtil.getCurrentUsername();
            
            trainQaImportMainService.updateMain(id, request.getQaImName(), request.getQaImDesc(), teamId, updater);
            
            return Result.success("更新成功");
        } catch (RuntimeException e) {
            log.error("更新知识库失败", e);
            if (e.getMessage().contains("不存在") || e.getMessage().contains("无权限")) {
                return Result.error("知识库不存在或无权限");
            }
            return Result.error("更新失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("更新知识库失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/main/{id}")
    @Operation(summary = "删除知识库", description = "删除知识库及其所有问答详情")
    public Result<String> deleteMain(@PathVariable Long id) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            trainQaImportMainService.deleteMainWithDetails(id, teamId);
            
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("删除知识库失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/main/batch")
    @Operation(summary = "批量删除知识库", description = "批量删除知识库及其所有问答详情")
    public Result<String> batchDeleteMain(@RequestParam String ids) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            List<Long> idList = Arrays.stream(ids.split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            
            trainQaImportMainService.batchDeleteMainWithDetails(idList, teamId);
            
            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除知识库失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    // ==================== 详情表操作 ====================
    
    @GetMapping("/detail/list")
    @Operation(summary = "查询问答详情列表", description = "分页查询问答详情列表")
    public Result<QaDtlListResponse> getDetailList(QaDtlListRequest request) {
        try {
            int offset = (request.getPage() - 1) * request.getPageSize();
            List<TrainQaImportDtl> dtlList;
            int total;

            if (request.getQaMainId() != null) {
                // 根据主表ID查询，如果question也有值则同时进行模糊查询
                String question = (request.getQuestion() != null && !request.getQuestion().trim().isEmpty())
                    ? request.getQuestion().trim() : null;
                dtlList = trainQaImportDtlService.getDtlListByQaMainId(request.getQaMainId(), question, offset, request.getPageSize());
                total = trainQaImportDtlService.countByQaMainId(request.getQaMainId(), question);
            } else if (request.getQuestion() != null && !request.getQuestion().trim().isEmpty()) {
                // 仅根据问题模糊查询（无指定知识库）
                Long teamId = SecurityUtil.getCurrentTeamId();
                dtlList = trainQaImportDtlService.getDtlListByQuestionLike(request.getQuestion(), teamId, offset, request.getPageSize());
                total = trainQaImportDtlService.countByQuestionLike(request.getQuestion(), teamId);
            } else {
                // 查询团队下所有记录
                Long teamId = SecurityUtil.getCurrentTeamId();
                dtlList = trainQaImportDtlService.getDtlListByTeamId(teamId, offset, request.getPageSize());
                total = trainQaImportDtlService.countByTeamId(teamId);
            }

            QaDtlListResponse response = new QaDtlListResponse();
            response.setList(dtlList);
            response.setTotal(total);
            response.setPage(request.getPage());
            response.setPageSize(request.getPageSize());

            return Result.success("查询成功", response);
        } catch (Exception e) {
            log.error("查询问答详情列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/detail/{id}")
    @Operation(summary = "查询问答详情", description = "根据ID查询问答详情")
    public Result<TrainQaImportDtl> getDetailById(@PathVariable Long id) {
        try {
            TrainQaImportDtl detail = trainQaImportDtlService.getDtlById(id);
            if (detail == null) {
                return Result.error("问答详情不存在");
            }
            return Result.success("查询成功", detail);
        } catch (Exception e) {
            log.error("查询问答详情失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/detail")
    @Operation(summary = "创建问答详情", description = "创建新的问答详情")
    public Result<String> createDetail(@RequestBody QaDtlCreateRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            String creator = String.valueOf(SecurityUtil.getCurrentUsername());

            TrainQaImportDtl dtl = new TrainQaImportDtl();
            dtl.setQaMainId(request.getQaMainId());
            dtl.setQuestion(request.getQuestion());
            dtl.setAnswer(request.getAnswer());
            
            trainQaImportDtlService.createDtl(dtl, teamId, creator);
            
            return Result.success("创建成功");
        } catch (Exception e) {
            log.error("创建问答详情失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/detail/{id}")
    @Operation(summary = "更新问答详情", description = "更新问答详情")
    public Result<String> updateDetail(@PathVariable Long id, @RequestBody QaDtlUpdateRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            String updater = SecurityUtil.getCurrentUsername();
            
            TrainQaImportDtl updateRecord = new TrainQaImportDtl();
            updateRecord.setQuestion(request.getQuestion());
            updateRecord.setAnswer(request.getAnswer());
            
            trainQaImportDtlService.updateDtl(id, updateRecord, teamId, updater);
            
            return Result.success("更新成功");
        } catch (Exception e) {
            log.error("更新问答详情失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/detail/{id}")
    @Operation(summary = "删除问答详情", description = "删除单个问答详情")
    public Result<String> deleteDetail(@PathVariable Long id) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            trainQaImportDtlService.deleteDtl(id, teamId);
            
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("删除问答详情失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/detail/batch")
    @Operation(summary = "批量删除问答详情", description = "批量删除问答详情")
    public Result<String> batchDeleteDetail(@RequestParam String ids) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();
            List<Long> idList = Arrays.stream(ids.split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            
            trainQaImportDtlService.batchDeleteDtl(idList, teamId);
            
            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除问答详情失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
}
