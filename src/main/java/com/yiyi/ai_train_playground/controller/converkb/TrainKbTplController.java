package com.yiyi.ai_train_playground.controller.converkb;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.converkb.KbTplCreateRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplQueryRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailResponse;
import com.yiyi.ai_train_playground.dto.converkb.KbTplListResponse;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 会话知识库模板管理Controller
 */
@RestController
@RequestMapping("/api/kb-templates")
@Tag(name = "会话知识库模板管理", description = "会话知识库模板的增删改查操作")
public class TrainKbTplController {
    
    private static final Logger log = LoggerFactory.getLogger(TrainKbTplController.class);
    
    @Autowired
    private TrainKbTplService trainKbTplService;
    
    @PostMapping
    @Operation(summary = "创建会话知识库模板", description = "创建新的会话知识库模板，包含主表信息和对话明细")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Long> createKbTpl(
            @Parameter(description = "创建请求") @RequestBody KbTplCreateRequest request) {
        try {
            log.info("创建会话知识库模板请求：{}", request.getName());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            String userId = SecurityUtil.getCurrentUserId().toString();
            
            Long tplId = trainKbTplService.createKbTpl(request, teamId, userId);
            
            log.info("创建会话知识库模板成功，tplId: {}", tplId);
            return Result.success("创建成功", tplId);
        } catch (IllegalArgumentException e) {
            log.warn("创建会话知识库模板参数错误：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("创建会话知识库模板失败", e);
            return Result.error("创建失败");
        }
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取模板详情", description = "根据ID获取会话知识库模板的详细信息，支持对话明细分页查询")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "模板不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<KbTplDetailResponse> getKbTplDetail(
            @Parameter(description = "模板ID", example = "1") @PathVariable Long id,
            @Parameter(description = "分页查询条件") KbTplDetailRequest request) {
        try {
            log.info("查询模板详情，id: {}, page: {}, pageSize: {}", id, request.getPage(), request.getPageSize());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            KbTplDetailResponse response = trainKbTplService.getKbTplDetail(id, teamId, request);
            if (response == null) {
                return Result.error("模板不存在");
            }
            
            return Result.success("查询成功", response);
        } catch (Exception e) {
            log.error("查询模板详情失败，id: {}", id, e);
            return Result.error("查询失败");
        }
    }
    
    @GetMapping
    @Operation(summary = "分页查询模板列表", description = "分页查询会话知识库模板列表，支持按名称和模板类型筛选")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功")
    })
    public Result<PageResult<KbTplListResponse>> getKbTplPageList(
            @Parameter(description = "查询条件") KbTplQueryRequest request) {
        try {
            log.info("分页查询模板列表，page: {}, pageSize: {}", request.getPage(), request.getPageSize());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            PageResult<KbTplListResponse> result = trainKbTplService.getKbTplPageList(request, teamId);
            
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询模板列表失败", e);
            return Result.error("查询失败");
        }
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新模板", description = "更新会话知识库模板的名称和对话明细")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "模板不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Boolean> updateKbTpl(
            @Parameter(description = "模板ID", example = "1") @PathVariable Long id,
            @Parameter(description = "更新请求") @RequestBody KbTplCreateRequest request) {
        try {
            log.info("更新模板，id: {}, name: {}", id, request.getName());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            String userId = SecurityUtil.getCurrentUserId().toString();
            
            boolean success = trainKbTplService.updateKbTpl(id, request, teamId, userId);
            if (!success) {
                return Result.error("模板不存在或更新失败");
            }
            
            log.info("更新模板成功，id: {}", id);
            return Result.success("更新成功", true);
        } catch (IllegalArgumentException e) {
            log.warn("更新模板参数错误：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新模板失败，id: {}", id, e);
            return Result.error("更新失败");
        }
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除模板", description = "删除指定的会话知识库模板及其所有对话明细")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "模板不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Boolean> deleteKbTpl(
            @Parameter(description = "模板ID", example = "1") @PathVariable Long id) {
        try {
            log.info("删除模板，id: {}", id);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            boolean success = trainKbTplService.deleteKbTpl(id, teamId);
            if (!success) {
                return Result.error("模板不存在或删除失败");
            }
            
            log.info("删除模板成功，id: {}", id);
            return Result.success("删除成功", true);
        } catch (Exception e) {
            log.error("删除模板失败，id: {}", id, e);
            return Result.error("删除失败");
        }
    }
    
    @GetMapping("/check-name")
    @Operation(summary = "检查名称是否存在", description = "检查指定的模板名称是否已存在")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检查完成")
    })
    public Result<Boolean> checkNameExists(
            @Parameter(description = "模板名称", example = "客服售前标准话术") @RequestParam String name,
            @Parameter(description = "排除的ID（用于更新时检查）", example = "1") @RequestParam(required = false) Long excludeId) {
        try {
            log.info("检查名称是否存在，name: {}, excludeId: {}", name, excludeId);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            boolean exists = trainKbTplService.checkNameExists(name, teamId, excludeId);
            
            return Result.success("检查完成", exists);
        } catch (Exception e) {
            log.error("检查名称失败，name: {}", name, e);
            return Result.error("检查失败");
        }
    }
    
    @PutMapping("/{id}/tokens")
    @Operation(summary = "更新模板token数量", description = "更新指定模板的累计token消耗数量")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "404", description = "模板不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Boolean> updateKbTplTokens(
            @Parameter(description = "模板ID", example = "1") @PathVariable Long id,
            @Parameter(description = "token数量", example = "1500") @RequestParam Long tokens) {
        try {
            log.info("更新模板token数量，id: {}, tokens: {}", id, tokens);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            boolean success = trainKbTplService.updateKbTplTokens(id, tokens, teamId);
            if (!success) {
                return Result.error("模板不存在或更新失败");
            }
            
            log.info("更新模板token数量成功，id: {}, tokens: {}", id, tokens);
            return Result.success("更新成功", true);
        } catch (Exception e) {
            log.error("更新模板token数量失败，id: {}, tokens: {}", id, tokens, e);
            return Result.error("更新失败");
        }
    }
}