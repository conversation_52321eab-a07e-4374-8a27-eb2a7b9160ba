package com.yiyi.ai_train_playground.controller.converkb;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.converkb.*;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 任务会话明细管理Controller
 */
@RestController
@RequestMapping("/api/task-conv-kb-dtl")
@Tag(name = "任务会话明细管理", description = "任务会话明细的增删改查操作")
public class TrainTaskConvKbDtlController {
    
    private static final Logger log = LoggerFactory.getLogger(TrainTaskConvKbDtlController.class);
    
    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;
    
    @PostMapping
    @Operation(summary = "创建任务会话明细", description = "创建新的任务会话明细记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Long> createTaskConvKbDtl(
            @Parameter(description = "创建请求") @Valid @RequestBody TaskConvKbDtlCreateRequest request) {
        try {
            log.info("创建任务会话明细请求：taskId={}, kbDtlId={}", request.getTaskId(), request.getKbDtlId());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            String userId = SecurityUtil.getCurrentUserId().toString();
            
            Long dtlId = trainTaskConvKbDtlService.createTaskConvKbDtl(request, teamId, userId);
            return Result.success(dtlId);
            
        } catch (Exception e) {
            log.error("创建任务会话明细失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }
    
    @PostMapping("/batch")
    @Operation(summary = "批量创建任务会话明细", description = "批量创建多个任务会话明细记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Integer> batchCreateTaskConvKbDtl(
            @Parameter(description = "批量创建请求") @Valid @RequestBody List<TaskConvKbDtlCreateRequest> requests) {
        try {
            log.info("批量创建任务会话明细请求：count={}", requests.size());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            String userId = SecurityUtil.getCurrentUserId().toString();
            
            int count = trainTaskConvKbDtlService.batchCreateTaskConvKbDtl(requests, teamId, userId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("批量创建任务会话明细失败", e);
            return Result.error("批量创建失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取任务会话明细详情", description = "根据ID获取任务会话明细的详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "记录不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<TaskConvKbDtlDetailDTO> getTaskConvKbDtlDetail(
            @Parameter(description = "明细ID") @PathVariable Long id) {
        try {
            log.info("获取任务会话明细详情：id={}", id);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            TaskConvKbDtlDetailDTO detail = trainTaskConvKbDtlService.getTaskConvKbDtlDetail(id, teamId);
            if (detail == null) {
                return Result.error("记录不存在");
            }
            
            return Result.success(detail);
            
        } catch (Exception e) {
            log.error("获取任务会话明细详情失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/list")
    @Operation(summary = "分页查询任务会话明细", description = "根据条件分页查询任务会话明细列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<PageResult<TaskConvKbDtlListDTO.TaskConvKbDtlItem>> getTaskConvKbDtlList(
            @Parameter(description = "查询条件") TaskConvKbDtlQueryRequest request) {
        try {
            log.info("分页查询任务会话明细：page={}, pageSize={}", request.getPage(), request.getPageSize());
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            PageResult<TaskConvKbDtlListDTO.TaskConvKbDtlItem> result = 
                trainTaskConvKbDtlService.getTaskConvKbDtlPageList(request, teamId);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("分页查询任务会话明细失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/task/{taskId}")
    @Operation(summary = "根据任务ID获取明细列表", description = "根据任务ID获取所有相关的会话明细")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<TaskConvKbDtlDetailDTO>> getTaskConvKbDtlByTaskId(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            log.info("根据任务ID获取明细列表：taskId={}", taskId);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            List<TaskConvKbDtlDetailDTO> list = trainTaskConvKbDtlService.getTaskConvKbDtlByTaskId(taskId, teamId);
            return Result.success(list);
            
        } catch (Exception e) {
            log.error("根据任务ID获取明细列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新任务会话明细", description = "根据ID更新任务会话明细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "记录不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Boolean> updateTaskConvKbDtl(
            @Parameter(description = "明细ID") @PathVariable Long id,
            @Parameter(description = "更新请求") @Valid @RequestBody TaskConvKbDtlUpdateRequest request) {
        try {
            log.info("更新任务会话明细：id={}", id);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            String userId = SecurityUtil.getCurrentUserId().toString();
            
            boolean success = trainTaskConvKbDtlService.updateTaskConvKbDtl(id, request, teamId, userId);
            if (!success) {
                return Result.error("更新失败，记录不存在或无权限");
            }
            
            return Result.success(true);
            
        } catch (Exception e) {
            log.error("更新任务会话明细失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }
    
    @PutMapping("/{id}/learning-status")
    @Operation(summary = "更新学习状态", description = "更新指定明细的学习状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "记录不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Boolean> updateLearningStatus(
            @Parameter(description = "明细ID") @PathVariable Long id,
            @Parameter(description = "学习状态") @RequestParam String learningStatus) {
        try {
            log.info("更新学习状态：id={}, status={}", id, learningStatus);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            String userId = SecurityUtil.getCurrentUserId().toString();
            
            boolean success = trainTaskConvKbDtlService.updateLearningStatus(id, learningStatus, teamId, userId);
            if (!success) {
                return Result.error("更新失败，记录不存在或无权限");
            }
            
            return Result.success(true);
            
        } catch (Exception e) {
            log.error("更新学习状态失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除任务会话明细", description = "根据ID删除任务会话明细")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "记录不存在"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Boolean> deleteTaskConvKbDtl(
            @Parameter(description = "明细ID") @PathVariable Long id) {
        try {
            log.info("删除任务会话明细：id={}", id);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            boolean success = trainTaskConvKbDtlService.deleteTaskConvKbDtl(id, teamId);
            if (!success) {
                return Result.error("删除失败，记录不存在或无权限");
            }
            
            return Result.success(true);
            
        } catch (Exception e) {
            log.error("删除任务会话明细失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
    
    @DeleteMapping("/task/{taskId}")
    @Operation(summary = "删除任务的所有明细", description = "根据任务ID删除所有相关的会话明细")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Integer> deleteByTaskId(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            log.info("删除任务的所有明细：taskId={}", taskId);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            int count = trainTaskConvKbDtlService.deleteByTaskId(taskId, teamId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("删除任务的所有明细失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/count/task/{taskId}")
    @Operation(summary = "统计任务明细数量", description = "统计指定任务的明细数量")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Integer> countByTaskId(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            log.info("统计任务明细数量：taskId={}", taskId);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            int count = trainTaskConvKbDtlService.countByTaskId(taskId, teamId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("统计任务明细数量失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/count/task/{taskId}/status/{status}")
    @Operation(summary = "统计指定状态的明细数量", description = "统计任务中指定学习状态的明细数量")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Integer> countByTaskIdAndStatus(
            @Parameter(description = "任务ID") @PathVariable Long taskId,
            @Parameter(description = "学习状态") @PathVariable String status) {
        try {
            log.info("统计指定状态的明细数量：taskId={}, status={}", taskId, status);
            
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            int count = trainTaskConvKbDtlService.countByTaskIdAndStatus(taskId, status, teamId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("统计指定状态的明细数量失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}