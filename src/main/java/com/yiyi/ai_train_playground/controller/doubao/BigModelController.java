package com.yiyi.ai_train_playground.controller.doubao;

import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/api/bigmodel")
public class BigModelController {
    private final SuperBigModelInterface bigModelService;

    public BigModelController(SuperBigModelInterface bigModelService) {
        this.bigModelService = bigModelService;
    }

    @PostMapping("/chat")
    public String chat(@RequestParam String prompt,
                      @RequestParam(required = false) String sessionId,
                      @RequestParam(required = false) String systemPrompt) {
        return bigModelService.tnsOnce(systemPrompt, prompt);
    }

    @PostMapping(value = "/stream-chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamChat(@RequestParam String prompt,
                                 @RequestParam(required = false) String sessionId,
                                 @RequestParam(required = false) String systemPrompt) {
        return bigModelService.tsOnce(systemPrompt, prompt);
    }

    @PostMapping(value = "/image-chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> imageChatStream(@RequestParam String imageUrl,
                                       @RequestParam String prompt,
                                       @RequestParam(required = false) String systemPrompt) {
        return bigModelService.imageChatWithStream(imageUrl, prompt, systemPrompt);
    }

    @PostMapping("/image-chat-sync")
    public String imageChat(@RequestParam String imageUrl,
                           @RequestParam String prompt,
                           @RequestParam(required = false) String systemPrompt) {
        return bigModelService.imageChat(imageUrl, prompt, systemPrompt);
    }
}