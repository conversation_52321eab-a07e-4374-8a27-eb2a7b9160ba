package com.yiyi.ai_train_playground.controller.doubao;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
public class BigmodelPromptsController {
    
    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;
    
    /**
     * 根据关键词查询提示词
     * @param keyword 关键词字符串，格式：keyword:SU （如：picture:SU）
     * @return 提示词列表
     */
    @GetMapping("/prompts/{keyword}")
    public Result<List<String>> getPromptsByKeyword(@PathVariable("keyword") String keyword) {
        try {
            log.info("查询提示词请求：keyword={}", keyword);
            
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(keyword);
            return Result.success("查询成功", prompts);
            
        } catch (IllegalArgumentException e) {
            log.warn("参数错误：keyword={}, error={}", keyword, e.getMessage());
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("查询提示词失败：keyword={}", keyword, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据关键词查询提示词（POST方式，支持复杂参数）
     * @param keyword 关键词字符串
     * @return 提示词列表
     */
    @PostMapping("/prompts/query")
    public Result<List<String>> getPromptsByKeywordPost(@RequestParam("keyword") String keyword) {
        try {
            log.info("查询提示词请求（POST）：keyword={}", keyword);
            
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(keyword);
            return Result.success("查询成功", prompts);
            
        } catch (IllegalArgumentException e) {
            log.warn("参数错误：keyword={}, error={}", keyword, e.getMessage());
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("查询提示词失败：keyword={}", keyword, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
} 