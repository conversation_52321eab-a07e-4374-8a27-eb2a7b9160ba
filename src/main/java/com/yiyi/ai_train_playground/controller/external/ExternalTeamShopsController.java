package com.yiyi.ai_train_playground.controller.external;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 外部团队店铺接口调用控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/external/team-shops")
public class ExternalTeamShopsController {

    @Resource
    private RestTemplate restTemplate;

    @Value("${external.team-shops.base-url}")
    private String baseUrl;

    /**
     * 获取有效店铺列表
     */
    @GetMapping("/valid-list")
    public Result<?> getValidShopList() {
        try {
            // 获取当前团队ID
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("调用外部接口获取有效店铺列表，teamId: {}", teamId);

            // 构建请求URL
            String url = baseUrl + "/api/team-shops/valid-list?teamId=" + teamId;

            // 调用外部接口
            ResponseEntity<Object> response = restTemplate.getForEntity(url, Object.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("调用外部接口成功，teamId: {}", teamId);
                return Result.success(response.getBody());
            } else {
                log.error("调用外部接口失败，状态码: {}, teamId: {}", response.getStatusCode(), teamId);
                return Result.error("调用外部接口失败");
            }

        } catch (Exception e) {
            log.error("调用外部接口异常，teamId: {}", SecurityUtil.getCurrentTeamId(), e);
            return Result.error("调用外部接口异常: " + e.getMessage());
        }
    }

    /**
     * 使用POST方式调用（如果外部接口需要POST）
     */
    @PostMapping("/valid-list")
    public Result<?> getValidShopListByPost() {
        try {
            // 获取当前团队ID
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("POST调用外部接口获取有效店铺列表，teamId: {}", teamId);

            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("teamId", teamId);

            // 构建请求URL
            String url = baseUrl + "/api/team-shops/valid-list";

            // 调用外部接口
            ResponseEntity<Object> response = restTemplate.postForEntity(url, requestBody, Object.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("POST调用外部接口成功，teamId: {}", teamId);
                return Result.success(response.getBody());
            } else {
                log.error("POST调用外部接口失败，状态码: {}, teamId: {}", response.getStatusCode(), teamId);
                return Result.error("调用外部接口失败");
            }

        } catch (Exception e) {
            log.error("POST调用外部接口异常，teamId: {}", SecurityUtil.getCurrentTeamId(), e);
            return Result.error("调用外部接口异常: " + e.getMessage());
        }
    }
}