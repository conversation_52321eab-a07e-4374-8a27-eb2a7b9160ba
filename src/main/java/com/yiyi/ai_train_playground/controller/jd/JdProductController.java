package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.jd.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListResponse;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 京东商品控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Tag(name = "京东商品管理", description = "京东商品相关接口")
public class JdProductController {

    private final TrainJdProductsService trainJdProductsService;

    /**
     * 分页查询京东商品列表
     *
     * @param request 查询请求参数
     * @return 商品列表响应结果
     */
    @GetMapping("/jd-prd-list")
    @Operation(summary = "分页查询京东商品列表", description = "支持按商品标题、SKU ID、品牌名称查询的分页接口")
    public Result<JdPrdListResponse> getJdProductList(
            @Parameter(description = "查询条件") JdPrdListRequest request) {

        try {
            log.info("查询京东商品列表: searchKeyword={}, page={}, pageSize={}",
                    request.getSearchKeyword(), request.getPage(), request.getPageSize());

            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }

            // 调用服务层查询
            JdPrdListResponse response = trainJdProductsService.findJdProductList(teamId, request);

            log.info("查询京东商品列表成功: 总数={}, 返回数量={}",
                    response.getTotal(), response.getRows().size());

            return Result.success(response);

        } catch (Exception e) {
            log.error("查询京东商品列表失败: searchKeyword={}, page={}, pageSize={}",
                    request.getSearchKeyword(), request.getPage(), request.getPageSize(), e);
            return Result.error("查询京东商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询京东商品详情
     *
     * @param wareId 京东商品ID
     * @return 商品详情响应结果
     */
    @GetMapping("/jd-prd-dtl/{wareId}")
    @Operation(summary = "查询京东商品详情", description = "根据wareId查询京东商品详情信息")
    public Result<JdPrdDtlResponse> getJdProductDetail(
            @Parameter(description = "京东商品ID", example = "12345", required = true)
            @PathVariable("wareId") Long wareId) {

        try {
            log.info("查询京东商品详情: wareId={}", wareId);

            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }

            // 调用服务层查询
            JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(teamId, wareId);

            if (response == null) {
                log.warn("未找到京东商品详情: wareId={}", wareId);
                return Result.error("未找到指定的商品详情");
            }

            log.info("查询京东商品详情成功: wareId={}", wareId);

            return Result.success(response);

        } catch (Exception e) {
            log.error("查询京东商品详情失败: wareId={}", wareId, e);
            return Result.error("查询京东商品详情失败: " + e.getMessage());
        }
    }
}
