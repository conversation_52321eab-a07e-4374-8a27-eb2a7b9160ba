package com.yiyi.ai_train_playground.controller.script;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.model.ScriptRequest;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/script")
public class ScriptChatController {
    
    private final JwtUtil jwtUtil;
    
    @Autowired
    public ScriptChatController(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }
    
    /**
     * 获取WebSocket连接信息
     * @return WebSocket连接地址和token
     */
    @GetMapping("/ws-info")
    public Result<Map<String, String>> getWebSocketInfo(@RequestHeader(value = "Authorization", required = false) String authorization) {
        String token;
        
        if (authorization != null && authorization.startsWith("Bearer ")) {
            // 如果请求中已经有有效的token，直接使用
            token = authorization.substring(7);
            try {
                // 验证token是否有效
                if (jwtUtil.validateToken(token)) {
                    // token有效，直接使用
                } else {
                    // token无效，生成包含默认teamId的临时token
                    token = jwtUtil.generateTempToken("admin");
                }
            } catch (Exception e) {
                // token解析失败，生成包含默认teamId的临时token
                token = jwtUtil.generateTempToken("admin");
            }
        } else {
            // 没有提供token，生成包含默认teamId的临时token
            token = jwtUtil.generateTempToken("admin");
        }
        
        // 添加调试信息：显示token的过期时间
        try {
            java.util.Date expiration = jwtUtil.extractExpiration(token);
            java.util.Date now = new java.util.Date();
            long diffInMillis = expiration.getTime() - now.getTime();
            long diffInMinutes = diffInMillis / (1000 * 60);
            
            System.out.println("=== JWT Token Debug Info ===");
            System.out.println("Current time: " + now);
            System.out.println("Token expires at: " + expiration);
            System.out.println("Token valid for: " + diffInMinutes + " minutes");
            System.out.println("Token valid for: " + diffInMillis + " milliseconds");
            
            // 检查token是否包含teamId
            try {
                Long teamId = jwtUtil.getTeamIdFromToken(token);
                System.out.println("Token contains teamId: " + teamId);
            } catch (Exception ex) {
                System.out.println("Token does not contain teamId: " + ex.getMessage());
            }
            
            System.out.println("============================");
        } catch (Exception e) {
            System.out.println("Error extracting token expiration: " + e.getMessage());
        }
        
        Map<String, String> info = new HashMap<>();
        info.put("wsUrl", "ws://localhost:8080/ws/script/chat/" + token);
        info.put("token", token);
        
        return Result.success(info);
    }
    
    /**
     * 调试接口：检查JWT配置
     * @return JWT配置信息
     */
    @GetMapping("/jwt-debug")
    public Result<Map<String, Object>> getJwtDebugInfo() {
        Map<String, Object> debugInfo = new HashMap<>();
        
        // 获取JWT配置信息
        Map<String, Object> configInfo = jwtUtil.getJwtConfigInfo();
        debugInfo.put("jwtConfig", configInfo);
        
        // 生成测试token
        String testToken = jwtUtil.generateToken("test-user");
        
        try {
            java.util.Date expiration = jwtUtil.extractExpiration(testToken);
            java.util.Date now = new java.util.Date();
            long diffInMillis = expiration.getTime() - now.getTime();
            long diffInMinutes = diffInMillis / (1000 * 60);
            long diffInHours = diffInMinutes / 60;
            
            debugInfo.put("currentTime", now.toString());
            debugInfo.put("tokenExpiresAt", expiration.toString());
            debugInfo.put("validForMinutes", diffInMinutes);
            debugInfo.put("validForHours", diffInHours);
            debugInfo.put("validForMilliseconds", diffInMillis);
            debugInfo.put("testToken", testToken);
            
            // 检查是否与配置一致
            Long configuredExpiration = (Long) configInfo.get("expiration");
            boolean isConsistent = Math.abs(diffInMillis - configuredExpiration) < 1000; // 允许1秒误差
            debugInfo.put("isConsistentWithConfig", isConsistent);
            
            if (!isConsistent) {
                debugInfo.put("warning", "Token过期时间与配置不一致！");
                debugInfo.put("expectedMs", configuredExpiration);
                debugInfo.put("actualMs", diffInMillis);
            }
            
        } catch (Exception e) {
            debugInfo.put("error", e.getMessage());
        }
        
        return Result.success(debugInfo);
    }
    
    /**
     * 示例接口：展示如何构建请求
     * @return 示例请求对象
     */
    @GetMapping("/example")
    public Result<ScriptRequest> getExample() {
        // 这里只是返回一个示例，实际应用中可能需要从数据库获取
        ScriptRequest example = new ScriptRequest();
        example.setGroupId(3);
        example.setName("咨询产品:产自新疆阿勒泰天山，昼夜温差大，积累大量糖分，口感香甜软糯，独特地理环境孕育，闲时来一口，体验人间美味");
        example.setBuyerRequirement("买家是一位美食爱好者，平时就喜欢品尝各种特色水果。听闻新疆水果以香甜著称，一直想尝尝。此次希望购买产自新疆阿勒泰天山的水果，看重这里昼夜温差大，水果积累了大量糖分，口感香甜软糯的特点。期望水果新鲜度高，个头饱满，能在闲暇时光享受这份人间美味，最好是当季新鲜采摘发货的，能原汁原味地体验到独特地理环境孕育出的水果风味。");
        
        // 添加意图
        Map<String, Object> intents = new HashMap<>();
        intents.put("id", 18);
        intents.put("name", "咨询产品");
        intents.put("parentName", "售前");
        example.setIntents(intents);
        
        // 添加产品列表
        List<Map<String, Object>> productList = new ArrayList<>();
        Map<String, Object> product1 = new HashMap<>();
        product1.put("externalProductId", "838591834210");
        product1.put("externalProductName", "对标 Mac Pro 性能，价格仅 1/5，内置全球首款消费级 AI 专用芯片，支持实时语音转写，下班时跑跑AI大模型，直逼世界巅峰");
        product1.put("externalProductLink", "https://detail.tmall.com/item.htm?detail_redpacket_pop=true&id=838591834247");
        product1.put("externalProductImage", "https://ai-playground.oss-cn-shanghai.aliyuncs.com/proc/pic_1_1749107342365_产品-LV.png");
        productList.add(product1);
        
        Map<String, Object> product2 = new HashMap<>();
        product2.put("externalProductId", "838591834211");
        product2.put("externalProductName", "性能对标万元级日系风扇，价格仅 1/7，驱蚊功能通过 SGS 防蚊认证，2 米内驱蚊率 98% ");
        product2.put("externalProductLink", "https://detail.tmall.com/item.htm?detail_redpacket_pop=true&id=838591834247");
        product2.put("externalProductImage", "https://ai-playground.oss-cn-shanghai.aliyuncs.com/proc/pic_1_1749107342646_su7.png");
        productList.add(product2);
        
        example.setProductList(productList);
        
        // 添加团队ID
        example.setTeamId(1L); // 假设团队ID为1
        
        // 其他字段需要在实际应用中填充
        return Result.success(example);
    }
} 