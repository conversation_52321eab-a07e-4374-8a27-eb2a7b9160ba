package com.yiyi.ai_train_playground.controller.script;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.ScriptGroup;
import com.yiyi.ai_train_playground.service.ScriptGroupService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.Claims;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/script-groups")
public class ScriptGroupController {

    private final ScriptGroupService scriptGroupService;
    private final JwtUtil jwtUtil;

    public ScriptGroupController(ScriptGroupService scriptGroupService, JwtUtil jwtUtil) {
        this.scriptGroupService = scriptGroupService;
        this.jwtUtil = jwtUtil;
    }

    @GetMapping
    public Result<Map<String, Object>> getScriptGroups(
            @RequestParam(required = false) String groupTitle,
            @RequestHeader("Authorization") String authorization) {
        // 从Authorization header中提取token
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);

        // 调用服务层方法获取分组树
        Map<String, Object> groupTree = scriptGroupService.getGroupTree(groupTitle, teamId);
        return Result.success(groupTree);
    }

    @PostMapping
    public Result<Void> save(@RequestBody ScriptGroup scriptGroup,
                           @RequestHeader("Authorization") String authorization) {
        try {
            String token = authorization.replace("Bearer ", "");
            Claims claims = jwtUtil.parseToken(token);
            Long teamId = claims.get("teamId", Long.class);

            scriptGroup.setTeamId(teamId);
            if (scriptGroupService.save(scriptGroup)) {
                return Result.success(null);
            }
            return Result.error("添加失败");
        } catch (Exception e) {
            // 检查是否是分组名称重复的异常
            if (e.getMessage() != null && e.getMessage().contains("Duplicate entry") &&
                (e.getMessage().contains("uniq_group_title") || e.getMessage().contains("uniq_team_group_title"))) {
                return Result.error("该团队下分组名称已存在，请使用其他名称");
            }
            // 其他异常
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    @PutMapping
    public Result<Void> update(@RequestBody ScriptGroup scriptGroup,
                             @RequestHeader("Authorization") String authorization) {
        try {
            String token = authorization.replace("Bearer ", "");
            Claims claims = jwtUtil.parseToken(token);
            Long teamId = claims.get("teamId", Long.class);

            scriptGroup.setTeamId(teamId);

            if (scriptGroupService.update(scriptGroup)) {
                return Result.success(null);
            }
            return Result.error("修改失败");
        } catch (Exception e) {
            // 检查是否是分组名称重复的异常
            if (e.getMessage() != null && e.getMessage().contains("Duplicate entry") &&
                (e.getMessage().contains("uniq_group_title") || e.getMessage().contains("uniq_team_group_title"))) {
                return Result.error("该团队下分组名称已存在，请使用其他名称");
            }
            // 其他异常
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    @DeleteMapping
    public Result<Void> delete(@RequestParam String ids,
                             @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        if (scriptGroupService.deleteByIds(ids, teamId)) {
            return Result.success(null);
        }
        return Result.error("删除失败");
    }
} 