package com.yiyi.ai_train_playground.controller.script;

import com.yiyi.ai_train_playground.common.Result;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.dto.ScriptQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptGroupQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptUpdateRequest;

import com.yiyi.ai_train_playground.service.TrainScriptService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.HashMap;
import java.util.Map;

/**
 * 剧本控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class TrainScriptController {
    
    @Autowired
    private TrainScriptService trainScriptService;



    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 分页查询剧本列表
     * 
     * @param name 剧本昵称
     * @param creator 创建人
     * @param generationTypeCode 生成方式代码
     * @param createTimeStart 开始时间起始点
     * @param createTimeEnd 开始时间结束点
     * @param updateTimeStart 更新时间起始点
     * @param updateTimeEnd 更新时间结束点
     * @param groupId 分组编号
     * @param page 页号
     * @param pageSize 每页大小
     * @param request HTTP请求
     * @return 分页结果
     */
    @GetMapping("/scripts")
    public Result<Map<String, Object>> getScripts(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "creator", required = false) String creator,
            @RequestParam(value = "generationTypeCode", required = false) Integer generationTypeCode,
            @RequestParam(value = "createTimeStart", required = false) String createTimeStart,
            @RequestParam(value = "createTimeEnd", required = false) String createTimeEnd,
            @RequestParam(value = "updateTimeStart", required = false) String updateTimeStart,
            @RequestParam(value = "updateTimeEnd", required = false) String updateTimeEnd,
            @RequestParam(value = "groupId", required = false) Long groupId,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {
        try {
            // 从请求头获取token
            String token = extractToken(request);
            
            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }
            
            log.info("查询剧本列表：teamId={}, name={}, creator={}, generationTypeCode={}, page={}, pageSize={}", 
                    teamId, name, creator, generationTypeCode, page, pageSize);
            
            // 构建查询请求参数
            ScriptQueryRequest queryRequest = new ScriptQueryRequest();
            queryRequest.setName(name);
            queryRequest.setCreator(creator);
            queryRequest.setGenerationTypeCode(generationTypeCode);
            queryRequest.setCreateTimeStart(createTimeStart);
            queryRequest.setCreateTimeEnd(createTimeEnd);
            queryRequest.setUpdateTimeStart(updateTimeStart);
            queryRequest.setUpdateTimeEnd(updateTimeEnd);
            queryRequest.setGroupId(groupId);
            queryRequest.setPage(page);
            queryRequest.setPageSize(pageSize);
            
            // 查询数据
            PageResult<ScriptListDTO> pageResult = trainScriptService.getScriptList(queryRequest, teamId);
            
            // 构建响应数据格式
            Map<String, Object> data = new HashMap<>();
            data.put("total", pageResult.getTotal());
            data.put("rows", pageResult.getRecords());
            
            return Result.success("success", data);
            
        } catch (Exception e) {
            log.error("查询剧本列表失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 根据ID查询剧本详情
     *
     * @param id 剧本ID
     * @param request HTTP请求
     * @return 剧本详情
     */
    @GetMapping("/scripts/{id}")
    public Result<ScriptDetailDTO> getScriptById(@PathVariable Long id, HttpServletRequest request) {
        try {
            // 从请求头获取token
            String token = extractToken(request);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("查询剧本详情：id={}, teamId={}", id, teamId);

            ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(id, teamId);
            return Result.success("success", scriptDetail);

        } catch (Exception e) {
            log.error("查询剧本详情失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 创建剧本
     *
     * @param request 剧本创建请求
     * @param httpRequest HTTP请求
     * @return 创建结果
     */
    @PostMapping("/scripts")
    public Result<Long> createScript(@RequestBody ScriptCreateRequest request, HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            // 从JWT中获取用户ID作为创建人
            Long userId = jwtUtil.getUserIdFromToken(token);
            String creator = userId != null ? userId.toString() : "unknown";

            log.info("创建剧本：name={}, teamId={}, creator={}", request.getName(), teamId, creator);

            Long scriptId = trainScriptService.createScriptWithRelatedData(request, teamId, creator);
            return Result.success(scriptId);

        } catch (Exception e) {
            log.error("创建剧本失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新剧本及相关数据
     *
     * @param id 剧本ID
     * @param request 剧本更新请求
     * @param httpRequest HTTP请求
     * @return 更新结果
     */
    @PutMapping("/scripts/{id}")
    public Result<String> updateScript(@PathVariable Long id, @RequestBody ScriptUpdateRequest request, HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            // 从JWT中获取用户ID作为更新人
            Long userId = jwtUtil.getUserIdFromToken(token);
            String updater = userId != null ? userId.toString() : "unknown";

            // 设置剧本ID
            request.setId(id);

            log.info("更新剧本：id={}, name={}, teamId={}, updater={}", id, request.getName(), teamId, updater);

            boolean success = trainScriptService.updateScriptWithRelatedData(request, teamId, updater);
            if (success) {
                return Result.success("剧本更新成功");
            } else {
                return Result.error("剧本更新失败");
            }

        } catch (Exception e) {
            log.error("更新剧本失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量删除剧本
     *
     * @param ids 剧本ID列表，逗号分隔
     * @param request HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/scripts")
    public Result<String> deleteScript(@RequestParam("ids") String ids, HttpServletRequest request) {
        try {
            // 从请求头获取token
            String token = extractToken(request);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("批量删除剧本：ids={}, teamId={}", ids, teamId);

            boolean success = trainScriptService.batchDeleteScripts(ids, teamId);
            if (success) {
                return Result.success("success", null);
            } else {
                return Result.error("剧本删除失败");
            }

        } catch (Exception e) {
            log.error("批量删除剧本失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据剧本分组ID分页查询剧本列表
     * 支持多个分组ID，用逗号分隔
     *
     * @param request 分组查询请求参数
     * @return 分页结果
     */
    @GetMapping("/scripts/group")
    @Operation(summary = "根据分组ID查询剧本列表", description = "根据剧本分组ID分页查询剧本列表，支持多个分组ID用逗号分隔")
    public Result<PageResult<ScriptListDTO>> getScriptsByGroup(
            @Parameter(description = "分组查询条件，支持多个分组ID用逗号分隔") ScriptGroupQueryRequest request) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }

            log.info("根据分组ID查询剧本列表：teamId={}, request={}", teamId, request);

            // 查询数据
            PageResult<ScriptListDTO> pageResult = trainScriptService.getScriptListByGroup(request, teamId);

            log.info("查询结果：total={}, records.size={}", pageResult.getTotal(), pageResult.getRecords().size());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("根据分组ID查询剧本列表失败", e);
            return Result.error(e.getMessage());
        }
    }



    /**
     * 从请求头中提取JWT token
     *
     * @param request HTTP请求
     * @return JWT token
     */
    private String extractToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        throw new RuntimeException("未找到有效的认证token");
    }
}
