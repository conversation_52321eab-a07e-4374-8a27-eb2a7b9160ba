package com.yiyi.ai_train_playground.controller.shortphrase;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseCreateRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseDetailDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseQueryRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseUpdateRequest;
import com.yiyi.ai_train_playground.service.shortphrase.ShortcutPhraseService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 快捷短语控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/api/shortcut-phrases")
@Tag(name = "快捷短语管理", description = "快捷短语的创建、查询、更新、删除等操作")
public class ShortcutPhraseController {
    
    @Autowired
    private ShortcutPhraseService shortcutPhraseService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 分页查询快捷短语列表
     *
     * @param request 查询请求参数
     * @param httpRequest HTTP请求
     * @return 分页结果
     */
    @GetMapping
    @Operation(
        summary = "分页查询快捷短语列表",
        description = "支持按分组ID、短语标题、短语内容、短语类型、启用状态、标签等条件查询"
    )
    public Result<PageResult<ShortcutPhraseListDTO>> getPhraseList(
            @Parameter(description = "查询条件") ShortcutPhraseQueryRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);
            
            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }
            
            log.info("查询快捷短语列表，teamId: {}, request: {}", teamId, request);
            
            PageResult<ShortcutPhraseListDTO> result = shortcutPhraseService.getPhraseList(request, teamId);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("查询快捷短语列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询快捷短语详情
     *
     * @param id 短语ID
     * @param httpRequest HTTP请求
     * @return 短语详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询快捷短语详情", description = "根据短语ID查询详细信息")
    public Result<ShortcutPhraseDetailDTO> getPhraseDetail(
            @Parameter(description = "短语ID") @PathVariable Long id,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);
            
            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }
            
            log.info("查询快捷短语详情，id: {}, teamId: {}", id, teamId);
            
            ShortcutPhraseDetailDTO detail = shortcutPhraseService.getPhraseDetail(id, teamId);
            if (detail == null) {
                return Result.error("短语不存在");
            }
            
            return Result.success(detail);
            
        } catch (Exception e) {
            log.error("查询快捷短语详情失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建快捷短语
     *
     * @param request 创建请求
     * @param httpRequest HTTP请求
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建快捷短语", description = "创建新的快捷短语")
    public Result<Long> createPhrase(
            @Parameter(description = "创建请求") @Valid @RequestBody ShortcutPhraseCreateRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);
            
            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }
            
            // 从JWT中获取用户ID作为创建人
            Long userId = jwtUtil.getUserIdFromToken(token);
            String creator = userId != null ? userId.toString() : "unknown";
            
            log.info("创建快捷短语，teamId: {}, creator: {}, phraseTitle: {}", teamId, creator, request.getPhraseTitle());
            
            Long phraseId = shortcutPhraseService.createPhrase(request, teamId, creator);
            return Result.success(phraseId);

        } catch (Exception e) {
            log.error("创建快捷短语失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新快捷短语
     *
     * @param id 短语ID
     * @param request 更新请求
     * @param httpRequest HTTP请求
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新快捷短语", description = "更新指定ID的快捷短语")
    public Result<Boolean> updatePhrase(
            @Parameter(description = "短语ID") @PathVariable Long id,
            @Parameter(description = "更新请求") @Valid @RequestBody ShortcutPhraseUpdateRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            // 从JWT中获取用户ID作为更新人
            Long userId = jwtUtil.getUserIdFromToken(token);
            String updater = userId != null ? userId.toString() : "unknown";

            // 设置ID
            request.setId(id);

            log.info("更新快捷短语，id: {}, teamId: {}, updater: {}", id, teamId, updater);

            boolean success = shortcutPhraseService.updatePhrase(request, teamId, updater);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("更新失败，短语不存在或版本冲突");
            }

        } catch (Exception e) {
            log.error("更新快捷短语失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除快捷短语
     *
     * @param id 短语ID
     * @param httpRequest HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除快捷短语", description = "删除指定ID的快捷短语")
    public Result<Boolean> deletePhrase(
            @Parameter(description = "短语ID") @PathVariable Long id,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("删除快捷短语，id: {}, teamId: {}", id, teamId);

            boolean success = shortcutPhraseService.deletePhrase(id, teamId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("删除失败，短语不存在");
            }

        } catch (Exception e) {
            log.error("删除快捷短语失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除快捷短语
     *
     * @param ids 短语ID列表（逗号分隔）
     * @param httpRequest HTTP请求
     * @return 删除结果
     */
    @DeleteMapping
    @Operation(summary = "批量删除快捷短语", description = "批量删除指定ID的快捷短语")
    public Result<Boolean> batchDeletePhrases(
            @Parameter(description = "短语ID列表，逗号分隔", example = "1,2,3") @RequestParam String ids,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("批量删除快捷短语，ids: {}, teamId: {}", ids, teamId);

            boolean success = shortcutPhraseService.batchDeletePhrases(ids, teamId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("批量删除失败");
            }

        } catch (Exception e) {
            log.error("批量删除快捷短语失败", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 增加快捷短语使用次数
     *
     * @param id 短语ID
     * @param httpRequest HTTP请求
     * @return 操作结果
     */
    @PostMapping("/{id}/increment-usage")
    @Operation(summary = "增加快捷短语使用次数", description = "增加指定短语的使用次数")
    public Result<Boolean> incrementUsageCount(
            @Parameter(description = "短语ID") @PathVariable Long id,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("增加快捷短语使用次数，id: {}, teamId: {}", id, teamId);

            boolean success = shortcutPhraseService.incrementUsageCount(id, teamId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("操作失败，短语不存在");
            }

        } catch (Exception e) {
            log.error("增加快捷短语使用次数失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据分组ID查询快捷短语列表
     *
     * @param spGroupId 分组ID
     * @param httpRequest HTTP请求
     * @return 短语列表
     */
    @GetMapping("/group/{spGroupId}")
    @Operation(summary = "根据分组ID查询快捷短语列表", description = "查询指定分组下的所有启用短语")
    public Result<List<ShortcutPhraseListDTO>> getPhrasesByGroupId(
            @Parameter(description = "分组ID") @PathVariable Long spGroupId,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取token
            String token = extractToken(httpRequest);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("根据分组ID查询快捷短语列表，spGroupId: {}, teamId: {}", spGroupId, teamId);

            List<ShortcutPhraseListDTO> list = shortcutPhraseService.getPhrasesByGroupId(spGroupId, teamId);
            return Result.success(list);

        } catch (Exception e) {
            log.error("根据分组ID查询快捷短语列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 从HTTP请求中提取JWT token
     *
     * @param request HTTP请求
     * @return JWT token
     */
    private String extractToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        throw new RuntimeException("Authorization header is missing or invalid");
    }
}
