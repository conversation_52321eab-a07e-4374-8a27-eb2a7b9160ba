package com.yiyi.ai_train_playground.controller.staff;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.util.JwtUtil;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * 生成临时JWT控制器
 * 用于生成临时性的JWT令牌，主要用于测试和开发环境
 * 
 * <AUTHOR> Assistant
 * @since 2025-09-01
 */
@Tag(name = "员工认证管理", description = "员工认证相关API")
@RestController
@RequestMapping("/api/staff")
@Slf4j
public class GeneTempJwtController {

    private final JwtUtil jwtUtil;
    private final Random random = new Random();

    public GeneTempJwtController(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    /**
     * 生成临时JWT令牌
     * 
     * @return 包含JWT令牌的响应结果
     */
    @Operation(
        summary = "生成临时JWT令牌", 
        description = "生成一个临时的JWT令牌，使用随机用户ID和'ano_'+UUID格式用户名"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功生成JWT令牌"),
        @ApiResponse(responseCode = "500", description = "内部服务器错误")
    })
    @PostMapping("/generate-temp-jwt")
    public Result<Map<String, Object>> generateTempJwt() {
        try {
            // 从SecurityUtil中获取teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            
            // 生成随机的负数userId（小于0的随机值）
            Long userId = (long) (random.nextInt(1000000) + 1) * -1;
            
            // 生成"ano_"+UUID作为username
            String username = "ano_" + UUID.randomUUID().toString();
            
            // rememberMe设置为false
            boolean rememberMe = false;
            
            // 调用jwtUtil.generateToken生成JWT令牌
            String token = jwtUtil.generateToken(userId, username, teamId, rememberMe);
            
            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("token", token);
            responseData.put("userId", userId);
            responseData.put("username", username);
            responseData.put("teamId", teamId);
            responseData.put("rememberMe", rememberMe);
            
            log.info("成功生成临时JWT令牌 - userId: {}, username: {}, teamId: {}", 
                    userId, username, teamId);
            
            return Result.success(responseData);
            
        } catch (Exception e) {
            log.error("生成临时JWT令牌失败", e);
            return Result.error("生成临时JWT令牌失败: " + e.getMessage());
        }
    }
}