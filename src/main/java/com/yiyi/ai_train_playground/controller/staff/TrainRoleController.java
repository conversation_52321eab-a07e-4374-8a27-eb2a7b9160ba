package com.yiyi.ai_train_playground.controller.staff;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.service.staff.TrainRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理Controller
 */
@RestController
@RequestMapping("/api/roles")
@Tag(name = "角色管理", description = "角色管理相关接口")
public class TrainRoleController {

    @Autowired
    private TrainRoleService trainRoleService;

    @PostMapping
    @Operation(summary = "创建角色", description = "创建新的角色")
    public Result<Long> createRole(@Valid @RequestBody TrainRoleCreateRequest request) {
        Long roleId = trainRoleService.createRole(request);
        return Result.success("角色创建成功", roleId);
    }

    @PutMapping
    @Operation(summary = "更新角色", description = "更新角色信息")
    public Result<Void> updateRole(@Valid @RequestBody TrainRoleUpdateRequest request) {
        trainRoleService.updateRole(request);
        return Result.success("角色更新成功", null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色", description = "根据ID删除角色")
    public Result<Void> deleteRole(@Parameter(description = "角色ID") @PathVariable Long id) {
        trainRoleService.deleteRole(id);
        return Result.success("角色删除成功", null);
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除角色", description = "根据ID列表批量删除角色")
    public Result<Void> deleteRoleBatch(@RequestBody List<Long> ids) {
        trainRoleService.deleteRoleBatch(ids);
        return Result.success("角色批量删除成功", null);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查询角色详情", description = "根据ID查询角色详细信息")
    public Result<TrainRoleDetailResponse> getRoleDetail(@Parameter(description = "角色ID") @PathVariable Long id) {
        TrainRoleDetailResponse response = trainRoleService.getRoleDetail(id);
        return Result.success("查询成功", response);
    }

    @GetMapping("/list")
    @Operation(summary = "分页查询角色列表", description = "分页查询角色列表")
    public Result<PageResult<TrainRoleDetailResponse>> getRolePageList(
            @Parameter(description = "查询条件") TrainRoleQueryRequest request) {
        PageResult<TrainRoleDetailResponse> result = trainRoleService.getRolePageList(request);
        return Result.success("查询成功", result);
    }

    @GetMapping("/all")
    @Operation(summary = "查询所有角色", description = "查询团队所有角色（不分页）")
    public Result<List<TrainRoleDetailResponse>> getAllRoles() {
        List<TrainRoleDetailResponse> result = trainRoleService.getAllRoles();
        return Result.success("查询成功", result);
    }

    @GetMapping("/check-name")
    @Operation(summary = "检查角色名称是否存在", description = "验证角色名称是否已被使用")
    public Result<Boolean> checkRoleNameExists(
            @Parameter(description = "角色名称") @RequestParam String roleName,
            @Parameter(description = "排除的角色ID（编辑时使用）") @RequestParam(required = false) Long excludeId) {
        boolean exists = trainRoleService.checkRoleNameExists(roleName, excludeId);
        return Result.success("检查完成", exists);
    }

    @GetMapping("/check-code")
    @Operation(summary = "检查角色编码是否存在", description = "验证角色编码是否已被使用")
    public Result<Boolean> checkRoleCodeExists(
            @Parameter(description = "角色编码") @RequestParam String roleCode,
            @Parameter(description = "排除的角色ID（编辑时使用）") @RequestParam(required = false) Long excludeId) {
        boolean exists = trainRoleService.checkRoleCodeExists(roleCode, excludeId);
        return Result.success("检查完成", exists);
    }

    @GetMapping("/by-staff/{staffId}")
    @Operation(summary = "查询员工角色", description = "根据员工ID查询其拥有的角色")
    public Result<List<TrainRoleDetailResponse>> getRolesByStaffId(
            @Parameter(description = "员工ID") @PathVariable Long staffId) {
        List<TrainRoleDetailResponse> result = trainRoleService.getRolesByStaffId(staffId);
        return Result.success("查询成功", result);
    }
}