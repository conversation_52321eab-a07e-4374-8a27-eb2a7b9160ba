package com.yiyi.ai_train_playground.controller.staff;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.staff.StaffLoginRequest;
import com.yiyi.ai_train_playground.dto.staff.StaffLoginResponse;
import com.yiyi.ai_train_playground.service.staff.StaffAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 员工认证控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@RestController
@RequestMapping("/api/staff/auth")
@Tag(name = "员工认证管理", description = "员工登录认证相关接口")
public class TrainStaffAuthController {

    private final StaffAuthService staffAuthService;

    public TrainStaffAuthController(StaffAuthService staffAuthService) {
        this.staffAuthService = staffAuthService;
    }

    @PostMapping("/login")
    @Operation(summary = "员工登录", description = "员工登录认证，返回JWT令牌")
    public Result<StaffLoginResponse> login(
            @Parameter(description = "员工登录请求参数") 
            @RequestBody @Valid StaffLoginRequest request) {
        
        StaffLoginResponse response = staffAuthService.login(request);
        return Result.success(response);
    }
}