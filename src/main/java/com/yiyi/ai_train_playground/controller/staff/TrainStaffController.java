package com.yiyi.ai_train_playground.controller.staff;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffCreateRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffDetailResponse;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffQueryRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffUpdateRequest;
import com.yiyi.ai_train_playground.service.staff.TrainStaffService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工管理控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/api/staff")
@RequiredArgsConstructor
@Validated
@Tag(name = "员工管理", description = "员工增删改查、密码管理、账户锁定等功能")
public class TrainStaffController {
    
    private final TrainStaffService trainStaffService;
    
    @PostMapping("/create")
    @Operation(summary = "创建员工", description = "新增员工信息")
    public Result<String> createStaff(@Valid @RequestBody TrainStaffCreateRequest request) {
        log.info("创建员工请求，用户名: {}", request.getUsername());
        
        int result = trainStaffService.createStaff(request);
        if (result > 0) {
            return Result.success("员工创建成功", null);
        } else {
            return Result.error("员工创建失败");
        }
    }
    
    @PutMapping("/update")
    @Operation(summary = "更新员工信息", description = "修改员工基本信息")
    public Result<String> updateStaff(@Valid @RequestBody TrainStaffUpdateRequest request) {
        log.info("更新员工请求，ID: {}", request.getId());
        
        int result = trainStaffService.updateStaff(request);
        if (result > 0) {
            return Result.success("员工信息更新成功", null);
        } else {
            return Result.error("员工信息更新失败，可能数据已被修改");
        }
    }
    
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除员工", description = "根据ID删除员工")
    public Result<String> deleteStaff(@Parameter(description = "员工ID") @PathVariable Long id) {
        log.info("删除员工请求，ID: {}", id);
        
        int result = trainStaffService.deleteStaff(id);
        if (result > 0) {
            return Result.success("员工删除成功", null);
        } else {
            return Result.error("员工删除失败");
        }
    }
    
    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除员工", description = "批量删除多个员工")
    public Result<String> deleteStaffBatch(@Parameter(description = "员工ID列表") @RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除员工请求，IDs: {}", ids);
        
        int result = trainStaffService.deleteStaffBatch(ids);
        return Result.success("成功删除 " + result + " 个员工", null);
    }
    
    @GetMapping("/detail/{id}")
    @Operation(summary = "查询员工详情", description = "根据ID查询员工详细信息")
    public Result<TrainStaffDetailResponse> getStaffDetail(@Parameter(description = "员工ID") @PathVariable Long id) {
        log.info("查询员工详情请求，ID: {}", id);
        
        TrainStaffDetailResponse response = trainStaffService.getStaffDetail(id);
        if (response != null) {
            return Result.success("查询成功", response);
        } else {
            return Result.error("员工不存在");
        }
    }
    
    @GetMapping("/list")
    @Operation(summary = "分页查询员工列表", description = "根据条件分页查询员工列表")
    public Result<PageResult<TrainStaffDetailResponse>> getStaffPageList(
            @Parameter(description = "查询条件") @Valid TrainStaffQueryRequest request) {
        log.info("分页查询员工列表请求，页码: {}, 页大小: {}", request.getPage(), request.getPageSize());
        
        PageResult<TrainStaffDetailResponse> result = trainStaffService.getStaffPageList(request);
        return Result.success("查询成功", result);
    }
    
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名是否存在", description = "检查用户名是否已被使用")
    public Result<Boolean> checkUsernameExists(
            @Parameter(description = "用户名") @RequestParam String username,
            @Parameter(description = "排除的员工ID") @RequestParam(required = false) Long excludeId) {
        log.info("检查用户名请求，username: {}, excludeId: {}", username, excludeId);
        
        boolean exists = trainStaffService.checkUsernameExists(username, excludeId);
        return Result.success("查询成功", exists);
    }
    
    @PutMapping("/reset-password")
    @Operation(summary = "重置员工密码", description = "管理员重置员工密码")
    public Result<String> resetPassword(
            @Parameter(description = "员工ID") @RequestParam @NotNull Long id,
            @Parameter(description = "新密码") @RequestParam @NotNull String newPassword) {
        log.info("重置密码请求，员工ID: {}", id);
        
        int result = trainStaffService.resetPassword(id, newPassword);
        if (result > 0) {
            return Result.success("密码重置成功", null);
        } else {
            return Result.error("密码重置失败");
        }
    }
    
    @PutMapping("/toggle-lock")
    @Operation(summary = "锁定/解锁账户", description = "切换员工账户的锁定状态")
    public Result<String> toggleAccountLock(
            @Parameter(description = "员工ID") @RequestParam @NotNull Long id,
            @Parameter(description = "是否锁定") @RequestParam @NotNull Boolean isLocked) {
        log.info("切换账户锁定状态请求，员工ID: {}, isLocked: {}", id, isLocked);
        
        int result = trainStaffService.toggleAccountLock(id, isLocked);
        if (result > 0) {
            String message = isLocked ? "账户锁定成功" : "账户解锁成功";
            return Result.success(message, null);
        } else {
            return Result.error("操作失败");
        }
    }
}