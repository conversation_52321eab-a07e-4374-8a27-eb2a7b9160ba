package com.yiyi.ai_train_playground.controller.staff;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.service.staff.TrainStaffTagService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/staff/tag")
@RequiredArgsConstructor
@Validated
@Tag(name = "员工标签管理", description = "员工标签的增删改查功能")
public class TrainStaffTagController {

    private final TrainStaffTagService staffTagService;

    @GetMapping("/page")
    @Operation(summary = "分页查询员工标签", description = "根据条件分页查询员工标签列表")
    public Result<PageResult<StaffTagDTO>> getStaffTagPage(
            @Parameter(description = "查询条件") StaffTagQueryRequest request) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        request.setTeamId(teamId);
        
        PageResult<StaffTagDTO> result = staffTagService.getStaffTagPage(request);
        return Result.success(result);
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有员工标签", description = "获取当前团队的所有员工标签（不分页）")
    public Result<List<StaffTagDTO>> getAllStaffTags() {
        Long teamId = SecurityUtil.getCurrentTeamId();
        List<StaffTagDTO> result = staffTagService.getAllStaffTags(teamId);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取员工标签详情", description = "根据ID获取员工标签详细信息")
    public Result<StaffTagDTO> getStaffTagById(
            @Parameter(description = "员工标签ID", required = true) @PathVariable Long id) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        StaffTagDTO result = staffTagService.getStaffTagById(id, teamId);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "创建员工标签", description = "创建新的员工标签")
    public Result<Long> createStaffTag(
            @Parameter(description = "创建请求", required = true) @Valid @RequestBody StaffTagCreateRequest request) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        Long creatorId = SecurityUtil.getCurrentUserId();
        String creator = creatorId != null ? creatorId.toString() : "system";
        
        Long staffTagId = staffTagService.createStaffTag(request, teamId, creator);
        return Result.success(staffTagId);
    }

    @PutMapping
    @Operation(summary = "更新员工标签", description = "更新员工标签信息")
    public Result<Void> updateStaffTag(
            @Parameter(description = "更新请求", required = true) @Valid @RequestBody StaffTagUpdateRequest request) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        Long updaterId = SecurityUtil.getCurrentUserId();
        String updater = updaterId != null ? updaterId.toString() : "system";
        
        staffTagService.updateStaffTag(request, teamId, updater);
        return Result.success("更新成功", null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除员工标签", description = "根据ID删除员工标签")
    public Result<Void> deleteStaffTag(
            @Parameter(description = "员工标签ID", required = true) @PathVariable Long id) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        staffTagService.deleteStaffTag(id, teamId);
        return Result.success("删除成功", null);
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除员工标签", description = "根据ID列表批量删除员工标签")
    public Result<Void> batchDeleteStaffTags(
            @Parameter(description = "员工标签ID列表", required = true) @RequestBody @NotEmpty List<@NotNull Long> ids) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        staffTagService.batchDeleteStaffTags(ids, teamId);
        return Result.success("批量删除成功", null);
    }

    @GetMapping("/check-name")
    @Operation(summary = "检查标签名称是否重复", description = "检查标签名称在当前团队是否已存在")
    public Result<Boolean> checkNameExists(
            @Parameter(description = "标签名称", required = true) @RequestParam String name,
            @Parameter(description = "排除的标签ID（更新时使用）") @RequestParam(required = false) Long excludeId) {
        
        Long teamId = SecurityUtil.getCurrentTeamId();
        boolean exists = staffTagService.checkNameExists(name, teamId, excludeId);
        return Result.success(exists);
    }
}