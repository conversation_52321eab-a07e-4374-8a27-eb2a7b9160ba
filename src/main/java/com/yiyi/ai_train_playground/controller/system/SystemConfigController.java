package com.yiyi.ai_train_playground.controller.system;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.system.*;
import com.yiyi.ai_train_playground.service.system.SystemConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 系统配置控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Slf4j
@RestController
@RequestMapping("/api/system-configs")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统配置管理", description = "系统配置的创建、查询、更新、删除等操作")
public class SystemConfigController {
    
    private final SystemConfigService systemConfigService;
    
    /**
     * 分页查询系统配置列表
     */
    @GetMapping
    @Operation(
        summary = "分页查询系统配置列表",
        description = "支持按命名空间、配置键、描述等条件查询"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功")
    })
    public Result<PageResult<SystemConfigResponse>> getSystemConfigPageList(
            @Parameter(description = "查询条件") SystemConfigQueryRequest request) {
        try {
            log.info("分页查询系统配置列表，page: {}, pageSize: {}", request.getPage(), request.getPageSize());
            
            PageResult<SystemConfigResponse> result = systemConfigService.getSystemConfigPageList(request);
            
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询系统配置列表失败", e);
            return Result.error("查询失败");
        }
    }
    
    /**
     * 根据ID查询系统配置详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询系统配置详情", description = "根据配置ID查询详细信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "配置不存在")
    })
    public Result<SystemConfigResponse> getSystemConfigDetail(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id) {
        try {
            log.info("查询系统配置详情，ID: {}", id);
            
            SystemConfigResponse result = systemConfigService.getSystemConfigDetail(id);
            
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询系统配置详情失败，ID: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建系统配置
     */
    @PostMapping
    @Operation(summary = "创建系统配置", description = "创建新的系统配置项")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    public Result<Long> createSystemConfig(
            @Parameter(description = "创建请求") @Valid @RequestBody SystemConfigCreateRequest request) {
        try {
            log.info("创建系统配置，命名空间: {}, 配置键: {}", request.getNamespace(), request.getConfigKey());
            
            Long id = systemConfigService.createSystemConfig(request);
            
            return Result.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建系统配置失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新系统配置
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新系统配置", description = "更新指定的系统配置项")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "404", description = "配置不存在")
    })
    public Result<Void> updateSystemConfig(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id,
            @Parameter(description = "更新请求") @Valid @RequestBody SystemConfigUpdateRequest request) {
        try {
            log.info("更新系统配置，ID: {}", id);
            
            // 确保路径参数和请求体中的ID一致
            request.setId(id);
            
            systemConfigService.updateSystemConfig(request);
            
            return Result.success("更新成功", null);
        } catch (Exception e) {
            log.error("更新系统配置失败，ID: {}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除系统配置
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除系统配置", description = "删除指定的系统配置项")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "配置不存在")
    })
    public Result<Void> deleteSystemConfig(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id) {
        try {
            log.info("删除系统配置，ID: {}", id);
            
            systemConfigService.deleteSystemConfig(id);
            
            return Result.success("删除成功", null);
        } catch (Exception e) {
            log.error("删除系统配置失败，ID: {}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除系统配置
     */
    @DeleteMapping
    @Operation(summary = "批量删除系统配置", description = "批量删除多个系统配置项")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "删除成功")
    })
    public Result<Void> deleteSystemConfigBatch(
            @Parameter(description = "配置ID列表，逗号分隔", example = "1,2,3") @RequestParam String ids) {
        try {
            log.info("批量删除系统配置，IDs: {}", ids);
            
            List<Long> idList = Arrays.stream(ids.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .toList();
            
            systemConfigService.deleteSystemConfigBatch(idList);
            
            return Result.success("批量删除成功", null);
        } catch (Exception e) {
            log.error("批量删除系统配置失败，IDs: {}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查配置键是否存在
     */
    @GetMapping("/check-config-key")
    @Operation(summary = "检查配置键是否存在", description = "检查指定的配置键是否已存在")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "检查完成")
    })
    public Result<Boolean> checkConfigKeyExists(
            @Parameter(description = "命名空间", example = "default") @RequestParam String namespace,
            @Parameter(description = "配置键", example = "system.timeout") @RequestParam String configKey,
            @Parameter(description = "排除的ID（用于更新时检查）", example = "1") @RequestParam(required = false) Long excludeId) {
        try {
            log.info("检查配置键是否存在，namespace: {}, configKey: {}, excludeId: {}", namespace, configKey, excludeId);
            
            boolean exists = systemConfigService.checkConfigKeyExists(namespace, configKey, excludeId);
            
            return Result.success("检查完成", exists);
        } catch (Exception e) {
            log.error("检查配置键失败，namespace: {}, configKey: {}", namespace, configKey, e);
            return Result.error("检查失败");
        }
    }
    
    /**
     * 获取配置值
     */
    @GetMapping("/config-value")
    @Operation(summary = "获取配置值", description = "根据命名空间和配置键获取配置值")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功")
    })
    public Result<String> getConfigValue(
            @Parameter(description = "命名空间", example = "default") @RequestParam String namespace,
            @Parameter(description = "配置键", example = "system.timeout") @RequestParam String configKey,
            @Parameter(description = "默认值", example = "30000") @RequestParam(required = false) String defaultValue) {
        try {
            log.info("获取配置值，namespace: {}, configKey: {}", namespace, configKey);
            
            String value = defaultValue != null 
                ? systemConfigService.getConfigValue(namespace, configKey, defaultValue)
                : systemConfigService.getConfigValue(namespace, configKey);
            
            return Result.success("获取成功", value);
        } catch (Exception e) {
            log.error("获取配置值失败，namespace: {}, configKey: {}", namespace, configKey, e);
            return Result.error("获取失败");
        }
    }
}
