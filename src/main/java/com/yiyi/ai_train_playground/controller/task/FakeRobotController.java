package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.service.task.FakeRobotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 虚拟机器人控制器
 * 用于模拟聊天记录中的买家消息
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-19
 */
@Slf4j
@RestController
@RequestMapping("/api/fake-robot")
@Tag(name = "虚拟机器人", description = "模拟聊天记录中的买家消息")
public class FakeRobotController {
    
    @Autowired
    @Qualifier("fakeRobotServiceImpl")
    private FakeRobotService fakeRobotService;
    
    @PostMapping("/next-message")
    @Operation(summary = "获取下一个买家消息", description = "解析聊天记录并返回下一个买家消息块")
    public ResponseEntity<String> getNextBuyerMessage(
            @Parameter(description = "聊天记录字符串", required = true)
            @RequestBody String chatRecord) {
        
        try {
            String response = fakeRobotService.getNextBuyerMessage(chatRecord);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取下一个买家消息失败", e);
            return ResponseEntity.internalServerError()
                    .body("{\"result\":[\"处理失败: " + e.getMessage() + "\"]}");
        }
    }
    
    @DeleteMapping("/session/{sessionId}")
    @Operation(summary = "清除会话", description = "清除指定会话的栈数据")
    public ResponseEntity<String> clearSession(
            @Parameter(description = "会话ID", required = true)
            @PathVariable String sessionId) {
        
        try {
            fakeRobotService.clearSession(sessionId);
            return ResponseEntity.ok("{\"message\":\"会话清除成功\"}");
        } catch (Exception e) {
            log.error("清除会话失败: sessionId={}", sessionId, e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"清除会话失败: " + e.getMessage() + "\"}");
        }
    }
    
    @GetMapping("/session/{sessionId}/has-more")
    @Operation(summary = "检查是否有更多消息", description = "检查指定会话是否还有更多消息")
    public ResponseEntity<String> hasMoreMessages(
            @Parameter(description = "会话ID", required = true)
            @PathVariable String sessionId) {
        
        try {
            boolean hasMore = fakeRobotService.hasMoreMessages(sessionId);
            return ResponseEntity.ok("{\"hasMore\":" + hasMore + "}");
        } catch (Exception e) {
            log.error("检查会话消息状态失败: sessionId={}", sessionId, e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"检查失败: " + e.getMessage() + "\"}");
        }
    }
}
