package com.yiyi.ai_train_playground.controller.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.consts.CONSTS;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.task.*;
import com.yiyi.ai_train_playground.enums.TaskPurposeTag;
import com.yiyi.ai_train_playground.service.bm.BmForQaService;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import com.yiyi.ai_train_playground.service.impl.TrainScriptServiceImpl;
import com.yiyi.ai_train_playground.service.task.FakeRobotService;
import com.yiyi.ai_train_playground.service.task.TrainReceptionChatroomService;
import com.yiyi.ai_train_playground.service.task.TrainQaReportMainService;
import com.yiyi.ai_train_playground.service.task.TrainQaReportDtlService;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatMainService;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatLogService;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.dto.converkb.TaskConvKbDtlDetailDTO;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportDtlService;
import com.yiyi.ai_train_playground.service.task.impl.Qa2StackServiceImpl;
import com.yiyi.ai_train_playground.enums.SenderType;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Controller
public class ScWS4QaAnoController {

    @Autowired
    private BmForQaService bmForQaService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;

    @Autowired
    private TrainReceptionChatroomService trainReceptionChatroomService;

    @Autowired
    private TrainScriptServiceImpl trainScriptService;

    @Autowired
    private TrainKbTplService trainKbTplService;

    @Autowired
    private TrainConvWinchatMainService trainConvWinchatMainService;

    @Autowired
    private com.yiyi.ai_train_playground.service.kb.TrainConvWinchatDtlService trainConvWinchatDtlService;

    @Autowired
    private TrainConvWinchatLogService trainConvWinchatLogService;

    @Autowired
    private JwtUtil jwtUtil;

    // 定时任务执行器，用于延时创建机器人
    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);

    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;

    @Autowired
    private TrainQaImportDtlService trainQaImportDtlService;

    @Autowired
    private Qa2StackServiceImpl qa2StackService;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    @Qualifier("fakeRobotServiceImpl")
    private FakeRobotService fakeRobotService;

    @Autowired
    private TrainQaReportMainService trainQaReportMainService;

    @Autowired
    private TrainQaReportDtlService trainQaReportDtlService;

    @Autowired
    private com.yiyi.ai_train_playground.service.task.TrainQaRdmService trainQaRdmService;


    /**
     * 初始化模拟聊天会话
     *
     * @param message 包含receChatRoomId、sceneName、token、isThinking、isStreaming、tempSubscribeId的JSON消息
     */
    @MessageMapping("/scws4qa/init")
    public void initSession(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);

            // 获取参数
            Long receChatRoomId = getLongFromRequest(request, "receChatRoomId");
            String sceneName = (String) request.get("sceneName");
            String token = (String) request.get("token");
            String isThinking = (String) request.get("isThinking");
            String isStreaming = (String) request.get("isStreaming");
            String tempSubscribeId = (String) request.get("tempSubscribeId"); // 新增临时订阅ID

            //添加一个主人参数，一个主人团队参数bygermmy@20250831
            String ownerUserName = (String) request.get("ownerUserName");
            String ownerId = (String) request.get("ownerId");
            String ownerTeamId = (String) request.get("ownerTeamId");
            String anoRealName = (String) request.get("anoRealName");//匿名用户名字
            String anoRealNo = (String) request.get("anoRealNo");//匿名用户编号

            log.info("开始初始化模拟聊天会话（知识库转换用）：receChatRoomId={}, sceneName={}, tempSubscribeId={}", receChatRoomId, sceneName, tempSubscribeId);

            // 1. 验证参数
            if (receChatRoomId == null || sceneName == null || token == null || ownerId == null || ownerTeamId == null || ownerUserName == null) {
                throw new RuntimeException("必要参数不能为空：receChatRoomId、sceneName、token、owner、ownerTeamId、ownerUserName");
            }

            // 验证tempSubscribeId
            if (tempSubscribeId == null || tempSubscribeId.trim().isEmpty()) {
                throw new RuntimeException("tempSubscribeId不能为空，用于消息隔离");
            }

            // 2. 从token中获取员工ID（WebSocket环境中SecurityContext为空，需要手动解析JWT）
//            Long staffId = jwtUtil.getUserIdFromToken(token);
            Long staffId = Long.valueOf(ownerId);
//            Long teamId = jwtUtil.getTeamIdFromToken(token);
            Long teamId = Long.valueOf(ownerTeamId);

            // 验证token有效性
            if (!jwtUtil.validateToken(token)) {
                throw new RuntimeException("无效的JWT token");
            }

          /*  if (staffId == null) {
                throw new RuntimeException("无法从token中获取员工ID");
            }*/

            // 3. 验证员工权限
           /* TrainStaff staff = trainReceptionChatroomService.validateStaffPermission(receChatRoomId, staffId);
            if (staff == null) {
                throw new RuntimeException("员工无权限访问该聊天室");
            }*/

            // 4. 查询聊天室任务列表bygermmy @20250829
            List<ChatRoomTaskInfo> chatRoomTaskList = trainReceptionChatroomService.getCrTL4Qa(receChatRoomId);
            if (chatRoomTaskList.isEmpty()) {
                throw new RuntimeException("聊天室没有配置任务");
            }

            // 4.1 插入winchat_main sp_step4
            Long winChatMainId = createWinchatMainRecord(receChatRoomId, staffId, teamId);

            // 4.2 插入qa_report_main
            Long qaReportMainId = createQaReportMainRecord(receChatRoomId, staffId, teamId, ownerUserName, anoRealName, anoRealNo);

            // 5. 构建聊天室信息
            ChatRoomInfo4Conv chatRoomInfo4Conv = buildChatRoomInfo(receChatRoomId, chatRoomTaskList, teamId);

            // 5.1 设置chatRoomInfo4Conv
            chatRoomInfo4Conv.setWinChatMainId(winChatMainId);

            // 5.2 继续设置chatRoomInfo4Conv
            chatRoomInfo4Conv.setQaReportMainId(qaReportMainId);

            // 6. 创建会话
            List<Map<String, Object>> sessionResults = createSessions(chatRoomInfo4Conv, sceneName, staffId, token, isThinking, isStreaming, teamId,ownerUserName, qaReportMainId, tempSubscribeId);

            // 7. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "模拟聊天会话初始化成功（知识库转换用）");
            result.put("chatroomId", receChatRoomId);
            result.put("sessionCount", sessionResults.size());
            result.put("sessions", sessionResults);
            result.put("staffName", anoRealName);//这个地方其实是匿名用户名字
            // result加上第一个任务的所有信息
            if (!chatRoomTaskList.isEmpty()) {
                result.put("taskInfo", chatRoomTaskList.get(0));
            }
            result.put("chatRoomInfo", chatRoomInfo4Conv);

            // 8. 发送到隔离的topic
            String isolatedTopic = "/topic/scws4qa/init/" + tempSubscribeId;
            messagingTemplate.convertAndSend(isolatedTopic, objectMapper.writeValueAsString(result));

            log.info("模拟聊天WebSocket（知识库转换用）初始化会话成功: 聊天室ID={}, 会话数量={}, 发送到topic={}", receChatRoomId, sessionResults.size(), isolatedTopic);

        } catch (Exception e) {
            log.error("模拟聊天WebSocket（知识库转换用）初始化会话失败", e);
            try {
                // 获取tempSubscribeId用于错误响应隔离
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String tempSubscribeId = (String) request.get("tempSubscribeId");
                
                Map<String, Object> errorResult = Map.of(
                        "error", true,
                        "message", "初始化会话失败: " + e.getMessage()
                );
                
                // 错误响应也要发送到隔离topic
                if (tempSubscribeId != null && !tempSubscribeId.trim().isEmpty()) {
                    String isolatedTopic = "/topic/scws4qa/init/" + tempSubscribeId;
                    messagingTemplate.convertAndSend(isolatedTopic, objectMapper.writeValueAsString(errorResult));
                } else {
                    // 如果无法获取tempSubscribeId，记录警告但不发送（避免全局广播）
                    log.warn("无法获取tempSubscribeId，错误响应未发送，避免全局广播");
                }
            } catch (Exception ex) {
                log.error("发送错误响应失败", ex);
            }
        }
    }

    /**
     * 处理客户端发来的消息（重命名自sendMessage）
     */
    @MessageMapping("/scws4qa/send")
    public void handlerAndResponseMessage(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sessionId = (String) request.get("sessionId");
            String userMessage = (String) request.get("message");

            //强行让前端传msgId add by germmy@20250827
            String msgId = (String) request.get("msgId");

            if (sessionId == null || userMessage == null) {
                messagingTemplate.convertAndSend("/topic/scws4qa/chat/" + sessionId,
                        "{\"error\":true,\"message\":\"参数不完整\"}");
                return;
            }

            // 调用大模型管理器发送消息（非流式响应）
            try {
                String response = bmForQaService.handlerAndRespWithNTNS(sessionId, userMessage, msgId);
                // 发送非流式响应到客户端
                messagingTemplate.convertAndSend("/topic/scws4qa/chat/" + sessionId, response);
                log.debug("模拟聊天WebSocket（问答匿名用）非流式响应完成: sessionId={}", sessionId);
            } catch (Exception error) {
                // 发送错误信息
                try {
                    Map<String, Object> errorResult = Map.of(
                            "error", true,
                            "message", error.getMessage()
                    );
                    String errorJson = objectMapper.writeValueAsString(errorResult);
                    messagingTemplate.convertAndSend("/topic/scws4qa/chat/" + sessionId, errorJson);
                } catch (Exception ex) {
                    messagingTemplate.convertAndSend("/topic/scws4qa/chat/" + sessionId,
                            "{\"error\":true,\"message\":\"发送消息失败\"}");
                }
                log.error("模拟聊天WebSocket（问答匿名用）发送消息失败: sessionId={}", sessionId, error);
            }

        } catch (Exception e) {
            log.error("模拟聊天WebSocket（问答匿名用）处理发送消息失败", e);
            try {
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String sessionId = (String) request.get("sessionId");
                messagingTemplate.convertAndSend("/topic/scws4qa/chat/" + sessionId,
                        "{\"error\":true,\"message\":\"处理消息失败\"}");
            } catch (Exception ex) {
                // 无法解析sessionId，无法发送错误消息
                log.error("无法解析sessionId发送错误消息", ex);
            }
        }
    }


    /**
     * 获取第三个系统提示词（从任务会话明细中随机选择）- 使用MySQL RAND()
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return TaskConvKbDtlDetailDTO对象
     */
    private TaskConvKbDtlDetailDTO get3rdSysPrmtByRdm(Long taskId, Long teamId) {
        try {
            log.info("开始获取第三个系统提示词（使用MySQL RAND()），taskId: {}, teamId: {}", taskId, teamId);

            // 使用新的随机查询方法
            TaskConvKbDtlDetailDTO randomDetail = trainTaskConvKbDtlService.getRdmConvDtlByTaskId(taskId, teamId);

            if (randomDetail == null) {
                log.warn("未找到任务会话明细，taskId: {}, teamId: {}", taskId, teamId);
                return null;
            }

            String finalChatLog = randomDetail.getFinalChatLog();
            if (finalChatLog == null || finalChatLog.trim().isEmpty()) {
                log.warn("选中的任务会话明细finalChatLog为空，id: {}", randomDetail.getId());
                return null;
            }

            log.info("随机选择了明细，id: {}, finalChatLog长度: {}",
                    randomDetail.getId(), finalChatLog.length());

            return randomDetail;

        } catch (Exception e) {
            log.error("获取第三个系统提示词失败，taskId: {}, teamId: {}", taskId, teamId, e);
            return null;
        }
    }

    /**
     * 获取第三个系统提示词（从任务会话明细中随机选择）- 原有方法保留
     *
     * @param taskId 任务ID
     * @param teamId
     * @return 系统提示词
     */
    private String get3rdSysPrmt(Long taskId, Long teamId) {
        try {
            log.info("开始获取第三个系统提示词，taskId: {}", taskId);

            // 1.1 先从缓存中获取任务会话明细列表
            String cacheKey = "task_conv_kb_dtl:" + taskId;
            List<TaskConvKbDtlDetailDTO> taskConvKbDtlList = null;

            // 尝试从缓存获取
            try {
                Object cachedData = cacheManager.get(cacheKey);
                if (cachedData != null) {
                    // 如果缓存中有数据，进行类型转换
                    if (cachedData instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<TaskConvKbDtlDetailDTO> cachedList = (List<TaskConvKbDtlDetailDTO>) cachedData;
                        taskConvKbDtlList = cachedList;
                        log.info("从缓存中获取到任务会话明细数量: {}", taskConvKbDtlList.size());
                    }
                }
            } catch (Exception e) {
                log.warn("从缓存获取任务会话明细失败，将从数据库获取，taskId: {}", taskId, e);
            }

            // 1.2 如果缓存中没有数据，从数据库获取
            if (taskConvKbDtlList == null || taskConvKbDtlList.isEmpty()) {
                log.info("缓存中无数据，从数据库获取任务会话明细，taskId: {}", taskId);
                taskConvKbDtlList = trainTaskConvKbDtlService.getTaskConvKbDtlByTaskId(taskId, teamId);

                if (taskConvKbDtlList == null || taskConvKbDtlList.isEmpty()) {
                    log.warn("未找到任务会话明细，taskId: {}", taskId);
                    return null;
                }

                log.info("从数据库获取到任务会话明细数量: {}", taskConvKbDtlList.size());

                // 1.3 将结果缓存到Redis中
                try {
                    cacheManager.put(cacheKey, taskConvKbDtlList, 30, TimeUnit.MINUTES); // 缓存30分钟
                    log.debug("任务会话明细已缓存，key: {}", cacheKey);
                } catch (Exception e) {
                    log.warn("缓存任务会话明细失败，但不影响业务流程，taskId: {}", taskId, e);
                }
            }

            // 1.3 随机获取一个TaskConvKbDtlDetailDTO的finalChatLog
            Random random = new Random();
            int randomIndex = random.nextInt(taskConvKbDtlList.size());
            TaskConvKbDtlDetailDTO selectedDetail = taskConvKbDtlList.get(randomIndex);

            String finalChatLog = selectedDetail.getFinalChatLog();
            if (finalChatLog == null || finalChatLog.trim().isEmpty()) {
                log.warn("选中的任务会话明细finalChatLog为空，id: {}", selectedDetail.getId());
                return null;
            }

            log.info("随机选择了第{}个明细，id: {}, finalChatLog长度: {}",
                    randomIndex + 1, selectedDetail.getId(), finalChatLog.length());

            // 1.4 返回finalChatLog作为systemPrompt
            return finalChatLog;

        } catch (Exception e) {
            log.error("获取第三个系统提示词失败，taskId: {}", taskId, e);
            return null;
        }
    }


    /**
     * 替换模板中的变量（ScriptDetailDTO版本）
     *
     * @param template     提示词模板
     * @param scriptDetail 剧本详情DTO
     * @return 替换后的提示词
     */
    private String replaceVariables(String template, ScriptDetailDTO scriptDetail) {
        String result = template;

        // 3.1 替换 {{buyerRequirement}}
        String buyerRequirement = scriptDetail.getBuyerRequirement();
        if (buyerRequirement != null && !buyerRequirement.trim().isEmpty()) {
            result = result.replace("{{buyerRequirement}}", buyerRequirement);
        } else {
            result = result.replace("{{buyerRequirement}}", "");
        }

        // 3.2 替换 {{externalProductName}} - 取第一个产品的名称
        String productName = "";
        if (scriptDetail.getProductList() != null && !scriptDetail.getProductList().isEmpty()) {
            productName = scriptDetail.getProductList().get(0).getExternalProductName();
            if (productName == null) {
                productName = "";
            }
        }
        result = result.replace("{{externalProductName}}", productName);

        // 3.3 处理 {{relateImgsTextListLoop}}{{/relateImgsTextListLoop}} 循环标签
        result = processRelateImgsLoop(result, scriptDetail);

        // 3.4 处理 {{flowNodeLoop}}{{/flowNodeLoop}} 循环标签
        result = processFlowNodeLoop(result, scriptDetail);

        // 3.5 替换意图相关变量
        String intentPhrase = scriptDetail.getParentIntentName() != null ? scriptDetail.getParentIntentName() : "";
        String intentName = scriptDetail.getIntentName() != null ? scriptDetail.getIntentName() : "";
        result = result.replace("{{intentPhrase}}", intentPhrase);
        result = result.replace("{{intentName}}", intentName);

        return result;
    }

    /**
     * 根据问题生成系统提示词
     *
     * @param question 问题内容
     * @return 生成的系统提示词
     */
    private String getSysPrmt(String question) {
        try {
            // 4.1.1 获取提示词模板
//            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_CONVERT_FREQ_FIRST_KEYWORD);
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_CONVERT_FREQ_SECOND_SYSTEM_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到转换频次标题提示词模板，关键词：{}", CONSTS.DEFAULT_CONVERT_FREQ_SECOND_SYSTEM_KEYWORD);
                return "";
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到提示词模板：{}", template);

            // 4.1.2 进行变量替换，用question替换{{ques}}
            String result = template;
            log.info("变量替换后的提示词：{}", result);

            return result;

        } catch (Exception e) {
            log.error("生成系统提示词失败，question：{}", question, e);
            return "";
        }
    }

    /**
     * @param t3rdRewrite
     * @return
     */
    private String getFinalSysPrmtFromTask(String t3rdRewrite) {
        try {
            // 1. 从数据库获取提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_APPLY_CHAT_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到多轮聊天提示词模板，使用默认模板");
                return null;
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到提示词模板：{}", template);

            // 2. 进行变量替换
            String result = replaceVar4Conv(template, t3rdRewrite);
            log.info("变量替换后的提示词：{}", result);

            return result;

        } catch (Exception e) {
            log.error("构建系统提示词失败，使用默认模板", e);
            return null;
        }
    }

    /**
     * @param template
     * @param t3rdRewrite
     * @return
     */
    private String replaceVar4Conv(String template, String t3rdRewrite) {
        String result = template;

        // 1. {{chat_log}}用chatRoomTaskInfo4Conv.randomKbChatLog替换
        String chatLog = t3rdRewrite;
        if (chatLog != null && !chatLog.trim().isEmpty()) {
            result = result.replace("{{chat_log}}", chatLog);
        } else {
            result = result.replace("{{chat_log}}", "");
        }


        return result;
    }


    /**
     * @param template
     * @param chatRoomTaskInfo4Conv
     * @return
     */
    private String replace4FinalSysPmt(String template, ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv) {
        String result = template;

        // 1. {{chat_log}}用chatRoomTaskInfo4Conv.randomKbChatLog替换
        String chatLog = chatRoomTaskInfo4Conv.getRandomKbChatLog();
        if (chatLog != null && !chatLog.trim().isEmpty()) {
            result = result.replace("{{chat_log}}", chatLog);
        } else {
            result = result.replace("{{chat_log}}", "");
        }

        // 2. {{targetProKnowledge}} 用chatRoomTaskInfo4Conv.scriptDetailDTO.productList[0].prodKb替换
        String targetProKnowledge = "";
        if (chatRoomTaskInfo4Conv.getScriptDetailDTO() != null &&
                chatRoomTaskInfo4Conv.getScriptDetailDTO().getProductList() != null &&
                !chatRoomTaskInfo4Conv.getScriptDetailDTO().getProductList().isEmpty()) {

            String prodKb = chatRoomTaskInfo4Conv.getScriptDetailDTO().getProductList().get(0).getProdKb();
            if (prodKb != null) {
                targetProKnowledge = prodKb;
            }
        }
        result = result.replace("{{targetProKnowledge}}", targetProKnowledge);

        // 3. {{targetRole}} 用chatRoomTaskInfo4Conv.scriptDetailDTO.buyerRequirement替换
        String targetRole = "";
        if (chatRoomTaskInfo4Conv.getScriptDetailDTO() != null) {
            String buyerRequirement = chatRoomTaskInfo4Conv.getScriptDetailDTO().getBuyerRequirement();
            if (buyerRequirement != null) {
                targetRole = buyerRequirement;
            }
        }
        result = result.replace("{{targetRole}}", targetRole);

        // 4. {{targetProductTitle}} 用chatRoomTaskInfo4Conv.scriptDetailDTO.productList[0].externalProductName替换
        String targetProductTitle = "";
        if (chatRoomTaskInfo4Conv.getScriptDetailDTO() != null &&
                chatRoomTaskInfo4Conv.getScriptDetailDTO().getProductList() != null &&
                !chatRoomTaskInfo4Conv.getScriptDetailDTO().getProductList().isEmpty()) {

            String productName = chatRoomTaskInfo4Conv.getScriptDetailDTO().getProductList().get(0).getExternalProductName();
            if (productName != null) {
                targetProductTitle = productName;
            }
        }
        result = result.replace("{{targetProductTitle}}", targetProductTitle);

        return result;
    }


    /**
     * 处理关联图片循环标签（ScriptDetailDTO版本）
     */
    private String processRelateImgsLoop(String template, ScriptDetailDTO scriptDetail) {
        String startTag = "{{relateImgsTextListLoop}}";
        String endTag = "{{/relateImgsTextListLoop}}";

        int startIndex = template.indexOf(startTag);
        int endIndex = template.indexOf(endTag);

        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            return template; // 没有找到循环标签，直接返回
        }

        String beforeLoop = template.substring(0, startIndex);
        String loopTemplate = template.substring(startIndex + startTag.length(), endIndex);
        String afterLoop = template.substring(endIndex + endTag.length());

        StringBuilder loopResult = new StringBuilder();

        // 遍历relateImgs数组
        if (scriptDetail.getRelateImgs() != null) {
            for (var img : scriptDetail.getRelateImgs()) {
                String recognizedText = img.getRecognizedText() != null ? img.getRecognizedText() : "";
                String url = img.getUrl() != null ? img.getUrl() : "";

                // 替换循环模板中的变量
                String currentLoop = loopTemplate
                        .replace("{{relateImgsText}}", recognizedText)
                        .replace("{{relateImgsUrl}}", url);

                loopResult.append(currentLoop);
            }
        }

        return beforeLoop + loopResult.toString() + afterLoop;
    }

    /**
     * 处理流程节点循环标签（ScriptDetailDTO版本）
     */
    private String processFlowNodeLoop(String template, ScriptDetailDTO scriptDetail) {
        String startTag = "{{flowNodeLoop}}";
        String endTag = "{{/flowNodeLoop}}";

        int startIndex = template.indexOf(startTag);
        int endIndex = template.indexOf(endTag);

        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            return template; // 没有找到循环标签，直接返回
        }

        String beforeLoop = template.substring(0, startIndex);
        String loopTemplate = template.substring(startIndex + startTag.length(), endIndex);
        String afterLoop = template.substring(endIndex + endTag.length());

        StringBuilder loopResult = new StringBuilder();

        // 遍历flowNodes数组
        if (scriptDetail.getFlowNodes() != null) {
            for (var node : scriptDetail.getFlowNodes()) {
                String nodeName = node.getNodeName() != null ? node.getNodeName() : "";
                String nodeBuyerRequirement = node.getNodeBuyerRequirement() != null ? node.getNodeBuyerRequirement() : "";

                // 替换循环模板中的变量
                String currentLoop = loopTemplate
                        .replace("{{flowNodesName}}", nodeName)
                        .replace("{{flowNodesBuyerRequirement}}", nodeBuyerRequirement);

                loopResult.append(currentLoop);
            }
        }

        return beforeLoop + loopResult.toString() + afterLoop;
    }

    /**
     * 构建默认系统提示词（ScriptDetailDTO版本，当数据库中没有模板时使用）
     */
    private String buildDefaultSystemPrompt(ScriptDetailDTO scriptDetail) {
        StringBuilder systemMessage = new StringBuilder();

        // 固定前缀
        String fixedPrefix = "你模拟一个客户，扮演成以下角色，和客服聊天，所提问题要聚焦在用户想要购买的产品上，按照第4步流程节点的顺序进行提问。具体的信息如下：";
        systemMessage.append(fixedPrefix);

        // 1. 买家背景信息
        String buyerRequirement = scriptDetail.getBuyerRequirement();
        if (buyerRequirement != null && !buyerRequirement.trim().isEmpty()) {
            systemMessage.append("买家背景信息是 : ").append(buyerRequirement).append(" ， ");
        }

        // 2. 商品信息
        if (scriptDetail.getProductList() != null && !scriptDetail.getProductList().isEmpty()) {
            var firstProduct = scriptDetail.getProductList().get(0);
            String productName = firstProduct.getExternalProductName();
            if (productName != null && !productName.trim().isEmpty()) {
                systemMessage.append("商品名称是 : ").append(productName).append(" ， ");
            }
        }

        // 3. 流程节点信息
        if (scriptDetail.getFlowNodes() != null && !scriptDetail.getFlowNodes().isEmpty()) {
            systemMessage.append("流程节点是 : ");
            for (int i = 0; i < scriptDetail.getFlowNodes().size(); i++) {
                var node = scriptDetail.getFlowNodes().get(i);
                String nodeName = node.getNodeName();
                String nodeBuyerRequirement = node.getNodeBuyerRequirement();

                systemMessage.append("第").append(i + 1).append("步：");
                if (nodeName != null && !nodeName.trim().isEmpty()) {
                    systemMessage.append(nodeName);
                }
                if (nodeBuyerRequirement != null && !nodeBuyerRequirement.trim().isEmpty()) {
                    systemMessage.append("（").append(nodeBuyerRequirement).append("）");
                }
                if (i < scriptDetail.getFlowNodes().size() - 1) {
                    systemMessage.append("，");
                }
            }
            systemMessage.append(" ， ");
        }

        // 4. 订单信息
        Integer orderPriority = scriptDetail.getOrderPriority();
        if (orderPriority != null) {
            systemMessage.append("订单优先级是 : ").append(orderPriority).append(" ， ");
        }

        String orderRemark = scriptDetail.getOrderRemark();
        if (orderRemark != null && !orderRemark.trim().isEmpty()) {
            systemMessage.append("订单备注是 : ").append(orderRemark).append(" ， ");
        }

        // 5. 买家进线意图
        String parentIntentName = scriptDetail.getParentIntentName();
        String intentName = scriptDetail.getIntentName();
        if (parentIntentName != null && intentName != null) {
            systemMessage.append("买家进线意图是 : ").append(parentIntentName)
                    .append("，").append(intentName).append("，");
        }

        // 结尾固定文本
        systemMessage.append("你需要首先向客服发起聊天。");

        return systemMessage.toString();
    }

    /**
     * 关闭会话并清除资源
     */
    @MessageMapping("/scws4qa/close")
    public void closeSession(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sessionId = (String) request.get("sessionId");

            if (sessionId == null || sessionId.trim().isEmpty()) {
                log.warn("模拟聊天（知识库转换用）关闭会话失败：sessionId为空");
                return;
            }

            log.info("模拟聊天（知识库转换用）收到关闭会话请求: sessionId={}", sessionId);

            // 调用BigModelManager清除会话资源
            bmForQaService.clearSession(sessionId);

            // 向客户端发送关闭确认
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会话已关闭（知识库转换用）");
            response.put("sessionId", sessionId);

            String responseJson = objectMapper.writeValueAsString(response);
            messagingTemplate.convertAndSend("/topic/scws4qa/close/" + sessionId, responseJson);

            log.info("模拟聊天（问答匿名用）会话关闭完成: sessionId={}", sessionId);

        } catch (Exception e) {
            log.error("模拟聊天（知识库转换用）关闭会话失败", e);
            try {
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String sessionId = (String) request.get("sessionId");

                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "关闭会话失败: " + e.getMessage());
                errorResponse.put("sessionId", sessionId);

                String errorJson = objectMapper.writeValueAsString(errorResponse);
                messagingTemplate.convertAndSend("/topic/scws4qa/close/" + sessionId, errorJson);
            } catch (Exception ex) {
                log.error("模拟聊天（问答匿名用）发送关闭会话错误响应失败", ex);
            }
        }
    }

    /**
     * 从请求中获取Long类型参数
     */
    private Long getLongFromRequest(Map<String, Object> request, String key) {
        Object value = request.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 构建聊天室信息
     */
    private ChatRoomInfo4Conv buildChatRoomInfo(Long receChatRoomId, List<ChatRoomTaskInfo> chatRoomTaskList, Long teamId) {
        log.info("构建聊天室信息（问答匿名用）：receChatRoomId={}", receChatRoomId);

        ChatRoomInfo4Conv chatRoomInfo4Conv = new ChatRoomInfo4Conv();
        chatRoomInfo4Conv.setChatroomId(receChatRoomId);
        chatRoomInfo4Conv.setChatRoomTaskList(chatRoomTaskList);

        // 设置聊天室配置
        setChatroomConfig(chatRoomInfo4Conv, receChatRoomId, teamId);

        //设置剧本
        List<ChatRoomTaskInfo4Conv> chatRoomTaskList4ConvList = new ArrayList<>();
        ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv;

        //构造新的任务列表，从原有的任务列表中，一点一点扒过来
        //先设置剧本
        for (ChatRoomTaskInfo taskInfo : chatRoomTaskList) {
                try {
//                    ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(taskInfo.getScriptId(), teamId);
                    chatRoomTaskInfo4Conv = new ChatRoomTaskInfo4Conv();
                    BeanUtils.copyProperties(taskInfo, chatRoomTaskInfo4Conv);
//                    chatRoomTaskInfo4Conv.setScriptDetailDTO(scriptDetail);
                    chatRoomTaskList4ConvList.add(chatRoomTaskInfo4Conv);
                } catch (Exception e) {
                    log.warn("设置新的任务列表失败", taskInfo.getScriptId(), e);
                }
        }

        // 设置处理后的任务列表到聊天室信息中
        chatRoomInfo4Conv.setChatRoomTaskList4Conv(chatRoomTaskList4ConvList);

        log.info("聊天室信息构建完成（问答匿名用）：任务数量={}, 问答匿名任务数量={}",
                chatRoomTaskList.size(), chatRoomTaskList4ConvList.size());
        return chatRoomInfo4Conv;
    }

    /**
     * 创建会话
     */
     private List<Map<String, Object>> createSessions(ChatRoomInfo4Conv chatRoomInfo4Conv, String sceneName, Long staffId,
                                                     String token, String isThinking, String isStreaming, Long teamId, String creator, Long qaReportMainId, String tempSubscribeId) {
        log.info("开始创建会话（问答匿名用）：聊天室ID={}", chatRoomInfo4Conv.getChatroomId());

        List<Map<String, Object>> sessionResults = new ArrayList<>();

        List<ChatRoomTaskInfo4Conv> chatRoomTaskList4Conv = chatRoomInfo4Conv.getChatRoomTaskList4Conv();

        // 计算总会话数：任务A有3个任务，任务B有2个任务，任务C有1个任务，则总会话数是6
        int totalSessions = 0;
        for (ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv : chatRoomTaskList4Conv) {
            int recycleCnt = chatRoomTaskInfo4Conv.getTrainingRecycleCnt() != null ? chatRoomTaskInfo4Conv.getTrainingRecycleCnt() : 1;
            totalSessions += recycleCnt;
        }

        log.info("计算总会话数（问答匿名用）：{}", totalSessions);

        // 立即创建第一个会话
        if (!chatRoomTaskList4Conv.isEmpty()) {
            ChatRoomTaskInfo4Conv firstTask4Conv = chatRoomTaskList4Conv.get(0);

            // 创建第一个session
            Map<String, Object> firstSession = createSingleSession4Conv(firstTask4Conv, sceneName, staffId, token, isThinking, isStreaming, teamId, chatRoomInfo4Conv, creator, qaReportMainId);
            log.info("立即创建第一个会话成功（问答匿名用）：sessionId={}", firstSession.get("sessionId"));

            sessionResults.add(firstSession);
        }

        // 使用ScheduledFuture延时创建其余会话
        createDelayedSessions4Conv(chatRoomInfo4Conv, sceneName, staffId, token, isThinking, isStreaming, sessionResults, totalSessions - 1, teamId, creator, qaReportMainId, tempSubscribeId);

        return sessionResults;
    }

    /**
     * 创建延时会话for问答匿名
     */
    private void createDelayedSessions4Conv(ChatRoomInfo4Conv chatRoomInfo4Conv, String sceneName, Long staffId,
                                            String token, String isThinking, String isStreaming,
                                            List<Map<String, Object>> sessionResults, int remainingSessions, Long teamId, String creator, Long qaReportMainId, String tempSubscribeId) {
        if (remainingSessions <= 0) {
            return;
        }

        Random random = new Random();
        List<ChatRoomTaskInfo4Conv> taskList = chatRoomInfo4Conv.getChatRoomTaskList4Conv();

        // 为每个剩余会话安排延时创建
        int sessionIndex = 0;
        for (ChatRoomTaskInfo4Conv taskInfo : taskList) {
            int recycleCnt = taskInfo.getTrainingRecycleCnt() != null ? taskInfo.getTrainingRecycleCnt() : 1;

            // 跳过第一个会话（已经立即创建）
            int startIndex = (sessionIndex == 0) ? 1 : 0;

            for (int i = startIndex; i < recycleCnt; i++) {
                if (remainingSessions <= 0) {
                    break;
                }

                // 在进线频率范围内生成随机延时（分钟）
                int delayMinutes = random.nextInt(chatRoomInfo4Conv.getEntryFreqMax() - chatRoomInfo4Conv.getEntryFreqMin() + 1)
                        + chatRoomInfo4Conv.getEntryFreqMin();

                ScheduledFuture<?> future = scheduledExecutorService.schedule(() -> {
                    try {
                        Map<String, Object> session = createSingleSession4Conv(taskInfo, sceneName, staffId, token, isThinking, isStreaming, teamId, chatRoomInfo4Conv,creator, qaReportMainId);

                        log.info("延时创建会话成功（问答匿名用）：sessionId={}, 延时={}分钟", session.get("sessionId"), delayMinutes);

                        // 构建延时会话的响应消息并发送到前端
                        Map<String, Object> delayedResponse = new HashMap<>();
                        delayedResponse.put("success", true);
                        delayedResponse.put("message", "延时会话创建成功（问答匿名用）");
                        delayedResponse.put("sessionCount", 1);
                        delayedResponse.put("sessions", List.of(session));
                        delayedResponse.put("isDelayed", true); // 标识这是延时创建的会话
                        //result加上第一个任务的所有信息
                        delayedResponse.put("taskInfo", taskInfo); // 标识这是延时创建的会话

                        // 发送到隔离的topic，防止串台
                        String isolatedTopic = "/topic/scws4qa/init/" + tempSubscribeId;
                        String responseJson = objectMapper.writeValueAsString(delayedResponse);
                        messagingTemplate.convertAndSend(isolatedTopic, responseJson);
                        log.info("延时会话消息已发送到隔离topic（问答匿名用）：sessionId={}, topic={}", session.get("sessionId"), isolatedTopic);

                    } catch (Exception e) {
                        log.error("延时创建会话失败（问答匿名用）：taskId={}, 延时={}分钟", taskInfo.getTaskId(), delayMinutes, e);
                    }
//                    }, delayMinutes, TimeUnit.MINUTES);
                }, delayMinutes, TimeUnit.SECONDS);//测试

                log.info("安排延时创建会话（问答匿名用）：taskId={}, 延时={}分钟", taskInfo.getTaskId(), delayMinutes);
//                }

                remainingSessions--;
            }
            sessionIndex++;
        }
    }








    /**
     * 根据ID查找剧本
     */
    private ScriptDetailDTO findScriptById(List<ScriptDetailDTO> scriptList, Long scriptId) {
        if (scriptList == null || scriptId == null) {
            return null;
        }
        return scriptList.stream()
                .filter(script -> scriptId.equals(script.getId()))
                .findFirst()
                .orElse(null);
    }


    /**
     * 创建单个会话for问答匿名
     */
    private Map<String, Object> createSingleSession4Conv(ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv, String sceneName, Long staffId,
                                                         String token, String isThinking, String isStreaming, Long teamId, ChatRoomInfo4Conv chatRoomInfo4Conv, String creator, Long qaReportMainId) {
        try {

            // 2.1 判断任务目的标签，决定使用新逻辑还是旧逻辑
            String fakeRobotToken;
            QaSimplelDtoWithUUID qaSimplelDtoWithUUID = null;
            String t3rdSysPrmt = null;
            Long convKbId = null;

            if (TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                // newLogic: 使用高频知识库
                log.info("使用新逻辑（高频知识库），taskPurposeTag=0，qaMainId={}", chatRoomTaskInfo4Conv.getQaMainId());

                // 2.2.1 获取问答列表
                List<QaSimpleDto> qaSimpDtoList = trainQaImportDtlService.getRdmListByMain(chatRoomTaskInfo4Conv.getQaMainId(), chatRoomTaskInfo4Conv.getFreqAuesCnt());
                log.info("获取到问答数据数量：{}", qaSimpDtoList.size());

                // 2.2.2 生成令牌
                fakeRobotToken = qa2StackService.generateFakeRobotToken(qaSimpDtoList,teamId, creator);
                log.info("生成问答栈式机器人令牌成功：{}", fakeRobotToken);

                // 3. 出栈第一条数据
                qaSimplelDtoWithUUID = qa2StackService.getNextBuyerMessageAsQa(fakeRobotToken);
                log.info("出栈第一条问答数据：{}", qaSimplelDtoWithUUID != null ? qaSimplelDtoWithUUID.getUuid() : "null");

            } else {
                // oldLogic: 使用原有逻辑
                log.info("使用旧逻辑（知识库模板），taskPurposeTag={}", chatRoomTaskInfo4Conv.getTaskPurposeTag());

                // 3.构建最终的系统提示词
                TaskConvKbDtlDetailDTO taskConvKbDtlDetailDTO = get3rdSysPrmtByRdm(chatRoomTaskInfo4Conv.getTaskId(), teamId);

                if (taskConvKbDtlDetailDTO != null) {
                    t3rdSysPrmt = taskConvKbDtlDetailDTO.getFinalChatLog();
                    convKbId = taskConvKbDtlDetailDTO.getId();
                }

                //3.1 获取假机器人的聊天令牌
                fakeRobotToken = fakeRobotService.generateFakeRobotToken(t3rdSysPrmt);
            }

            // 4. 根据逻辑类型获取系统提示词
            String finalSysPrmtFromTask;
            if ((TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) && qaSimplelDtoWithUUID != null) {
                // 使用新逻辑：从问答数据生成系统提示词
                finalSysPrmtFromTask = getSysPrmt(qaSimplelDtoWithUUID.getQuestion());
                log.info("使用新逻辑生成系统提示词，问题：{}", qaSimplelDtoWithUUID.getQuestion());
            } else {
                // 使用旧逻辑：空字符串
                finalSysPrmtFromTask = "";
                log.info("使用旧逻辑，系统提示词为空");
            }

            // 2.1 无论哪个地方调用大模型的，都要完成计费，我觉得这个可以后面再做

            // 2.2 这个地方漏了一步，应该用最终的提示词，然后进行各种替换，但是昨天我好像写了的，不知道放哪去了


            // 获取第一个外部产品ID
            String firstExternalProductId = getExternalProductId(chatRoomTaskInfo4Conv.getScriptDetailDTO());

            // 创建会话
            long startTimeInitSession = System.currentTimeMillis();
            log.info("🚀 开始调用bmForQaService.initSession，时间戳：{}", startTimeInitSession);

            Map<String, Object> result = bmForQaService.initSession(
                    sceneName,
                    staffId.toString(),
                    token,
                    Boolean.parseBoolean(isThinking),
                    Boolean.parseBoolean(isStreaming),
                    finalSysPrmtFromTask,
                    firstExternalProductId, 0,
                    fakeRobotToken, qaSimplelDtoWithUUID, chatRoomTaskInfo4Conv,teamId
            );

            long endTimeInitSession = System.currentTimeMillis();
            long durationInitSession = endTimeInitSession - startTimeInitSession;
            log.info("✅ bmForQaService.initSession调用完成，耗时：{}ms ({}秒)，时间戳：{}",
                    durationInitSession, durationInitSession / 1000.0, endTimeInitSession);

            // 2.2 新建一个私有方法，完成train_conv_winchat_dtl表的插入 sp_step3
            Long winchatDtlId = createWinchatDtlRecord(chatRoomInfo4Conv.getWinChatMainId(),
                    chatRoomTaskInfo4Conv.getTaskId(),
                    (String) result.get("sessionId"),
                    chatRoomTaskInfo4Conv.getRandomKbChatLog(),
                    "",
                    t3rdSysPrmt,
                    finalSysPrmtFromTask,
                    teamId,
                    convKbId);

            // 将返回的主键id设置到result中，使用sessionId作为key的前缀防止重复
            result.put("winchatDtlId_" + result.get("sessionId"), winchatDtlId);
            // TODO 3 存进session中


            // 2.3 新建一个私有方法，完成train_qa_report_dtl表的插入
            Long qaReportDtlId = createQaReportDtlRecord(
                    chatRoomTaskInfo4Conv.getTaskId(),
                    (String) result.get("sessionId"),
                    qaReportMainId,
                    teamId, creator, chatRoomTaskInfo4Conv.getQaMainId());

            // 将返回的主键id设置到result中，使用sessionId作为key的前缀防止重复
            result.put("qaReportDtlId_" + result.get("sessionId"), qaReportDtlId);
            saveReportDtlIdToCache((String) result.get("sessionId"), qaReportDtlId);

            // 添加商品信息到结果中
            // 只有在非训练任务时才添加
            if (TaskPurposeTag.TRAINING.getCode() != chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                enrichResultWithScriptInfo(result, chatRoomTaskInfo4Conv.getScriptDetailDTO());
            }

            // 2.3 将firstMessage写进聊天记录表中
            createChatLogRecord(winchatDtlId, (String) result.get("sessionId"),
                    (String) result.get("robotName"), result.get("firstMessageWithoutJson"), teamId);

            // 2.4 将qaReportDtlId更新回train_qa_rdm表中
            if (qaSimplelDtoWithUUID != null && qaReportDtlId != null) {
                savDtlIdToDb(qaSimplelDtoWithUUID, qaReportDtlId, teamId);
            }

            return result;

        } catch (Exception e) {
            log.error("创建单个会话失败（问答匿名用）：chatRoomTaskInfo4Conv={}", chatRoomTaskInfo4Conv.getTaskId(), e);
            throw new RuntimeException("创建会话失败：" + e.getMessage());
        }
    }




    /**
     * 重载的getExternalProductId方法，从ScriptDetailDTO中获取
     */
    private String getExternalProductId(ScriptDetailDTO scriptDetail) {
        if (scriptDetail == null || scriptDetail.getProductList() == null || scriptDetail.getProductList().isEmpty()) {
            return null;
        }

        var firstProduct = scriptDetail.getProductList().get(0);
        return firstProduct.getExternalProductId();
    }

    /**
     * 为结果添加剧本信息
     */
    private void enrichResultWithScriptInfo(Map<String, Object> result, ScriptDetailDTO scriptDetail) {
        if (scriptDetail.getProductList() != null && !scriptDetail.getProductList().isEmpty()) {
            List<Map<String, Object>> processedProducts = new ArrayList<>();

            for (var product : scriptDetail.getProductList()) {
                Map<String, Object> processedProduct = new HashMap<>();
                processedProduct.put("id", product.getId());
                processedProduct.put("externalProductId", product.getExternalProductId());
                processedProduct.put("externalProductName", product.getExternalProductName());
                processedProduct.put("externalProductLink", product.getExternalProductLink());
                processedProduct.put("externalProductImage", product.getExternalProductImage());
                processedProduct.put("status", product.getStatus());
                processedProduct.put("category", product.getCategory());
                processedProduct.put("platform", product.getPlatform());
                processedProduct.put("shopName", product.getShopName());
                processedProducts.add(processedProduct);
            }

            result.put("productList", processedProducts);
        }
    }

    /**
     * 设置聊天室配置
     */
    private void setChatroomConfig(ChatRoomInfo4Conv chatRoomInfo, Long receChatRoomId, Long teamId) {
        try {
            ChatroomDetailDTO chatroomDetail = trainReceptionChatroomService.getChatroomDetail(receChatRoomId, teamId);
            if (chatroomDetail != null) {
                chatRoomInfo.setEntryFreqMin(chatroomDetail.getEntryFreqMin());
                chatRoomInfo.setEntryFreqMax(chatroomDetail.getEntryFreqMax());
                chatRoomInfo.setReceptionDuration(chatroomDetail.getReceptionDuration());
                chatRoomInfo.setReceptionSkin(chatroomDetail.getReceptionSkin());
                chatRoomInfo.setSceneMode(chatroomDetail.getSceneMode());
                chatRoomInfo.setTimerDisplay(chatroomDetail.getTimerDisplay());
                log.info("成功从数据库获取聊天室配置（问答匿名用）：entryFreqMin={}, entryFreqMax={}, receptionDuration={}, receptionSkin={}, sceneMode={}, timerDisplay={}",
                        chatroomDetail.getEntryFreqMin(), chatroomDetail.getEntryFreqMax(), chatroomDetail.getReceptionDuration(),
                        chatroomDetail.getReceptionSkin(), chatroomDetail.getSceneMode(), chatroomDetail.getTimerDisplay());
            } else {
                setDefaultChatroomConfig(chatRoomInfo);
            }
        } catch (Exception e) {
            log.warn("查询聊天室配置失败，使用默认值（问答匿名用）：receChatRoomId={}", receChatRoomId, e);
            setDefaultChatroomConfig(chatRoomInfo);
        }
    }

    /**
     * 设置默认聊天室配置
     */
    private void setDefaultChatroomConfig(ChatRoomInfo4Conv chatRoomInfo) {
        chatRoomInfo.setEntryFreqMin(1);
        chatRoomInfo.setEntryFreqMax(5);
        chatRoomInfo.setReceptionDuration(30);
        chatRoomInfo.setReceptionSkin("0");
        chatRoomInfo.setSceneMode(0);
        chatRoomInfo.setTimerDisplay(false);
        log.info("使用默认聊天室配置（问答匿名用）：entryFreqMin=1, entryFreqMax=5, receptionDuration=30, receptionSkin=\"0\", sceneMode=0, timerDisplay=false");
    }

    /**
     * 创建聊天窗口主记录
     *
     * @param chatroomId 聊天室ID
     * @param staffId    员工ID
     * @param teamId     团队ID
     * @return 创建的记录ID
     */
    private Long createWinchatMainRecord(Long chatroomId, Long staffId, Long teamId) {
        try {
            log.info("开始创建聊天窗口主记录：chatroomId={}, staffId={}, teamId={}", chatroomId, staffId, teamId);

            var winchatMain = trainConvWinchatMainService.createWinchatMain(chatroomId, staffId, teamId, "system");

            if (winchatMain != null && winchatMain.getId() != null) {
                log.info("创建聊天窗口主记录成功：winChatMainId={}", winchatMain.getId());
                return winchatMain.getId();
            } else {
                log.error("创建聊天窗口主记录失败：返回结果为空");
                throw new RuntimeException("创建聊天窗口主记录失败");
            }
        } catch (Exception e) {
            log.error("创建聊天窗口主记录异常：chatroomId={}, staffId={}, teamId={}", chatroomId, staffId, teamId, e);
            throw new RuntimeException("创建聊天窗口主记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建问答报告主表记录
     *
     * @param receChatRoomId 聊天室ID
     * @param ownerId        员工ID
     * @param teamId         团队ID
     * @param ownerUserName  创建人用户名
     * @param anoRealName    考试者姓名
     * @param anoRealNo      考试者编号
     * @return 创建的记录ID
     */
    private Long createQaReportMainRecord(Long receChatRoomId, Long ownerId, Long teamId,
                                         String ownerUserName, String anoRealName, String anoRealNo) {
        try {
            log.info("开始创建问答报告主表记录：receChatRoomId={}, ownerId={}, teamId={}, ownerUserName={}, anoRealName={}, anoRealNo={}",
                    receChatRoomId, ownerId, teamId, ownerUserName, anoRealName, anoRealNo);

            // 创建TrainQaReportMain对象
            var qaReportMain = new com.yiyi.ai_train_playground.entity.task.TrainQaReportMain();
            qaReportMain.setChatroomId(receChatRoomId);
            qaReportMain.setStaffId(ownerId);
            qaReportMain.setExamUserRealName(anoRealName);
            qaReportMain.setExamUserNo(anoRealNo);
            qaReportMain.setExamScore(java.math.BigDecimal.ZERO);
            qaReportMain.setCreator(ownerUserName);

            // 调用service创建记录
            var trainQaReportMain = trainQaReportMainService.create(qaReportMain, teamId);

            if (trainQaReportMain != null && trainQaReportMain.getId() != null) {
                log.info("创建问答报告主表记录成功：qaReportMainId={}", trainQaReportMain.getId());
                return trainQaReportMain.getId();
            } else {
                log.error("创建问答报告主表记录失败：返回结果为空");
                throw new RuntimeException("创建问答报告主表记录失败");
            }
        } catch (Exception e) {
            log.error("创建问答报告主表记录异常：receChatRoomId={}, ownerId={}, teamId={}", receChatRoomId, ownerId, teamId, e);
            throw new RuntimeException("创建问答报告主表记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建问答报告明细记录
     *
     * @param taskId    任务ID
     * @param sessionId 会话ID
     * @param qaMainId  QA主表ID
     * @param teamId    团队ID
     * @param creator
     * @param qaMainId  QA主表ID
     * @return 创建的记录ID
     */
    private Long createQaReportDtlRecord(Long taskId, String sessionId, Long qaReportMainId, Long teamId, String creator, Long qaMainId) {
        try {
            log.info("开始创建问答报告明细记录：taskId={}, sessionId={}, qaMainId={}, teamId={}",
                    taskId, sessionId, qaMainId, teamId);

            // 创建TrainQaReportDtl对象
            var qaReportDtl = new com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl();
            qaReportDtl.setTaskId(taskId);
            qaReportDtl.setSessionId(sessionId);
            qaReportDtl.setQaMainId(qaMainId);
            qaReportDtl.setRpMainId(qaReportMainId);

            // 调用service创建记录
            boolean createResult = trainQaReportDtlService.create(qaReportDtl, teamId, creator);

            if (createResult && qaReportDtl.getId() != null) {
                log.info("创建问答报告明细记录成功：qaReportDtlId={}", qaReportDtl.getId());
                return qaReportDtl.getId();
            } else {
                log.error("创建问答报告明细记录失败：createResult={}", createResult);
                throw new RuntimeException("创建问答报告明细记录失败");
            }
        } catch (Exception e) {
            log.error("创建问答报告明细记录异常：taskId={}, sessionId={}, qaMainId={}, teamId={}",
                    taskId, sessionId, qaMainId, teamId, e);
            throw new RuntimeException("创建问答报告明细记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建聊天窗口明细记录
     *
     * @param convWinchatMainId  聊天窗口主记录ID
     * @param taskId             任务ID
     * @param sessionId          会话ID
     * @param f1stRawChatlog     最原始的聊天记录
     * @param s2ndAggSysPrompt   聚合过后的系统提示词
     * @param t3rdRewrite        重写过后的聊天记录
     * @param f4thFinalSysPrompt 最终的系统提示词
     * @param teamId             团队ID
     * @param convKbId           知识库明细ID
     * @return 创建的明细记录ID
     */
    private Long createWinchatDtlRecord(Long convWinchatMainId, Long taskId, String sessionId,
                                        String f1stRawChatlog, String s2ndAggSysPrompt,
                                        String t3rdRewrite, String f4thFinalSysPrompt,
                                        Long teamId, Long convKbId) {
        try {
            log.info("开始创建聊天窗口明细记录：convWinchatMainId={}, taskId={}, sessionId={}, teamId={}, convKbId={}",
                    convWinchatMainId, taskId, sessionId, teamId, convKbId);

            var winchatDtl = trainConvWinchatDtlService.createWinchatDtlWithFields(
                    convWinchatMainId, taskId, sessionId,
                    f1stRawChatlog, s2ndAggSysPrompt, t3rdRewrite, f4thFinalSysPrompt,
                    teamId, "system", convKbId);

            if (winchatDtl != null && winchatDtl.getId() != null) {
                log.info("创建聊天窗口明细记录成功：winchatDtlId={}", winchatDtl.getId());
                return winchatDtl.getId();
            } else {
                log.error("创建聊天窗口明细记录失败：返回结果为空");
                throw new RuntimeException("创建聊天窗口明细记录失败");
            }
        } catch (Exception e) {
            log.error("创建聊天窗口明细记录异常：convWinchatMainId={}, taskId={}, sessionId={}, teamId={}, convKbId={}",
                    convWinchatMainId, taskId, sessionId, teamId, convKbId, e);
            throw new RuntimeException("创建聊天窗口明细记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建聊天记录
     *
     * @param convDtlId    聊天窗口明细记录ID
     * @param sessionId    会话ID
     * @param sender       发送者
     * @param firstMessage 首条消息（可能是JSON格式）
     * @param teamId       团队ID
     */
    private void createChatLogRecord(Long convDtlId, String sessionId, String sender, Object firstMessage, Long teamId) {
        try {
            log.info("开始创建聊天记录：convDtlId={}, sessionId={}, sender={}, teamId={}",
                    convDtlId, sessionId, sender, teamId);

            // 处理firstMessage内容
            String content = processFirstMessage(firstMessage);

            // 创建聊天记录
            var chatLog = trainConvWinchatLogService.createChatLog(
                    convDtlId,
                    sessionId,
                    sender,
                    content,
                    LocalDateTime.now(),
                    SenderType.BUYER.getCode(),
                    teamId,
                    "system"
            );

            if (chatLog != null && chatLog.getId() != null) {
                log.info("创建聊天记录成功：chatLogId={}", chatLog.getId());
            } else {
                log.error("创建聊天记录失败：返回结果为空");
                throw new RuntimeException("创建聊天记录失败");
            }
        } catch (Exception e) {
            log.error("创建聊天记录异常：convDtlId={}, sessionId={}, sender={}, teamId={}",
                    convDtlId, sessionId, sender, teamId, e);
            throw new RuntimeException("创建聊天记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理firstMessage内容
     * 如果是JSON数组格式，则提取result数组转换为字符串
     * 否则直接返回字符串形式
     *
     * @param firstMessage 原始firstMessage对象
     * @return 处理后的内容字符串
     */
    private String processFirstMessage(Object firstMessage) {
        if (firstMessage == null) {
            return "";
        }

        try {
            // 如果是字符串，直接返回
            if (firstMessage instanceof String) {
                return (String) firstMessage;
            }

            // 如果是Map类型，尝试解析JSON结构
            if (firstMessage instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> messageMap = (Map<String, Object>) firstMessage;

                // 检查是否有result字段且为数组
                Object result = messageMap.get("result");
                if (result instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> resultList = (List<String>) result;
                    return String.join("\n", resultList);
                }
            }

            // 其他情况，转换为JSON字符串
            return objectMapper.writeValueAsString(firstMessage);

        } catch (Exception e) {
            log.warn("处理firstMessage时发生异常，使用toString()方法：{}", e.getMessage());
            return firstMessage.toString();
        }
    }

    /**
     * 将qaReportDtlId更新回train_qa_rdm表中
     *
     * @param qaSimplelDtoWithUUID 问答数据对象
     * @param qaReportDtlId        问答报告明细ID
     * @param teamId               团队ID
     */
    private void savDtlIdToDb(QaSimplelDtoWithUUID qaSimplelDtoWithUUID, Long qaReportDtlId, Long teamId) {
        try {
            if (qaSimplelDtoWithUUID == null || qaSimplelDtoWithUUID.getUuid() == null) {
                log.warn("qaSimplelDtoWithUUID为空或uuid为空，跳过更新reportDtlId");
                return;
            }

            log.info("开始将qaReportDtlId更新回train_qa_rdm表：uuid={}, qaReportDtlId={}, teamId={}",
                    qaSimplelDtoWithUUID.getUuid(), qaReportDtlId, teamId);

            // 1.1 利用qaSimplelDtoWithUUID.getUuid先查询到数据，获得trainQaRdm
            com.yiyi.ai_train_playground.entity.task.TrainQaRdm trainQaRdm = trainQaRdmService.getByUUID(qaSimplelDtoWithUUID.getUuid());
            if (trainQaRdm == null) {
                log.warn("根据UUID未找到TrainQaRdm记录：uuid={}, teamId={}", qaSimplelDtoWithUUID.getUuid(), teamId);
                return;
            }

            // 1.2 利用trainQaRdm.setReportDtlId(qaReportDtlId)
            trainQaRdm.setReportDtlId(qaReportDtlId);

            // 1.3 再更新回去即可
            boolean updateResult = trainQaRdmService.updateByUUID(trainQaRdm);
            if (updateResult) {
                log.info("将qaReportDtlId更新回train_qa_rdm表成功：uuid={}, qaReportDtlId={}", qaSimplelDtoWithUUID.getUuid(), qaReportDtlId);
            } else {
                log.error("将qaReportDtlId更新回train_qa_rdm表失败：uuid={}, qaReportDtlId={}", qaSimplelDtoWithUUID.getUuid(), qaReportDtlId);
            }
        } catch (Exception e) {
            log.error("将qaReportDtlId更新回train_qa_rdm表异常：uuid={}, qaReportDtlId={}, teamId={}",
                    qaSimplelDtoWithUUID != null ? qaSimplelDtoWithUUID.getUuid() : "null", qaReportDtlId, teamId, e);
        }
    }

    /**
     * 将qaReportDtlId_存储到缓存中
     *
     * @param sessionId 会话ID
     * @param qaReportDtlId 聊天明细ID
     */
    private void saveReportDtlIdToCache(String sessionId, Long qaReportDtlId) {
        try {
            // 1、先从session里面拿到session配置
            String sessionConfigKey = "session:" + sessionId;
            Map<Object, Object> sessionFromRedis = cacheManager.getHash(sessionConfigKey);

            // 2、将qaReportDtlId_存储到session配置中
            sessionFromRedis.put("qaReportDtlId_", qaReportDtlId);

            // 3、将更新后的session配置保存回Redis
            Map<String, Object> sessionConfig = new HashMap<>();
            for (Map.Entry<Object, Object> entry : sessionFromRedis.entrySet()) {
                sessionConfig.put(String.valueOf(entry.getKey()), entry.getValue());
            }
            cacheManager.putHash(sessionConfigKey, sessionConfig, 24, TimeUnit.HOURS);

            log.info("qaReportDtlId已存储到session配置中：sessionId={}, qaReportDtlId={}", sessionId, qaReportDtlId);
        } catch (Exception e) {
            log.error("存储qaReportDtlId到session配置失败：sessionId={}, qaReportDtlId={}", sessionId, qaReportDtlId, e);
            // 不抛出异常，避免影响主流程
        }
    }
}