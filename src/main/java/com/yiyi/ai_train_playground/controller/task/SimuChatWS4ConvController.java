package com.yiyi.ai_train_playground.controller.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.consts.CONSTS;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.task.*;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.enums.TaskPurposeTag;
import com.yiyi.ai_train_playground.service.bm.BmForKbService;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import com.yiyi.ai_train_playground.service.impl.TrainScriptServiceImpl;
import com.yiyi.ai_train_playground.service.task.FakeRobotService;
import com.yiyi.ai_train_playground.service.task.TrainReceptionChatroomService;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatMainService;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatLogService;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.dto.converkb.TaskConvKbDtlDetailDTO;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportDtlService;
import com.yiyi.ai_train_playground.service.task.impl.Qa2StackServiceImpl;
import com.yiyi.ai_train_playground.enums.SenderType;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Controller
public class SimuChatWS4ConvController {

    @Autowired
    private BmForKbService bmForKbService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;

    @Autowired
    private TrainReceptionChatroomService trainReceptionChatroomService;

    @Autowired
    private TrainScriptServiceImpl trainScriptService;

    @Autowired
    private TrainKbTplService trainKbTplService;

    @Autowired
    private TrainConvWinchatMainService trainConvWinchatMainService;

    @Autowired
    private com.yiyi.ai_train_playground.service.kb.TrainConvWinchatDtlService trainConvWinchatDtlService;

    @Autowired
    private TrainConvWinchatLogService trainConvWinchatLogService;

    @Autowired
    private JwtUtil jwtUtil;

    // 定时任务执行器，用于延时创建机器人
    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);

    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;

    @Autowired
    private TrainQaImportDtlService trainQaImportDtlService;

    @Autowired
    private Qa2StackServiceImpl qa2StackService;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    @Qualifier("fakeRobotServiceImpl")
    private FakeRobotService fakeRobotService;


    /**
     * 初始化模拟聊天会话
     *
     * @param message 包含receChatRoomId、sceneName、token、isThinking、isStreaming的JSON消息
     * @return 初始化结果
     */
    @MessageMapping("/smc4conv/init")
    @SendTo("/topic/smc4conv/init")
    public String initSession(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);

            // 获取参数
            Long receChatRoomId = getLongFromRequest(request, "receChatRoomId");
            String sceneName = (String) request.get("sceneName");
            String token = (String) request.get("token");
            String isThinking = (String) request.get("isThinking");
            String isStreaming = (String) request.get("isStreaming");

            log.info("开始初始化模拟聊天会话（知识库转换用）：receChatRoomId={}, sceneName={}", receChatRoomId, sceneName);

            // 1. 验证参数
            if (receChatRoomId == null || sceneName == null || token == null) {
                throw new RuntimeException("必要参数不能为空：receChatRoomId、sceneName、token");
            }

            // 2. 从token中获取员工ID（WebSocket环境中SecurityContext为空，需要手动解析JWT）
            Long staffId = jwtUtil.getUserIdFromToken(token);
            Long teamId = jwtUtil.getTeamIdFromToken(token);

            // 验证token有效性
            if (!jwtUtil.validateToken(token)) {
                throw new RuntimeException("无效的JWT token");
            }

            if (staffId == null) {
                throw new RuntimeException("无法从token中获取员工ID");
            }

            // 3. 验证员工权限
            TrainStaff staff = trainReceptionChatroomService.validateStaffPermission(receChatRoomId, staffId);
            if (staff == null) {
                throw new RuntimeException("员工无权限访问该聊天室");
            }

            // 4. 查询聊天室任务列表
            List<ChatRoomTaskInfo> chatRoomTaskList = trainReceptionChatroomService.getChatRoomTaskList(receChatRoomId);
            if (chatRoomTaskList.isEmpty()) {
                throw new RuntimeException("聊天室没有配置任务");
            }

            // 4.1 插入winchat_main
            Long winChatMainId = createWinchatMainRecord(receChatRoomId, staffId, teamId);

            // 5. 构建聊天室信息
            ChatRoomInfo4Conv chatRoomInfo4Conv = buildChatRoomInfo(receChatRoomId, chatRoomTaskList, teamId);

            // 5.1 设置chatRoomInfo4Conv
            chatRoomInfo4Conv.setWinChatMainId(winChatMainId);

            // 6. 创建会话
            List<Map<String, Object>> sessionResults = createSessions(chatRoomInfo4Conv, sceneName, staffId, token, isThinking, isStreaming, teamId);        

            // 7. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "模拟聊天会话初始化成功（知识库转换用）");
            result.put("chatroomId", receChatRoomId);
            result.put("sessionCount", sessionResults.size());
            result.put("sessions", sessionResults);
            result.put("staffName", staff.getDisplayName());
            // result加上第一个任务的所有信息
            if (!chatRoomTaskList.isEmpty()) {
                result.put("taskInfo", chatRoomTaskList.get(0));
            }
            result.put("chatRoomInfo", chatRoomInfo4Conv);

            log.info("模拟聊天WebSocket（知识库转换用）初始化会话成功: 聊天室ID={}, 会话数量={}", receChatRoomId, sessionResults.size());
            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            log.error("模拟聊天WebSocket（知识库转换用）初始化会话失败", e);
            try {
                Map<String, Object> errorResult = Map.of(
                        "error", true,
                        "message", "初始化会话失败: " + e.getMessage()
                );
                return objectMapper.writeValueAsString(errorResult);
            } catch (Exception ex) {
                return "{\"error\":true,\"message\":\"初始化会话失败\"}";
            }
        }
    }

    /**
     * 处理客户端发来的消息（重命名自sendMessage）
     */
    @MessageMapping("/smc4conv/send")
    public void handlerAndResponseMessage(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sessionId = (String) request.get("sessionId");
            String userMessage = (String) request.get("message");

            //强行让前端传msgId add by germmy@20250827
            String msgId = (String) request.get("msgId");

            if (sessionId == null || userMessage == null) {
                messagingTemplate.convertAndSend("/topic/smc4conv/chat/" + sessionId,
                        "{\"error\":true,\"message\":\"参数不完整\"}");
                return;
            }

            // 调用大模型管理器发送消息（非流式响应）
            try {
                String response = bmForKbService.handlerAndRespWithNTNS(sessionId, userMessage, msgId);
                // 发送非流式响应到客户端
                messagingTemplate.convertAndSend("/topic/smc4conv/chat/" + sessionId, response);
                log.debug("模拟聊天WebSocket（知识库转换用）非流式响应完成: sessionId={}", sessionId);
            } catch (Exception error) {
                // 发送错误信息
                try {
                    Map<String, Object> errorResult = Map.of(
                            "error", true,
                            "message", error.getMessage()
                    );
                    String errorJson = objectMapper.writeValueAsString(errorResult);
                    messagingTemplate.convertAndSend("/topic/smc4conv/chat/" + sessionId, errorJson);
                } catch (Exception ex) {
                    messagingTemplate.convertAndSend("/topic/smc4conv/chat/" + sessionId,
                            "{\"error\":true,\"message\":\"发送消息失败\"}");
                }
                log.error("模拟聊天WebSocket（知识库转换用）发送消息失败: sessionId={}", sessionId, error);
            }

        } catch (Exception e) {
            log.error("模拟聊天WebSocket（知识库转换用）处理发送消息失败", e);
            try {
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String sessionId = (String) request.get("sessionId");
                messagingTemplate.convertAndSend("/topic/smc4conv/chat/" + sessionId,
                        "{\"error\":true,\"message\":\"处理消息失败\"}");
            } catch (Exception ex) {
                // 无法解析sessionId，无法发送错误消息
                log.error("无法解析sessionId发送错误消息", ex);
            }
        }
    }

    /**
     * 关闭会话并清除资源
     */
    @MessageMapping("/smc4conv/close")
    public void closeSession(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sessionId = (String) request.get("sessionId");

            if (sessionId == null || sessionId.trim().isEmpty()) {
                log.warn("模拟聊天（知识库转换用）关闭会话失败：sessionId为空");
                return;
            }

            log.info("模拟聊天（知识库转换用）收到关闭会话请求: sessionId={}", sessionId);

            // 调用BigModelManager清除会话资源
            bmForKbService.clearSession(sessionId);

            // 向客户端发送关闭确认
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会话已关闭（知识库转换用）");
            response.put("sessionId", sessionId);

            String responseJson = objectMapper.writeValueAsString(response);
            messagingTemplate.convertAndSend("/topic/smc4conv/close/" + sessionId, responseJson);

            log.info("模拟聊天（知识库转换用）会话关闭完成: sessionId={}", sessionId);

        } catch (Exception e) {
            log.error("模拟聊天（知识库转换用）关闭会话失败", e);
            try {
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String sessionId = (String) request.get("sessionId");

                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "关闭会话失败: " + e.getMessage());
                errorResponse.put("sessionId", sessionId);

                String errorJson = objectMapper.writeValueAsString(errorResponse);
                messagingTemplate.convertAndSend("/topic/smc4conv/close/" + sessionId, errorJson);
            } catch (Exception ex) {
                log.error("模拟聊天（知识库转换用）发送关闭会话错误响应失败", ex);
            }
        }
    }

    /**
     * 从请求中获取Long类型参数
     */
    private Long getLongFromRequest(Map<String, Object> request, String key) {
        Object value = request.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 构建聊天室信息
     */
    private ChatRoomInfo4Conv buildChatRoomInfo(Long receChatRoomId, List<ChatRoomTaskInfo> chatRoomTaskList, Long teamId) {
        log.info("构建聊天室信息（知识库转换用）：receChatRoomId={}", receChatRoomId);

        ChatRoomInfo4Conv chatRoomInfo4Conv = new ChatRoomInfo4Conv();
        chatRoomInfo4Conv.setChatroomId(receChatRoomId);
        chatRoomInfo4Conv.setChatRoomTaskList(chatRoomTaskList);

        // 设置聊天室配置
        setChatroomConfig(chatRoomInfo4Conv, receChatRoomId, teamId);

        //设置剧本
        List<ChatRoomTaskInfo4Conv> chatRoomTaskList4ConvList = new ArrayList<>();
        ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv;

        //构造新的任务列表，从原有的任务列表中，一点一点扒过来
        //先设置剧本
        for (ChatRoomTaskInfo taskInfo : chatRoomTaskList) {
            if (taskInfo.getScriptId() != null) {
                try {
                    ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(taskInfo.getScriptId(), teamId);
                    chatRoomTaskInfo4Conv = new ChatRoomTaskInfo4Conv();
                    BeanUtils.copyProperties(taskInfo, chatRoomTaskInfo4Conv);
                    chatRoomTaskInfo4Conv.setScriptDetailDTO(scriptDetail);
                    chatRoomTaskList4ConvList.add(chatRoomTaskInfo4Conv);
                } catch (Exception e) {
                    log.warn("设置新的任务列表失败", taskInfo.getScriptId(), e);
                }
            }
        }

        // 设置处理后的任务列表到聊天室信息中
        chatRoomInfo4Conv.setChatRoomTaskList4Conv(chatRoomTaskList4ConvList);

        log.info("聊天室信息构建完成（知识库转换用）：任务数量={}, 知识库转换任务数量={}",
                chatRoomTaskList.size(), chatRoomTaskList4ConvList.size());
        return chatRoomInfo4Conv;
    }

    /**
     * 设置聊天室配置
     */
    private void setChatroomConfig(ChatRoomInfo4Conv chatRoomInfo, Long receChatRoomId, Long teamId) {
        try {
            ChatroomDetailDTO chatroomDetail = trainReceptionChatroomService.getChatroomDetail(receChatRoomId, teamId);
            if (chatroomDetail != null) {
                chatRoomInfo.setEntryFreqMin(chatroomDetail.getEntryFreqMin());
                chatRoomInfo.setEntryFreqMax(chatroomDetail.getEntryFreqMax());
                chatRoomInfo.setReceptionDuration(chatroomDetail.getReceptionDuration());
                chatRoomInfo.setReceptionSkin(chatroomDetail.getReceptionSkin());
                chatRoomInfo.setSceneMode(chatroomDetail.getSceneMode());
                chatRoomInfo.setTimerDisplay(chatroomDetail.getTimerDisplay());
                log.info("成功从数据库获取聊天室配置（知识库转换用）：entryFreqMin={}, entryFreqMax={}, receptionDuration={}, receptionSkin={}, sceneMode={}, timerDisplay={}",
                        chatroomDetail.getEntryFreqMin(), chatroomDetail.getEntryFreqMax(), chatroomDetail.getReceptionDuration(),
                        chatroomDetail.getReceptionSkin(), chatroomDetail.getSceneMode(), chatroomDetail.getTimerDisplay());
            } else {
                setDefaultChatroomConfig(chatRoomInfo);
            }
        } catch (Exception e) {
            log.warn("查询聊天室配置失败，使用默认值（知识库转换用）：receChatRoomId={}", receChatRoomId, e);
            setDefaultChatroomConfig(chatRoomInfo);
        }
    }

    /**
     * 设置默认聊天室配置
     */
    private void setDefaultChatroomConfig(ChatRoomInfo4Conv chatRoomInfo) {
        chatRoomInfo.setEntryFreqMin(1);
        chatRoomInfo.setEntryFreqMax(5);
        chatRoomInfo.setReceptionDuration(30);
        chatRoomInfo.setReceptionSkin("0");
        chatRoomInfo.setSceneMode(0);
        chatRoomInfo.setTimerDisplay(false);
        log.info("使用默认聊天室配置（知识库转换用）：entryFreqMin=1, entryFreqMax=5, receptionDuration=30, receptionSkin=\"0\", sceneMode=0, timerDisplay=false");
    }

    /**
     * 创建会话
     */
    private List<Map<String, Object>> createSessions(ChatRoomInfo4Conv chatRoomInfo4Conv, String sceneName, Long staffId,
                                                     String token, String isThinking, String isStreaming, Long teamId) {
        log.info("开始创建会话（知识库转换用）：聊天室ID={}", chatRoomInfo4Conv.getChatroomId());

        List<Map<String, Object>> sessionResults = new ArrayList<>();

        List<ChatRoomTaskInfo4Conv> chatRoomTaskList4Conv = chatRoomInfo4Conv.getChatRoomTaskList4Conv();

        // 计算总会话数：任务A有3个任务，任务B有2个任务，任务C有1个任务，则总会话数是6
        int totalSessions = 0;
        for (ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv : chatRoomTaskList4Conv) {
            int recycleCnt = chatRoomTaskInfo4Conv.getTrainingRecycleCnt() != null ? chatRoomTaskInfo4Conv.getTrainingRecycleCnt() : 1;
            totalSessions += recycleCnt;
        }

        log.info("计算总会话数（知识库转换用）：{}", totalSessions);

        // 立即创建第一个会话
        if (!chatRoomTaskList4Conv.isEmpty()) {
            ChatRoomTaskInfo4Conv firstTask4Conv = chatRoomTaskList4Conv.get(0);

            // 创建第一个session
            Map<String, Object> firstSession = createSingleSession4Conv(firstTask4Conv, sceneName, staffId, token, isThinking, isStreaming, teamId, chatRoomInfo4Conv);
            log.info("立即创建第一个会话成功（知识库转换用）：sessionId={}", firstSession.get("sessionId"));

            sessionResults.add(firstSession);
        }

        // 使用ScheduledFuture延时创建其余会话
        createDelayedSessions4Conv(chatRoomInfo4Conv, sceneName, staffId, token, isThinking, isStreaming, sessionResults, totalSessions - 1, teamId);

        return sessionResults;
    }

    /**
     * 创建延时会话for会话知识库
     */
    private void createDelayedSessions4Conv(ChatRoomInfo4Conv chatRoomInfo4Conv, String sceneName, Long staffId,
                                            String token, String isThinking, String isStreaming,
                                            List<Map<String, Object>> sessionResults, int remainingSessions, Long teamId) {
        if (remainingSessions <= 0) {
            return;
        }

        Random random = new Random();
        List<ChatRoomTaskInfo4Conv> taskList = chatRoomInfo4Conv.getChatRoomTaskList4Conv();

        // 为每个剩余会话安排延时创建
        int sessionIndex = 0;
        for (ChatRoomTaskInfo4Conv taskInfo : taskList) {
            int recycleCnt = taskInfo.getTrainingRecycleCnt() != null ? taskInfo.getTrainingRecycleCnt() : 1;

            // 跳过第一个会话（已经立即创建）
            int startIndex = (sessionIndex == 0) ? 1 : 0;

            for (int i = startIndex; i < recycleCnt; i++) {
                if (remainingSessions <= 0) {
                    break;
                }

                // 在进线频率范围内生成随机延时（分钟）
                int delayMinutes = random.nextInt(chatRoomInfo4Conv.getEntryFreqMax() - chatRoomInfo4Conv.getEntryFreqMin() + 1)
                        + chatRoomInfo4Conv.getEntryFreqMin();

                ScheduledFuture<?> future = scheduledExecutorService.schedule(() -> {
                    try {
                        Map<String, Object> session = createSingleSession4Conv(taskInfo, sceneName, staffId, token, isThinking, isStreaming, teamId, chatRoomInfo4Conv);

                        log.info("延时创建会话成功（知识库转换用）：sessionId={}, 延时={}分钟", session.get("sessionId"), delayMinutes);

                        // 构建延时会话的响应消息并发送到前端
                        Map<String, Object> delayedResponse = new HashMap<>();
                        delayedResponse.put("success", true);
                        delayedResponse.put("message", "延时会话创建成功（知识库转换用）");
                        delayedResponse.put("sessionCount", 1);
                        delayedResponse.put("sessions", List.of(session));
                        delayedResponse.put("isDelayed", true); // 标识这是延时创建的会话
                        //result加上第一个任务的所有信息
                        delayedResponse.put("taskInfo", taskInfo); // 标识这是延时创建的会话

                        // 发送到WebSocket主题，前端可以收到
                        String responseJson = objectMapper.writeValueAsString(delayedResponse);
                        messagingTemplate.convertAndSend("/topic/smc4conv/init", responseJson);
                        log.info("延时会话消息已发送到前端（知识库转换用）：sessionId={}", session.get("sessionId"));

                    } catch (Exception e) {
                        log.error("延时创建会话失败（知识库转换用）：taskId={}, 延时={}分钟", taskInfo.getTaskId(), delayMinutes, e);
                    }
//                    }, delayMinutes, TimeUnit.MINUTES);
                }, delayMinutes, TimeUnit.SECONDS);//测试

                log.info("安排延时创建会话（知识库转换用）：taskId={}, 延时={}分钟", taskInfo.getTaskId(), delayMinutes);
//                }

                remainingSessions--;
            }
            sessionIndex++;
        }
    }

    /**
     * 创建单个会话for会话知识库
     */
    private Map<String, Object> createSingleSession4Conv(ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv, String sceneName, Long staffId,
                                                         String token, String isThinking, String isStreaming, Long teamId, ChatRoomInfo4Conv chatRoomInfo4Conv) {
        try {

            // 2.1 判断任务目的标签，决定使用新逻辑还是旧逻辑
            String fakeRobotToken;
            QaSimplelDtoWithUUID qaSimplelDtoWithUUID = null;
            String t3rdSysPrmt = null;
            Long convKbId = null;

            if (TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                // newLogic: 使用高频知识库
                log.info("使用新逻辑（高频知识库），taskPurposeTag=0，qaMainId={}", chatRoomTaskInfo4Conv.getQaMainId());

                // 2.2.1 获取问答列表
                List<QaSimpleDto> qaSimpDtoList = trainQaImportDtlService.getAllDtlByMainId(chatRoomTaskInfo4Conv.getQaMainId());
                log.info("获取到问答数据数量：{}", qaSimpDtoList.size());

                // 2.2.2 生成令牌
                fakeRobotToken = qa2StackService.generateFakeRobotToken(qaSimpDtoList,teamId,"system");
                log.info("生成问答栈式机器人令牌成功：{}", fakeRobotToken);

                // 3. 出栈第一条数据
                qaSimplelDtoWithUUID = qa2StackService.getNextBuyerMessageAsQa(fakeRobotToken);
                log.info("出栈第一条问答数据：{}", qaSimplelDtoWithUUID != null ? qaSimplelDtoWithUUID.getUuid() : "null");

            } else {
                // oldLogic: 使用原有逻辑
                log.info("使用旧逻辑（知识库模板），taskPurposeTag={}", chatRoomTaskInfo4Conv.getTaskPurposeTag());

                // 3.构建最终的系统提示词
                TaskConvKbDtlDetailDTO taskConvKbDtlDetailDTO = get3rdSysPrmtByRdm(chatRoomTaskInfo4Conv.getTaskId(), teamId);

                if (taskConvKbDtlDetailDTO != null) {
                    t3rdSysPrmt = taskConvKbDtlDetailDTO.getFinalChatLog();
                    convKbId = taskConvKbDtlDetailDTO.getId();
                }

                //3.1 获取假机器人的聊天令牌
                fakeRobotToken = fakeRobotService.generateFakeRobotToken(t3rdSysPrmt);
            }

            // 4. 根据逻辑类型获取系统提示词
            String finalSysPrmtFromTask;
            if ((TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) && qaSimplelDtoWithUUID != null) {
                // 使用新逻辑：从问答数据生成系统提示词
                finalSysPrmtFromTask = getSysPrmt(qaSimplelDtoWithUUID.getQuestion());
                log.info("使用新逻辑生成系统提示词，问题：{}", qaSimplelDtoWithUUID.getQuestion());
            } else {
                // 使用旧逻辑：空字符串
                finalSysPrmtFromTask = "";
                log.info("使用旧逻辑，系统提示词为空");
            }

            // 获取第一个外部产品ID
            String firstExternalProductId = getExternalProductId(chatRoomTaskInfo4Conv.getScriptDetailDTO());

            // 创建会话
            long startTimeInitSession = System.currentTimeMillis();
            log.info("🚀 开始调用bmForKbService.initSession，时间戳：{}", startTimeInitSession);

            Map<String, Object> result = bmForKbService.initSession(
                    sceneName,
                    staffId.toString(),
                    token,
                    Boolean.parseBoolean(isThinking),
                    Boolean.parseBoolean(isStreaming),
                    finalSysPrmtFromTask,
                    firstExternalProductId, chatRoomTaskInfo4Conv.getScriptDetailDTO().getProdType(), fakeRobotToken, qaSimplelDtoWithUUID, chatRoomTaskInfo4Conv
            );

            long endTimeInitSession = System.currentTimeMillis();
            long durationInitSession = endTimeInitSession - startTimeInitSession;
            log.info("✅ bmForKbService.initSession调用完成，耗时：{}ms ({}秒)，时间戳：{}",
                    durationInitSession, durationInitSession / 1000.0, endTimeInitSession);

            // 2.2 新建一个私有方法，完成train_conv_winchat_dtl表的插入
            Long winchatDtlId = createWinchatDtlRecord(chatRoomInfo4Conv.getWinChatMainId(),
                    chatRoomTaskInfo4Conv.getTaskId(),
                    (String) result.get("sessionId"),
                    chatRoomTaskInfo4Conv.getRandomKbChatLog(),
                    "",
                    t3rdSysPrmt,
                    finalSysPrmtFromTask,
                    teamId,
                    convKbId);

            // 将返回的主键id设置到result中，使用sessionId作为key的前缀防止重复
            result.put("winchatDtlId_" + result.get("sessionId"), winchatDtlId);

            // 添加商品信息到结果中
            // 只有在非训练任务时才添加
            if (TaskPurposeTag.TRAINING.getCode() != chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                enrichResultWithScriptInfo(result, chatRoomTaskInfo4Conv.getScriptDetailDTO());
            }

            // 2.3 将firstMessage写进聊天记录表中
            createChatLogRecord(winchatDtlId, (String) result.get("sessionId"),
                    (String) result.get("robotName"), result.get("firstMessageWithoutJson"), teamId);

            return result;

        } catch (Exception e) {
            log.error("创建单个会话失败（知识库转换用）：chatRoomTaskInfo4Conv={}", chatRoomTaskInfo4Conv.getTaskId(), e);
            throw new RuntimeException("创建会话失败：" + e.getMessage());
        }
    }

    /**
     * 获取第三个系统提示词（从任务会话明细中随机选择）- 使用MySQL RAND()
     */
    private TaskConvKbDtlDetailDTO get3rdSysPrmtByRdm(Long taskId, Long teamId) {
        try {
            log.info("开始获取第三个系统提示词（使用MySQL RAND()），taskId: {}, teamId: {}", taskId, teamId);

            // 使用新的随机查询方法
            TaskConvKbDtlDetailDTO randomDetail = trainTaskConvKbDtlService.getRdmConvDtlByTaskId(taskId, teamId);

            if (randomDetail == null) {
                log.warn("未找到任务会话明细，taskId: {}, teamId: {}", taskId, teamId);
                return null;
            }

            String finalChatLog = randomDetail.getFinalChatLog();
            if (finalChatLog == null || finalChatLog.trim().isEmpty()) {
                log.warn("选中的任务会话明细finalChatLog为空，id: {}", randomDetail.getId());
                return null;
            }

            log.info("随机选择了明细，id: {}, finalChatLog长度: {}",
                    randomDetail.getId(), finalChatLog.length());

            return randomDetail;

        } catch (Exception e) {
            log.error("获取第三个系统提示词失败，taskId: {}, teamId: {}", taskId, teamId, e);
            return null;
        }
    }

    /**
     * 根据问题生成系统提示词
     */
    private String getSysPrmt(String question) {
        try {
            // 获取提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_CONVERT_FREQ_SECOND_SYSTEM_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到转换频次标题提示词模板，关键词：{}", CONSTS.DEFAULT_CONVERT_FREQ_SECOND_SYSTEM_KEYWORD);
                return "";
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到提示词模板：{}", template);

            // 进行变量替换，用question替换{{ques}}
            String result = template;
            log.info("变量替换后的提示词：{}", result);

            return result;

        } catch (Exception e) {
            log.error("生成系统提示词失败，question：{}", question, e);
            return "";
        }
    }

    /**
     * 重载的getExternalProductId方法，从ScriptDetailDTO中获取
     */
    private String getExternalProductId(ScriptDetailDTO scriptDetail) {
        if (scriptDetail == null || scriptDetail.getProductList() == null || scriptDetail.getProductList().isEmpty()) {
            return null;
        }

        var firstProduct = scriptDetail.getProductList().get(0);
        return firstProduct.getExternalProductId();
    }

    /**
     * 为结果添加剧本信息
     */
    private void enrichResultWithScriptInfo(Map<String, Object> result, ScriptDetailDTO scriptDetail) {
        if (scriptDetail.getProductList() != null && !scriptDetail.getProductList().isEmpty()) {
            List<Map<String, Object>> processedProducts = new ArrayList<>();

            for (var product : scriptDetail.getProductList()) {
                Map<String, Object> processedProduct = new HashMap<>();
                processedProduct.put("id", product.getId());
                processedProduct.put("externalProductId", product.getExternalProductId());
                processedProduct.put("externalProductName", product.getExternalProductName());
                processedProduct.put("externalProductLink", product.getExternalProductLink());
                processedProduct.put("externalProductImage", product.getExternalProductImage());
                processedProduct.put("status", product.getStatus());
                processedProduct.put("category", product.getCategory());
                processedProduct.put("platform", product.getPlatform());
                processedProduct.put("shopName", product.getShopName());
                processedProducts.add(processedProduct);
            }

            result.put("productList", processedProducts);
        }
    }

    /**
     * 创建聊天窗口主记录
     */
    private Long createWinchatMainRecord(Long chatroomId, Long staffId, Long teamId) {
        try {
            log.info("开始创建聊天窗口主记录：chatroomId={}, staffId={}, teamId={}", chatroomId, staffId, teamId);

            var winchatMain = trainConvWinchatMainService.createWinchatMain(chatroomId, staffId, teamId, "system");

            if (winchatMain != null && winchatMain.getId() != null) {
                log.info("创建聊天窗口主记录成功：winChatMainId={}", winchatMain.getId());
                return winchatMain.getId();
            } else {
                log.error("创建聊天窗口主记录失败：返回结果为空");
                throw new RuntimeException("创建聊天窗口主记录失败");
            }
        } catch (Exception e) {
            log.error("创建聊天窗口主记录异常：chatroomId={}, staffId={}, teamId={}", chatroomId, staffId, teamId, e);
            throw new RuntimeException("创建聊天窗口主记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建聊天窗口明细记录
     */
    private Long createWinchatDtlRecord(Long convWinchatMainId, Long taskId, String sessionId,
                                        String f1stRawChatlog, String s2ndAggSysPrompt,
                                        String t3rdRewrite, String f4thFinalSysPrompt,
                                        Long teamId, Long convKbId) {
        try {
            log.info("开始创建聊天窗口明细记录：convWinchatMainId={}, taskId={}, sessionId={}, teamId={}, convKbId={}",
                    convWinchatMainId, taskId, sessionId, teamId, convKbId);

            var winchatDtl = trainConvWinchatDtlService.createWinchatDtlWithFields(
                    convWinchatMainId, taskId, sessionId,
                    f1stRawChatlog, s2ndAggSysPrompt, t3rdRewrite, f4thFinalSysPrompt,
                    teamId, "system", convKbId);

            if (winchatDtl != null && winchatDtl.getId() != null) {
                log.info("创建聊天窗口明细记录成功：winchatDtlId={}", winchatDtl.getId());
                return winchatDtl.getId();
            } else {
                log.error("创建聊天窗口明细记录失败：返回结果为空");
                throw new RuntimeException("创建聊天窗口明细记录失败");
            }
        } catch (Exception e) {
            log.error("创建聊天窗口明细记录异常：convWinchatMainId={}, taskId={}, sessionId={}, teamId={}, convKbId={}",
                    convWinchatMainId, taskId, sessionId, teamId, convKbId, e);
            throw new RuntimeException("创建聊天窗口明细记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建聊天记录
     */
    private void createChatLogRecord(Long convDtlId, String sessionId, String sender, Object firstMessage, Long teamId) {
        try {
            log.info("开始创建聊天记录：convDtlId={}, sessionId={}, sender={}, teamId={}",
                    convDtlId, sessionId, sender, teamId);

            // 处理firstMessage内容
            String content = processFirstMessage(firstMessage);

            // 创建聊天记录
            var chatLog = trainConvWinchatLogService.createChatLog(
                    convDtlId,
                    sessionId,
                    sender,
                    content,
                    LocalDateTime.now(),
                    SenderType.BUYER.getCode(),
                    teamId,
                    "system"
            );

            if (chatLog != null && chatLog.getId() != null) {
                log.info("创建聊天记录成功：chatLogId={}", chatLog.getId());
            } else {
                log.error("创建聊天记录失败：返回结果为空");
                throw new RuntimeException("创建聊天记录失败");
            }
        } catch (Exception e) {
            log.error("创建聊天记录异常：convDtlId={}, sessionId={}, sender={}, teamId={}",
                    convDtlId, sessionId, sender, teamId, e);
            throw new RuntimeException("创建聊天记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理firstMessage内容
     */
    private String processFirstMessage(Object firstMessage) {
        if (firstMessage == null) {
            return "";
        }

        try {
            // 如果是字符串，直接返回
            if (firstMessage instanceof String) {
                return (String) firstMessage;
            }

            // 如果是Map类型，尝试解析JSON结构
            if (firstMessage instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> messageMap = (Map<String, Object>) firstMessage;

                // 检查是否有result字段且为数组
                Object result = messageMap.get("result");
                if (result instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> resultList = (List<String>) result;
                    return String.join("\n", resultList);
                }
            }

            // 其他情况，转换为JSON字符串
            return objectMapper.writeValueAsString(firstMessage);

        } catch (Exception e) {
            log.warn("处理firstMessage时发生异常，使用toString()方法：{}", e.getMessage());
            return firstMessage.toString();
        }
    }
}
