package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportMain;
import com.yiyi.ai_train_playground.service.task.TrainQaReportMainService;
import com.yiyi.ai_train_playground.dto.task.*;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.util.ArrayList;
import java.util.List;

/**
 * 问答报告主表控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@RestController
@RequestMapping("/api/qa-report-main")
@Tag(name = "问答报告主表管理", description = "问答报告主表相关接口")
public class TrainQaReportMainController {

    @Autowired
    private TrainQaReportMainService trainQaReportMainService;

    /**
     * 创建记录
     */
    @PostMapping
    public Result<TrainQaReportMain> create(@RequestBody TrainQaReportMainCreateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        TrainQaReportMain record = new TrainQaReportMain();
        BeanUtils.copyProperties(request, record);

        TrainQaReportMain result = trainQaReportMainService.create(record, teamId);

        if (result != null) {
            return Result.success("创建成功", result);
        } else {
            return Result.error("创建失败");
        }
    }

    /**
     * 根据ID查询记录
     */
    @GetMapping("/{id}")
    public Result<TrainQaReportMain> getById(@PathVariable Long id) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        TrainQaReportMain result = trainQaReportMainService.getById(id, teamId);

        if (result != null) {
            return Result.success("查询成功", result);
        } else {
            return Result.error("记录不存在");
        }
    }

    /**
     * 根据ID更新记录
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateById(@PathVariable Long id, @RequestBody TrainQaReportMainUpdateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        TrainQaReportMain record = new TrainQaReportMain();
        BeanUtils.copyProperties(request, record);
        record.setId(id);

        boolean result = trainQaReportMainService.updateById(record, teamId);

        if (result) {
            return Result.success("更新成功", true);
        } else {
            return Result.error("更新失败");
        }
    }

    /**
     * 根据ID删除记录
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteById(@PathVariable Long id) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        boolean result = trainQaReportMainService.deleteById(id, teamId);

        if (result) {
            return Result.success("删除成功", true);
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 分页查询记录
     * 就是接待详情查询
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询问答报告主表记录", description = "支持按聊天室ID、员工ID、姓名、编号、创建时间、最低分数等条件查询")
    public Result<PageResult<TrainQaReportMain>> getPageList(@Parameter(description = "查询条件") TrainQaReportMainQueryRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        PageResult<TrainQaReportMain> result = trainQaReportMainService.getPageList(request, teamId);

        return Result.success("查询成功", result);
    }

    /**
     * 根据聊天室ID查询记录列表
     */
    @GetMapping("/chatroom/{chatroomId}")
    public Result<List<TrainQaReportMain>> getByChatroomId(@PathVariable Long chatroomId) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        List<TrainQaReportMain> result = trainQaReportMainService.getByChatroomId(chatroomId, teamId);

        return Result.success("查询成功", result);
    }

    /**
     * 根据员工ID查询记录列表
     */
    @GetMapping("/staff/{staffId}")
    public Result<List<TrainQaReportMain>> getByStaffId(@PathVariable Long staffId) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        List<TrainQaReportMain> result = trainQaReportMainService.getByStaffId(staffId, teamId);

        return Result.success("查询成功", result);
    }

    /**
     * 批量创建记录
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody TrainQaReportMainBatchCreateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        String creator = SecurityUtil.getCurrentUsername();

        List<TrainQaReportMain> records = new ArrayList<>();
        for (TrainQaReportMainCreateRequest createRequest : request.getRecords()) {
            TrainQaReportMain record = new TrainQaReportMain();
            BeanUtils.copyProperties(createRequest, record);
            records.add(record);
        }

        boolean result = trainQaReportMainService.batchCreate(records, teamId, creator);

        if (result) {
            return Result.success("批量创建成功", true);
        } else {
            return Result.error("批量创建失败");
        }
    }

    /**
     * 根据条件统计记录数
     */
    @GetMapping("/count")
    @Operation(summary = "根据条件统计记录数", description = "根据条件统计问答报告主表记录数")
    public Result<Long> countByCondition(@Parameter(description = "查询条件") TrainQaReportMainQueryRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        long result = trainQaReportMainService.countByCondition(request, teamId);

        return Result.success("查询成功", result);
    }

    /**
     * 展示考试结果
     * 根据报告主记录ID查询完整的考试结果，包括答题记录和计算总分
     */
    @GetMapping("/show-exam-result/{qaReportMainId}")
    @Operation(summary = "展示考试结果", description = "根据报告主记录ID查询完整的考试结果，包括答题记录和总分计算")
    public Result<ShowExamResultDTO> showExamResult(
            @Parameter(description = "报告主记录ID", required = true, example = "1")
            @PathVariable Long qaReportMainId,
            @Parameter(description = "是否需要更新分数", required = false, example = "true")
            @RequestParam(defaultValue = "true") Boolean isNeedUpdate,
            @Parameter(description = "团队ID", required = false, example = "1")
            @RequestParam(required = false) Long teamId) {

        // 先从SecurityContext获取teamId
        Long currentTeamId = SecurityUtil.getCurrentTeamId();

        // 如果SecurityContext中的teamId为null，则使用入参中的teamId
        if (currentTeamId == null) {
            currentTeamId = teamId;
        }

        isNeedUpdate=true;//临时设置，暂时找不到原因

        ShowExamResultDTO result = trainQaReportMainService.showExamResult(qaReportMainId, currentTeamId, isNeedUpdate);

        if (result != null) {
            return Result.success("查询成功", result);
        } else {
            return Result.error("未找到考试结果");
        }
    }

    /**
     * 废弃，见分页查询记录（支持多条件过滤）
     * 接待详情查看接口
     * 根据聊天室ID分页查询接待详情
     */
    @GetMapping("/reception-details/{chatroomId}")
    @Operation(summary = "接待详情查看", description = "根据聊天室ID分页查询接待详情，支持按员工ID过滤")
    public Result<PageResult<TrainQaReportMain>> getReceptionDetails(
            @Parameter(description = "聊天室ID", required = true, example = "1")
            @PathVariable Long chatroomId,
            @Parameter(description = "查询条件") TrainQaReportMainQueryRequest request) {

        Long teamId = SecurityUtil.getCurrentTeamId();

        PageResult<TrainQaReportMain> result = trainQaReportMainService.getReceptionDetailsByChatroomId(chatroomId, request, teamId);

        return Result.success("查询成功", result);
    }

    /**
     * 导出Excel接口
     * 查询逻辑和getPageList一样，只是不要分页，限制10万条以内
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出问答报告Excel", description = "根据查询条件导出问答报告Excel文件，最多10万条记录")
    public ResponseEntity<byte[]> exportExcel(@Parameter(description = "查询条件") TrainQaReportMainQueryRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        
        // 导出Excel
        byte[] excelData = trainQaReportMainService.exportExcel(request, teamId);
        
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "qa-report-main.xlsx");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(excelData);
    }
}
