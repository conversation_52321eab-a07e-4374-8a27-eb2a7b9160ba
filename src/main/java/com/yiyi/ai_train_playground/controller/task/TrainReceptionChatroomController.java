package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.*;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffDetailResponse;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.service.staff.TrainStaffService;
import com.yiyi.ai_train_playground.service.task.TrainReceptionChatroomService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 接待聊天室控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "接待聊天室管理", description = "模拟接待聊天室的增删改查功能")
public class TrainReceptionChatroomController {
    
    @Autowired
    private TrainReceptionChatroomService chatroomService;
    
    @Autowired
    private TrainStaffService trainStaffService;
    
    /**
     * 分页查询聊天室列表
     *
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    @GetMapping("/chatrooms")
    @Operation(summary = "分页查询聊天室列表", description = "根据条件分页查询接待聊天室列表")
    public Result<PageResult<ChatroomListDTO>> getChatroomList(
            @Parameter(description = "查询条件") ChatroomQueryRequest queryRequest) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            log.info("分页查询聊天室列表：teamId={}, queryRequest={}", teamId, queryRequest);
            
            PageResult<ChatroomListDTO> pageResult = chatroomService.getChatroomList(queryRequest, teamId);
            
            log.info("查询聊天室列表成功：total={}, records.size={}", 
                    pageResult.getTotal(), pageResult.getRecords().size());
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("分页查询聊天室列表失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 根据ID查询聊天室详情
     *
     * @param id 聊天室ID
     * @return 聊天室详情
     */
    @GetMapping("/chatrooms/{id}")
    @Operation(summary = "查询聊天室详情", description = "根据ID查询聊天室详细信息")
    public Result<ChatroomDetailDTO> getChatroomDetail(@PathVariable Long id) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            log.info("查询聊天室详情：id={}, teamId={}", id, teamId);
            
            ChatroomDetailDTO detail = chatroomService.getChatroomDetail(id, teamId);
            
            return Result.success(detail);
            
        } catch (Exception e) {
            log.error("查询聊天室详情失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 创建聊天室
     *
     * @param request 聊天室创建请求
     * @return 创建结果
     */
    @PostMapping("/chatrooms")
    @Operation(summary = "创建聊天室", description = "创建新的接待聊天室及关联任务")
    public Result<Long> createChatroom(@RequestBody ChatroomCreateRequest request) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }

            // 验证任务数量必须大于0
            if (request.getTaskList() == null || request.getTaskList().size() <= 0) {
                log.warn("创建聊天室失败：任务数量必须大于0，当前任务数量={}",
                        request.getTaskList() != null ? request.getTaskList().size() : 0);
                return Result.error("聊天室必须关联至少1个任务");
            }

            // 获取当前用户ID作为创建人（这里简化处理，实际项目中可能需要从SecurityContext获取）
            String creator = SecurityUtil.getCurrentUsername();

            log.info("创建聊天室：teamId={}, creator={}, taskCount={}, request={}",
                    teamId, creator, request.getTaskList().size(), request);

            Long chatroomId = chatroomService.createChatroomWithTasks(request, teamId, creator);

            return Result.success(chatroomId);

        } catch (Exception e) {
            log.error("创建聊天室失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新聊天室
     *
     * @param id 聊天室ID
     * @param request 聊天室更新请求
     * @return 更新结果
     */
    @PutMapping("/chatrooms/{id}")
    @Operation(summary = "更新聊天室", description = "更新聊天室信息及关联任务")
    public Result<String> updateChatroom(@PathVariable Long id, @RequestBody ChatroomUpdateRequest request) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }

            // 验证任务数量必须大于0（只有当taskList不为null时才进行验证，null表示不更新任务列表）
            if (request.getTaskList() != null && request.getTaskList().size() <= 0) {
                log.warn("更新聊天室失败：任务数量必须大于0，当前任务数量={}", request.getTaskList().size());
                return Result.error("聊天室必须关联至少1个任务");
            }

            // 获取当前用户ID作为更新人
            String updater = "system"; // TODO: 从SecurityContext获取真实的用户ID

            // 设置聊天室ID
            request.setId(id);

            log.info("更新聊天室：id={}, teamId={}, updater={}, taskCount={}, request={}",
                    id, teamId, updater,
                    request.getTaskList() != null ? request.getTaskList().size() : "不更新", request);

            boolean success = chatroomService.updateChatroomWithTasks(request, teamId, updater);
            if (success) {
                return Result.success("聊天室更新成功");
            } else {
                return Result.error("聊天室更新失败");
            }

        } catch (Exception e) {
            log.error("更新聊天室失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除聊天室
     *
     * @param id 聊天室ID
     * @return 删除结果
     */
    @DeleteMapping("/chatrooms/{id}")
    @Operation(summary = "删除聊天室", description = "删除聊天室及其关联任务")
    public Result<String> deleteChatroom(@PathVariable Long id) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            log.info("删除聊天室：id={}, teamId={}", id, teamId);
            
            boolean success = chatroomService.deleteChatroom(id, teamId);
            if (success) {
                return Result.success("聊天室删除成功");
            } else {
                return Result.error("聊天室删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除聊天室失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量删除聊天室
     *
     * @param ids 聊天室ID列表，逗号分隔
     * @return 删除结果
     */
    @DeleteMapping("/chatrooms")
    @Operation(summary = "批量删除聊天室", description = "根据ID列表批量删除聊天室")
    public Result<String> batchDeleteChatrooms(@RequestParam("ids") String ids) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            log.info("批量删除聊天室：ids={}, teamId={}", ids, teamId);
            
            boolean success = chatroomService.batchDeleteChatrooms(ids, teamId);
            if (success) {
                return Result.success("聊天室批量删除成功");
            } else {
                return Result.error("聊天室批量删除失败");
            }
            
        } catch (Exception e) {
            log.error("批量删除聊天室失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 查询我的接待聊天任务
     *
     * @param queryRequest 查询条件
     * @return 我的任务分页列表
     */
    @GetMapping("/chatrooms/my-tasks")
    @Operation(summary = "查询我的接待聊天任务", description = "查询当前登录员工的所有接待聊天任务，支持分页和条件查询")
    public Result<PageResult<ChatroomListDTO>> getMyTasks(
            @Parameter(description = "查询条件") ChatroomQueryRequest queryRequest) {
        try {
            // 获取当前用户的teamId和userId
            Long teamId = SecurityUtil.getCurrentTeamId();
            Long userId = SecurityUtil.getCurrentUserId();
            
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            if (userId == null) {
                log.warn("无法从SecurityContext获取userId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            // 通过staffId（JWT中的userId实际是staffId）获取员工信息
            TrainStaffDetailResponse staffDetail = trainStaffService.getStaffDetail(userId);
            if (staffDetail == null) {
                log.warn("员工信息不存在：staffId={}", userId);
                return Result.error("当前用户不是员工，无法查询接待任务");
            }
            
            // 创建简单的staff对象用于后续业务逻辑
            TrainStaff staff = new TrainStaff();
            staff.setId(userId);
            staff.setTeamId(teamId);
            
            // 初始化查询请求对象
            if (queryRequest == null) {
                queryRequest = new ChatroomQueryRequest();
            }
            
            log.info("查询我的接待聊天任务：staffId={}, teamId={}, queryRequest={}", staff.getId(), teamId, queryRequest);
            
            PageResult<ChatroomListDTO> pageResult = chatroomService.getMyTasks(queryRequest, staff.getId(), teamId);
            
            log.info("查询我的接待聊天任务成功：员工ID={}, 总数={}, 当前页数量={}", 
                    staff.getId(), pageResult.getTotal(), pageResult.getRecords().size());
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("查询我的接待聊天任务失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查聊天室任务可用性
     *
     * @param roomId 聊天室ID
     * @return 检查结果
     */
    @GetMapping("/chatrooms/{roomId}/check-task-available")
    @Operation(summary = "检查聊天室任务可用性", description = "检查聊天室中的任务是否仍在学习中，如果有任务在学习则拒绝")
    public Result<String> checkTaskAvailable(@PathVariable Long roomId) {
        try {
            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            log.info("检查聊天室任务可用性：roomId={}, teamId={}", roomId, teamId);
            
            List<TaskAvailableCheckDTO> learningTasks = chatroomService.checkTasksAvailability(roomId, teamId);
            
            // 如果有任务正在学习中，则拒绝
            if (learningTasks != null && !learningTasks.isEmpty()) {
                // 获取第一个正在学习的任务ID
                Long taskId = learningTasks.get(0).getId();
                String message = taskId + "仍在学习中，请稍等。";
                
                log.warn("聊天室任务不可用：roomId={}, 正在学习的任务ID={}", roomId, taskId);
                return Result.error(0, message);
            }
            
            // 没有任务正在学习，可以使用
            log.info("聊天室任务可用：roomId={}", roomId);
            return Result.success("聊天室任务可用");
            
        } catch (Exception e) {
            log.error("检查聊天室任务可用性失败", e);
            return Result.error(e.getMessage());
        }
    }
}