package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskCreateRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskDetailDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskListDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskQueryRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskUpdateRequest;
import com.yiyi.ai_train_playground.dto.converkb.TaskConvKbDtlCreateRequest;
import com.yiyi.ai_train_playground.mapper.task.TrainReceptionTaskMapper;
import com.yiyi.ai_train_playground.service.task.TrainReceptionTaskService;
import com.yiyi.ai_train_playground.service.task.TemplateLearnService;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;

/**
 * 接待任务控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/api/reception-tasks")
@Tag(name = "接待任务管理", description = "接待任务的创建、查询、更新、删除等操作")
public class TrainReceptionTaskController {
    
    @Autowired
    private TrainReceptionTaskService receptionTaskService;

    @Autowired
    private TrainReceptionTaskMapper receptionTaskMapper;

    @Autowired
    private TemplateLearnService templateLearnService;

    @Autowired
    private TrainKbTplService trainKbTplService;

    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;
    
    /**
     * 分页查询接待任务列表
     *
     * @param request 查询请求参数
     * @param httpRequest HTTP请求
     * @return 分页结果
     */
    @GetMapping
    @Operation(
        summary = "分页查询接待任务列表",
        description = "支持按任务名称、任务模式、任务类型、指定客服、场景模式等条件查询"
    )
    public Result<PageResult<TrainReceptionTaskListDTO>> getTaskList(
            @Parameter(description = "查询条件") TrainReceptionTaskQueryRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();

            log.info("查询接待任务列表，teamId: {}, request: {}", teamId, request);

            PageResult<TrainReceptionTaskListDTO> result = receptionTaskService.getTaskList(request, teamId);
            return Result.success(result);

        } catch (Exception e) {
            log.error("查询接待任务列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询接待任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询接待任务详情", description = "根据任务ID查询详细信息")
    public Result<TrainReceptionTaskDetailDTO> getTaskDetail(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();

            log.info("查询接待任务详情，id: {}, teamId: {}", id, teamId);

            TrainReceptionTaskDetailDTO detail = receptionTaskService.getTaskDetail(id, teamId);
            if (detail == null) {
                return Result.error("任务不存在");
            }

            return Result.success(detail);

        } catch (Exception e) {
            log.error("查询接待任务详情失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建接待任务
     *
     * @param request 创建请求
     * @param httpRequest HTTP请求
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建接待任务", description = "创建新的接待任务")
    public Result<Long> createTask(
            @Parameter(description = "创建请求") @Valid @RequestBody TrainReceptionTaskCreateRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();

            // 从SecurityUtil获取用户ID作为创建人
            Long userId = SecurityUtil.getCurrentUserId();
//            String creator = userId != null ? userId.toString() : "unknown";
            String creator = SecurityUtil.getCurrentUsername();

            log.info("创建接待任务，teamId: {}, creator: {}, taskName: {}", teamId, creator, request.getTaskName());

            // 根据任务标签进行验证
            String validationError = validateTaskFields(request);
            if (validationError != null) {
                return Result.error(validationError);
            }

            Long taskId = receptionTaskService.createTask(request, teamId, creator);

            // 创建知识库明细并更新任务的待学习数量
            createKnowledgeBaseDetails(taskId, request.getConvKbId(), teamId, creator);

            // 任务创建成功后，启动异步模板学习
            try {
                log.info("启动异步模板学习，taskId: {}, teamId: {}", taskId, teamId);
                templateLearnService.startTemplateLearning(taskId, teamId);
            } catch (Exception e) {
                log.error("启动异步模板学习失败，taskId: {}, teamId: {}", taskId, teamId, e);
                // 这里不抛出异常，允许任务创建成功，只是模板学习启动失败
            }

            return Result.success(taskId);

        } catch (Exception e) {
            log.error("创建接待任务失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新接待任务
     *
     * @param id 任务ID
     * @param request 更新请求
     * @param httpRequest HTTP请求
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新接待任务", description = "更新指定ID的接待任务")
    public Result<Boolean> updateTask(
            @Parameter(description = "任务ID") @PathVariable Long id,
            @Parameter(description = "更新请求") @Valid @RequestBody TrainReceptionTaskUpdateRequest request) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();

            // 从SecurityUtil获取用户ID作为更新人
            Long userId = SecurityUtil.getCurrentUserId();
            String updater = userId != null ? userId.toString() : "unknown";

            // 设置ID
            request.setId(id);

            log.info("更新接待任务，id: {}, teamId: {}, updater: {}", id, teamId, updater);

            // 根据任务标签进行验证
            String validationError = validateTaskFields(request);
            if (validationError != null) {
                return Result.error(validationError);
            }

            boolean success = receptionTaskService.updateTask(request, teamId, updater);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("更新失败，任务不存在或版本冲突");
            }

        } catch (Exception e) {
            log.error("更新接待任务失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除接待任务
     *
     * @param id 任务ID
     * @param httpRequest HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除接待任务", description = "删除指定ID的接待任务")
    public Result<Boolean> deleteTask(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();

            log.info("删除接待任务，id: {}, teamId: {}", id, teamId);

            boolean success = receptionTaskService.deleteTask(id, teamId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("删除失败，任务不存在");
            }

        } catch (Exception e) {
            log.error("删除接待任务失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除接待任务
     *
     * @param ids 任务ID列表（逗号分隔）
     * @param httpRequest HTTP请求
     * @return 删除结果
     */
    @DeleteMapping
    @Operation(summary = "批量删除接待任务", description = "批量删除指定ID的接待任务")
    public Result<Boolean> batchDeleteTasks(
            @Parameter(description = "任务ID列表，逗号分隔", example = "1,2,3") @RequestParam String ids) {
        try {
            Long teamId = SecurityUtil.getCurrentTeamId();

            log.info("批量删除接待任务，ids: {}, teamId: {}", ids, teamId);

            boolean success = receptionTaskService.batchDeleteTasks(ids, teamId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("批量删除失败");
            }

        } catch (Exception e) {
            log.error("批量删除接待任务失败", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }



    /**
     * 创建知识库明细并更新任务的待学习数量
     *
     * @param taskId 任务ID
     * @param convKbId 知识库ID
     * @param teamId 团队ID
     * @param creator 创建人
     */
    private void createKnowledgeBaseDetails(Long taskId, Long convKbId, Long teamId, String creator) {
        try {
            log.info("开始为任务创建知识库会话明细，taskId: {}, convKbId: {}", taskId, convKbId);
            
            // 1. 根据conv_kb_id获取知识库模板下的所有明细
            List<TrainKbTplService.KbTplDetailForTaskCreation> kbDetails = 
                trainKbTplService.getKbTplDetailsForTaskCreation(convKbId, teamId);
            
            if (kbDetails != null && !kbDetails.isEmpty()) {
                // 2. 构建批量插入的请求列表
                List<TaskConvKbDtlCreateRequest> createRequests = new ArrayList<>();
                for (TrainKbTplService.KbTplDetailForTaskCreation kbDetail : kbDetails) {
                    TaskConvKbDtlCreateRequest createRequest = new TaskConvKbDtlCreateRequest();
                    createRequest.setTaskId(taskId);
                    createRequest.setKbDtlId(kbDetail.getId());
                    createRequest.setLearningStatus(LearnStatus.UN_LEARN.getCode());
                    createRequest.setF1stRawChatlog(kbDetail.getContent());
                    createRequests.add(createRequest);
                }
                
                // 3. 批量插入到train_task_conv_kb_dtl表
                int insertedCount = trainTaskConvKbDtlService.batchCreateTaskConvKbDtl(createRequests, teamId, creator);
                log.info("成功为任务创建知识库会话明细，taskId: {}, 插入记录数: {}", taskId, insertedCount);
                
                // 4. 更新任务的待学习数量
                updateTaskAmtToBeLearned(taskId, (long) kbDetails.size(), teamId);
                
            } else {
                log.warn("未找到知识库模板下的明细数据，taskId: {}, convKbId: {}", taskId, convKbId);
            }
        } catch (Exception e) {
            log.error("为任务创建知识库会话明细失败，taskId: {}, convKbId: {}", taskId, convKbId, e);
            // 这里不抛出异常，允许任务创建成功，只是知识库明细创建失败
        }
    }

    /**
     * 更新任务的待学习数量
     *
     * @param taskId 任务ID
     * @param size 待学习数量
     * @param teamId 团队ID
     */
    private void updateTaskAmtToBeLearned(Long taskId, Long size, Long teamId) {
        try {
            // 直接调用TrainReceptionTaskServiceImpl.updateTask(Long taskId,Long amtToBeLearned)
            boolean success = receptionTaskService.updateTask(taskId, size, teamId);

            if (success) {
                log.info("成功更新任务待学习数量，taskId: {}, amtToBeLearned: {}", taskId, size);
            } else {
                log.warn("更新任务待学习数量失败，taskId: {}, amtToBeLearned: {}", taskId, size);
            }
        } catch (Exception e) {
            log.error("更新任务待学习数量失败，taskId: {}, size: {}", taskId, size, e);
        }
    }

    /**
     * 验证任务字段（创建任务）
     *
     * @param request 创建请求
     * @return 验证错误信息，null表示验证通过
     */
    private String validateTaskFields(TrainReceptionTaskCreateRequest request) {
        Integer taskPurposeTag = request.getTaskPurposeTag();

        // 如果任务标签为null，使用默认值0（训练）
        if (taskPurposeTag == null) {
            taskPurposeTag = 0;
        }

        // 1. 只有在任务标签不为训练时，才进行check剧本ID不能为空
        if (taskPurposeTag != 0 && request.getScriptId() == null) {
            return "剧本ID不能为空";
        }

        // 2. 只有在任务标签不为训练时，才检查会话知识库ID不能为空
        if (taskPurposeTag != 0 && request.getConvKbId() == null) {
            return "会话知识库ID不能为空";
        }

        // 3. 在任务标签为训练时，才会检查高频知识库ID不能为空
        if (taskPurposeTag == 0 && request.getQaMainId() == null) {
            return "高频知识库ID不能为空";
        }

        return null; // 验证通过
    }

    /**
     * 验证任务字段（更新任务）
     *
     * @param request 更新请求
     * @return 验证错误信息，null表示验证通过
     */
    private String validateTaskFields(TrainReceptionTaskUpdateRequest request) {
        Integer taskPurposeTag = request.getTaskPurposeTag();

        // 如果任务标签为null，跳过验证（更新时可能只更新部分字段）
        if (taskPurposeTag == null) {
            return null;
        }

        // 1. 只有在任务标签不为训练时，才进行check剧本ID不能为空
        if (taskPurposeTag != 0 && request.getScriptId() == null) {
            return "剧本ID不能为空";
        }

        // 2. 只有在任务标签不为训练时，才检查会话知识库ID不能为空
        if (taskPurposeTag != 0 && request.getConvKbId() == null) {
            return "会话知识库ID不能为空";
        }

        // 3. 在任务标签为训练时，才会检查高频知识库ID不能为空
        if (taskPurposeTag == 0 && request.getQaMainId() == null) {
            return "高频知识库ID不能为空";
        }

        return null; // 验证通过
    }
}
