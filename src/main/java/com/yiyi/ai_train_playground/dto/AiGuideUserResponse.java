package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * AI Guide用户信息响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Data
public class AiGuideUserResponse {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 用户数据
     */
    private UserData data;
    
    @Data
    public static class UserData {
        /**
         * 用户ID
         */
        private Long id;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码哈希（通常为null）
         */
        private String passwordHash;
        
        /**
         * 手机号
         */
        private String mobile;
        
        /**
         * 邮箱
         */
        private String email;
        
        /**
         * 显示名称
         */
        private String displayName;
        
        /**
         * 团队ID
         */
        private Long teamId;
        
        /**
         * 连续登录失败次数
         */
        private Integer failedAttempts;
        
        /**
         * 账户锁定状态
         */
        private Boolean isLocked;
        
        /**
         * 锁定时间
         */
        private LocalDateTime lockTime;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
        
        /**
         * 创建人
         */
        private String creator;
        
        /**
         * 更新人
         */
        private String updater;
        
        /**
         * 版本号
         */
        private Long version;
    }
}
