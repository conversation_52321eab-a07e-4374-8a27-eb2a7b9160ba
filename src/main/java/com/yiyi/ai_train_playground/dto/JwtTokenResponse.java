package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "JwtTokenResponse", description = "JWT令牌响应")
public class JwtTokenResponse {

    @Schema(description = "JWT访问令牌",
            example = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ")
    private String token;

    @Schema(description = "过期时间（小时）", example = "1.0")
    private Double expireTime;
    
    public JwtTokenResponse(String token, Double expireTime) {
        this.token = token;
        this.expireTime = expireTime;
    }
    
    public JwtTokenResponse() {}
}