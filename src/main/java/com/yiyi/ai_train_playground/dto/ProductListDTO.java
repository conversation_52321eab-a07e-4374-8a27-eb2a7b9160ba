package com.yiyi.ai_train_playground.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class ProductListDTO {
    private Long id;
    private String externalProductId;
    private String externalProductName;
    private String externalProductLink;
    private String externalProductImage;
    private String status;
    private Integer learnStatus;
    private String category;
    private String tags;
    private String platform;
    @JsonProperty("shop")
    private String shopName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String creator;
    private String updater;
    private String prodKb;
} 