package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 剧本详情响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class ScriptDetailDTO {
    /**
     * 剧本ID
     */
    private Long id;
    
    /**
     * 剧本名称
     */
    private String name;
    
    /**
     * 生成类型代码
     */
    private Integer generationTypeCode;
    
    /**
     * 生成类型描述
     */
    private String generationType;
    
    /**
     * 分组ID
     */
    private Long groupId;
    
    /**
     * 意图ID
     */
    private Long intentId;
    
    /**
     * 意图名称
     */
    private String intentName;
    
    /**
     * 父意图ID
     */
    private Long parentIntentId;
    
    /**
     * 父意图名称
     */
    private String parentIntentName;
    
    /**
     * 商品列表
     */
    private List<ProductListDTO> productList;
    
    /**
     * 评价方案ID
     */
    private Long evaluationPlanId;
    
    /**
     * 评价方案名称
     */
    private String evaluationPlanName;
    
    /**
     * 评价方案分组名称
     */
    private String evaluationPlanGroupName;
    
    /**
     * 买家需求
     */
    private String buyerRequirement;
    
    /**
     * 订单是否备注
     */
    private Integer orderIsRemarked;
    
    /**
     * 订单优先级
     */
    private Integer orderPriority;
    
    /**
     * 订单备注
     */
    private String orderRemark;
    
    /**
     * 流程节点重试次数
     */
    private Integer retryFlowNodeCounts;
    
    /**
     * 买家需求重试次数
     */
    private Integer retryBuyerRequirementCounts;
    
    /**
     * 模拟工具
     */
    private String simulationTool;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 版本号
     */
    private Long version;
    
    /**
     * 商品类型：0-自主导入商品，1-JD商品
     */
    private Integer prodType;

    /**
     * 关联图片列表
     */
    private List<RelatedImageDTO> relateImgs;

    /**
     * 流程节点列表
     */
    private List<FlowNodeDTO> flowNodes;
}
