package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 剧本分组查询请求参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Schema(description = "剧本分组查询请求")
public class ScriptGroupQueryRequest {
    
    /**
     * 剧本分组ID，支持多个值用逗号分隔
     * -1: 查询所有分组
     * 1: 查询group_id为NULL的记录
     * 其他值: 查询对应的group_id
     * 多个值示例: "1,13,25"
     */
    @Schema(description = "剧本分组ID，支持多个值用逗号分隔。-1表示查询所有分组，1表示查询默认分组(NULL)，其他值查询对应分组", example = "1,13,25")
    private String scriptGroupIds;
    
    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
