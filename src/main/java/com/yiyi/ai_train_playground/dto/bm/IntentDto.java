package com.yiyi.ai_train_playground.dto.bm;

import lombok.Data;

/**
 * 意图识别结果DTO
 * 包含模型响应、意图识别结果和评分
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
public class IntentDto {
    /**
     * 模型响应内容
     */
    private String modelResponse;

    /**
     * 意图识别结果
     */
    private String intentResult;

    /**
     * 意图识别评分
     */
    private int score;

    /**
     * 无参构造器
     */
    public IntentDto() {
    }

    /**
     * 全参构造器
     *
     * @param modelResponse 模型响应内容
     * @param intentResult  意图识别结果
     * @param score         意图识别评分
     */
    public IntentDto(String modelResponse, String intentResult, int score) {
        this.modelResponse = modelResponse;
        this.intentResult = intentResult;
        this.score = score;
    }
}