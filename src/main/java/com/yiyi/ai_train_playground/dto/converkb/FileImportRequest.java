package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件导入请求DTO
 */
@Schema(description = "文件导入请求")
public class FileImportRequest {

    @Schema(description = "上传的文件", required = true)
    private MultipartFile file;

    @Schema(description = "文件描述", example = "客服对话记录文件")
    private String description;

    public FileImportRequest() {}

    public FileImportRequest(MultipartFile file, String description) {
        this.file = file;
        this.description = description;
    }

    public MultipartFile getFile() {
        return file;
    }
    
    public void setFile(MultipartFile file) {
        this.file = file;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "FileImportRequest{" +
                "file=" + (file != null ? file.getOriginalFilename() : null) +
                ", description='" + description + '\'' +
                '}';
    }
}
