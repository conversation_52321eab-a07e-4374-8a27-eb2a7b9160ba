package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件导入响应DTO
 */
@Schema(description = "文件导入响应")
public class FileImportResponse {
    
    @Schema(description = "模板ID", example = "1")
    private Long tplId;
    
    @Schema(description = "原始文件名", example = "conversation_data.xlsx")
    private String originalFileName;
    
    @Schema(description = "文件大小（字节）", example = "102400")
    private Long fileSize;
    
    @Schema(description = "文件类型", example = "xlsx")
    private String fileType;
    
    @Schema(description = "总块数", example = "15")
    private Integer totalChunks;
    
    @Schema(description = "成功导入的块数", example = "15")
    private Integer successChunks;
    
    @Schema(description = "失败的块数", example = "0")
    private Integer failedChunks;
    
    @Schema(description = "处理开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "处理结束时间")
    private LocalDateTime endTime;
    
    @Schema(description = "处理耗时（毫秒）", example = "1500")
    private Long processingTimeMs;
    
    @Schema(description = "错误信息列表")
    private List<String> errorMessages;
    
    @Schema(description = "块详情列表")
    private List<ChunkInfo> chunkInfos;
    
    public FileImportResponse() {}
    
    // Getter和Setter方法
    public Long getTplId() {
        return tplId;
    }
    
    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }
    
    public String getOriginalFileName() {
        return originalFileName;
    }
    
    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }
    
    public Long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public Integer getTotalChunks() {
        return totalChunks;
    }
    
    public void setTotalChunks(Integer totalChunks) {
        this.totalChunks = totalChunks;
    }
    
    public Integer getSuccessChunks() {
        return successChunks;
    }
    
    public void setSuccessChunks(Integer successChunks) {
        this.successChunks = successChunks;
    }
    
    public Integer getFailedChunks() {
        return failedChunks;
    }
    
    public void setFailedChunks(Integer failedChunks) {
        this.failedChunks = failedChunks;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }
    
    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
    
    public List<String> getErrorMessages() {
        return errorMessages;
    }
    
    public void setErrorMessages(List<String> errorMessages) {
        this.errorMessages = errorMessages;
    }
    
    public List<ChunkInfo> getChunkInfos() {
        return chunkInfos;
    }
    
    public void setChunkInfos(List<ChunkInfo> chunkInfos) {
        this.chunkInfos = chunkInfos;
    }
    
    /**
     * 块信息
     */
    @Schema(description = "块信息")
    public static class ChunkInfo {
        
        @Schema(description = "块索引", example = "0")
        private Integer index;
        
        @Schema(description = "块大小（字节）", example = "20480")
        private Integer size;
        
        @Schema(description = "块内容预览（前100字符）", example = "以下为一通会话\n客服：您好，有什么可以帮助您的吗？\n客户：我想了解一下...")
        private String preview;
        
        @Schema(description = "是否包含完整会话", example = "true")
        private Boolean hasCompleteConversation;
        
        public ChunkInfo() {}
        
        public ChunkInfo(Integer index, Integer size, String preview, Boolean hasCompleteConversation) {
            this.index = index;
            this.size = size;
            this.preview = preview;
            this.hasCompleteConversation = hasCompleteConversation;
        }
        
        // Getter和Setter方法
        public Integer getIndex() {
            return index;
        }
        
        public void setIndex(Integer index) {
            this.index = index;
        }
        
        public Integer getSize() {
            return size;
        }
        
        public void setSize(Integer size) {
            this.size = size;
        }
        
        public String getPreview() {
            return preview;
        }
        
        public void setPreview(String preview) {
            this.preview = preview;
        }
        
        public Boolean getHasCompleteConversation() {
            return hasCompleteConversation;
        }
        
        public void setHasCompleteConversation(Boolean hasCompleteConversation) {
            this.hasCompleteConversation = hasCompleteConversation;
        }
    }
    
    @Override
    public String toString() {
        return "FileImportResponse{" +
                "tplId=" + tplId +
                ", originalFileName='" + originalFileName + '\'' +
                ", fileSize=" + fileSize +
                ", fileType='" + fileType + '\'' +
                ", totalChunks=" + totalChunks +
                ", successChunks=" + successChunks +
                ", failedChunks=" + failedChunks +
                ", processingTimeMs=" + processingTimeMs +
                '}';
    }
}
