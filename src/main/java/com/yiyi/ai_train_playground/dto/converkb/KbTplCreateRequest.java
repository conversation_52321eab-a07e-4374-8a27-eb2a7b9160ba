package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建会话知识库模板请求DTO
 */
@Schema(description = "创建会话知识库模板请求")
public class KbTplCreateRequest {
    
    @Schema(description = "会话知识库模板名称", example = "客服售前标准话术")
    private String name;

    @Schema(description = "模板描述", example = "用于售前咨询的标准话术模板")
    private String description;

    @Schema(description = "文件类型", allowableValues = {"csv", "excel", "txt"}, example = "csv")
    private String fileType;

    @Schema(description = "学习状态", allowableValues = {"un_learn", "learning", "learned"}, example = "un_learn")
    private String learnStatus;

    @Schema(description = "对话明细列表")
    private List<KbTplDetailItem> details;

    /**
     * 对话明细项
     */
    @Schema(description = "对话明细项")
    public static class KbTplDetailItem {

        @Schema(description = "对话内容", example = "您好，有什么可以帮助您的吗？")
        private String content;

        @Schema(description = "模板类型", allowableValues = {"pre_sales", "saling", "after_sale", "other"}, example = "pre_sales")
        private String tplType;

        // Getter和Setter方法
        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getTplType() {
            return tplType;
        }

        public void setTplType(String tplType) {
            this.tplType = tplType;
        }
    }

    // Getter和Setter方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getLearnStatus() {
        return learnStatus;
    }

    public void setLearnStatus(String learnStatus) {
        this.learnStatus = learnStatus;
    }

    public List<KbTplDetailItem> getDetails() {
        return details;
    }

    public void setDetails(List<KbTplDetailItem> details) {
        this.details = details;
    }
}