package com.yiyi.ai_train_playground.dto.converkb;

import com.yiyi.ai_train_playground.dto.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 会话知识库模板详情响应DTO
 */
@Schema(description = "会话知识库模板详情响应")
public class KbTplDetailResponse {
    
    @Schema(description = "模板ID", example = "1")
    private Long id;
    
    @Schema(description = "会话知识库模板名称", example = "客服售前标准话术")
    private String name;
    
    @Schema(description = "模板描述", example = "专为售前客服设计的标准话术模板，包含常见问答和处理流程")
    private String description;
    
    @Schema(description = "本次会话累计消耗token数", example = "1500")
    private Long tokens;

    @Schema(description = "文件类型", example = "csv")
    private String fileType;

    @Schema(description = "学习状态", example = "un_learn")
    private String learnStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "对话明细分页结果")
    private PageResult<KbTplDetailItem> details;

    /**
     * 对话明细项
     */
    @Schema(description = "对话明细项")
    public static class KbTplDetailItem {
        
        @Schema(description = "明细ID", example = "1")
        private Long id;

        @Schema(description = "对话内容", example = "您好，有什么可以帮助您的吗？")
        private String content;

        @Schema(description = "模板类型代码", example = "pre_sales")
        private String tplTypeCode;

        @Schema(description = "模板类型描述", example = "售前")
        private String tplTypeValue;

        // Getter和Setter方法
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getTplTypeCode() {
            return tplTypeCode;
        }

        public void setTplTypeCode(String tplTypeCode) {
            this.tplTypeCode = tplTypeCode;
        }

        public String getTplTypeValue() {
            return tplTypeValue;
        }

        public void setTplTypeValue(String tplTypeValue) {
            this.tplTypeValue = tplTypeValue;
        }
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getTokens() {
        return tokens;
    }

    public void setTokens(Long tokens) {
        this.tokens = tokens;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getLearnStatus() {
        return learnStatus;
    }

    public void setLearnStatus(String learnStatus) {
        this.learnStatus = learnStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public PageResult<KbTplDetailItem> getDetails() {
        return details;
    }

    public void setDetails(PageResult<KbTplDetailItem> details) {
        this.details = details;
    }
}