package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 会话知识库模板列表响应DTO
 */
@Schema(description = "会话知识库模板列表响应")
public class KbTplListResponse {
    
    @Schema(description = "模板ID", example = "1")
    private Long id;
    
    @Schema(description = "会话知识库模板名称", example = "客服售前标准话术")
    private String name;
    
    @Schema(description = "模板描述", example = "专为售前客服设计的标准话术模板")
    private String description;
    
    @Schema(description = "本次会话累计消耗token数", example = "1500")
    private Long tokens;
    
    @Schema(description = "明细数量", example = "8")
    private Integer detailCount;

    @Schema(description = "文件类型", example = "csv")
    private String fileType;

    @Schema(description = "学习状态代码", example = "un_learn")
    private String learnStatusCode;

    @Schema(description = "学习状态描述", example = "未学习")
    private String learnStatusValue;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getTokens() {
        return tokens;
    }

    public void setTokens(Long tokens) {
        this.tokens = tokens;
    }

    public Integer getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(Integer detailCount) {
        this.detailCount = detailCount;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getLearnStatusCode() {
        return learnStatusCode;
    }

    public void setLearnStatusCode(String learnStatusCode) {
        this.learnStatusCode = learnStatusCode;
    }

    public String getLearnStatusValue() {
        return learnStatusValue;
    }

    public void setLearnStatusValue(String learnStatusValue) {
        this.learnStatusValue = learnStatusValue;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}