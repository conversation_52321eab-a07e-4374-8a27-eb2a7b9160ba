package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 会话知识库模板查询请求DTO
 */
@Schema(description = "会话知识库模板查询请求")
public class KbTplQueryRequest {
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "模板名称（模糊查询）", example = "客服")
    private String name;
    
    @Schema(description = "模板类型", allowableValues = {"pre_sales", "saling", "after_sale", "other"})
    private String tplType;

    // Getter和Setter方法
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTplType() {
        return tplType;
    }

    public void setTplType(String tplType) {
        this.tplType = tplType;
    }
}