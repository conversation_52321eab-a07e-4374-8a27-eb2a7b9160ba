package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 问答详情列表查询请求
 */
@Data
@Schema(description = "问答详情列表查询请求")
public class QaDtlListRequest {
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "主表ID，查询指定知识库下的问答", example = "1")
    private Long qaMainId;
    
    @Schema(description = "问题关键词，模糊查询", example = "防晒")
    private String question;
}
