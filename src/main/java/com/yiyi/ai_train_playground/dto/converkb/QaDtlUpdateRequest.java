package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 问答详情更新请求
 */
@Data
@Schema(description = "问答详情更新请求")
public class QaDtlUpdateRequest {
    
    @NotBlank(message = "问题不能为空")
    @Schema(description = "问题", example = "防晒干是水", required = true)
    private String question;
    
    @NotBlank(message = "答案不能为空")
    @Schema(description = "答案", example = "亲爱的～防晒到货水分的高的状态下是哦～", required = true)
    private String answer;
}
