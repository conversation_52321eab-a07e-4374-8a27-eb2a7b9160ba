package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 问答导入响应DTO
 */
@Data
@Schema(description = "问答导入响应")
public class QaImportResponse {
    
    @Schema(description = "总条数")
    private Integer totalCount;
    
    @Schema(description = "成功条数")
    private Integer successCount;
    
    @Schema(description = "失败条数")
    private Integer failCount;
    
    @Schema(description = "成功导入的问答项")
    private List<QaItem> successItems;
    
    @Schema(description = "失败的问答项")
    private List<FailedItem> failedItems;

    @Schema(description = "成功导入Excel文件下载URL（24小时有效）")
    private String successExcelUrl;

    @Schema(description = "失败明细Excel文件下载URL（24小时有效）")
    private String failedExcelUrl;
    
    /**
     * 问答项
     */
    @Data
    @Schema(description = "问答项")
    public static class QaItem {
        @Schema(description = "问题")
        private String question;
        
        @Schema(description = "答案")
        private String answer;
    }
    
    /**
     * 失败项
     */
    @Data
    @Schema(description = "失败项")
    public static class FailedItem {
        @Schema(description = "行号")
        private Integer row;
        
        @Schema(description = "问题")
        private String question;
        
        @Schema(description = "失败原因")
        private String reason;
    }
}
