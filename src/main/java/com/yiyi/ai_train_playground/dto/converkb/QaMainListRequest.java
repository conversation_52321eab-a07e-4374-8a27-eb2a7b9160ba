package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 问答主表列表查询请求
 */
@Data
@Schema(description = "问答主表列表查询请求")
public class QaMainListRequest {
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "知识库名称（模糊搜索）", example = "客服知识库")
    private String qaImName;
}
