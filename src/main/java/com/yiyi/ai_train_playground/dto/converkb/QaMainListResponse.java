package com.yiyi.ai_train_playground.dto.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 问答主表列表查询响应
 */
@Data
@Schema(description = "问答主表列表查询响应")
public class QaMainListResponse {
    
    @Schema(description = "知识库列表")
    private List<TrainQaImportMain> list;
    
    @Schema(description = "总记录数")
    private Integer total;
    
    @Schema(description = "当前页码")
    private Integer page;
    
    @Schema(description = "每页大小")
    private Integer pageSize;
}
