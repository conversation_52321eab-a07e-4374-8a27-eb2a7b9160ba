package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 知识库编辑请求
 */
@Data
@Schema(description = "知识库编辑请求")
public class QaMainUpdateRequest {
    
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 100, message = "知识库名称长度不能超过100个字符")
    @Schema(description = "知识库名称", example = "客服知识库", required = true)
    private String qaImName;
    
    @Size(max = 500, message = "知识库描述长度不能超过500个字符")
    @Schema(description = "知识库描述", example = "用于客服人员的常见问题答案库")
    private String qaImDesc;
}