package com.yiyi.ai_train_playground.dto.converkb;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 问答简单DTO - 只包含问题和答案
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
@Data
@Schema(description = "问答简单信息")
public class QaSimpleDto {
    
    /**
     * 问题
     */
    @Schema(description = "问题", example = "这个产品的保修期是多长？")
    private String question;
    
    /**
     * 答案
     */
    @Schema(description = "答案", example = "我们的产品提供一年免费保修服务。")
    private String answer;
}