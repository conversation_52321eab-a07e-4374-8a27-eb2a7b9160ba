package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 任务会话明细详情响应DTO
 */
@Data
@Schema(description = "任务会话明细详情响应")
public class TaskConvKbDtlDetailDTO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "任务ID", example = "1")
    private Long taskId;

    @Schema(description = "知识库明细ID", example = "1")
    private Long kbDtlId;

    @Schema(description = "学习状态", example = "un_learn")
    private String learningStatus;

    @Schema(description = "学习状态描述", example = "未学习")
    private String learningStatusDesc;

    @Schema(description = "初始的聊天记录", example = "客户：你好，有什么推荐的产品吗？\n客服：您好，欢迎咨询...")
    private String f1stRawChatlog;

    @Schema(description = "最终生成的聊天记录", example = "客户：你好，有什么优惠吗？\n客服：您好，我们目前有新用户专享优惠...")
    private String finalChatLog;

    @Schema(description = "团队ID", example = "1")
    private Long teamId;

    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-01-01 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "更新人", example = "admin")
    private String updater;

    @Schema(description = "版本号", example = "1")
    private Long version;
}