package com.yiyi.ai_train_playground.dto.converkb;

import com.yiyi.ai_train_playground.dto.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务会话明细列表响应DTO
 */
@Data
@Schema(description = "任务会话明细列表响应")
public class TaskConvKbDtlListDTO {

    @Schema(description = "分页结果")
    private PageResult<TaskConvKbDtlItem> pageResult;

    @Schema(description = "任务会话明细项")
    @Data
    public static class TaskConvKbDtlItem {

        @Schema(description = "主键ID", example = "1")
        private Long id;

        @Schema(description = "任务ID", example = "1")
        private Long taskId;

        @Schema(description = "知识库明细ID", example = "1")
        private Long kbDtlId;

        @Schema(description = "学习状态", example = "un_learn")
        private String learningStatus;

        @Schema(description = "学习状态描述", example = "未学习")
        private String learningStatusDesc;

        @Schema(description = "初始聊天记录（摘要）", example = "客户：你好，有什么推荐的产品吗？\n客服：您好，欢迎...")
        private String f1stRawChatlogSummary;

        @Schema(description = "最终生成的聊天记录（摘要）", example = "客户：你好，有什么优惠吗？\n客服：您好，我们目前有...")
        private String finalChatLogSummary;

        @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
        private LocalDateTime createTime;

        @Schema(description = "更新时间", example = "2024-01-01 12:00:00")
        private LocalDateTime updateTime;

        @Schema(description = "创建人", example = "admin")
        private String creator;

        @Schema(description = "更新人", example = "admin")
        private String updater;
    }
}