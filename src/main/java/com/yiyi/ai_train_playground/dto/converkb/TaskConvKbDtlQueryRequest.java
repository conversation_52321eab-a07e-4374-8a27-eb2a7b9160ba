package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务会话明细查询请求DTO
 */
@Data
@Schema(description = "任务会话明细查询请求")
public class TaskConvKbDtlQueryRequest {

    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    @Schema(description = "任务ID", example = "1")
    private Long taskId;

    @Schema(description = "知识库明细ID", example = "1")
    private Long kbDtlId;

    @Schema(description = "学习状态筛选", example = "un_learn")
    private String learningStatus;
}