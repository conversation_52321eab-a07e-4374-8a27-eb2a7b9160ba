package com.yiyi.ai_train_playground.dto.converkb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务会话明细更新请求DTO
 */
@Data
@Schema(description = "任务会话明细更新请求")
public class TaskConvKbDtlUpdateRequest {

    @Schema(description = "学习状态", example = "learning")
    private String learningStatus;

    @Schema(description = "初始的聊天记录", example = "客户：你好，有什么推荐的产品吗？\n客服：您好，欢迎咨询...")
    private String f1stRawChatlog;

    @Schema(description = "最终生成的聊天记录", example = "客户：你好，有什么优惠吗？\n客服：您好，我们目前有新用户专享优惠...")
    private String finalChatLog;
}