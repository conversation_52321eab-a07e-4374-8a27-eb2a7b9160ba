package com.yiyi.ai_train_playground.dto.doubao;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChoice;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 豆包模型响应DTO
 * 封装豆包模型的完整响应结果，包括choices、usage和提取的message
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DoubaoResponseDto {
    
    /**
     * 完整的choices数组
     */
    private List<ChatCompletionChoice> choices;
    
    /**
     * Token使用情况
     */
    private UsageDto usage;
    
    /**
     * 提取的消息内容（来自choices数组第一个元素的message.content）
     */
    private String message;
    
    /**
     * 响应ID
     */
    private String id;
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 创建时间戳
     */
    private Long created;
    
    /**
     * 对象类型
     */
    private String object;
    
    /**
     * 服务层级
     */
    private String serviceTier;
}
