package com.yiyi.ai_train_playground.dto.jd;

import lombok.Data;

/**
 * 京东回调结果DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class JdCallbackResult {
    
    /**
     * 处理是否成功
     */
    private boolean success;
    
    /**
     * 是否已授权
     */
    private boolean authorize;
    
    /**
     * 是否同步完成
     */
    private boolean syncComplete;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * Access Token（用于后续操作）
     */
    private String accessToken;
    
    /**
     * 创建成功结果
     */
    public static JdCallbackResult success(boolean authorize, boolean syncComplete, String accessToken) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(true);
        result.setAuthorize(authorize);
        result.setSyncComplete(syncComplete);
        result.setAccessToken(accessToken);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static JdCallbackResult failure(String errorMessage) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(false);
        result.setAuthorize(false);
        result.setSyncComplete(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
} 