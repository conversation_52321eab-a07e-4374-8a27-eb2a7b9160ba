package com.yiyi.ai_train_playground.dto.jd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 京东商品详情响应DTO
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "京东商品详情响应")
public class JdPrdDtlResponse {

    /**
     * 商品主键ID
     */
    @Schema(description = "商品主键ID", example = "1")
    private Long id;

    /**
     * 京东商品ID
     */
    @Schema(description = "京东商品ID", example = "1")
    private Long wareId;

    /**
     * 京东详情markdown
     */
    @Schema(description = "京东详情markdown", example = "小米手环7详细介绍...")
    private String jdProdDtl;

    /**
     * 京东详情图片列表（逗号分隔）
     */
    @Schema(description = "京东详情图片列表", 
            example = "https://img11.360buyimg.com/devfe/jfs/t1/137620/25/33301/62910/63ef1f7eFeba5d9e1/19c5a79dee1137be.jpg,https://img11.360buyimg.com/devfe/jfs/t1/137620/25/33301/62910/63ef1f7eFeba5d9e1/19c5a79dee1137be.jpg")
    private String jdProdImgList;
}
