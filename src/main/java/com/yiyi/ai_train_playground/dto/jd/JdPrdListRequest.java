package com.yiyi.ai_train_playground.dto.jd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 京东商品列表查询请求参数
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "京东商品列表查询请求参数")
public class JdPrdListRequest {

    /**
     * 搜索关键词（同时匹配商品标题、SKU ID、品牌名称）
     */
    @Schema(description = "搜索关键词（同时匹配商品标题、SKU ID、品牌名称）", example = "小米手环")
    private String searchKeyword;

    /**
     * 页号，从1开始
     */
    @Schema(description = "页号，从1开始", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
