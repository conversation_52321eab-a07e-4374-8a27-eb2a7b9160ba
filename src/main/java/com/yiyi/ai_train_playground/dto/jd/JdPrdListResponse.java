package com.yiyi.ai_train_playground.dto.jd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 京东商品列表查询响应结果
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "京东商品列表查询响应结果")
public class JdPrdListResponse {

    /**
     * 总记录数
     */
    @Schema(description = "总记录数", example = "2")
    private Long total;

    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID", example = "654321")
    private Long shopId;

    /**
     * 是否授权
     */
    @Schema(description = "是否授权", example = "true")
    private Boolean isAuthorize;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "1")
    private Integer isSyncComplete;

    /**
     * 同步状态描述
     */
    @Schema(description = "同步状态描述", example = "同步中")
    private String isSyncCompleteDesc;

    /**
     * 商品列表
     */
    @Schema(description = "商品列表")
    private List<JdProductItem> rows;

    /**
     * 京东商品项
     */
    @Data
    @Schema(description = "京东商品项")
    public static class JdProductItem {

        /**
         * 京东商品表主键
         */
        @Schema(description = "京东商品表主键", example = "1")
        private Long id;

        /**
         * 品牌ID
         */
        @Schema(description = "品牌ID", example = "1")
        private Long brandId;

        /**
         * 商品ID
         */
        @Schema(description = "商品ID", example = "12345")
        private Long wareId;

        /**
         * 品牌名称
         */
        @Schema(description = "品牌名称", example = "小米")
        private String brandName;

        /**
         * 商品LOGO完整URL
         */
        @Schema(description = "商品LOGO完整URL", example = "https://img11.360buyimg.com/devfe/jfs/t1/137620/25/33301/62910/63ef1f7eFeba5d9e1/19c5a79dee1137be.jpg")
        private String logo;

        /**
         * 商品标题
         */
        @Schema(description = "商品标题", example = "数据分析初级版试用")
        private String title;

        /**
         * 商品状态描述
         */
        @Schema(description = "商品状态描述", example = "上架")
        private String status;

        /**
         * 上线时间
         */
        @Schema(description = "上线时间", example = "2019-01-01 01:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime onlineTime;

        /**
         * 下线时间
         */
        @Schema(description = "下线时间", example = "2019-01-01 01:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime offLineTime;
    }
}
