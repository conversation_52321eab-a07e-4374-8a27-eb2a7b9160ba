package com.yiyi.ai_train_playground.dto.shortphrase;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 快捷短语创建请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@Schema(description = "快捷短语创建请求")
public class ShortcutPhraseCreateRequest {
    
    @Schema(description = "快捷短语分组ID", example = "1")
    @NotNull(message = "分组ID不能为空")
    private Long spGroupId;
    
    @Schema(description = "短语标题", example = "欢迎语")
    @NotBlank(message = "短语标题不能为空")
    private String phraseTitle;
    
    @Schema(description = "短语内容", example = "您好，欢迎咨询！")
    @NotBlank(message = "短语内容不能为空")
    private String phraseContent;
    
    @Schema(description = "短语类型：0-文本，1-图片，2-链接", example = "0")
    @NotNull(message = "短语类型不能为空")
    @Min(value = 0, message = "短语类型值无效")
    @Max(value = 2, message = "短语类型值无效")
    private Integer phraseType;
    
    @Schema(description = "是否启用：false-禁用，true-启用", example = "true")
    private Boolean isActive;
    
    @Schema(description = "排序位置", example = "0")
    @Min(value = 0, message = "排序位置不能为负数")
    private Integer sortOrder;
    
    @Schema(description = "标签（逗号分隔）", example = "欢迎,问候")
    private String tags;
}
