package com.yiyi.ai_train_playground.dto.shortphrase;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 快捷短语列表DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@Schema(description = "快捷短语列表信息")
public class ShortcutPhraseListDTO {
    
    @Schema(description = "短语ID", example = "1")
    private Long id;
    
    @Schema(description = "快捷短语分组ID", example = "1")
    private Long spGroupId;
    
    @Schema(description = "分组标题", example = "常用问候语")
    private String groupTitle;
    
    @Schema(description = "短语标题", example = "欢迎语")
    private String phraseTitle;
    
    @Schema(description = "短语内容", example = "您好，欢迎咨询！")
    private String phraseContent;
    
    @Schema(description = "短语类型：0-文本，1-图片，2-链接", example = "0")
    private Integer phraseType;
    
    @Schema(description = "短语类型名称", example = "文本")
    private String phraseTypeName;
    
    @Schema(description = "使用次数", example = "10")
    private Integer usageCount;
    
    @Schema(description = "是否启用：false-禁用，true-启用", example = "true")
    private Boolean isActive;
    
    @Schema(description = "排序位置", example = "0")
    private Integer sortOrder;
    
    @Schema(description = "标签（逗号分隔）", example = "欢迎,问候")
    private String tags;
    
    @Schema(description = "创建时间", example = "2025-07-24T10:30:00")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间", example = "2025-07-24T10:30:00")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建人", example = "admin")
    private String creator;
    
    @Schema(description = "更新人", example = "admin")
    private String updater;
}
