package com.yiyi.ai_train_playground.dto.shortphrase;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 快捷短语查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@Schema(description = "快捷短语查询请求")
public class ShortcutPhraseQueryRequest {
    
    @Schema(description = "快捷短语分组ID", example = "1")
    private Long spGroupId;
    
    @Schema(description = "短语标题（模糊查询）", example = "欢迎")
    private String phraseTitle;
    
    @Schema(description = "短语内容（模糊查询）", example = "您好")
    private String phraseContent;
    
    @Schema(description = "短语类型：0-文本，1-图片，2-链接", example = "0")
    private Integer phraseType;
    
    @Schema(description = "是否启用：false-禁用，true-启用", example = "true")
    private Boolean isActive;
    
    @Schema(description = "标签（模糊查询）", example = "欢迎")
    private String tags;
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
