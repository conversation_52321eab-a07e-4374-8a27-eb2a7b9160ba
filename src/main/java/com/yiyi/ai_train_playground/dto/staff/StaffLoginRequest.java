package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 员工登录请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
@Schema(name = "StaffLoginRequest", description = "员工登录请求")
public class StaffLoginRequest {

    @Schema(description = "用户名（员工登录名）",
            example = "staff001",
            required = true)
    @NotBlank(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码",
            example = "123456",
            required = true)
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "是否记住登录状态，默认为false",
            example = "false",
            defaultValue = "false")
    private Boolean rememberMe = false;
}