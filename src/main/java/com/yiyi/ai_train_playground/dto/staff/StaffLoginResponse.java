package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 员工登录响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
@Schema(name = "StaffLoginResponse", description = "员工登录响应")
public class StaffLoginResponse {

    @Schema(description = "员工ID", example = "1")
    private Long staffId;

    @Schema(description = "用户名", example = "staff001")
    private String username;

    @Schema(description = "显示名称", example = "张三")
    private String displayName;

    @Schema(description = "团队ID", example = "1")
    private Long teamId;

    @Schema(description = "员工状态", example = "1")
    private Integer status;

    @Schema(description = "JWT访问令牌",
            example = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ")
    private String token;
}