package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "员工标签详情响应")
public class StaffTagDTO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "标签名称", example = "技术专家")
    private String name;

    @Schema(description = "团队ID", example = "1")
    private Long teamId;

    @Schema(description = "创建时间", example = "2025-07-27T10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2025-07-27T10:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "更新人", example = "admin")
    private String updater;

    @Schema(description = "版本号（乐观锁）", example = "0")
    private Long version;
}