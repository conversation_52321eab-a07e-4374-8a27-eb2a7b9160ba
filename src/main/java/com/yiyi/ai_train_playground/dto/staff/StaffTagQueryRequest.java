package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "员工标签查询请求")
public class StaffTagQueryRequest {

    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    @Schema(description = "标签名称（模糊查询）", example = "技术")
    private String name;

    @Schema(description = "团队ID", example = "1")
    private Long teamId;
}