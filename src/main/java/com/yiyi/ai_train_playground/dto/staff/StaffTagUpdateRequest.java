package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "员工标签更新请求")
public class StaffTagUpdateRequest {

    @NotNull(message = "标签ID不能为空")
    @Schema(description = "标签ID", example = "1", required = true)
    private Long id;

    @NotBlank(message = "标签名称不能为空")
    @Size(max = 50, message = "标签名称长度不能超过50个字符")
    @Schema(description = "标签名称", example = "高级技术专家", required = true)
    private String name;

    @NotNull(message = "版本号不能为空")
    @Schema(description = "版本号（乐观锁）", example = "0", required = true)
    private Long version;
}