package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 角色创建请求DTO
 */
@Schema(description = "角色创建请求")
public class TrainRoleCreateRequest {

    @Schema(description = "角色名称", example = "客服", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 64, message = "角色名称长度不能超过64字符")
    private String roleName;

    @Schema(description = "角色编码", example = "CUSTOMER_SERVICE", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 64, message = "角色编码长度不能超过64字符")
    private String roleCode;

    @Schema(description = "角色描述", example = "负责客户服务相关工作")
    @Size(max = 255, message = "角色描述长度不能超过255字符")
    private String description;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}