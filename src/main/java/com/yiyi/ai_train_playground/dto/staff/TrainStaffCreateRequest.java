package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 员工创建请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
@Schema(description = "员工创建请求")
public class TrainStaffCreateRequest {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50字符之间")
    @Schema(description = "用户名（登录名）", example = "admin", required = true)
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 50, message = "密码长度必须在6-50字符之间")
    @Schema(description = "密码", example = "123456", required = true)
    private String password;
    
    @NotBlank(message = "显示名称不能为空")
    @Size(max = 100, message = "显示名称长度不能超过100字符")
    @Schema(description = "显示名称", example = "管理员", required = true)
    private String displayName;
    
    @Schema(description = "角色ID", example = "1")
    private Long roleId;
    
    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<Long> tagIds;
}