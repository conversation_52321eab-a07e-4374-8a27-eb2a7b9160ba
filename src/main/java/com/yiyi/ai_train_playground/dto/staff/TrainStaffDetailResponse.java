package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工详情响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
@Schema(description = "员工详情响应")
public class TrainStaffDetailResponse {
    
    @Schema(description = "员工ID", example = "1")
    private Long id;
    
    @Schema(description = "用户ID", example = "1001")
    private Long userId;
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    @Schema(description = "显示名称", example = "管理员")
    private String displayName;
    
    @Schema(description = "是否锁定", example = "false")
    private Boolean isLocked;
    
    @Schema(description = "登录失败次数", example = "0")
    private Integer failedAttempts;
    
    @Schema(description = "账户锁定时间")
    private LocalDateTime lockTime;
    
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;
    
    @Schema(description = "最后登录IP", example = "*************")
    private String lastLoginIp;
    
    @Schema(description = "团队ID", example = "1")
    private Long teamId;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建人", example = "admin")
    private String creator;
    
    @Schema(description = "更新人", example = "admin")
    private String updater;
    
    @Schema(description = "版本号", example = "0")
    private Long version;
    
    @Schema(description = "角色ID", example = "1")
    private Long roleId;
    
    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<Long> tagIds;
}