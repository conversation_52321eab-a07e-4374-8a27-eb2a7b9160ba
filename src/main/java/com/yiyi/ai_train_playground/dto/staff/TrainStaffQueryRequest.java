package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 员工查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
@Schema(description = "员工查询请求")
public class TrainStaffQueryRequest {
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "用户名（模糊查询）", example = "admin")
    private String username;
    
    @Schema(description = "显示名称（模糊查询）", example = "管理员")
    private String displayName;
    
    @Schema(description = "邮箱（模糊查询）", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "员工状态：1-正常，2-停用", example = "1")
    private Integer status;
    
    @Schema(description = "是否锁定：true-已锁定，false-未锁定", example = "false")
    private Boolean isLocked;
}