package com.yiyi.ai_train_playground.dto.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 员工更新请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
@Schema(description = "员工更新请求")
public class TrainStaffUpdateRequest {
    
    @NotNull(message = "员工ID不能为空")
    @Schema(description = "员工ID", example = "1", required = true)
    private Long id;
    
    @NotBlank(message = "显示名称不能为空")
    @Size(max = 100, message = "显示名称长度不能超过100字符")
    @Schema(description = "显示名称", example = "管理员", required = true)
    private String displayName;
    
    @NotNull(message = "版本号不能为空")
    @Schema(description = "版本号（用于乐观锁）", example = "0", required = true)
    private Long version;
    
    @Schema(description = "角色ID", example = "1")
    private Long roleId;
    
    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<Long> tagIds;
}