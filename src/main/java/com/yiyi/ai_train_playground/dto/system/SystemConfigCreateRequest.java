package com.yiyi.ai_train_playground.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统配置创建请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Data
@Schema(description = "系统配置创建请求")
public class SystemConfigCreateRequest {
    
    @NotBlank(message = "命名空间不能为空")
    @Size(max = 32, message = "命名空间长度不能超过32个字符")
    @Schema(description = "命名空间", example = "default", requiredMode = Schema.RequiredMode.REQUIRED)
    private String namespace;
    
    @NotBlank(message = "配置键不能为空")
    @Size(max = 64, message = "配置键长度不能超过64个字符")
    @Schema(description = "配置键", example = "system.timeout", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configKey;
    
    @NotBlank(message = "配置值不能为空")
    @Schema(description = "配置值", example = "30000", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configValue;
    
    @Size(max = 255, message = "描述长度不能超过255个字符")
    @Schema(description = "描述", example = "系统超时配置，单位毫秒")
    private String description;
}
