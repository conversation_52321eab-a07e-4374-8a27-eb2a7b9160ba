package com.yiyi.ai_train_playground.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统配置查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Data
@Schema(description = "系统配置查询请求")
public class SystemConfigQueryRequest {
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "命名空间", example = "default")
    private String namespace;
    
    @Schema(description = "配置键（支持模糊查询）", example = "system.timeout")
    private String configKey;
    
    @Schema(description = "描述（支持模糊查询）", example = "超时配置")
    private String description;
}
