package com.yiyi.ai_train_playground.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统配置响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Data
@Schema(description = "系统配置响应")
public class SystemConfigResponse {
    
    @Schema(description = "配置ID", example = "1")
    private Long id;
    
    @Schema(description = "团队ID", example = "1")
    private Long teamId;
    
    @Schema(description = "命名空间", example = "default")
    private String namespace;
    
    @Schema(description = "配置键", example = "system.timeout")
    private String configKey;
    
    @Schema(description = "配置值", example = "30000")
    private String configValue;
    
    @Schema(description = "描述", example = "系统超时配置，单位毫秒")
    private String description;
    
    @Schema(description = "版本号", example = "0")
    private Long version;
    
    @Schema(description = "创建人", example = "admin")
    private String creator;
    
    @Schema(description = "更新人", example = "admin")
    private String updater;
    
    @Schema(description = "创建时间", example = "2025-08-20 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间", example = "2025-08-20 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
