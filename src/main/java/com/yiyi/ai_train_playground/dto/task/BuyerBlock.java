package com.yiyi.ai_train_playground.dto.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 买家聊天块数据传输对象
 * 包含一组连续的买家消息（被客服回复分割）
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BuyerBlock {
    
    /**
     * 买家姓名
     */
    private String buyerName;
    
    /**
     * 买家消息列表
     */
    private List<ChatLine> messages;
    
    /**
     * 块的序号（用于调试）
     */
    private int blockIndex;
}
