package com.yiyi.ai_train_playground.dto.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天记录行数据传输对象
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatLine {
    
    /**
     * 发送人姓名
     */
    private String sender;
    
    /**
     * 发送时间（字符串格式）
     */
    private String sendTime;
    
    /**
     * 发送内容
     */
    private String content;
    
    /**
     * 是否为买家消息
     */
    private boolean isBuyer;
    
    /**
     * 原始行号（用于调试）
     */
    private int lineNumber;
}
