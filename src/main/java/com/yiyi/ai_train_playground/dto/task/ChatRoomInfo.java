package com.yiyi.ai_train_playground.dto.task;

import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import lombok.Data;
import java.util.List;

/**
 * 聊天室信息DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-29
 */
@Data
public class ChatRoomInfo {
    
    /**
     * 聊天室ID
     */
    private Long chatroomId;
    
    /**
     * 进线频率下限（分钟）
     */
    private Integer entryFreqMin;
    
    /**
     * 进线频率上限（分钟）
     */
    private Integer entryFreqMax;
    
    /**
     * 接待时长（分钟）
     */
    private Integer receptionDuration;
    
    /**
     * 剧本列表
     */
    private List<ScriptDetailDTO> scriptList;
    
    /**
     * 聊天室任务列表
     */
    private List<ChatRoomTaskInfo> chatRoomTaskList;
}
