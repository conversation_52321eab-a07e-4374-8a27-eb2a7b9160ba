package com.yiyi.ai_train_playground.dto.task;

import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import lombok.Data;
import java.util.List;

/**
 * 知识库转换用聊天室信息DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
@Data
public class ChatRoomInfo4Conv {
    
    /**
     * 聊天室ID
     */
    private Long chatroomId;
    
    /**
     * 进线频率下限（分钟）
     */
    private Integer entryFreqMin;
    
    /**
     * 进线频率上限（分钟）
     */
    private Integer entryFreqMax;
    
    /**
     * 接待时长（分钟）
     */
    private Integer receptionDuration;
    
    /**
     * 剧本列表
     */
    private List<ScriptDetailDTO> scriptList;
    
    /**
     * 聊天室任务列表-兼容老的，后面要删除
     */
    private List<ChatRoomTaskInfo> chatRoomTaskList;

    /**
     * 为会话知识库单独建的任务列表
     */
    private List<ChatRoomTaskInfo4Conv> chatRoomTaskList4Conv;

   /**
     * 模拟聊天室窗口主表ID
     */
    private Long winChatMainId;

    /**
     * qaReportMainId
     */
    private Long qaReportMainId;

    /**
     * 接待皮肤：支持字符串值，如"0"(千牛)、"1"(咚咚)、"2"(抖音)、"3"(默认)等
     */
    private String receptionSkin;

    /**
     * 场景模式
     */
    private Integer sceneMode;

    /**
     * 定时器显示
     */
    private Boolean timerDisplay;

}