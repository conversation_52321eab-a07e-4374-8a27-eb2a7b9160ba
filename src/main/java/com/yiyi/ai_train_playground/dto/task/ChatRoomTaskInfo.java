package com.yiyi.ai_train_playground.dto.task;

import lombok.Data;

/**
 * 聊天室任务信息DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-29
 */
@Data
public class ChatRoomTaskInfo {
    
    /**
     * 剧本ID
     */
    private Long scriptId;
    
    /**
     * 剧本名称
     */
    private String scriptName;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 训练循环次数
     */
    private Integer trainingRecycleCnt;
    
    /**
     * 买家需求
     */
    private String buyerRequirement;
    
    /**
     * 生成类型代码
     */
    private Integer generationTypeCode;
    
    /**
     * 意图ID
     */
    private Long intentId;
    
    /**
     * 意图名称
     */
    private String intentName;
    
    /**
     * 父意图名称
     */
    private String parentIntentName;

    /**
     * 客服发送消息倒计时
     */
    private Long srvSendCd;

    /**
     * QA主表ID
     */
    private Long qaMainId;
    
    /**
     * 任务目的标签：0-训练，1-考核，2-面试，3-其他
     */
    private Integer taskPurposeTag;

    /**
     * 判断类型
     */
    private Integer judgeType;

    /**
     * 是否显示解决方案
     */
    private Integer isShowResolve;

    /**
     * 是否显示正确答案
     */
    private Integer isShowCorrect;

    /**
     * 频次问题数量
     */
    private Integer freqAuesCnt;
}
