package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 聊天室列表响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Data
@Schema(description = "聊天室列表响应")
public class ChatroomListDTO {
    
    /**
     * 主键ID
     */
    @Schema(description = "聊天室ID", example = "1")
    private Long id;
    
    /**
     * 聊天室名称
     */
    @Schema(description = "聊天室名称", example = "客服培训室A")
    private String roomName;
    
    /**
     * 接待皮肤：支持字符串值，如"qianniu_skin"(千牛)、"dongdong_skin"(咚咚)、"douyin_skin"(抖音)、"default_skin"(默认)等
     */
    @Schema(description = "接待皮肤：支持字符串值，如\"qianniu_skin\"(千牛)、\"dongdong_skin\"(咚咚)、\"douyin_skin\"(抖音)、\"default_skin\"(默认)等", example = "qianniu_skin")
    private String receptionSkin;
    
    /**
     * 接待皮肤名称
     */
    @Schema(description = "接待皮肤名称", example = "干牛")
    private String receptionSkinName;
    
    /**
     * 场景模式：0-萌新友好，1-压力考核，2-自定义
     */
    @Schema(description = "场景模式：0-萌新友好，1-压力考核，2-自定义", example = "0")
    private Integer sceneMode;
    
    /**
     * 场景模式名称
     */
    @Schema(description = "场景模式名称", example = "萌新友好")
    private String sceneModeName;
    
    /**
     * 快捷短语ID
     */
    @Schema(description = "快捷短语ID", example = "1")
    private Long quickPhrasesId;
    
    /**
     * 快捷短语名称
     */
    @Schema(description = "快捷短语名称", example = "标准短语")
    private String quickPhrasesName;
    
    /**
     * 接待时长（分钟）
     */
    @Schema(description = "接待时长（分钟）", example = "30")
    private Integer receptionDuration;
    
    /**
     * 读秒显示
     */
    @Schema(description = "读秒显示", example = "false")
    private Boolean timerDisplay;

    /**
     * 进线频率下限（分钟）
     */
    @Schema(description = "进线频率下限（分钟）", example = "1")
    private Integer entryFreqMin;

    /**
     * 进线频率上限（分钟）
     */
    @Schema(description = "进线频率上限（分钟）", example = "5")
    private Integer entryFreqMax;

    /**
     * 关联任务数量
     */
    @Schema(description = "关联任务数量", example = "3")
    private Integer taskCount;
    
    /**
     * 关联员工数量
     */
    @Schema(description = "关联员工数量", example = "2")
    private Integer staffCount;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "admin")
    private String creator;
}