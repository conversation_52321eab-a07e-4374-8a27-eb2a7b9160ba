package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 聊天室查询请求参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Data
@Schema(description = "聊天室查询请求")
public class ChatroomQueryRequest {
    
    /**
     * 接待皮肤：支持字符串值，如"qianniu_skin"(千牛)、"dongdong_skin"(咚咚)、"douyin_skin"(抖音)、"default_skin"(默认)等
     */
    @Schema(description = "接待皮肤：支持字符串值，如\"qianniu_skin\"(千牛)、\"dongdong_skin\"(咚咚)、\"douyin_skin\"(抖音)、\"default_skin\"(默认)等", example = "qianniu_skin")
    private String receptionSkin;
    
    /**
     * 场景模式：0-萌新友好，1-压力考核，2-自定义
     */
    @Schema(description = "场景模式：0-萌新友好，1-压力考核，2-自定义", example = "0")
    private Integer sceneMode;
    
    /**
     * 聊天室名称：支持模糊查询
     */
    @Schema(description = "聊天室名称：支持模糊查询", example = "测试聊天室")
    private String roomName;
    
    /**
     * 快捷短语ID
     */
    @Schema(description = "快捷短语ID", example = "1")
    private Long quickPhrasesId;
    
    /**
     * 创建时间起始点
     */
    @Schema(description = "创建时间起始点", example = "2025-01-01")
    private String createTimeStart;
    
    /**
     * 创建时间结束点
     */
    @Schema(description = "创建时间结束点", example = "2025-12-31")
    private String createTimeEnd;
    
    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    /**
     * 偏移量（用于SQL查询）
     */
    @Schema(hidden = true)
    private Integer offset;
}