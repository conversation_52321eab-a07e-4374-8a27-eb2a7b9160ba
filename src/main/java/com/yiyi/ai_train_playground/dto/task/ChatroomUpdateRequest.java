package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * 聊天室更新请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Data
@Schema(description = "聊天室更新请求")
public class ChatroomUpdateRequest {
    
    /**
     * 聊天室ID
     */
    @Schema(description = "聊天室ID", example = "1")
    private Long id;
    
    /**
     * 聊天室名称
     */
    @NotBlank(message = "聊天室名称不能为空")
    @Schema(description = "聊天室名称", example = "客服培训室A")
    private String roomName;
    
    /**
     * 接待皮肤：支持字符串值，如"0"(千牛)、"1"(咚咚)、"2"(抖音)、"3"(默认)等
     */
    @Schema(description = "接待皮肤：支持字符串值，如\"0\"(千牛)、\"1\"(咚咚)、\"2\"(抖音)、\"3\"(默认)等", example = "0")
    private String receptionSkin;
    
    /**
     * 场景模式：0-萌新友好，1-压力考核，2-自定义
     */
    @Schema(description = "场景模式：0-萌新友好，1-压力考核，2-自定义", example = "0")
    private Integer sceneMode;
    
    /**
     * 快捷短语ID
     */
    @Schema(description = "快捷短语ID", example = "1")
    private Long quickPhrasesId;
    
    /**
     * 接待时长（分钟）
     */
    @Schema(description = "接待时长（分钟）", example = "30")
    private Integer receptionDuration;
    
    /**
     * 读秒：0-不显示，1-显示
     */
    @Schema(description = "读秒：0-不显示，1-显示", example = "false")
    private Boolean timerDisplay;

    /**
     * 进线频率下限（分钟）
     */
    @Schema(description = "进线频率下限（分钟）", example = "1")
    private Integer entryFreqMin;

    /**
     * 进线频率上限（分钟）
     */
    @Schema(description = "进线频率上限（分钟）", example = "5")
    private Integer entryFreqMax;

    /**
     * 版本号（用于乐观锁）
     */
    @Schema(description = "版本号", example = "0")
    private Long version;
    
    /**
     * 关联任务列表
     */
    @Schema(description = "关联任务列表")
    private List<ChatroomTaskUpdateDTO> taskList;
    
    /**
     * 关联员工ID列表
     */
    @Schema(description = "关联员工ID列表，支持多个员工")
    private List<Long> staffList;
    
    /**
     * 聊天室任务更新DTO
     */
    @Data
    @Schema(description = "聊天室任务更新DTO")
    public static class ChatroomTaskUpdateDTO {
        /**
         * 任务关联ID
         */
        @Schema(description = "任务关联ID", example = "1")
        private Long id;
        
        /**
         * 任务ID，关联train_reception_task表的主键
         */
        @Schema(description = "任务ID", example = "1")
        private Long taskId;
        
        /**
         * 此任务循环次数
         */
        @Schema(description = "此任务循环次数", example = "0")
        private Integer trainingRecycleCnt;
    }
}