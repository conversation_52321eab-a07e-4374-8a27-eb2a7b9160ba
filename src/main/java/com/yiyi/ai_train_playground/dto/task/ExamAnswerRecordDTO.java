package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 考试答题记录DTO
 * 用于ShowExamRst接口返回答题详情
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "考试答题记录DTO")
public class ExamAnswerRecordDTO {

    @Schema(description = "答题记录ID", example = "1")
    private Long id;

    @Schema(description = "UUID标识", example = "uuid-12345")
    private String uuid;

    @Schema(description = "题目编号", example = "1/16")
    private String quesNo;

    @Schema(description = "题目内容", example = "请问这个产品的功效是什么？")
    private String question;

    @Schema(description = "标准答案", example = "这个产品具有美白保湿功效")
    private String answer;

    @Schema(description = "实际问题", example = "用户实际提出的问题")
    private String actualQuestion;

    @Schema(description = "实际答案", example = "客服实际给出的回答")
    private String actualAnswer;

    @Schema(description = "解析结果JSON", example = "{\"分析\":\"匹配度分析\",\"得分\":85}")
    private String resolve;

    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "题目得分", example = "85")
    private Integer score;
}