package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * QA解析结果DTO
 * 用于封装train_qa_rdm表resolve字段的解析结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA解析结果DTO")
public class QaResolveResultDTO {

    @Schema(description = "匹配度分析描述", example = "完全不匹配，客服回答与正确答案无关")
    private String analysis;

    @Schema(description = "评分分数", example = "0")
    private Integer score;

    @Schema(description = "扣分点列表", example = "[\"回答与正确答案无关，未提及防晒到货状态\"]")
    private List<String> deductionPoints;

    @Schema(description = "正确答案参考", example = "应明确说明'亲爱哒~防晒到货是水油分离的状态哈~'")
    private String correctAnswer;

    @Schema(description = "丰富后的下一问题", example = "您好，我现在处于孕期，想了解下这款防晒产品孕妇能不能使用呢？")
    private String nextQuestion;
}