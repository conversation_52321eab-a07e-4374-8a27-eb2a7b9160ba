package com.yiyi.ai_train_playground.dto.task;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 问答简单DTO（带UUID）
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
@Data
@Schema(description = "问答简单信息（带UUID）")
public class QaSimplelDtoWithUUID {
    
    /**
     * 全球唯一标识符
     */
    @Schema(description = "全球唯一标识符", example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")
    private String uuid;
    
    /**
     * 问题
     */
    @Schema(description = "问题", example = "这个产品的保修期是多长？")
    private String question;
    
    /**
     * 答案
     */
    @Schema(description = "答案", example = "我们的产品提供一年免费保修服务。")
    private String answer;

    /**
     * 实际问题
     */
    @Schema(description = "实际问题", example = "用户实际提出的问题")
    private String actualQuestion;

    /**
     * 实际答案
     */
    @Schema(description = "实际答案", example = "客服实际回复的答案")
    private String actualAnswer;

    /**
     * 解决方案
     */
    @Schema(description = "解决方案", example = "详细的解决方案描述")
    private String resolve;

    /**
     * 问题编号
     */
    @Schema(description = "问题编号", example = "Q001")
    private String quesNo;

    /**
     * 构造函数
     */
    public QaSimplelDtoWithUUID() {}

    /**
     * 构造函数
     * @param uuid 唯一标识符
     * @param question 问题
     * @param answer 答案
     */
    public QaSimplelDtoWithUUID(String uuid, String question, String answer) {
        this.uuid = uuid;
        this.question = question;
        this.answer = answer;
    }

    /**
     * 完整构造函数
     * @param uuid 唯一标识符
     * @param question 问题
     * @param answer 答案
     * @param actualQuestion 实际问题
     * @param actualAnswer 实际答案
     * @param resolve 解决方案
     * @param quesNo 问题编号
     */
    public QaSimplelDtoWithUUID(String uuid, String question, String answer,
                               String actualQuestion, String actualAnswer,
                               String resolve, String quesNo) {
        this.uuid = uuid;
        this.question = question;
        this.answer = answer;
        this.actualQuestion = actualQuestion;
        this.actualAnswer = actualAnswer;
        this.resolve = resolve;
        this.quesNo = quesNo;
    }
}