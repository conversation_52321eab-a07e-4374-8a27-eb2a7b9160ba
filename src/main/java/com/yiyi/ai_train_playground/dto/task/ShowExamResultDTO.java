package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考试结果展示DTO
 * 用于ShowExamRst接口返回完整的考试结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "考试结果展示DTO")
public class ShowExamResultDTO {

    @Schema(description = "报告主记录ID", example = "1")
    private Long qaReportMainId;

    @Schema(description = "考试用户真实姓名", example = "张三")
    private String examUserRealName;

    @Schema(description = "考试用户编号", example = "EMP001")
    private String examUserNo;

    @Schema(description = "考试总分", example = "85.50")
    private BigDecimal examScore;

    @Schema(description = "题目数量", example = "10")
    private Integer totalQuestions;

    @Schema(description = "答题记录列表")
    private List<ExamAnswerRecordDTO> qaRdmList;
}