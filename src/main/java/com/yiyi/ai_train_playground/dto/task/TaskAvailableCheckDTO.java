package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务可用性检查DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-12
 */
@Data
@Schema(description = "任务可用性检查结果")
public class TaskAvailableCheckDTO {
    
    @Schema(description = "任务ID", example = "1")
    private Long id;
    
    @Schema(description = "任务名称", example = "接待任务001")
    private String taskName;
    
    @Schema(description = "已学习条数", example = "5")
    private Long amtHasLearned;
    
    @Schema(description = "学习状态", example = "learning")
    private String learningStatus;
}