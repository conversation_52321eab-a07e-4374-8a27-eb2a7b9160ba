package com.yiyi.ai_train_playground.dto.task;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 任务学习数据传输对象
 * 用于模板学习功能的数据传输
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-12
 */
@Data
public class TaskLearningDTO {
    
    /**
     * 明细表主键ID
     */
    private Long id;
    
    /**
     * 任务ID（来自train_reception_task.id）
     */
    private Long taskId;
    
    /**
     * train_kb_tpl_detail的主键ID
     */
    private Long kbDtlId;
    
    /**
     * 剧本ID（来自train_reception_task.script_id）
     */
    private Long scriptId;
    
    /**
     * 学习状态
     */
    private String learningStatus;
    
    /**
     * 初始的聊天记录
     */
    private String f1stRawChatlog;
    
    /**
     * 最终生成的聊天记录
     */
    private String finalChatLog;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
