package com.yiyi.ai_train_playground.dto.task;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 问答报告主表查询请求DTO
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Data
@Schema(description = "问答报告主表查询请求")
public class TrainQaReportMainQueryRequest {

    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    /**
     * 聊天室ID（可选）
     */
    @Schema(description = "聊天室ID", example = "1")
    private Long chatroomId;

    /**
     * 员工ID（可选）
     */
    @Schema(description = "员工ID", example = "1")
    private Long staffId;

    /**
     * 考试用户真实姓名（模糊查询）
     */
    @Schema(description = "考试用户真实姓名（模糊查询）", example = "张三")
    private String examUserRealName;

    /**
     * 考试用户编号（模糊查询）
     */
    @Schema(description = "考试用户编号（模糊查询）", example = "E001")
    private String examUserNo;

    /**
     * 创建时间开始（包含）
     */
    @Schema(description = "创建时间开始（包含）", example = "2024-01-01T00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束（包含）
     */
    @Schema(description = "创建时间结束（包含）", example = "2024-12-31T23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 最低分数（大于等于此分数）
     */
    @Schema(description = "最低分数（大于等于此分数）", example = "60.0")
    private BigDecimal minScore;
}
