package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 接待任务创建请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@Schema(description = "接待任务创建请求")
public class TrainReceptionTaskCreateRequest {

    @Schema(description = "接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法", example = "1")
    @NotNull(message = "任务模式不能为空")
    @Min(value = 0, message = "任务模式值无效")
    @Max(value = 2, message = "任务模式值无效")
    private Integer taskMode;

    @Schema(description = "接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务", example = "0")
    @NotNull(message = "任务类型不能为空")
    @Min(value = 0, message = "任务类型值无效")
    @Max(value = 2, message = "任务类型值无效")
    private Integer taskType;

    @Schema(description = "接待任务名称", example = "商品知识训练任务")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @Schema(description = "任务描述", example = "针对新员工的商品知识培训")
    private String taskDescription;

    @Schema(description = "剧本ID，来自train_script表的主键", example = "1")
    private Long scriptId;

    @Schema(description = "接待时长（分钟）", example = "30")
//    @NotNull(message = "接待时长不能为空")
    @Min(value = 1, message = "接待时长必须大于0")
    private Integer receptionDuration;

    @Schema(description = "顾客提问时间间隔：0-随机，1-固定", example = "0")
    @Min(value = 0, message = "提问间隔类型值无效")
    @Max(value = 1, message = "提问间隔类型值无效")
    private Integer questionIntervalType;

    @Schema(description = "提问间隔秒数", example = "3")
    @Min(value = 1, message = "提问间隔秒数必须大于0")
    private Integer questionIntervalSeconds;

    @Schema(description = "训练次数限制：false-关，true-开", example = "false")
    private Boolean trainingLimitEnabled;

    @Schema(description = "任务标签：0-训练，1-考核，2-面试，3-其他", example = "0")
    @Min(value = 0, message = "任务标签值无效")
    @Max(value = 3, message = "任务标签值无效")
    private Integer taskPurposeTag;

    @Schema(description = "打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打", example = "0")
    @Min(value = 0, message = "打分响应值无效")
    @Max(value = 2, message = "打分响应值无效")
    private Integer judgeType;

    @Schema(description = "关联的知识库模板ID，关联train_kb_tpl_main表主键", example = "1")
    @Min(value = 1, message = "知识库模板ID必须大于0")
    private Long convKbId;

    @Schema(description = "高频知识库ID，关联train_qa_import_main表主键", example = "1")
    private Long qaMainId;
    
    @Schema(description = "学习状态：un_learn-未学习，learning-学习中，learned-已学习", example = "un_learn")
    private String learningStatus;
    
    @Schema(description = "待学习的总条数", example = "100")
    private Long amtToBeLearned;
    
    @Schema(description = "目前已经学习条数", example = "50")
    private Long amtHasLearned;
    
    @Schema(description = "是否显示解析详情：false-不显示，true-显示", example = "false")
    private Boolean isShowResolve;
    
    @Schema(description = "是否显示正确答案：false-不显示，true-显示", example = "false")
    private Boolean isShowCorrect;
    
    @Schema(description = "选取的知识库条数", example = "10")
    @Min(value = 0, message = "选取的知识库条数不能为负数")
    private Integer freqAuesCnt;
    
    @Schema(description = "是否显示考察点：false-不显示，true-显示", example = "false")
    private Boolean isShowInspect;

    @Schema(description = "客服发送消息倒计时", example = "30")
    private Long srvSendCd;
}
