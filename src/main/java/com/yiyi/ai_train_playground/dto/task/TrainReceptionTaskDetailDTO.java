package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 接待任务详情DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@Schema(description = "接待任务详情信息")
public class TrainReceptionTaskDetailDTO {
    
    @Schema(description = "任务ID", example = "1")
    private Long id;

    @Schema(description = "接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法", example = "1")
    private Integer taskMode;

    @Schema(description = "任务模式名称", example = "AI智训")
    private String taskModeName;

    @Schema(description = "接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务", example = "0")
    private Integer taskType;

    @Schema(description = "任务类型名称", example = "商品知识训练")
    private String taskTypeName;

    @Schema(description = "接待任务名称", example = "商品知识训练任务")
    private String taskName;

    @Schema(description = "任务描述", example = "针对新员工的商品知识培训")
    private String taskDescription;

    @Schema(description = "剧本ID", example = "1")
    private Long scriptId;

    @Schema(description = "剧本名称", example = "商品咨询剧本")
    private String scriptName;

    @Schema(description = "接待时长（分钟）", example = "30")
    private Integer receptionDuration;

    @Schema(description = "顾客提问时间间隔：0-随机，1-固定", example = "0")
    private Integer questionIntervalType;

    @Schema(description = "提问间隔类型名称", example = "随机")
    private String questionIntervalTypeName;

    @Schema(description = "提问间隔秒数", example = "3")
    private Integer questionIntervalSeconds;

    @Schema(description = "训练次数限制：false-关，true-开", example = "false")
    private Boolean trainingLimitEnabled;

    @Schema(description = "任务标签：0-训练，1-考核，2-面试，3-其他", example = "0")
    private Integer taskPurposeTag;

    @Schema(description = "任务标签名称", example = "训练")
    private String taskPurposeTagName;

    @Schema(description = "打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打", example = "0")
    private Integer judgeType;

    @Schema(description = "打分响应名称", example = "单条会话打分")
    private String judgeTypeName;

    @Schema(description = "关联的知识库模板ID，关联train_kb_tpl_main表主键", example = "0")
    private Long convKbId;

    @Schema(description = "高频知识库ID，关联train_qa_import_main表主键", example = "1")
    private Long qaMainId;

    @Schema(description = "高频知识库名称", example = "商品问答知识库")
    private String qaMainName;
    
    @Schema(description = "学习状态：un_learn-未学习，learning-学习中，learned-已学习", example = "un_learn")
    private String learningStatus;
    
    @Schema(description = "学习状态代码", example = "un_learn")
    private String learningStatusCode;
    
    @Schema(description = "学习状态中文描述", example = "未学习")
    private String learningStatusValue;
    
    @Schema(description = "学习完成进度百分比（带%符号）", example = "50%")
    private String learningProgressPercent;
    
    @Schema(description = "待学习的总条数", example = "100")
    private Long amtToBeLearned;
    
    @Schema(description = "目前已经学习条数", example = "50")
    private Long amtHasLearned;
    
    @Schema(description = "是否显示解析详情：false-不显示，true-显示", example = "false")
    private Boolean isShowResolve;
    
    @Schema(description = "是否显示正确答案：false-不显示，true-显示", example = "false")
    private Boolean isShowCorrect;
    
    @Schema(description = "选取的知识库条数", example = "10")
    private Integer freqAuesCnt;
    
    @Schema(description = "是否显示考察点：false-不显示，true-显示", example = "false")
    private Boolean isShowInspect;

    @Schema(description = "客服发送消息倒计时", example = "30")
    private Long srvSendCd;

    @Schema(description = "团队ID", example = "1")
    private Long teamId;
    
    @Schema(description = "创建时间", example = "2025-07-24T10:30:00")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间", example = "2025-07-24T10:30:00")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建人", example = "admin")
    private String creator;
    
    @Schema(description = "更新人", example = "admin")
    private String updater;
    
    @Schema(description = "版本号", example = "1")
    private Long version;
}
