package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 接待任务查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@Schema(description = "接待任务查询请求")
public class TrainReceptionTaskQueryRequest {
    
    @Schema(description = "接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法", example = "1")
    private Integer taskMode;

    @Schema(description = "接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务", example = "0")
    private Integer taskType;

    @Schema(description = "任务名称（模糊查询）", example = "商品知识")
    private String taskName;

    @Schema(description = "剧本ID", example = "1")
    private Long scriptId;

    @Schema(description = "任务标签：0-训练，1-考核，2-面试，3-其他", example = "0")
    private Integer taskPurposeTag;

    @Schema(description = "打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打", example = "0")
    private Integer judgeType;
    
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
