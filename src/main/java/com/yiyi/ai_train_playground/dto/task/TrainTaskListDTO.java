package com.yiyi.ai_train_playground.dto.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "任务列表返回")
public class TrainTaskListDTO {

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "任务状态")
    private Integer taskStatus;

    @Schema(description = "训练内容")
    private String trainingContent;

    @Schema(description = "已开始人数")
    private Integer startedCount;

    @Schema(description = "已完成人数")
    private Integer completedCount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
