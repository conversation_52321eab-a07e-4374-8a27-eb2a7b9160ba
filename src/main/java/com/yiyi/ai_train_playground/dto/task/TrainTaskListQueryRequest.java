package com.yiyi.ai_train_playground.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "任务列表查询请求")
public class TrainTaskListQueryRequest {

    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    @Schema(description = "任务名称", example = "任务A")
    private String taskName;

    @Schema(description = "任务状态", example = "1")
    private Integer taskStatus;

    @Schema(description = "训练内容", example = "内容B")
    private String trainingContent;
}
