package com.yiyi.ai_train_playground.entity.converkb;

import java.time.LocalDateTime;

/**
 * 会话知识库模板明细表
 */
public class TrainKbTplDetail {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 会话知识库模板主表ID
     */
    private Long tplId;
    
    /**
     * 对话内容
     */
    private String content;
    
    /**
     * 模板类型：pre_sales-售前, saling-售中, after_sale-售后, other-其他
     */
    private String tplType;
    
    /**
     * train_kb_tpl_pre表的主键ID
     */
    private Long preId;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    // 构造函数
    public TrainKbTplDetail() {}

    public TrainKbTplDetail(Long tplId, String content, String tplType, Long teamId) {
        this.tplId = tplId;
        this.content = content;
        this.tplType = tplType;
        this.teamId = teamId;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTplId() {
        return tplId;
    }

    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTplType() {
        return tplType;
    }

    public void setTplType(String tplType) {
        this.tplType = tplType;
    }

    public Long getPreId() {
        return preId;
    }

    public void setPreId(Long preId) {
        this.preId = preId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "TrainKbTplDetail{" +
                "id=" + id +
                ", tplId=" + tplId +
                ", content='" + content + '\'' +
                ", tplType='" + tplType + '\'' +
                ", preId=" + preId +
                ", teamId=" + teamId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", version=" + version +
                '}';
    }
}