package com.yiyi.ai_train_playground.entity.converkb;

import java.time.LocalDateTime;

/**
 * 会话知识库模板主表
 */
public class TrainKbTplMain {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 会话知识库模板名称
     */
    private String name;
    
    /**
     * 本次会话累计消耗token数
     */
    private Long tokens;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 学习状态
     */
    private String learnStatus;

    /**
     * 输入token数
     */
    private Long tokenIn;

    /**
     * 输出token数
     */
    private Long tokenOut;

    /**
     * 模板描述
     */
    private String description;

    // 构造函数
    public TrainKbTplMain() {}

    public TrainKbTplMain(String name, Long tokens, Long teamId) {
        this.name = name;
        this.tokens = tokens;
        this.teamId = teamId;
    }

    public TrainKbTplMain(String name, Long tokens, Long teamId, String fileType, String learnStatus) {
        this.name = name;
        this.tokens = tokens;
        this.teamId = teamId;
        this.fileType = fileType;
        this.learnStatus = learnStatus;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTokens() {
        return tokens;
    }

    public void setTokens(Long tokens) {
        this.tokens = tokens;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getLearnStatus() {
        return learnStatus;
    }

    public void setLearnStatus(String learnStatus) {
        this.learnStatus = learnStatus;
    }

    public Long getTokenIn() {
        return tokenIn;
    }

    public void setTokenIn(Long tokenIn) {
        this.tokenIn = tokenIn;
    }

    public Long getTokenOut() {
        return tokenOut;
    }

    public void setTokenOut(Long tokenOut) {
        this.tokenOut = tokenOut;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "TrainKbTplMain{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", tokens=" + tokens +
                ", teamId=" + teamId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", version=" + version +
                ", fileType='" + fileType + '\'' +
                ", learnStatus='" + learnStatus + '\'' +
                ", tokenIn=" + tokenIn +
                ", tokenOut=" + tokenOut +
                ", description='" + description + '\'' +
                '}';
    }
}