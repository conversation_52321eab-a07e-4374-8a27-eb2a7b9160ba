package com.yiyi.ai_train_playground.entity.converkb;

import java.time.LocalDateTime;

/**
 * 会话知识库模板预处理表
 */
public class TrainKbTplPre {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 会话知识库模板主表ID
     */
    private Long tplId;
    
    /**
     * 读取的块内容
     */
    private String content;
    
    /**
     * 读取的顺序
     */
    private Integer index;
    
    /**
     * 学习状态
     */
    private String learnStatus;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    // 构造函数
    public TrainKbTplPre() {}

    public TrainKbTplPre(Long tplId, String content, Integer index, String learnStatus, Long teamId) {
        this.tplId = tplId;
        this.content = content;
        this.index = index;
        this.learnStatus = learnStatus;
        this.teamId = teamId;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTplId() {
        return tplId;
    }

    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getLearnStatus() {
        return learnStatus;
    }

    public void setLearnStatus(String learnStatus) {
        this.learnStatus = learnStatus;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "TrainKbTplPre{" +
                "id=" + id +
                ", tplId=" + tplId +
                ", content='" + content + '\'' +
                ", index=" + index +
                ", learnStatus='" + learnStatus + '\'' +
                ", teamId=" + teamId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", version=" + version +
                '}';
    }
}
