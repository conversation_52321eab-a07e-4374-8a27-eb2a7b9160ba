package com.yiyi.ai_train_playground.entity.converkb;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问答导入详情表实体类
 */
@Data
public class TrainQaImportDtl {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主表ID
     */
    private Long qaMainId;
    
    /**
     * 问题
     */
    private String question;
    
    /**
     * 答案（支持表情存储）
     */
    private String answer;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
