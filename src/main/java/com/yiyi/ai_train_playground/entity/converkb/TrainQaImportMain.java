package com.yiyi.ai_train_playground.entity.converkb;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问答导入主表实体类
 */
@Data
public class TrainQaImportMain {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 知识库名称
     */
    private String qaImName;
    
    /**
     * 知识库描述
     */
    private String qaImDesc;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
