package com.yiyi.ai_train_playground.entity.converkb;

import com.yiyi.ai_train_playground.enums.LearnStatus;
import java.time.LocalDateTime;

/**
 * 任务会话明细表
 */
public class TrainTaskConvKbDtl {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * train_kb_tpl_detail的主键ID
     */
    private Long kbDtlId;
    
    /**
     * 学习状态
     */
    private String learningStatus;
    
    /**
     * 初始的聊天记录
     */
    private String f1stRawChatlog;

    /**
     * 第二步聚合系统提示词
     */
    private String f2ndAggSysPrompt;

    /**
     * 最终生成的聊天记录
     */
    private String finalChatLog;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    // 构造函数
    public TrainTaskConvKbDtl() {}

    public TrainTaskConvKbDtl(Long taskId, Long kbDtlId, String learningStatus, Long teamId) {
        this.taskId = taskId;
        this.kbDtlId = kbDtlId;
        this.learningStatus = learningStatus;
        this.teamId = teamId;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getKbDtlId() {
        return kbDtlId;
    }

    public void setKbDtlId(Long kbDtlId) {
        this.kbDtlId = kbDtlId;
    }

    public String getLearningStatus() {
        return learningStatus;
    }

    public void setLearningStatus(String learningStatus) {
        this.learningStatus = learningStatus;
    }

    public String getF1stRawChatlog() {
        return f1stRawChatlog;
    }

    public void setF1stRawChatlog(String f1stRawChatlog) {
        this.f1stRawChatlog = f1stRawChatlog;
    }

    public String getF2ndAggSysPrompt() {
        return f2ndAggSysPrompt;
    }

    public void setF2ndAggSysPrompt(String f2ndAggSysPrompt) {
        this.f2ndAggSysPrompt = f2ndAggSysPrompt;
    }

    public String getFinalChatLog() {
        return finalChatLog;
    }

    public void setFinalChatLog(String finalChatLog) {
        this.finalChatLog = finalChatLog;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "TrainTaskConvKbDtl{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", kbDtlId=" + kbDtlId +
                ", learningStatus='" + learningStatus + '\'' +
                ", f1stRawChatlog='" + f1stRawChatlog + '\'' +
                ", f2ndAggSysPrompt='" + f2ndAggSysPrompt + '\'' +
                ", finalChatLog='" + finalChatLog + '\'' +
                ", teamId=" + teamId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", version=" + version +
                '}';
    }
}