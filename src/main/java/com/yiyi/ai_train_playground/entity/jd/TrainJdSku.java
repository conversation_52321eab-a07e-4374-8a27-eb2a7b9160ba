package com.yiyi.ai_train_playground.entity.jd;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 京东SKU表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdSku {
    /**
     * 京东SKU表主键ID
     */
    private Long id;
    
    /**
     * 团队ID，0代表系统
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    // 业务字段 - SKU基本信息
    
    /**
     * 商品ID
     */
    private Long wareId;
    
    /**
     * 商品skuId
     */
    private Long skuId;
    
    /**
     * 状态，1：上架，2：下架，4：删除
     */
    private Integer status;
    
    /**
     * 京东价
     */
    private BigDecimal jdPrice;
    
    /**
     * 外部ID
     */
    private String outerId;
    
    /**
     * SKU条形码
     */
    private String barCode;
    
    /**
     * 类目id
     */
    private Integer categoryId;
    
    /**
     * 图片标签
     */
    private Integer imgTag;
    
    /**
     * sku颜色的主图
     */
    private String logo;
    
    /**
     * sku名称
     */
    private String skuName;
    
    /**
     * 总库存数
     */
    private Long stockNum;
    
    /**
     * 商品名称
     */
    private String wareTitle;
    
    /**
     * 大件商品固定发货时效
     */
    private String fixedDeliveryTime;
    
    /**
     * 相对发货时效
     */
    private String relativeDeliveryTime;
    
    /**
     * 父id
     */
    private Integer parentId;
    
    /**
     * 修改时间
     */
    private LocalDateTime modified;
    
    /**
     * 创建时间
     */
    private LocalDateTime created;
    
    /**
     * 容量
     */
    private String capacity;
    
    /**
     * 无效字段
     */
    private Integer promiseId;
    
    /**
     * 销售属性模板id
     */
    private String saleAttrTemplateId;
    
    /**
     * 启用停用
     */
    private Integer enable;
    
    /**
     * 标品ID
     */
    private Long standardProductId;
} 