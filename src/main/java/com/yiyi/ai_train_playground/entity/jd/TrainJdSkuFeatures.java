package com.yiyi.ai_train_playground.entity.jd;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东SKU功能表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdSkuFeatures {
    /**
     * 京东商品功能表主键ID
     */
    private Long id;
    
    /**
     * 京东skuID
     */
    private Long skuId;
    
    /**
     * 团队ID，0代表系统
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    // 业务字段
    
    /**
     * 特殊属性value
     */
    private String featureValue;
    
    /**
     * 特殊属性key，人工分配
     */
    private String featureKey;
    
    /**
     * 特殊属性中文含义
     */
    private String featureCn;
} 