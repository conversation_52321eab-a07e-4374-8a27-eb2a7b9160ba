package com.yiyi.ai_train_playground.entity.jd;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SKU属性表
 */
@Data
public class TrainJdSkuProps {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * SKU ID
     */
    private Long skuId;
    
    /**
     * 属性ID
     */
    private String propId;
    
    /**
     * 属性名称
     */
    private String propName;
    
    /**
     * 属性值（如果是数组转换为逗号分隔的字符串）
     */
    private String propValue;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
} 