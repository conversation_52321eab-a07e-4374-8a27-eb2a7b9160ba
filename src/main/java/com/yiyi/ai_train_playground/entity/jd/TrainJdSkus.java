package com.yiyi.ai_train_playground.entity.jd;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东SKU信息表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdSkus {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * SKU ID
     */
    private Long skuId;
    
    /**
     * 商品ID
     */
    private Long wareId;
    
    /**
     * SKU名称
     */
    private String skuName;
    
    /**
     * 商品标题
     */
    private String wareTitle;
    
    /**
     * 条形码
     */
    private String barCode;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 京东价格
     */
    private Double jdPrice;
    
    /**
     * 商品logo图片URL
     */
    private String logo;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 启用状态
     */
    private Integer enable;
    
    /**
     * 库存数量
     */
    private Long stockNum;
    
    /**
     * 京东创建时间
     */
    private LocalDateTime created;
    
    /**
     * 京东修改时间
     */
    private LocalDateTime modified;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 