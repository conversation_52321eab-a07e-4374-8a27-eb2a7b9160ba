package com.yiyi.ai_train_playground.entity.kb;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模拟聊天室窗口明细表
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
public class TrainConvWinchatDtl {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 聊天窗口主表ID，关联train_conv_winchat_main
     */
    private Long convWinchatMainId;

    /**
     * 接待任务ID，关联train_reception_task
     */
    private Long taskId;

    /**
     * 当前窗口会话的在redis中的sessionId，全局唯一
     */
    private String sessionId;

    /**
     * 最原始的聊天记录
     */
    private String f1stRawChatlog;

    /**
     * 聚合过后的系统提示词
     */
    private String s2ndAggSysPrompt;

    /**
     * 重写过后的聊天记录
     */
    private String t3rdRewrite;

    /**
     * 最终的系统提示词
     */
    private String f4thFinalSysPrompt;

    /**
     * train_task_conv_kb_dtl表的主键ID
     */
    private Long convKbId;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}