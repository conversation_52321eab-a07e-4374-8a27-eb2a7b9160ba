package com.yiyi.ai_train_playground.entity.kb;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模拟聊天室窗口聊天记录表
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
public class TrainConvWinchatLog {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 聊天窗口明细表ID，关联train_conv_winchat_dtl
     */
    private Long convDtlId;

    /**
     * 当前窗口会话的在redis中的sessionId，在此表中不唯一
     */
    private String sessionId;

    /**
     * 发送者昵称
     */
    private String sender;

    /**
     * 对话内容
     */
    private String content;

    /**
     * 消息发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 发送者类型：buyer-买家, staff-员工
     */
    private String senderType;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    /**
     * 商品参考答案
     */
    private String prodReferAnswer;

    /**
     * 意图识别评分
     */
    private Integer score;

    /**
     * 意图识别完整JSON结果
     */
    private String intentResult;
}