package com.yiyi.ai_train_playground.entity.kb;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模拟聊天室窗口主表
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
public class TrainConvWinchatMain {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 聊天室ID，关联train_reception_chatroom
     */
    private Long chatroomId;

    /**
     * 员工ID，关联train_staff
     */
    private Long staffId;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}