package com.yiyi.ai_train_playground.entity.shortphrase;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 快捷短语实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
public class ShortcutPhrase {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 快捷短语分组表ID，来自于train_shortcut_phrases_group的主键
     */
    private Long spGroupId;
    
    /**
     * 短语标题
     */
    private String phraseTitle;
    
    /**
     * 短语内容
     */
    private String phraseContent;
    
    /**
     * 短语类型：0-文本，1-图片，2-链接
     */
    private Integer phraseType;
    
    /**
     * 使用次数
     */
    private Integer usageCount;
    
    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean isActive;
    
    /**
     * 排序位置
     */
    private Integer sortOrder;
    
    /**
     * 标签（逗号分隔）
     */
    private String tags;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
