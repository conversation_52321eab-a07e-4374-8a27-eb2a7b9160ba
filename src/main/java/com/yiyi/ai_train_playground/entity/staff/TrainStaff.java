package com.yiyi.ai_train_playground.entity.staff;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 员工表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
public class TrainStaff {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID（关联用户表）
     */
    private Long userId;
    
    /**
     * 用户名（登录名）
     */
    private String username;
    
    /**
     * 密码哈希值
     */
    private String passwordHash;
    
    /**
     * 显示名称
     */
    private String displayName;
    
    /**
     * 是否锁定：0-未锁定，1-已锁定
     */
    private Boolean isLocked;
    
    /**
     * 登录失败次数
     */
    private Integer failedAttempts;
    
    /**
     * 账户锁定时间
     */
    private LocalDateTime lockTime;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}