package com.yiyi.ai_train_playground.entity.staff;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 员工标签表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
public class TrainStaffTag {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 标签名称
     */
    private String tagName;
    
    /**
     * 标签描述
     */
    private String tagDesc;
    
    /**
     * 标签颜色（用于前端显示）
     */
    private String tagColor;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 状态：1-正常，2-停用
     */
    private Integer status;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}