package com.yiyi.ai_train_playground.entity.staff;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 员工标签关联表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Data
public class TrainStaffTagRelation {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 员工ID
     */
    private Long staffId;
    
    /**
     * 标签ID
     */
    private Long tagId;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}