package com.yiyi.ai_train_playground.entity.system;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统配置实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SystemConfig {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * 配置键
     */
    private String configKey;
    
    /**
     * 配置值
     */
    private String configValue;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
