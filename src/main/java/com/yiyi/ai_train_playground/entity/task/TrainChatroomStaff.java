package com.yiyi.ai_train_playground.entity.task;

import java.time.LocalDateTime;

/**
 * 聊天室员工关联表实体类
 */
public class TrainChatroomStaff {
    
    private Long id;
    private Long receChatroomId;
    private Long staffId;
    private Long teamId;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String creator;
    private String updater;
    private Long version;

    public TrainChatroomStaff() {
    }

    public TrainChatroomStaff(Long receChatroomId, Long staffId, Long teamId, String creator) {
        this.receChatroomId = receChatroomId;
        this.staffId = staffId;
        this.teamId = teamId;
        this.creator = creator;
        this.updater = creator;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReceChatroomId() {
        return receChatroomId;
    }

    public void setReceChatroomId(Long receChatroomId) {
        this.receChatroomId = receChatroomId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}