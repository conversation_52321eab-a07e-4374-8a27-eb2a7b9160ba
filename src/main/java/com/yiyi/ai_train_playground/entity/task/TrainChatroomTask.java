package com.yiyi.ai_train_playground.entity.task;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 聊天室任务关联实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Data
public class TrainChatroomTask {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 聊天室ID，关联train_reception_chatroom表
     */
    private Long chatroomId;
    
    /**
     * 任务ID，关联train_reception_task表的主键
     */
    private Long taskId = 0L;
    
    /**
     * 此任务循环次数
     */
    private Integer trainingRecycleCnt = 0;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version = 0L;
}