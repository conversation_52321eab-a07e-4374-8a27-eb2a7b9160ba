package com.yiyi.ai_train_playground.entity.task;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问答随机表
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TrainQaRdm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 全球唯一标识符
     */
    private String uuid;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 实际问题
     */
    private String actualQuestion;

    /**
     * 实际答案
     */
    private String actualAnswer;

    /**
     * 解决方案
     */
    private String resolve;

    /**
     * 问题编号
     */
    private String quesNo;

    /**
     * train_qa_report_dtl表的主键ID
     */
    private Long reportDtlId;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
