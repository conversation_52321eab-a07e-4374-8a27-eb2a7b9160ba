package com.yiyi.ai_train_playground.entity.task;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 模拟聊天室报告明细表
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TrainQaReportDtl {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * train_qa_report_main
     */
    private Long rpMainId;

    /**
     * 接待任务ID，关联train_reception_task
     */
    private Long taskId;

    /**
     * 当前窗口会话的在redis中的sessionId，全局唯一
     */
    private String sessionId;

    /**
     * train_qa_import_main表的主键ID
     */
    private Long qaMainId;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
