package com.yiyi.ai_train_playground.entity.task;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 问答报告主表
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TrainQaReportMain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 聊天室ID，关联train_reception_chatroom
     */
    private Long chatroomId;

    /**
     * 员工ID，关联train_staff
     */
    private Long staffId;

    /**
     * 考试者姓名
     */
    private String examUserRealName;

    /**
     * 考试者编号
     */
    private String examUserNo;

    /**
     * 考试分数
     */
    private BigDecimal examScore;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
