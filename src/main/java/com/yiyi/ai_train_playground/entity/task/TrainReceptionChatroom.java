package com.yiyi.ai_train_playground.entity.task;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 接待聊天室实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Data
public class TrainReceptionChatroom {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 聊天室名称
     */
    private String roomName;
    
    /**
     * 接待皮肤：支持字符串值，如"0"(千牛)、"1"(咚咚)、"2"(抖音)、"3"(默认)等
     */
    private String receptionSkin = "0";
    
    /**
     * 场景模式：0-萌新友好，1-压力考核，2-自定义
     */
    private Integer sceneMode = 0;
    
    /**
     * 快捷短语ID
     */
    private Long quickPhrasesId;
    
    /**
     * 接待时长（分钟）
     */
    private Integer receptionDuration = 30;
    
    /**
     * 读秒：0-不显示，1-显示
     */
    private Boolean timerDisplay = false;

    /**
     * 进线频率下限（分钟）
     */
    private Integer entryFreqMin = 1;

    /**
     * 进线频率上限（分钟）
     */
    private Integer entryFreqMax = 5;

    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version = 0L;
}