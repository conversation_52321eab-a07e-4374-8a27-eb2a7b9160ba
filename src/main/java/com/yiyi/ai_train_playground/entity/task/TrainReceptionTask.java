package com.yiyi.ai_train_playground.entity.task;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 接待任务实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
public class TrainReceptionTask {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法
     */
    private Integer taskMode;

    /**
     * 接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务
     */
    private Integer taskType;

    /**
     * 接待任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDescription;

    /**
     * 剧本ID，来自train_script表的主键，1对1映射
     */
    private Long scriptId;

    /**
     * 接待时长（分钟）
     */
    private Integer receptionDuration;

    /**
     * 顾客提问时间间隔：0-随机，1-固定
     */
    private Integer questionIntervalType;

    /**
     * 提问间隔秒数
     */
    private Integer questionIntervalSeconds;

    /**
     * 训练次数限制：0-关，1-开
     */
    private Boolean trainingLimitEnabled;

    /**
     * 任务标签：0-训练，1-考核，2-面试，3-其他
     */
    private Integer taskPurposeTag;

    /**
     * 打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打
     */
    private Integer judgeType;

    /**
     * 关联的知识库模板ID，关联train_kb_tpl_main表主键
     */
    private Long convKbId = 0L;

    /**
     * 高频知识库ID，关联train_qa_import_main表主键
     */
    private Long qaMainId;
    
    /**
     * 学习状态：un_learn-未学习，learning-学习中，learned-已学习
     */
    private String learningStatus;
    
    /**
     * 待学习的总条数
     */
    private Long amtToBeLearned;
    
    /**
     * 目前已经学习条数
     */
    private Long amtHasLearned;
    
    /**
     * 是否显示解析详情：0-不显示，1-显示
     */
    private Boolean isShowResolve;
    
    /**
     * 是否显示正确答案：0-不显示，1-显示
     */
    private Boolean isShowCorrect;
    
    /**
     * 选取的知识库条数
     */
    private Integer freqAuesCnt;
    
    /**
     * 是否显示考察点：0-不显示，1-显示
     */
    private Boolean isShowInspect;

    /**
     * 客服发送消息倒计时
     */
    private Long srvSendCd;

    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
