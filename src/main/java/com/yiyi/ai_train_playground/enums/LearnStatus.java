package com.yiyi.ai_train_playground.enums;

/**
 * 学习状态枚举
 */
public enum LearnStatus {
    
    /**
     * 文件上传中
     */
    FILE_UPLOADING("file_uploading", "文件上传中"),

    /**
     * 未学习
     */
    UN_LEARN("un_learn", "未学习"),

    /**
     * 学习中
     */
    LEARNING("learning", "学习中"),

    /**
     * 已学习
     */
    LEARNED("learned", "已学习");
    
    private final String code;
    private final String description;
    
    LearnStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * @param code 状态代码
     * @return 对应的枚举值
     */
    public static LearnStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (LearnStatus status : LearnStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的学习状态代码: " + code);
    }
    
    /**
     * 检查代码是否有效
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }
        
        for (LearnStatus status : LearnStatus.values()) {
            if (status.getCode().equals(code)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public String toString() {
        return code;
    }
}
