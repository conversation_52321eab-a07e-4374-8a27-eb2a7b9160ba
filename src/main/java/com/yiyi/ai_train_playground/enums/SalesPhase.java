package com.yiyi.ai_train_playground.enums;

/**
 * 销售阶段枚举
 */
public enum SalesPhase {
    
    /**
     * 售前
     */
    PRE_SALES("pre_sales", "售前"),
    
    /**
     * 销售中
     */
    SALING("saling", "销售中"),
    
    /**
     * 售后
     */
    AFTER_SALE("after_sale", "售后"),
    
    /**
     * 其他
     */
    OTHER("other", "其他");

    private final String code;
    private final String description;

    SalesPhase(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * @param code 代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static SalesPhase fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (SalesPhase phase : SalesPhase.values()) {
            if (phase.getCode().equals(code)) {
                return phase;
            }
        }
        return null;
    }

    /**
     * 验证代码是否有效
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    @Override
    public String toString() {
        return code;
    }
}