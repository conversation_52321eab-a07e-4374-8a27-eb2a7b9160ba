package com.yiyi.ai_train_playground.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发送者类型枚举
 *
 * <AUTHOR> Assistant
 * @since 2025-08-11
 */
@Getter
@AllArgsConstructor
public enum SenderType {

    /**
     * 买家
     */
    BUYER("buyer", "买家"),

    /**
     * 客服
     */
    STAFF("staff", "客服");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static SenderType fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (SenderType type : SenderType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    @Override
    public String toString() {
        return code;
    }
}
