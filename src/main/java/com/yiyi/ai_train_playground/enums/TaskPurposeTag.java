package com.yiyi.ai_train_playground.enums;

/**
 * 任务目的标签枚举
 */
public enum TaskPurposeTag {
    
    /**
     * 训练
     */
    TRAINING(0, "训练"),

    /**
     * 考核
     */
    ASSESSMENT(1, "考核"),

    /**
     * 面试
     */
    INTERVIEW(2, "面试"),

    /**
     * 其他
     */
    OTHER(3, "其他");
    
    private final Integer code;
    private final String description;
    
    TaskPurposeTag(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * @param code 状态代码
     * @return 对应的枚举值
     */
    public static TaskPurposeTag fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (TaskPurposeTag tag : TaskPurposeTag.values()) {
            if (tag.getCode().equals(code)) {
                return tag;
            }
        }
        
        throw new IllegalArgumentException("未知的任务目的标签代码: " + code);
    }
    
    /**
     * 检查代码是否有效
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        if (code == null) {
            return false;
        }
        
        for (TaskPurposeTag tag : TaskPurposeTag.values()) {
            if (tag.getCode().equals(code)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public String toString() {
        return code.toString();
    }
}