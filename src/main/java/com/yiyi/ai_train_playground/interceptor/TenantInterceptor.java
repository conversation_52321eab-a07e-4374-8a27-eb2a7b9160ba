package com.yiyi.ai_train_playground.interceptor;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.interceptor.tenant.MethodMetadata;
import com.yiyi.ai_train_playground.interceptor.tenant.TenantCondition;
import com.yiyi.ai_train_playground.util.ReflectUtil;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import com.yiyi.ai_train_playground.util.SqlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 多租户数据拦截器
 * 基于MyBatis拦截器实现自动的租户数据隔离
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Component
@Slf4j
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class TenantInterceptor implements Interceptor {
    
    /**
     * 方法元数据缓存，提升性能
     */
    private final Map<String, MethodMetadata> methodCache = new ConcurrentHashMap<>();
    
    @Value("${tenant.filter.enabled:true}")
    private boolean tenantFilterEnabled;
    
    @Value("${tenant.filter.cache-method-metadata:true}")
    private boolean cacheMethodMetadata;
    
    @Value("${tenant.filter.log-sql:true}")
    private boolean logSql;
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 检查租户拦截器是否启用
        if (!tenantFilterEnabled) {
            log.debug("租户拦截器已禁用，跳过处理");
            return invocation.proceed();
        }
        
        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
            MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
            BoundSql boundSql = statementHandler.getBoundSql();
            String originalSql = boundSql.getSql();
            
            if (logSql) {
                log.debug("租户拦截器处理SQL: {}", originalSql);
            }
            
            // 基础检查
            if (!SqlUtil.shouldProcess(originalSql)) {
                log.debug("SQL无需处理，跳过: {}", originalSql);
                return invocation.proceed();
            }
            
            // 获取方法元数据
            MethodMetadata metadata = getMethodMetadata(mappedStatement.getId());
            if (metadata == null || !metadata.getTenantFilter().enable()) {
                log.debug("方法无@TenantFilter注解或已禁用，跳过: {}", mappedStatement.getId());
                return invocation.proceed();
            }
            
            // 构建租户条件
            TenantCondition condition = buildTenantCondition(metadata, boundSql.getParameterObject());
            if (condition.isEmpty()) {
                log.debug("租户条件为空，跳过: {}", mappedStatement.getId());
                return invocation.proceed();
            }
            
            // 修改SQL
            String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
            if (!originalSql.equals(modifiedSql)) {
                ReflectUtil.setFieldValue(boundSql, "sql", modifiedSql);
                if (logSql) {
                    log.info("租户拦截器生效 [{}]: {} -> {}", 
                            mappedStatement.getId(), 
                            originalSql.length() > 100 ? originalSql.substring(0, 100) + "..." : originalSql.trim(), 
                            modifiedSql.length() > 100 ? modifiedSql.substring(0, 100) + "..." : modifiedSql.trim());
                }
            } else {
                log.debug("SQL未发生变化，可能因复杂度过高被跳过: {}", mappedStatement.getId());
            }
            
            return invocation.proceed();
            
        } catch (Exception e) {
            log.error("租户拦截器处理异常，继续执行原始SQL", e);
            // 异常情况下不影响业务执行
            return invocation.proceed();
        }
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以在这里读取配置
        log.info("租户拦截器初始化完成");
    }
    
    /**
     * 获取方法元数据（带缓存）
     */
    private MethodMetadata getMethodMetadata(String mappedStatementId) {
        return methodCache.computeIfAbsent(mappedStatementId, this::loadMethodMetadata);
    }
    
    /**
     * 加载方法元数据
     */
    private MethodMetadata loadMethodMetadata(String mappedStatementId) {
        try {
            // 解析 namespace.methodName
            String[] parts = mappedStatementId.split("\\.");
            if (parts.length < 2) {
                log.debug("无效的MappedStatement ID: {}", mappedStatementId);
                return null;
            }
            
            String className = String.join(".", Arrays.copyOf(parts, parts.length - 1));
            String methodName = parts[parts.length - 1];
            
            // 加载类
            Class<?> mapperClass = Class.forName(className);
            
            // 查找方法
            Method targetMethod = null;
            for (Method method : mapperClass.getDeclaredMethods()) {
                if (method.getName().equals(methodName)) {
                    targetMethod = method;
                    break;
                }
            }
            
            if (targetMethod == null) {
                log.debug("方法不存在: {}.{}", className, methodName);
                return null;
            }
            
            // 获取TenantFilter注解
            TenantFilter tenantFilter = targetMethod.getAnnotation(TenantFilter.class);
            if (tenantFilter == null) {
                log.debug("方法无@TenantFilter注解: {}", mappedStatementId);
                return null;
            }
            
            // 解析参数名
            Set<String> paramNames = parseParameterNames(targetMethod);
            
            MethodMetadata metadata = new MethodMetadata(mappedStatementId, tenantFilter, paramNames, targetMethod);
            log.debug("方法元数据加载完成: {}", mappedStatementId);
            return metadata;
            
        } catch (ClassNotFoundException e) {
            log.debug("Mapper类不存在: {}", mappedStatementId);
            return null;
        } catch (Exception e) {
            log.warn("加载方法元数据失败: {}", mappedStatementId, e);
            return null;
        }
    }
    
    /**
     * 解析方法参数名
     */
    private Set<String> parseParameterNames(Method method) {
        Set<String> paramNames = new HashSet<>();
        
        // 这里可以通过@Param注解获取参数名
        // 简化实现，实际项目中可以用更复杂的参数解析逻辑
        java.lang.reflect.Parameter[] parameters = method.getParameters();
        for (java.lang.reflect.Parameter param : parameters) {
            org.apache.ibatis.annotations.Param paramAnnotation = param.getAnnotation(org.apache.ibatis.annotations.Param.class);
            if (paramAnnotation != null) {
                paramNames.add(paramAnnotation.value());
            }
        }
        
        return paramNames;
    }
    
    /**
     * 构建租户条件
     */
    private TenantCondition buildTenantCondition(MethodMetadata metadata, Object parameterObject) {
        TenantCondition condition = new TenantCondition();
        TenantFilter filter = metadata.getTenantFilter();
        
        try {
            // 处理 team_id
            if (filter.includeTeamId()) {
                Long teamId = getTeamId(filter, parameterObject, metadata);
                condition.setTeamId(teamId);
            }
            
            // 处理 creator
            if (filter.includeCreator()) {
                String creator = getCreator(filter, parameterObject, metadata);
                condition.setCreator(creator);
            }
            
        } catch (Exception e) {
            log.warn("构建租户条件失败: {}", metadata.getMethodId(), e);
        }
        
        return condition;
    }
    
    /**
     * 获取TeamId
     */
    private Long getTeamId(TenantFilter filter, Object parameterObject, MethodMetadata metadata) {
        try {
            switch (filter.teamIdSource()) {
                case SECURITY_CONTEXT:
                    return SecurityUtil.getCurrentTeamId();
                    
                case PARAM:
                    return getValueFromParam(parameterObject, filter.teamIdParamName(), metadata, Long.class);
                    
                case ENTITY:
                    return getValueFromEntity(parameterObject, "teamId", Long.class);
                    
                default:
                    return SecurityUtil.getCurrentTeamId();
            }
        } catch (Exception e) {
            log.warn("获取TeamId失败，使用SecurityContext: {}", metadata.getMethodId(), e);
            return SecurityUtil.getCurrentTeamId();
        }
    }
    
    /**
     * 获取Creator
     */
    private String getCreator(TenantFilter filter, Object parameterObject, MethodMetadata metadata) {
        try {
            switch (filter.creatorSource()) {
                case SECURITY_CONTEXT:
                    return SecurityUtil.getCurrentUsername();

                case PARAM:
                    return getValueFromParam(parameterObject, filter.creatorParamName(), metadata, String.class);

                case ENTITY:
                    return getValueFromEntity(parameterObject, filter.creatorEntityField(), String.class);

                default:
                    return SecurityUtil.getCurrentUsername();
            }
        } catch (Exception e) {
            log.warn("获取Creator失败，使用SecurityContext: {}", metadata.getMethodId(), e);
            return SecurityUtil.getCurrentUsername();
        }
    }
    
    /**
     * 从方法参数获取值
     */
    @SuppressWarnings("unchecked")
    private <T> T getValueFromParam(Object parameterObject, String paramName, MethodMetadata metadata, Class<T> targetType) {
        if (parameterObject == null) {
            return null;
        }
        
        if (parameterObject instanceof Map) {
            Map<String, Object> paramMap = (Map<String, Object>) parameterObject;
            Object value = paramMap.get(paramName);
            return ReflectUtil.castValue(value, targetType);
        }
        
        // 处理单个参数的情况
        if (metadata.getParamNames().size() == 1 && metadata.getParamNames().contains(paramName)) {
            return ReflectUtil.castValue(parameterObject, targetType);
        }
        
        return null;
    }
    
    /**
     * 从实体对象获取值
     */
    private <T> T getValueFromEntity(Object parameterObject, String fieldName, Class<T> targetType) {
        if (parameterObject == null) {
            return null;
        }
        
        try {
            if (parameterObject instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramMap = (Map<String, Object>) parameterObject;
                // 尝试从Map中的第一个实体对象获取
                for (Object value : paramMap.values()) {
                    if (value != null && !ReflectUtil.isPrimitiveType(value.getClass())) {
                        T result = ReflectUtil.getFieldValue(value, fieldName, targetType);
                        if (result != null) {
                            return result;
                        }
                    }
                }
            } else if (!ReflectUtil.isPrimitiveType(parameterObject.getClass())) {
                return ReflectUtil.getFieldValue(parameterObject, fieldName, targetType);
            }
        } catch (Exception e) {
            log.warn("获取实体字段值失败: {}.{}", parameterObject.getClass().getSimpleName(), fieldName, e);
        }
        
        return null;
    }
}