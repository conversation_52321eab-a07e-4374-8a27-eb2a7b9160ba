package com.yiyi.ai_train_playground.interceptor.tenant;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import lombok.Data;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * 方法元数据缓存类
 * 用于缓存Mapper方法的注解信息和参数信息，提升性能
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Data
public class MethodMetadata {
    
    /**
     * TenantFilter注解信息
     */
    private TenantFilter tenantFilter;
    
    /**
     * 方法参数名集合
     */
    private Set<String> paramNames;
    
    /**
     * 反射方法对象
     */
    private Method method;
    
    /**
     * 方法的完整ID（namespace.methodName）
     */
    private String methodId;
    
    /**
     * 构造函数
     */
    public MethodMetadata(String methodId, TenantFilter tenantFilter, Set<String> paramNames, Method method) {
        this.methodId = methodId;
        this.tenantFilter = tenantFilter;
        this.paramNames = paramNames;
        this.method = method;
    }
}