package com.yiyi.ai_train_playground.interceptor.tenant;

import lombok.Data;

/**
 * 租户条件封装类
 * 用于存储从不同来源获取的租户隔离条件
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Data
public class TenantCondition {
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 创建人用户名
     */
    private String creator;
    
    /**
     * 判断条件是否为空
     * @return true表示没有任何过滤条件
     */
    public boolean isEmpty() {
        return teamId == null && creator == null;
    }
    
    /**
     * 判断是否有team_id条件
     */
    public boolean hasTeamId() {
        return teamId != null;
    }
    
    /**
     * 判断是否有creator条件
     */
    public boolean hasCreator() {
        return creator != null;
    }
}