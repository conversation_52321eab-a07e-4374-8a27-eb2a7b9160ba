package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import org.apache.ibatis.annotations.*;

/**
 * 团队店铺表Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainTeamShopsMapper {
    
    /**
     * 根据团队ID和店铺类型查询店铺信息
     */
    @Select("SELECT * FROM train_team_shops WHERE team_id = #{teamId} AND shop_type = #{shopType}")
    TrainTeamShops findByTeamIdAndShopType(@Param("teamId") Long teamId, @Param("shopType") Integer shopType);
    
    /**
     * 更新店铺授权状态和同步状态
     */
    @Update("UPDATE train_team_shops SET is_authorize = #{isAuthorize}, is_sync_complete = #{isSyncComplete}, " +
            "updater = #{updater}, update_time = NOW(), version = version + 1 " +
            "WHERE team_id = #{teamId} AND shop_type = #{shopType}")
    int updateAuthorizationStatus(@Param("teamId") Long teamId, 
                                 @Param("shopType") Integer shopType,
                                 @Param("isAuthorize") Boolean isAuthorize,
                                 @Param("isSyncComplete") Integer isSyncComplete,
                                 @Param("updater") String updater);
    
    /**
     * 插入新的店铺记录
     */
    @Insert("INSERT INTO train_team_shops (team_id, shop_id, shop_type, creator, updater, create_time, update_time, version, is_authorize, is_sync_complete) " +
            "VALUES (#{teamId}, #{shopId}, #{shopType}, #{creator}, #{updater}, NOW(), NOW(), 0, #{isAuthorize}, #{isSyncComplete})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TrainTeamShops trainTeamShops);

    /**
     * 根据店铺ID查询店铺信息
     *
     * @param shopId 店铺ID
     * @return 店铺信息（如果有多条记录，返回第一条）
     */
    @Select("SELECT * FROM train_team_shops WHERE shop_id = #{shopId} LIMIT 1")
    TrainTeamShops findByShopId(@Param("shopId") Long shopId);

    /**
     * 根据店铺ID更新店铺授权状态和同步状态
     *
     * @param shopId 店铺ID
     * @param isAuthorize 是否授权
     * @param isSyncComplete 同步完成状态
     * @param updater 更新人
     * @return 影响的行数
     */
    @Update("UPDATE train_team_shops SET is_authorize = #{isAuthorize}, is_sync_complete = #{isSyncComplete}, " +
            "updater = #{updater}, update_time = NOW(), version = version + 1 " +
            "WHERE shop_id = #{shopId}")
    int updateByShopId(@Param("shopId") Long shopId,
                      @Param("isAuthorize") Boolean isAuthorize,
                      @Param("isSyncComplete") Integer isSyncComplete,
                      @Param("updater") String updater);

    /**
     * 根据店铺ID更新团队店铺信息（完整实体更新）
     *
     * @param trainTeamShops 团队店铺信息
     * @return 影响的行数
     */
    int updateEntityByShopId(TrainTeamShops trainTeamShops);
}