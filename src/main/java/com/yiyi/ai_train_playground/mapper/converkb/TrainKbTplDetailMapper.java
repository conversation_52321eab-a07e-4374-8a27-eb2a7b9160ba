package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailResponse;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会话知识库模板明细表Mapper接口
 */
@Mapper
public interface TrainKbTplDetailMapper {
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 模板明细信息
     */
    TrainKbTplDetail selectById(Long id);
    
    /**
     * 根据模板ID查询明细列表
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 明细列表
     */
    List<TrainKbTplDetail> selectByTplId(@Param("tplId") Long tplId, @Param("teamId") Long teamId);
    
    /**
     * 插入记录
     * @param record 模板明细信息
     * @return 影响行数
     */
    int insert(TrainKbTplDetail record);
    
    /**
     * 批量插入
     * @param records 明细记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<TrainKbTplDetail> records);
    
    /**
     * 根据ID更新
     * @param record 模板明细信息
     * @return 影响行数
     */
    int updateById(TrainKbTplDetail record);
    
    /**
     * 根据ID删除
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据模板ID删除所有明细
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByTplId(@Param("tplId") Long tplId, @Param("teamId") Long teamId);
    
    /**
     * 统计模板的明细数量
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 明细数量
     */
    int countByTplId(@Param("tplId") Long tplId, @Param("teamId") Long teamId);
    
    /**
     * 分页查询模板明细列表（带枚举描述）
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @param request 分页请求
     * @param offset 偏移量
     * @return 明细项列表
     */
    List<KbTplDetailResponse.KbTplDetailItem> selectPageByTplId(@Param("tplId") Long tplId, 
                                                               @Param("teamId") Long teamId,
                                                               @Param("request") KbTplDetailRequest request,
                                                               @Param("offset") int offset);
    
    /**
     * 查询明细总数（带筛选条件）
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @param request 请求参数
     * @return 总数
     */
    Long selectPageCountByTplId(@Param("tplId") Long tplId,
                               @Param("teamId") Long teamId,
                               @Param("request") KbTplDetailRequest request);

    /**
     * 根据任务ID随机获取知识库明细内容
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 随机的知识库明细内容
     */
    String selectRandomContentByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);
}