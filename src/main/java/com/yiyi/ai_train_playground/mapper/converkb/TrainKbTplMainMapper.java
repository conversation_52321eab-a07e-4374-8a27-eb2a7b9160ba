package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain;
import com.yiyi.ai_train_playground.dto.converkb.KbTplQueryRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplListResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会话知识库模板主表Mapper接口
 */
@Mapper
public interface TrainKbTplMainMapper {
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 模板主表信息
     */
    TrainKbTplMain selectById(Long id);
    
    /**
     * 根据ID和团队ID查询
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 模板主表信息
     */
    TrainKbTplMain selectByIdAndTeamId(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 插入记录并返回主键ID
     * @param record 模板主表信息
     * @return 影响行数
     */
    int insert(TrainKbTplMain record);
    
    /**
     * 根据ID更新
     * @param record 模板主表信息
     * @return 影响行数
     */
    int updateById(TrainKbTplMain record);
    
    /**
     * 根据ID删除
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 分页查询列表
     * @param request 查询请求
     * @param teamId 团队ID
     * @param offset 偏移量
     * @return 模板列表
     */
    List<KbTplListResponse> selectPageList(@Param("request") KbTplQueryRequest request, 
                                          @Param("teamId") Long teamId, 
                                          @Param("offset") Integer offset);
    
    /**
     * 查询总数
     * @param request 查询请求
     * @param teamId 团队ID
     * @return 总数
     */
    Long selectPageCount(@Param("request") KbTplQueryRequest request, @Param("teamId") Long teamId);
    
    /**
     * 检查名称是否存在
     * @param name 模板名称
     * @param teamId 团队ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在数量
     */
    int checkNameExists(@Param("name") String name, 
                       @Param("teamId") Long teamId, 
                       @Param("excludeId") Long excludeId);
    
    /**
     * 更新token数量
     * @param id 模板ID
     * @param tokens 新的token数量
     * @param teamId 团队ID
     * @return 影响行数
     */
    int updateTokens(@Param("id") Long id, @Param("tokens") Long tokens, @Param("teamId") Long teamId);

    /**
     * 更新模板的学习状态
     * @param id 模板ID
     * @param learnStatus 新的学习状态
     * @param teamId 团队ID
     * @return 影响行数
     */
    int updateLearnStatus(@Param("id") Long id, @Param("learnStatus") String learnStatus, @Param("teamId") Long teamId);
}