package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会话知识库模板预处理表Mapper接口
 */
@Mapper
public interface TrainKbTplPreMapper {
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 预处理记录
     */
    TrainKbTplPre selectById(Long id);
    
    /**
     * 根据ID和团队ID查询
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 预处理记录
     */
    TrainKbTplPre selectByIdAndTeamId(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据模板ID查询所有预处理记录
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 预处理记录列表
     */
    List<TrainKbTplPre> selectByTplId(@Param("tplId") Long tplId, @Param("teamId") Long teamId);
    
    /**
     * 根据模板ID和学习状态查询
     * @param tplId 模板ID
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 预处理记录列表
     */
    List<TrainKbTplPre> selectByTplIdAndLearnStatus(@Param("tplId") Long tplId, 
                                                   @Param("learnStatus") String learnStatus, 
                                                   @Param("teamId") Long teamId);
    
    /**
     * 根据学习状态查询
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 预处理记录列表
     */
    List<TrainKbTplPre> selectByLearnStatus(@Param("learnStatus") String learnStatus, @Param("teamId") Long teamId);
    
    /**
     * 插入记录并返回主键ID
     * @param record 预处理记录
     * @return 影响行数
     */
    int insert(TrainKbTplPre record);
    
    /**
     * 批量插入记录
     * @param records 预处理记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<TrainKbTplPre> records);
    
    /**
     * 根据ID更新
     * @param record 预处理记录
     * @return 影响行数
     */
    int updateById(TrainKbTplPre record);
    
    /**
     * 批量更新学习状态
     * @param tplId 模板ID
     * @param oldLearnStatus 原学习状态
     * @param newLearnStatus 新学习状态
     * @param teamId 团队ID
     * @param updater 更新人
     * @return 影响行数
     */
    int updateLearnStatusByTplId(@Param("tplId") Long tplId,
                                @Param("oldLearnStatus") String oldLearnStatus,
                                @Param("newLearnStatus") String newLearnStatus,
                                @Param("teamId") Long teamId,
                                @Param("updater") String updater);
    
    /**
     * 根据ID删除
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据模板ID删除所有预处理记录
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByTplId(@Param("tplId") Long tplId, @Param("teamId") Long teamId);
    
    /**
     * 根据模板ID和学习状态删除
     * @param tplId 模板ID
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByTplIdAndLearnStatus(@Param("tplId") Long tplId, 
                                   @Param("learnStatus") String learnStatus, 
                                   @Param("teamId") Long teamId);
    
    /**
     * 统计模板的预处理记录数量
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 记录数量
     */
    Long countByTplId(@Param("tplId") Long tplId, @Param("teamId") Long teamId);
    
    /**
     * 统计模板指定学习状态的记录数量
     * @param tplId 模板ID
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 记录数量
     */
    Long countByTplIdAndLearnStatus(@Param("tplId") Long tplId,
                                   @Param("learnStatus") String learnStatus,
                                   @Param("teamId") Long teamId);

    /**
     * 分页查询指定学习状态的记录
     * @param learnStatus 学习状态
     * @param limit 限制数量
     * @return 预处理记录列表
     */
    List<TrainKbTplPre> selectByLearnStatusWithLimit(@Param("learnStatus") String learnStatus,
                                                    @Param("limit") Integer limit);
}
