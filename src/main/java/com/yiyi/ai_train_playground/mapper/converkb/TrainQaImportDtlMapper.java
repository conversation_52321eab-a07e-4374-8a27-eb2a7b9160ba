package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问答导入详情表Mapper
 */
@Mapper
public interface TrainQaImportDtlMapper {

    /**
     * 插入问答详情记录
     * @param qaImportDtl 问答详情记录
     * @return 影响行数
     */
    int insert(TrainQaImportDtl qaImportDtl);

    /**
     * 根据ID删除详情记录
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID批量删除详情记录
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据主表ID删除详情记录
     * @param qaMainId 主表ID
     * @return 影响行数
     */
    int deleteByQaMainId(@Param("qaMainId") Long qaMainId);

    /**
     * 更新详情记录
     * @param qaImportDtl 详情记录
     * @return 影响行数
     */
    int updateById(TrainQaImportDtl qaImportDtl);

    /**
     * 根据ID查询详情记录
     * @param id 主键ID
     * @return 详情记录
     */
    TrainQaImportDtl selectById(@Param("id") Long id);

    /**
     * 根据主表ID查询详情记录列表
     * @param qaMainId 主表ID
     * @param question 问题关键词（可选，用于模糊查询）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 详情记录列表
     */
    List<TrainQaImportDtl> selectByQaMainId(@Param("qaMainId") Long qaMainId,
                                            @Param("question") String question,
                                            @Param("offset") Integer offset,
                                            @Param("limit") Integer limit);

    /**
     * 根据团队ID查询详情记录列表
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 详情记录列表
     */
    List<TrainQaImportDtl> selectByTeamId(@Param("teamId") Long teamId,
                                          @Param("offset") Integer offset,
                                          @Param("limit") Integer limit);

    /**
     * 根据主表ID统计详情记录数量
     * @param qaMainId 主表ID
     * @param question 问题关键词（可选，用于模糊查询）
     * @return 记录数量
     */
    int countByQaMainId(@Param("qaMainId") Long qaMainId, @Param("question") String question);

    /**
     * 根据团队ID统计详情记录数量
     * @param teamId 团队ID
     * @return 记录数量
     */
    int countByTeamId(@Param("teamId") Long teamId);

    /**
     * 检查问题是否已存在
     * @param question 问题
     * @param teamId 团队ID
     * @return 存在的记录数
     */
    @TenantFilter(includeTeamId = false,includeCreator = true)
    int existsByQuestion(@Param("question") String question, @Param("teamId") Long teamId);

    /**
     * 根据问题模糊查询详情记录
     * @param question 问题关键词
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 详情记录列表
     */
    List<TrainQaImportDtl> selectByQuestionLike(@Param("question") String question,
                                                @Param("teamId") Long teamId,
                                                @Param("offset") Integer offset,
                                                @Param("limit") Integer limit);

    /**
     * 根据问题模糊查询统计数量
     * @param question 问题关键词
     * @param teamId 团队ID
     * @return 记录数量
     */
    int countByQuestionLike(@Param("question") String question, @Param("teamId") Long teamId);

    /**
     * 根据主表ID查询所有明细（只返回question和answer字段，不分页）
     * @param qaMainId 主表ID
     * @return 简单问答记录列表
     */
    List<QaSimpleDto> selectAllByMainId(@Param("qaMainId") Long qaMainId);

    /**
     * 根据主表ID随机获取指定数量的明细（只返回question和answer字段）
     * @param qaMainId 主表ID
     * @param count 需要获取的数量
     * @return 随机获取的问答列表
     */
    List<QaSimpleDto> selectRandomByMainId(@Param("qaMainId") Long qaMainId, @Param("count") Integer count);
}
