package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问答导入主表Mapper
 */
@Mapper
public interface TrainQaImportMainMapper {

    /**
     * 插入主表记录
     * @param qaImportMain 主表记录
     * @return 影响行数
     */
    int insert(TrainQaImportMain qaImportMain);

    /**
     * 根据ID删除主表记录
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID批量删除主表记录
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 更新主表记录
     * @param qaImportMain 主表记录
     * @return 影响行数
     */
    int updateById(TrainQaImportMain qaImportMain);

    /**
     * 根据ID查询主表记录
     * @param id 主键ID
     * @return 主表记录
     */
    TrainQaImportMain selectById(@Param("id") Long id);

    /**
     * 根据团队ID查询主表记录列表
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 主表记录列表
     */
    List<TrainQaImportMain> selectByTeamId(@Param("teamId") Long teamId,
                                           @Param("offset") Integer offset,
                                           @Param("limit") Integer limit);

    /**
     * 根据团队ID和创建人查询主表记录列表
     * @param teamId 团队ID
     * @param creator 创建人
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 主表记录列表
     */
    List<TrainQaImportMain> selectByTeamIdAndCreator(@Param("teamId") Long teamId,
                                                     @Param("creator") String creator,
                                                     @Param("offset") Integer offset,
                                                     @Param("limit") Integer limit);

    /**
     * 根据团队ID统计主表记录数量
     * @param teamId 团队ID
     * @return 记录数量
     */
    int countByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据团队ID和创建人统计主表记录数量
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 记录数量
     */
    int countByTeamIdAndCreator(@Param("teamId") Long teamId, @Param("creator") String creator);

    /**
     * 根据条件查询主表记录列表
     * @param teamId 团队ID
     * @param creator 创建人
     * @param qaImName 知识库名称（可选，模糊搜索）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 主表记录列表
     */
    List<TrainQaImportMain> selectByConditions(@Param("teamId") Long teamId,
                                               @Param("creator") String creator,
                                               @Param("qaImName") String qaImName,
                                               @Param("offset") Integer offset,
                                               @Param("limit") Integer limit);

    /**
     * 根据条件统计主表记录数量
     * @param teamId 团队ID
     * @param creator 创建人
     * @param qaImName 知识库名称（可选，模糊搜索）
     * @return 记录数量
     */
    int countByConditions(@Param("teamId") Long teamId, 
                         @Param("creator") String creator,
                         @Param("qaImName") String qaImName);

    /**
     * 更新知识库名称和描述
     * @param id 主键ID
     * @param qaImName 知识库名称
     * @param qaImDesc 知识库描述
     * @param updater 更新人
     * @return 影响行数
     */
    int updateMain(@Param("id") Long id,
                   @Param("qaImName") String qaImName,
                   @Param("qaImDesc") String qaImDesc,
                   @Param("updater") String updater);

    /**
     * 根据批次号查询主表记录
     * @param batchNo 批次号
     * @param teamId 团队ID
     * @return 主表记录
     */
    TrainQaImportMain selectByBatchNo(@Param("batchNo") String batchNo, @Param("teamId") Long teamId);
}
