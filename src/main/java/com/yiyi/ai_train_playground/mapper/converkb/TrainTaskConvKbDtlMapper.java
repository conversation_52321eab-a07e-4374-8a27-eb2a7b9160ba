package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl;
import com.yiyi.ai_train_playground.dto.task.TaskLearningDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务会话明细表Mapper接口
 */
@Mapper
public interface TrainTaskConvKbDtlMapper {
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 任务会话明细信息
     */
    TrainTaskConvKbDtl selectById(Long id);
    
    /**
     * 根据任务ID查询明细列表
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 明细列表
     */
    List<TrainTaskConvKbDtl> selectByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);

    /**
     * 根据任务ID随机查询一个明细
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 随机的明细
     */
    TrainTaskConvKbDtl selectRandomByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);
    
    /**
     * 根据知识库明细ID查询
     * @param kbDtlId 知识库明细ID
     * @param teamId 团队ID
     * @return 任务会话明细列表
     */
    List<TrainTaskConvKbDtl> selectByKbDtlId(@Param("kbDtlId") Long kbDtlId, @Param("teamId") Long teamId);
    
    /**
     * 根据学习状态查询
     * @param learningStatus 学习状态
     * @param teamId 团队ID
     * @return 任务会话明细列表
     */
    List<TrainTaskConvKbDtl> selectByLearningStatus(@Param("learningStatus") String learningStatus, @Param("teamId") Long teamId);
    
    /**
     * 插入记录
     * @param record 任务会话明细信息
     * @return 影响行数
     */
    int insert(TrainTaskConvKbDtl record);
    
    /**
     * 批量插入
     * @param records 明细记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<TrainTaskConvKbDtl> records);
    
    /**
     * 根据ID更新
     * @param record 任务会话明细信息
     * @return 影响行数
     */
    int updateById(TrainTaskConvKbDtl record);
    
    /**
     * 更新学习状态
     * @param id 主键ID
     * @param learningStatus 学习状态
     * @param updater 更新人
     * @param teamId 团队ID
     * @return 影响行数
     */
    int updateLearningStatus(@Param("id") Long id, 
                           @Param("learningStatus") String learningStatus, 
                           @Param("updater") String updater,
                           @Param("teamId") Long teamId);
    
    /**
     * 更新初始聊天记录
     * @param id 主键ID
     * @param f1stRawChatlog 初始聊天记录
     * @param updater 更新人
     * @param teamId 团队ID
     * @return 影响行数
     */
    int updateF1stRawChatlog(@Param("id") Long id, 
                            @Param("f1stRawChatlog") String f1stRawChatlog,
                            @Param("updater") String updater,
                            @Param("teamId") Long teamId);
    
    /**
     * 更新最终聊天记录
     * @param id 主键ID
     * @param finalChatLog 最终聊天记录
     * @param updater 更新人
     * @param teamId 团队ID
     * @return 影响行数
     */
    int updateFinalChatLog(@Param("id") Long id, 
                          @Param("finalChatLog") String finalChatLog,
                          @Param("updater") String updater,
                          @Param("teamId") Long teamId);
    
    /**
     * 根据ID删除
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据任务ID删除所有明细
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);
    
    /**
     * 统计任务的明细数量
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 明细数量
     */
    int countByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);
    
    /**
     * 统计指定学习状态的数量
     * @param taskId 任务ID
     * @param learningStatus 学习状态
     * @param teamId 团队ID
     * @return 数量
     */
    int countByTaskIdAndStatus(@Param("taskId") Long taskId,
                              @Param("learningStatus") String learningStatus,
                              @Param("teamId") Long teamId);

    /**
     * 查询未学习的任务明细（分页）
     * 联合查询train_reception_task和train_task_conv_kb_dtl表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 未学习的任务明细列表
     */
    List<TaskLearningDTO> selectUnlearnedTaskDetails(@Param("offset") Integer offset,
                                                    @Param("limit") Integer limit);

    /**
     * 查询指定任务下未学习的明细
     * 联合查询train_reception_task和train_task_conv_kb_dtl表
     * @param taskId 任务ID
     * @return 未学习的任务明细列表
     */
    List<TaskLearningDTO> selectUnlearnedTaskDetailsByTaskId(@Param("taskId") Long taskId);

    /**
     * 更新学习结果（使用乐观锁）
     * @param id 明细ID
     * @param f2ndAggSysPrompt 第二步聚合系统提示词
     * @param finalChatLog 最终聊天记录
     * @param learningStatus 学习状态
     * @param updater 更新人
     * @param version 版本号
     * @return 影响行数
     */
    int updateLearningResult(@Param("id") Long id,
                           @Param("f2ndAggSysPrompt") String f2ndAggSysPrompt,
                           @Param("finalChatLog") String finalChatLog,
                           @Param("learningStatus") String learningStatus,
                           @Param("updater") String updater,
                           @Param("version") Long version);

    /**
     * 更新学习结果（使用悲观锁）
     * @param id 明细ID
     * @param f2ndAggSysPrompt 第二步聚合系统提示词
     * @param finalChatLog 最终聊天记录
     * @param learningStatus 学习状态
     * @param updater 更新人
     * @return 影响行数
     */
    int updateLearningResultWithPessimisticLock(@Param("id") Long id,
                                              @Param("f2ndAggSysPrompt") String f2ndAggSysPrompt,
                                              @Param("finalChatLog") String finalChatLog,
                                              @Param("learningStatus") String learningStatus,
                                              @Param("updater") String updater);
}