package com.yiyi.ai_train_playground.mapper.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProductsMcp;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 京东商品多种类属性表Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdProductsMcpMapper {
    
    /**
     * 根据商品ID删除多种类属性信息
     * 
     * @param tjpId 商品ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    @Delete("DELETE FROM train_jd_products_mcp WHERE tjp_id = #{tjpId} AND team_id = #{teamId}")
    int deleteByTjpIdAndTeamId(@Param("tjpId") Long tjpId, @Param("teamId") Long teamId);
    
    /**
     * 批量插入商品多种类属性信息
     * 
     * @param mcpList 多种类属性信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdProductsMcp> mcpList);
    
    /**
     * 根据商品ID查询多种类属性信息
     * 
     * @param tjpId 商品ID
     * @param teamId 团队ID
     * @return 多种类属性信息列表
     */
    @Select("SELECT * FROM train_jd_products_mcp WHERE tjp_id = #{tjpId} AND team_id = #{teamId}")
    List<TrainJdProductsMcp> findByTjpIdAndTeamId(@Param("tjpId") Long tjpId, @Param("teamId") Long teamId);
} 