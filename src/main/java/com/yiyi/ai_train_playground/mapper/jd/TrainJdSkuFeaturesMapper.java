package com.yiyi.ai_train_playground.mapper.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainJdSkuFeatures;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 京东SKU特色服务表 Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdSkuFeaturesMapper {
    
    /**
     * 批量插入SKU特色服务
     * 
     * @param featuresList 特色服务列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdSkuFeatures> featuresList);
    
    /**
     * 根据skuId物理删除特色服务
     * 
     * @param skuId SKU ID
     * @return 影响行数
     */
    int deleteBySkuId(@Param("skuId") Long skuId);
    
    /**
     * 根据skuId查询特色服务列表
     * 
     * @param skuId SKU ID
     * @return 特色服务列表
     */
    List<TrainJdSkuFeatures> findBySkuId(@Param("skuId") Long skuId);
} 