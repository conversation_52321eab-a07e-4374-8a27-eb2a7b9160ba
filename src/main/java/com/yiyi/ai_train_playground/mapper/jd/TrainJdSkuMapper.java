package com.yiyi.ai_train_playground.mapper.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainJdSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 京东SKU表 Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdSkuMapper {
    
    /**
     * 根据skuId查询SKU信息
     * 
     * @param skuId SKU ID
     * @return SKU信息
     */
    TrainJdSku findBySkuId(@Param("skuId") Long skuId);
    
    /**
     * 插入SKU信息
     * 
     * @param trainJdSku SKU信息
     * @return 影响行数
     */
    int insert(TrainJdSku trainJdSku);
    
    /**
     * 根据skuId更新SKU信息
     * 
     * @param trainJdSku SKU信息
     * @return 影响行数
     */
    int updateBySkuId(TrainJdSku trainJdSku);
    
    /**
     * 检查SKU的modified时间是否比本地新
     * 
     * @param skuId SKU ID
     * @param remoteModified 远程修改时间
     * @return 如果远程时间更新返回true，否则返回false
     */
    boolean isRemoteNewer(@Param("skuId") Long skuId, @Param("remoteModified") LocalDateTime remoteModified);
} 