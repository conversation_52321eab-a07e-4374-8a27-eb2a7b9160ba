package com.yiyi.ai_train_playground.mapper.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainJdSkuProps;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SKU属性表Mapper接口
 */
@Mapper
public interface TrainJdSkuPropsMapper {
    
    /**
     * 批量插入SKU属性
     *
     * @param skuPropsList SKU属性列表
     * @return 插入行数
     */
    int batchInsert(@Param("list") List<TrainJdSkuProps> skuPropsList);
    
    /**
     * 根据SKU ID删除相关属性
     *
     * @param skuId SKU ID
     * @return 删除行数
     */
    int deleteBySkuId(@Param("skuId") Long skuId);
    
    /**
     * 根据SKU ID查询属性列表
     *
     * @param skuId SKU ID
     * @return 属性列表
     */
    List<TrainJdSkuProps> findBySkuId(@Param("skuId") Long skuId);
} 