package com.yiyi.ai_train_playground.mapper.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainJdSkuSaleAttrs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 京东SKU销售属性表 Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdSkuSaleAttrsMapper {
    
    /**
     * 批量插入SKU销售属性
     * 
     * @param saleAttrsList 销售属性列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdSkuSaleAttrs> saleAttrsList);
    
    /**
     * 根据skuId物理删除销售属性
     * 
     * @param skuId SKU ID
     * @return 影响行数
     */
    int deleteBySkuId(@Param("skuId") Long skuId);
    
    /**
     * 根据skuId查询销售属性列表
     * 
     * @param skuId SKU ID
     * @return 销售属性列表
     */
    List<TrainJdSkuSaleAttrs> findBySkuId(@Param("skuId") Long skuId);
} 