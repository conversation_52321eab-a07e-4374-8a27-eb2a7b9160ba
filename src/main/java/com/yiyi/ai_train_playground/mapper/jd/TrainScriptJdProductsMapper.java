package com.yiyi.ai_train_playground.mapper.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 剧本京东商品关联Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2025-01-23
 */
@Mapper
public interface TrainScriptJdProductsMapper {

    /**
     * 插入剧本京东商品关联记录
     *
     * @param scriptJdProducts 剧本京东商品关联信息
     * @return 影响行数
     */
    int insert(TrainScriptJdProducts scriptJdProducts);

    /**
     * 批量插入剧本京东商品关联记录
     *
     * @param scriptJdProductsList 剧本京东商品关联信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainScriptJdProducts> scriptJdProductsList);

    /**
     * 根据剧本ID删除剧本京东商品关联
     *
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByScriptIdAndTeamId(@Param("scriptId") Long scriptId, @Param("teamId") Long teamId);

    /**
     * 根据剧本ID查询剧本京东商品关联
     *
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 剧本京东商品关联列表
     */
    List<TrainScriptJdProducts> selectByScriptIdAndTeamId(@Param("scriptId") Long scriptId, @Param("teamId") Long teamId);

    /**
     * 根据剧本ID和团队ID查询关联的京东SKU ID列表
     *
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 京东SKU ID列表
     */
    List<Long> selectJdSkuIdsByScriptIdAndTeamId(@Param("scriptId") Long scriptId, @Param("teamId") Long teamId);

    /**
     * 根据ID查询剧本京东商品关联
     *
     * @param id 主键ID
     * @return 剧本京东商品关联信息
     */
    TrainScriptJdProducts selectById(@Param("id") Long id);

    /**
     * 根据ID更新剧本京东商品关联
     *
     * @param scriptJdProducts 剧本京东商品关联信息
     * @return 影响行数
     */
    int updateById(TrainScriptJdProducts scriptJdProducts);

    /**
     * 根据ID删除剧本京东商品关联
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据京东SKU ID查询关联的剧本列表
     *
     * @param trJdSkuId 京东SKU ID
     * @param teamId 团队ID
     * @return 剧本京东商品关联列表
     */
    List<TrainScriptJdProducts> selectByJdSkuIdAndTeamId(@Param("trJdSkuId") Long trJdSkuId, @Param("teamId") Long teamId);

    /**
     * 批量删除剧本京东商品关联
     *
     * @param ids ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);
}
