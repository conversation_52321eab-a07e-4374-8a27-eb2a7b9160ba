package com.yiyi.ai_train_playground.mapper.kb;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatDtl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模拟聊天室窗口明细表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Mapper
public interface TrainConvWinchatDtlMapper {

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int insert(TrainConvWinchatDtl record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKey(TrainConvWinchatDtl record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    TrainConvWinchatDtl selectByPrimaryKey(Long id);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据sessionId查询记录
     *
     * @param sessionId 会话ID
     * @return 记录
     */
    TrainConvWinchatDtl selectBySessionId(String sessionId);

    /**
     * 根据聊天窗口主表ID查询记录列表
     *
     * @param convWinchatMainId 聊天窗口主表ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatDtl> selectByConvWinchatMainId(@Param("convWinchatMainId") Long convWinchatMainId,
                                                        @Param("teamId") Long teamId);

    /**
     * 根据任务ID查询记录列表
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatDtl> selectByTaskId(@Param("taskId") Long taskId,
                                             @Param("teamId") Long teamId);

    /**
     * 根据条件查询记录列表
     *
     * @param convWinchatMainId 聊天窗口主表ID（可选）
     * @param taskId 任务ID（可选）
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatDtl> selectByConditions(@Param("convWinchatMainId") Long convWinchatMainId,
                                                 @Param("taskId") Long taskId,
                                                 @Param("teamId") Long teamId);
}