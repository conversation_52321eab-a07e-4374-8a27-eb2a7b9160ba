package com.yiyi.ai_train_playground.mapper.kb;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模拟聊天室窗口聊天记录表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Mapper
public interface TrainConvWinchatLogMapper {

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int insert(TrainConvWinchatLog record);

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<TrainConvWinchatLog> records);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKey(TrainConvWinchatLog record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    TrainConvWinchatLog selectByPrimaryKey(Long id);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据明细ID查询聊天记录列表
     *
     * @param convDtlId 明细ID
     * @param teamId 团队ID
     * @return 聊天记录列表
     */
    List<TrainConvWinchatLog> selectByConvDtlId(@Param("convDtlId") Long convDtlId,
                                                @Param("teamId") Long teamId);

    /**
     * 根据sessionId查询聊天记录列表
     *
     * @param sessionId 会话ID
     * @param teamId 团队ID
     * @return 聊天记录列表
     */
    List<TrainConvWinchatLog> selectBySessionId(@Param("sessionId") String sessionId,
                                                @Param("teamId") Long teamId);

    /**
     * 根据条件查询记录列表
     *
     * @param convDtlId 明细ID（可选）
     * @param sessionId 会话ID（可选）
     * @param senderType 发送者类型（可选）
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatLog> selectByConditions(@Param("convDtlId") Long convDtlId,
                                                 @Param("sessionId") String sessionId,
                                                 @Param("senderType") String senderType,
                                                 @Param("teamId") Long teamId);

    /**
     * 根据明细ID删除聊天记录
     *
     * @param convDtlId 明细ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByConvDtlId(@Param("convDtlId") Long convDtlId,
                          @Param("teamId") Long teamId);
}