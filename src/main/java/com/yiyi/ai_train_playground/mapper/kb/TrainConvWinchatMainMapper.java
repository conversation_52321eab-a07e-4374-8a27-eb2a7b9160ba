package com.yiyi.ai_train_playground.mapper.kb;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模拟聊天室窗口主表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Mapper
public interface TrainConvWinchatMainMapper {

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int insert(TrainConvWinchatMain record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKey(TrainConvWinchatMain record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    TrainConvWinchatMain selectByPrimaryKey(Long id);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据聊天室ID和员工ID查询记录
     *
     * @param chatroomId 聊天室ID
     * @param staffId 员工ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatMain> selectByChatroomIdAndStaffId(@Param("chatroomId") Long chatroomId, 
                                                            @Param("staffId") Long staffId, 
                                                            @Param("teamId") Long teamId);

    /**
     * 根据条件查询记录列表
     *
     * @param chatroomId 聊天室ID（可选）
     * @param staffId 员工ID（可选）
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatMain> selectByConditions(@Param("chatroomId") Long chatroomId,
                                                  @Param("staffId") Long staffId,
                                                  @Param("teamId") Long teamId);
}