package com.yiyi.ai_train_playground.mapper.shortphrase;

import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseDetailDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseQueryRequest;
import com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 快捷短语Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Mapper
public interface ShortcutPhraseMapper {
    
    /**
     * 分页查询快捷短语列表
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 短语列表
     */
    List<ShortcutPhraseListDTO> selectPhraseList(@Param("request") ShortcutPhraseQueryRequest request, 
                                                @Param("teamId") Long teamId,
                                                @Param("offset") Integer offset, 
                                                @Param("limit") Integer limit);
    
    /**
     * 查询快捷短语总数
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 总数
     */
    Long countPhrases(@Param("request") ShortcutPhraseQueryRequest request, 
                     @Param("teamId") Long teamId);
    
    /**
     * 根据ID查询快捷短语
     * 
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 短语信息
     */
    ShortcutPhrase selectById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据ID查询快捷短语详情
     * 
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 短语详情
     */
    ShortcutPhraseDetailDTO selectDetailById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 插入快捷短语
     * 
     * @param phrase 短语信息
     * @return 影响行数
     */
    int insert(ShortcutPhrase phrase);
    
    /**
     * 更新快捷短语
     * 
     * @param phrase 短语信息
     * @return 影响行数
     */
    int update(ShortcutPhrase phrase);
    
    /**
     * 删除快捷短语
     *
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 批量删除快捷短语
     *
     * @param ids 短语ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);
    
    /**
     * 增加使用次数
     *
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int incrementUsageCount(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据分组ID查询短语列表
     *
     * @param spGroupId 分组ID
     * @param teamId 团队ID
     * @return 短语列表
     */
    List<ShortcutPhraseListDTO> selectByGroupId(@Param("spGroupId") Long spGroupId, @Param("teamId") Long teamId);
}
