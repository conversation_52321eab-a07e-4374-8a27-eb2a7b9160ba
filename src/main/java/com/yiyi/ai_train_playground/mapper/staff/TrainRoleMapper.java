package com.yiyi.ai_train_playground.mapper.staff;

import com.yiyi.ai_train_playground.entity.staff.TrainRole;
import com.yiyi.ai_train_playground.dto.staff.TrainRoleQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色管理Mapper接口
 */
@Mapper
public interface TrainRoleMapper {

    /**
     * 根据ID查询角色
     */
    TrainRole selectById(@Param("id") Long id);

    /**
     * 插入角色
     */
    int insert(TrainRole trainRole);

    /**
     * 根据ID更新角色
     */
    int updateById(TrainRole trainRole);

    /**
     * 根据ID删除角色
     */
    int deleteById(@Param("id") Long id);

    /**
     * 分页查询角色列表
     */
    List<TrainRole> selectPageList(@Param("request") TrainRoleQueryRequest request);

    /**
     * 查询角色总数
     */
    long selectCount(@Param("request") TrainRoleQueryRequest request);

    /**
     * 根据角色编码查询角色
     */
    TrainRole selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 检查角色名称是否存在（用于重名校验）
     */
    boolean existsByRoleName(@Param("roleName") String roleName, @Param("teamId") Long teamId, @Param("excludeId") Long excludeId);

    /**
     * 检查角色编码是否存在（用于重名校验）
     */
    boolean existsByRoleCode(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);

    /**
     * 批量删除角色
     */
    int deleteBatch(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);

    /**
     * 根据团队ID查询角色列表
     */
    List<TrainRole> selectByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据员工ID查询其拥有的角色
     */
    List<TrainRole> selectByStaffId(@Param("staffId") Long staffId, @Param("teamId") Long teamId);
}