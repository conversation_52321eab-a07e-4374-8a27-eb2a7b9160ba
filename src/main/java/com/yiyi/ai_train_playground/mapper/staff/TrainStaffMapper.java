package com.yiyi.ai_train_playground.mapper.staff;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工表Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Mapper
public interface TrainStaffMapper {

    /**
     * 插入员工记录
     *
     * @param staff 员工信息
     * @return 影响行数
     */
    int insert(TrainStaff staff);

    /**
     * 根据ID删除员工
     *
     * @param id 员工ID
     * @return 影响行数
     */
    @TenantFilter(includeCreator = false) // 只按team_id过滤，不按creator过滤
    int deleteById(Long id);

    /**
     * 批量删除员工
     *
     * @param ids 员工ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    @TenantFilter(
        teamIdSource = TenantFilter.TeamIdSource.PARAM,
        teamIdParamName = "teamId",
        includeCreator = false
    )
    int deleteBatch(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);

    /**
     * 批量删除员工(通过拦截器添加team_id)
     *
     * @param ids 员工ID列表
     * @return 影响行数
     */
    @TenantFilter(includeCreator = false) // 通过拦截器自动添加team_id条件
    int deleteBatchWithIts(@Param("ids") List<Long> ids);

    /**
     * 根据ID更新员工信息
     *
     * @param staff 员工信息
     * @return 影响行数
     */
    @TenantFilter(
        teamIdSource = TenantFilter.TeamIdSource.ENTITY,
        includeCreator = false
    )
    int updateById(TrainStaff staff);

    /**
     * 根据ID查询员工
     *
     * @param id 员工ID
     * @return 员工信息
     */
    @TenantFilter(includeCreator = false) // 查询不需要creator过滤
    TrainStaff selectById(Long id);

    /**
     * 根据用户ID查询员工
     *
     * @param userId 用户ID
     * @return 员工信息
     */
    TrainStaff selectByUserId(Long userId);

    /**
     * 根据用户名查询员工
     *
     * @param username 用户名
     * @param teamId 团队ID
     * @return 员工信息
     */
    TrainStaff selectByUsername(@Param("username") String username, @Param("teamId") Long teamId);

    /**
     * 根据用户名查询员工（不限制团队，用于登录）
     *
     * @param username 用户名
     * @return 员工信息
     */
    @TenantFilter(enable = false) // 登录时不过滤租户
    TrainStaff selectByUsernameForLogin(@Param("username") String username);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的ID（用于更新时检查）
     * @param teamId 团队ID
     * @return 存在的数量
     */
    int countByUsername(@Param("username") String username, @Param("excludeId") Long excludeId, @Param("teamId") Long teamId);

    /**
     * 分页查询员工列表
     *
     * @param teamId 团队ID
     * @param username 用户名（模糊查询）
     * @param displayName 显示名称（模糊查询）
     * @param email 邮箱（模糊查询）
     * @param status 员工状态
     * @param isLocked 是否锁定
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 员工列表
     */
    List<TrainStaff> selectPageList(@Param("teamId") Long teamId,
                                   @Param("username") String username,
                                   @Param("displayName") String displayName,
                                   @Param("email") String email,
                                   @Param("status") Integer status,
                                   @Param("isLocked") Boolean isLocked,
                                   @Param("offset") Integer offset,
                                   @Param("limit") Integer limit);

    /**
     * 统计员工总数
     *
     * @param teamId 团队ID
     * @param username 用户名（模糊查询）
     * @param displayName 显示名称（模糊查询）
     * @param email 邮箱（模糊查询）
     * @param status 员工状态
     * @param isLocked 是否锁定
     * @return 总数
     */
    long countPageList(@Param("teamId") Long teamId,
                      @Param("username") String username,
                      @Param("displayName") String displayName,
                      @Param("email") String email,
                      @Param("status") Integer status,
                      @Param("isLocked") Boolean isLocked);

    /**
     * 更新登录相关信息
     *
     * @param id 员工ID
     * @param failedAttempts 失败次数
     * @param isLocked 是否锁定
     * @param lockTime 锁定时间
     * @param lastLoginTime 最后登录时间
     * @param lastLoginIp 最后登录IP
     * @return 影响行数
     */
    int updateLoginInfo(@Param("id") Long id,
                       @Param("failedAttempts") Integer failedAttempts,
                       @Param("isLocked") Boolean isLocked,
                       @Param("lockTime") String lockTime,
                       @Param("lastLoginTime") String lastLoginTime,
                       @Param("lastLoginIp") String lastLoginIp);

    /**
     * 更新登录状态
     *
     * @param id 员工ID
     * @param failedAttempts 失败次数
     * @param isLocked 是否锁定
     * @param lockTime 锁定时间
     * @return 影响行数
     */
    int updateLoginStatus(@Param("id") Long id,
                         @Param("failedAttempts") Integer failedAttempts,
                         @Param("isLocked") Boolean isLocked,
                         @Param("lockTime") LocalDateTime lockTime);

    /**
     * 更新最后登录时间
     *
     * @param id 员工ID
     * @param lastLoginTime 最后登录时间
     * @return 影响行数
     */
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") LocalDateTime lastLoginTime);

    /**
     * 更新密码
     *
     * @param id 员工ID
     * @param passwordHash 新密码哈希
     * @return 影响行数
     */
    int updatePassword(@Param("id") Long id, @Param("passwordHash") String passwordHash);
}