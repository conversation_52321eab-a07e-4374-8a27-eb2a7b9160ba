package com.yiyi.ai_train_playground.mapper.staff;

import com.yiyi.ai_train_playground.entity.staff.TrainStaffRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工角色关系Mapper接口
 */
@Mapper
public interface TrainStaffRoleMapper {

    /**
     * 根据ID查询员工角色关系
     */
    TrainStaffRole selectById(@Param("id") Long id);

    /**
     * 插入员工角色关系
     */
    int insert(TrainStaffRole trainStaffRole);

    /**
     * 根据ID更新员工角色关系
     */
    int updateById(TrainStaffRole trainStaffRole);

    /**
     * 根据ID删除员工角色关系
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据员工ID和角色ID查询关系
     */
    TrainStaffRole selectByStaffIdAndRoleId(@Param("staffId") Long staffId, @Param("roleId") Long roleId);

    /**
     * 根据员工ID删除所有角色关系
     */
    int deleteByStaffId(@Param("staffId") Long staffId, @Param("teamId") Long teamId);

    /**
     * 根据角色ID删除所有员工关系
     */
    int deleteByRoleId(@Param("roleId") Long roleId, @Param("teamId") Long teamId);

    /**
     * 批量插入员工角色关系
     */
    int insertBatch(@Param("list") List<TrainStaffRole> list);

    /**
     * 根据员工ID查询其角色关系列表
     */
    List<TrainStaffRole> selectByStaffId(@Param("staffId") Long staffId, @Param("teamId") Long teamId);

    /**
     * 根据角色ID查询其员工关系列表
     */
    List<TrainStaffRole> selectByRoleId(@Param("roleId") Long roleId, @Param("teamId") Long teamId);

    /**
     * 检查员工角色关系是否存在
     */
    boolean existsByStaffIdAndRoleId(@Param("staffId") Long staffId, @Param("roleId") Long roleId);

    /**
     * 批量删除员工角色关系
     */
    int deleteBatchByStaffIdAndRoleIds(@Param("staffId") Long staffId, @Param("roleIds") List<Long> roleIds, @Param("teamId") Long teamId);
}