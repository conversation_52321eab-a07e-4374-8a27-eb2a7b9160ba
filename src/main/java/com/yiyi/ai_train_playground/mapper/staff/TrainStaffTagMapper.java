package com.yiyi.ai_train_playground.mapper.staff;

import com.yiyi.ai_train_playground.entity.staff.TrainStaffTag;
import com.yiyi.ai_train_playground.dto.staff.StaffTagQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TrainStaffTagMapper {

    /**
     * 根据ID查询员工标签
     */
    TrainStaffTag selectById(@Param("id") Long id);

    /**
     * 插入员工标签
     */
    int insert(TrainStaffTag staffTag);

    /**
     * 根据ID更新员工标签
     */
    int updateById(TrainStaffTag staffTag);

    /**
     * 根据ID删除员工标签
     */
    int deleteById(@Param("id") Long id);

    /**
     * 分页查询员工标签
     */
    List<TrainStaffTag> selectStaffTagPage(@Param("request") StaffTagQueryRequest request,
                                          @Param("offset") Integer offset,
                                          @Param("limit") Integer limit);

    /**
     * 查询员工标签总数
     */
    Integer countStaffTag(@Param("request") StaffTagQueryRequest request);

    /**
     * 检查标签名称在团队内是否重复
     */
    Integer checkNameDuplicate(@Param("teamId") Long teamId, 
                              @Param("name") String name, 
                              @Param("excludeId") Long excludeId);

    /**
     * 批量删除员工标签
     */
    Integer batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);

    /**
     * 根据条件查询员工标签列表
     */
    List<TrainStaffTag> selectList(@Param("teamId") Long teamId);

    /**
     * 根据员工ID查询其拥有的标签
     */
    List<TrainStaffTag> selectByStaffId(@Param("staffId") Long staffId);
}