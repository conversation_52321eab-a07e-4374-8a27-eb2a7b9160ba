package com.yiyi.ai_train_playground.mapper.staff;

import com.yiyi.ai_train_playground.entity.staff.TrainStaffTagRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工标签关系Mapper接口
 */
@Mapper
public interface TrainStaffTagRelationMapper {

    /**
     * 根据ID查询员工标签关系
     */
    TrainStaffTagRelation selectById(@Param("id") Long id);

    /**
     * 插入员工标签关系
     */
    int insert(TrainStaffTagRelation trainStaffTagRelation);

    /**
     * 根据ID更新员工标签关系
     */
    int updateById(TrainStaffTagRelation trainStaffTagRelation);

    /**
     * 根据ID删除员工标签关系
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据员工ID和标签ID查询关系
     */
    TrainStaffTagRelation selectByStaffIdAndTagId(@Param("staffId") Long staffId, @Param("tagId") Long tagId);

    /**
     * 根据员工ID删除所有标签关系
     */
    int deleteByStaffId(@Param("staffId") Long staffId);

    /**
     * 根据标签ID删除所有员工关系
     */
    int deleteByTagId(@Param("tagId") Long tagId);

    /**
     * 批量插入员工标签关系
     */
    int insertBatch(@Param("list") List<TrainStaffTagRelation> list);

    /**
     * 根据员工ID查询其标签关系列表
     */
    List<TrainStaffTagRelation> selectByStaffId(@Param("staffId") Long staffId);

    /**
     * 根据标签ID查询其员工关系列表
     */
    List<TrainStaffTagRelation> selectByTagId(@Param("tagId") Long tagId);

    /**
     * 检查员工标签关系是否存在
     */
    boolean existsByStaffIdAndTagId(@Param("staffId") Long staffId, @Param("tagId") Long tagId);

    /**
     * 批量删除员工标签关系
     */
    int deleteBatchByStaffIdAndTagIds(@Param("staffId") Long staffId, @Param("tagIds") List<Long> tagIds);
}