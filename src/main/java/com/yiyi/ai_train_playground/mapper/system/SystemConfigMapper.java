package com.yiyi.ai_train_playground.mapper.system;

import com.yiyi.ai_train_playground.entity.system.SystemConfig;
import com.yiyi.ai_train_playground.dto.system.SystemConfigResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统配置Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Mapper
public interface SystemConfigMapper {
    
    /**
     * 插入系统配置
     */
    int insert(SystemConfig systemConfig);
    
    /**
     * 根据ID更新系统配置
     */
    int updateById(SystemConfig systemConfig);
    
    /**
     * 根据ID删除系统配置
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 批量删除系统配置
     */
    int deleteBatchByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);
    
    /**
     * 根据ID查询系统配置
     */
    SystemConfig selectById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 分页查询系统配置列表
     */
    List<SystemConfigResponse> selectPageList(@Param("teamId") Long teamId,
                                            @Param("namespace") String namespace,
                                            @Param("configKey") String configKey,
                                            @Param("description") String description,
                                            @Param("offset") Integer offset,
                                            @Param("pageSize") Integer pageSize);
    
    /**
     * 查询系统配置总数
     */
    long countPageList(@Param("teamId") Long teamId,
                      @Param("namespace") String namespace,
                      @Param("configKey") String configKey,
                      @Param("description") String description);
    
    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(@Param("teamId") Long teamId,
                             @Param("namespace") String namespace,
                             @Param("configKey") String configKey,
                             @Param("excludeId") Long excludeId);
    
    /**
     * 根据命名空间和配置键查询配置值
     */
    String selectConfigValue(@Param("teamId") Long teamId,
                           @Param("namespace") String namespace,
                           @Param("configKey") String configKey);
    
    /**
     * 根据命名空间查询所有配置
     */
    List<SystemConfig> selectByNamespace(@Param("teamId") Long teamId,
                                       @Param("namespace") String namespace);
}
