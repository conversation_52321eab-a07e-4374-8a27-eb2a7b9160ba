package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.entity.task.TrainChatroomStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天室员工关联表数据访问层
 */
@Mapper
public interface TrainChatroomStaffMapper {

    /**
     * 插入聊天室员工关联记录
     */
    int insert(TrainChatroomStaff chatroomStaff);

    /**
     * 批量插入聊天室员工关联记录
     */
    int batchInsert(@Param("list") List<TrainChatroomStaff> chatroomStaffList);

    /**
     * 根据聊天室ID删除关联记录
     */
    int deleteByReceChatroomId(@Param("receChatroomId") Long receChatroomId, @Param("teamId") Long teamId);

    /**
     * 根据聊天室ID批量删除关联记录
     */
    int deleteByReceChatroomIds(@Param("receChatroomIds") List<Long> receChatroomIds, @Param("teamId") Long teamId);

    /**
     * 根据聊天室ID查询关联的员工ID列表
     */
    List<Long> selectStaffIdsByReceChatroomId(@Param("receChatroomId") Long receChatroomId, @Param("teamId") Long teamId);

    /**
     * 根据员工ID查询关联的聊天室ID列表
     */
    List<Long> selectReceChatroomIdsByStaffId(@Param("staffId") Long staffId, @Param("teamId") Long teamId);

    /**
     * 检查聊天室员工关联是否存在
     */
    boolean existsByChatroomAndStaff(@Param("receChatroomId") Long receChatroomId, 
                                     @Param("staffId") Long staffId, 
                                     @Param("teamId") Long teamId);

    /**
     * 统计聊天室关联的员工数量
     */
    int countStaffByReceChatroomId(@Param("receChatroomId") Long receChatroomId, @Param("teamId") Long teamId);

    /**
     * 根据聊天室ID查询完整的关联记录
     */
    List<TrainChatroomStaff> selectByReceChatroomId(@Param("receChatroomId") Long receChatroomId, @Param("teamId") Long teamId);
}