package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.entity.task.TrainChatroomTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天室任务关联Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Mapper
public interface TrainChatroomTaskMapper {
    
    /**
     * 插入聊天室任务关联记录
     *
     * @param chatroomTask 聊天室任务关联实体
     * @return 影响行数
     */
    int insert(TrainChatroomTask chatroomTask);
    
    /**
     * 批量插入聊天室任务关联记录
     *
     * @param chatroomTasks 聊天室任务关联实体列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainChatroomTask> chatroomTasks);
    
    /**
     * 根据聊天室ID查询任务列表
     *
     * @param chatroomId 聊天室ID
     * @param teamId 团队ID
     * @return 任务关联列表
     */
    List<TrainChatroomTask> selectByChatroomId(@Param("chatroomId") Long chatroomId, 
                                               @Param("teamId") Long teamId);
    
    /**
     * 根据ID更新聊天室任务关联
     *
     * @param chatroomTask 聊天室任务关联实体
     * @return 影响行数
     */
    int updateById(TrainChatroomTask chatroomTask);
    
    /**
     * 根据聊天室ID删除所有任务关联
     *
     * @param chatroomId 聊天室ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByChatroomId(@Param("chatroomId") Long chatroomId, @Param("teamId") Long teamId);
    
    /**
     * 根据ID删除任务关联
     *
     * @param id 任务关联ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
}