package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;
import com.yiyi.ai_train_playground.dto.task.ExamAnswerRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问答随机表 Mapper 接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Mapper
public interface TrainQaRdmMapper {

    /**
     * 插入记录
     *
     * @param trainQaRdm 问答随机记录
     * @return 插入行数
     */
    int insert(TrainQaRdm trainQaRdm);

    /**
     * 根据UUID更新记录
     *
     * @param trainQaRdm 问答随机记录
     * @return 更新行数
     */
//    @TenantFilter(includeTeamId = true, includeCreator = true)
    int updateByUUID(@Param("record") TrainQaRdm trainQaRdm);

    /**
     * 根据UUID查询记录（不带Tenant注解）
     *
     * @param uuid UUID
     * @return 问答随机记录
     */
    TrainQaRdm selectByUUID(@Param("uuid") String uuid);

    /**
     * 根据UUID查询记录（带团队ID过滤）
     *
     * @param uuid UUID
     * @param teamId 团队ID
     * @return 问答随机记录
     */
    @TenantFilter(includeTeamId = true)
    TrainQaRdm selectByUUIDWithTeam(@Param("uuid") String uuid, @Param("teamId") Long teamId);

    /**
     * 分页查询问答随机记录
     *
     * @param teamId 团队ID
     * @param quesNo 问题编号（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页结果
     */
    @TenantFilter(includeTeamId = true)
    List<TrainQaRdm> selectPageList(@Param("teamId") Long teamId,
                                    @Param("quesNo") String quesNo,
                                    @Param("offset") int offset,
                                    @Param("limit") int limit);

    /**
     * 查询总记录数
     *
     * @param teamId 团队ID
     * @param quesNo 问题编号（可选）
     * @return 总记录数
     */
    @TenantFilter(includeTeamId = true)
    long countByCondition(@Param("teamId") Long teamId,
                          @Param("quesNo") String quesNo);

    /**
     * 根据团队ID查询所有记录
     *
     * @param teamId 团队ID
     * @return 问答随机记录列表
     */
    @TenantFilter(includeTeamId = true)
    List<TrainQaRdm> selectByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据问题编号查询记录
     *
     * @param quesNo 问题编号
     * @param teamId 团队ID
     * @return 问答随机记录列表
     */
    @TenantFilter(includeTeamId = true)
    List<TrainQaRdm> selectByQuesNo(@Param("quesNo") String quesNo, @Param("teamId") Long teamId);

    /**
     * 根据报告明细ID查询记录
     *
     * @param reportDtlId 报告明细ID
     * @param teamId      团队ID
     * @return 问答随机记录列表
     */
//    @TenantFilter(includeTeamId = true)
    List<TrainQaRdm> selectByReportDtlId(@Param("reportDtlId") Long reportDtlId, @Param("teamId") Long teamId);

    /**
     * 批量插入问答随机记录
     *
     * @param records 问答随机记录列表
     * @return 插入行数
     */
    int batchInsert(@Param("records") List<TrainQaRdm> records);

    /**
     * 根据UUID删除记录
     *
     * @param uuid UUID
     * @param teamId 团队ID
     * @return 删除行数
     */
    @TenantFilter(includeTeamId = true)
    int deleteByUUID(@Param("uuid") String uuid, @Param("teamId") Long teamId);

    /**
     * 根据团队ID删除所有记录
     *
     * @param teamId 团队ID
     * @return 删除行数
     */
    @TenantFilter(includeTeamId = true)
    int deleteByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 根据报告明细ID删除记录
     *
     * @param reportDtlId 报告明细ID
     * @param teamId      团队ID
     * @return 删除行数
     */
    int deleteByReportDtlId(@Param("reportDtlId") Long reportDtlId, @Param("teamId") Long teamId);

    /**
     * 根据报告主记录ID查询考试答题记录
     * 用于ShowExamRst接口
     *
     * @param qaReportMainId 报告主记录ID
     * @param teamId 团队ID
     * @return 考试答题记录列表
     */
//    @TenantFilter(includeTeamId = true)
    List<ExamAnswerRecordDTO> selectExamAnswerRecordsByMainId(@Param("qaReportMainId") Long qaReportMainId, @Param("teamId") Long teamId);
}
