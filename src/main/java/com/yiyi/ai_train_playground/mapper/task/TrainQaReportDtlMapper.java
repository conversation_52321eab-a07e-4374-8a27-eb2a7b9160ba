package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模拟聊天室报告明细表 Mapper 接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Mapper
public interface TrainQaReportDtlMapper {

    /**
     * 插入记录
     *
     * @param record 记录对象
     * @return 插入行数
     */
    int insert(@Param("record") TrainQaReportDtl record);

    /**
     * 根据ID查询记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 记录对象
     */
    TrainQaReportDtl selectById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 根据ID更新记录
     *
     * @param record 记录对象
     * @return 更新行数
     */
    int updateById(@Param("record") TrainQaReportDtl record);

    /**
     * 根据ID删除记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 删除行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 根据sessionId查询记录
     *
     * @param sessionId 会话ID
     * @param teamId    团队ID
     * @return 记录对象
     */
    TrainQaReportDtl selectBySessionId(@Param("sessionId") String sessionId, @Param("teamId") Long teamId);

    /**
     * 根据taskId查询记录列表
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainQaReportDtl> selectByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);

    /**
     * 根据rpMainId查询记录列表
     *
     * @param rpMainId 聊天窗口主表ID
     * @param teamId   团队ID
     * @return 记录列表
     */
    List<TrainQaReportDtl> selectByRpMainId(@Param("rpMainId") Long rpMainId, @Param("teamId") Long teamId);

    /**
     * 分页查询记录
     *
     * @param teamId   团队ID
     * @param taskId   任务ID（可选）
     * @param qaMainId QA主表ID（可选）
     * @param offset   偏移量
     * @param limit    限制数量
     * @return 分页结果
     */
    List<TrainQaReportDtl> selectPageList(@Param("teamId") Long teamId,
                                          @Param("taskId") Long taskId,
                                          @Param("qaMainId") Long qaMainId,
                                          @Param("offset") int offset,
                                          @Param("limit") int limit);

    /**
     * 查询总记录数
     *
     * @param teamId   团队ID
     * @param taskId   任务ID（可选）
     * @param qaMainId QA主表ID（可选）
     * @return 总记录数
     */
    long countByCondition(@Param("teamId") Long teamId,
                          @Param("taskId") Long taskId,
                          @Param("qaMainId") Long qaMainId);

    /**
     * 根据团队ID查询所有记录
     *
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainQaReportDtl> selectByTeamId(@Param("teamId") Long teamId);

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 插入行数
     */
    int batchInsert(@Param("records") List<TrainQaReportDtl> records);

    /**
     * 根据团队ID删除所有记录
     *
     * @param teamId 团队ID
     * @return 删除行数
     */
    int deleteByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据taskId删除记录
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 删除行数
     */
    int deleteByTaskId(@Param("taskId") Long taskId, @Param("teamId") Long teamId);
    
    /**
     * 根据rpMainId删除记录
     *
     * @param rpMainId 聊天窗口主表ID
     * @param teamId   团队ID
     * @return 删除行数
     */
    int deleteByRpMainId(@Param("rpMainId") Long rpMainId, @Param("teamId") Long teamId);
}
