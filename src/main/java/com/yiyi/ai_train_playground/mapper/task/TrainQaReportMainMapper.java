package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.entity.task.TrainQaReportMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问答报告主表 Mapper 接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Mapper
public interface TrainQaReportMainMapper {

    /**
     * 插入记录
     *
     * @param record 记录对象
     * @return 插入行数
     */
    int insert(TrainQaReportMain record);

    /**
     * 根据ID查询记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 记录对象
     */
    TrainQaReportMain selectById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 根据ID更新记录
     *
     * @param record 记录对象
     * @return 更新行数
     */
    int updateById(TrainQaReportMain record);

    /**
     * 根据ID删除记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 删除行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 分页查询记录
     *
     * @param teamId           团队ID
     * @param chatroomId       聊天室ID（可选）
     * @param staffId          员工ID（可选）
     * @param examUserRealName 考试用户真实姓名（可选）
     * @param examUserNo       考试用户编号（可选）
     * @param createTimeStart  创建时间开始（可选）
     * @param createTimeEnd    创建时间结束（可选）
     * @param minScore         最低分数（可选）
     * @param offset           偏移量
     * @param limit            限制数量
     * @return 分页结果
     */
    @TenantFilter(includeTeamId = false, includeCreator = true)
    List<TrainQaReportMain> selectPageList(@Param("teamId") Long teamId,
                                           @Param("chatroomId") Long chatroomId,
                                           @Param("staffId") Long staffId,
                                           @Param("examUserRealName") String examUserRealName,
                                           @Param("examUserNo") String examUserNo,
                                           @Param("createTimeStart") java.time.LocalDateTime createTimeStart,
                                           @Param("createTimeEnd") java.time.LocalDateTime createTimeEnd,
                                           @Param("minScore") java.math.BigDecimal minScore,
                                           @Param("offset") int offset,
                                           @Param("limit") int limit);

    /**
     * 查询总记录数
     *
     * @param teamId           团队ID
     * @param chatroomId       聊天室ID（可选）
     * @param staffId          员工ID（可选）
     * @param examUserRealName 考试用户真实姓名（可选）
     * @param examUserNo       考试用户编号（可选）
     * @param createTimeStart  创建时间开始（可选）
     * @param createTimeEnd    创建时间结束（可选）
     * @param minScore         最低分数（可选）
     * @return 总记录数
     */
    @TenantFilter(includeTeamId = false, includeCreator = true)
    long countByCondition(@Param("teamId") Long teamId,
                          @Param("chatroomId") Long chatroomId,
                          @Param("staffId") Long staffId,
                          @Param("examUserRealName") String examUserRealName,
                          @Param("examUserNo") String examUserNo,
                          @Param("createTimeStart") java.time.LocalDateTime createTimeStart,
                          @Param("createTimeEnd") java.time.LocalDateTime createTimeEnd,
                          @Param("minScore") java.math.BigDecimal minScore);

    /**
     * 查询记录列表（用于导出Excel，限制10万条）
     *
     * @param teamId           团队ID
     * @param chatroomId       聊天室ID（可选）
     * @param staffId          员工ID（可选）
     * @param examUserRealName 考试用户真实姓名（可选）
     * @param examUserNo       考试用户编号（可选）
     * @param createTimeStart  创建时间开始（可选）
     * @param createTimeEnd    创建时间结束（可选）
     * @param minScore         最低分数（可选）
     * @return 记录列表，最多10万条
     */
    @TenantFilter(includeTeamId = false, includeCreator = true)
    List<TrainQaReportMain> selectListForExport(@Param("teamId") Long teamId,
                                                @Param("chatroomId") Long chatroomId,
                                                @Param("staffId") Long staffId,
                                                @Param("examUserRealName") String examUserRealName,
                                                @Param("examUserNo") String examUserNo,
                                                @Param("createTimeStart") java.time.LocalDateTime createTimeStart,
                                                @Param("createTimeEnd") java.time.LocalDateTime createTimeEnd,
                                                @Param("minScore") java.math.BigDecimal minScore);

    /**
     * 根据聊天室ID查询记录
     *
     * @param chatroomId 聊天室ID
     * @param teamId     团队ID
     * @return 记录列表
     */
    List<TrainQaReportMain> selectByChatroomId(@Param("chatroomId") Long chatroomId, @Param("teamId") Long teamId);

    /**
     * 根据员工ID查询记录
     *
     * @param staffId 员工ID
     * @param teamId  团队ID
     * @return 记录列表
     */
    List<TrainQaReportMain> selectByStaffId(@Param("staffId") Long staffId, @Param("teamId") Long teamId);

    /**
     * 根据团队ID查询所有记录
     *
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainQaReportMain> selectByTeamId(@Param("teamId") Long teamId);

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 插入行数
     */
    int batchInsert(@Param("records") List<TrainQaReportMain> records);

    /**
     * 根据团队ID删除所有记录
     *
     * @param teamId 团队ID
     * @return 删除行数
     */
    int deleteByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据ID更新考试分数
     *
     * @param id 主键ID
     * @param examScore 考试分数
     * @param teamId 团队ID
     * @return 更新行数
     */
    int updateExamScoreById(@Param("id") Long id, @Param("examScore") java.math.BigDecimal examScore, @Param("teamId") Long teamId);
}
