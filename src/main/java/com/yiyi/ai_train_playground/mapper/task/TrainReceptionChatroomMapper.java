package com.yiyi.ai_train_playground.mapper.task;

import java.util.List;
import com.yiyi.ai_train_playground.dto.task.ChatroomDetailDTO;
import com.yiyi.ai_train_playground.dto.task.ChatroomListDTO;
import com.yiyi.ai_train_playground.dto.task.ChatroomQueryRequest;
import com.yiyi.ai_train_playground.dto.task.ChatRoomTaskInfo;
import com.yiyi.ai_train_playground.dto.task.TaskAvailableCheckDTO;
import com.yiyi.ai_train_playground.entity.task.TrainReceptionChatroom;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 接待聊天室Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Mapper
public interface TrainReceptionChatroomMapper {
    
    /**
     * 插入聊天室记录
     *
     * @param chatroom 聊天室实体
     * @return 影响行数
     */
    int insert(TrainReceptionChatroom chatroom);
    
    /**
     * 根据ID查询聊天室
     *
     * @param id 聊天室ID
     * @param teamId 团队ID
     * @return 聊天室实体
     */
    TrainReceptionChatroom selectById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 查询聊天室详情（包含关联任务信息）
     *
     * @param id 聊天室ID
     * @param teamId 团队ID
     * @return 聊天室详情DTO
     */
    ChatroomDetailDTO selectDetailById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 分页查询聊天室列表
     *
     * @param queryRequest 查询参数
     * @param teamId 团队ID
     * @param creator 创建者
     * @return 聊天室列表
     */
    List<ChatroomListDTO> selectPageList(@Param("request") ChatroomQueryRequest queryRequest,
                                        @Param("teamId") Long teamId,
                                        @Param("creator") String creator);
    
    /**
     * 查询聊天室列表总数
     *
     * @param queryRequest 查询参数
     * @param teamId 团队ID
     * @param creator 创建者
     * @return 总数
     */
    Long selectPageCount(@Param("request") ChatroomQueryRequest queryRequest, 
                        @Param("teamId") Long teamId,
                        @Param("creator") String creator);
    
    /**
     * 更新聊天室
     *
     * @param chatroom 聊天室实体
     * @return 影响行数
     */
    int updateById(TrainReceptionChatroom chatroom);
    
    /**
     * 根据ID删除聊天室
     *
     * @param id 聊天室ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 批量删除聊天室
     *
     * @param ids ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);
    
    /**
     * 查询员工的接待聊天任务列表（分页）
     *
     * @param queryRequest 查询条件
     * @param staffId 员工ID
     * @param teamId 团队ID
     * @return 员工的任务分页列表
     */
    List<ChatroomListDTO> selectMyTasksPageList(@Param("request") ChatroomQueryRequest queryRequest,
                                               @Param("staffId") Long staffId, 
                                               @Param("teamId") Long teamId);
    
    /**
     * 查询员工的接待聊天任务总数
     *
     * @param queryRequest 查询条件
     * @param staffId 员工ID
     * @param teamId 团队ID
     * @return 员工的任务总数
     */
    Long selectMyTasksPageCount(@Param("request") ChatroomQueryRequest queryRequest,
                               @Param("staffId") Long staffId,
                               @Param("teamId") Long teamId);

    /**
     * 根据聊天室ID和员工ID查询员工信息（验证权限）
     *
     * @param receChatRoomId 接待聊天室ID
     * @param staffId 员工ID
     * @return 员工信息，如果无权限则返回null
     */
    TrainStaff selectStaffByReceChatroomId(@Param("receChatRoomId") Long receChatRoomId,
                                          @Param("staffId") Long staffId);

    /**
     * 根据聊天室ID查询剧本任务列表
     *
     * @param receChatRoomId 接待聊天室ID
     * @return 剧本任务列表
     */
    List<ChatRoomTaskInfo> selectChatRoomTaskList(@Param("receChatRoomId") Long receChatRoomId);

    /**
     * 根据聊天室ID查询问答任务列表（专用于问答场景）
     *
     * @param receChatRoomId 接待聊天室ID
     * @return 问答任务列表
     */
    List<ChatRoomTaskInfo> selectCrTL4Qa(@Param("receChatRoomId") Long receChatRoomId);

    /**
     * 检查聊天室中的任务是否仍在学习中
     *
     * @param chatroomId 聊天室ID
     * @param teamId 团队ID
     * @return 仍在学习中的任务列表，如果为空则表示可以使用
     */
    List<TaskAvailableCheckDTO> checkTasksLearningStatus(@Param("chatroomId") Long chatroomId,
                                                        @Param("teamId") Long teamId);
}