package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskDetailDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskListDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskQueryRequest;
import com.yiyi.ai_train_playground.entity.task.TrainReceptionTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 接待任务Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Mapper
public interface TrainReceptionTaskMapper {
    
    /**
     * 分页查询接待任务列表
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    List<TrainReceptionTaskListDTO> selectTaskList(@Param("request") TrainReceptionTaskQueryRequest request,
                                             @Param("teamId") Long teamId,
                                             @Param("creator") String creator,
                                             @Param("offset") Integer offset, 
                                             @Param("limit") Integer limit);
    
    /**
     * 查询接待任务总数
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 总数
     */
//    @TenantFilter(includeTeamId = false,includeCreator = true,creatorSource = TenantFilter.CreatorSource.SECURITY_CONTEXT)
    Long countTasks(@Param("request") TrainReceptionTaskQueryRequest request,
                   @Param("teamId") Long teamId,
                   @Param("creator") String creator);
    
    /**
     * 根据ID查询接待任务
     * 
     * @param id 任务ID
     * @param teamId 团队ID
     * @return 任务信息
     */
    TrainReceptionTask selectById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 根据ID查询接待任务详情
     * 
     * @param id 任务ID
     * @param teamId 团队ID
     * @return 任务详情
     */
    TrainReceptionTaskDetailDTO selectDetailById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 插入接待任务
     * 
     * @param task 任务信息
     * @return 影响行数
     */
    int insert(TrainReceptionTask task);
    
    /**
     * 更新接待任务
     * 
     * @param task 任务信息
     * @return 影响行数
     */
    int update(TrainReceptionTask task);
    
    /**
     * 删除接待任务
     *
     * @param id 任务ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 批量删除接待任务
     *
     * @param ids 任务ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);

    /**
     * 更新已学习数量（使用乐观锁）
     *
     * @param id 任务ID
     * @param amtHasLearned 已学习数量
     * @param updater 更新人
     * @param version 版本号
     * @return 影响行数
     */
    int updateAmtHasLearned(@Param("id") Long id,
                           @Param("amtHasLearned") Long amtHasLearned,
                           @Param("updater") String updater,
                           @Param("version") Long version);

    /**
     * 更新学习状态
     *
     * @param id 任务ID
     * @param learningStatus 学习状态
     * @param updater 更新人
     * @param teamId 团队ID
     * @return 影响行数
     */
    int updateLearningStatus(@Param("id") Long id,
                           @Param("learningStatus") String learningStatus,
                           @Param("updater") String updater,
                           @Param("teamId") Long teamId);

    /**
     * 更新已学习数量（使用悲观锁）
     *
     * @param id 任务ID
     * @param updater 更新人
     * @return 影响行数
     */
    int updateAmtHasLearnedWithPessimisticLock(@Param("id") Long id, @Param("updater") String updater);

    /**
     * 使用悲观锁批量更新已学习数量
     *
     * @param id 任务ID
     * @param incrementCount 增加的数量
     * @param updater 更新人
     * @return 影响行数
     */
    int updateAmtHasLearnedBatchWithPessimisticLock(@Param("id") Long id,
                                                  @Param("incrementCount") int incrementCount,
                                                  @Param("updater") String updater);

    /**
     * 查询任务的学习统计信息
     *
     * @param id 任务ID
     * @return 任务信息（包含amt_has_learned和version）
     */
    TrainReceptionTask selectLearningInfo(@Param("id") Long id);

    /**
     * 使用悲观锁查询任务的学习统计信息
     *
     * @param id 任务ID
     * @return 任务信息（包含amt_has_learned和version）
     */
    TrainReceptionTask selectLearningInfoWithLock(@Param("id") Long id);
}
