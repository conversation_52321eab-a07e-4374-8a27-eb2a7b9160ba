package com.yiyi.ai_train_playground.scheduler;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.dto.RstObj;
import com.yiyi.ai_train_playground.entity.jd.TrainJdAccessToken;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.enums.JdSyncStatus;
import com.yiyi.ai_train_playground.service.TrainTeamService;
import com.yiyi.ai_train_playground.service.TrainTeamShopsService;
import com.yiyi.ai_train_playground.service.jd.TrainJdAccessTokenService;
import com.yiyi.ai_train_playground.service.jd.TrainJdProdImagesService;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import com.yiyi.ai_train_playground.service.jd.impl.JdProductSyncServiceUtil;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 京东商品图片向量处理定时任务
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "jd.img-vector.enabled", havingValue = "true", matchIfMissing = true)
public class JdImg2VecScheduler {

    private final TrainTeamService trainTeamService;
    private final TrainJdProductsService trainJdProductsService;
    private final TrainJdProdImagesService trainJdProdImagesService;
    private final TrainJdAccessTokenService trainJdAccessTokenService;
    private final TrainTeamShopsService trainTeamShopsService;
    private final JdProductSyncServiceUtil jdProductSyncServiceUtil;
    private final VectorSearchService vectorSearchService;
    private final JdConfig jdConfig;

    @Qualifier("imgVectorThreadPool")
    private final ThreadPoolExecutor threadPoolExecutor;

    @Value("${jd.img-vector.batch-size:20}")
    private Integer batchSize;

    @Value("${jd.img-vector.timeout-seconds:300}")
    private Integer timeoutSeconds;

    /**
     * 定时处理京东商品图片的img解析、向量生成等内容
     * 每5分钟执行一次
     */
    @Scheduled(fixedDelayString = "${jd.img-vector.scheduler-interval:300000}")
    public void processJdImg2Vec() {
        log.info("=== 开始执行京东商品图片向量处理定时任务 ===");
        
        try {
            // 1. 获取所有团队ID
            List<TrainTeam> allTeams = getAllTeams();
            if (allTeams.isEmpty()) {
                log.info("没有找到任何团队，跳过处理");
                return;
            }
            
            log.info("找到 {} 个团队，开始处理", allTeams.size());
            
            // 2. 遍历每个团队
            for (TrainTeam team : allTeams) {
                Long currTeamId = team.getId();
                log.info("开始处理团队: teamId={}, teamName={}", currTeamId, team.getName());
                
                try {
                    processTeamProducts(currTeamId);
                } catch (Exception e) {
                    log.error("处理团队 {} 时发生异常", currTeamId, e);
                    // 继续处理下一个团队
                }
            }
            
            log.info("=== 京东商品图片向量处理定时任务执行完成 ===");
            
        } catch (Exception e) {
            log.error("京东商品图片向量处理定时任务执行失败", e);
        }
    }

    /**
     * 获取所有团队
     */
    private List<TrainTeam> getAllTeams() {
        try {
            log.debug("获取所有团队列表");
            List<TrainTeam> teams = trainTeamService.findAll();
            log.debug("获取到 {} 个团队", teams.size());
            return teams;
        } catch (Exception e) {
            log.error("获取所有团队失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理团队的商品
     */
    @Transactional(rollbackFor = Exception.class)
    public void processTeamProducts(Long currTeamId) {
        log.debug("开始处理团队 {} 的商品", currTeamId);
        
        int offset = 0;
        int pageSize = jdConfig.getSync().getPageSize();
        Long savedShopId = null;
        Long savedId = null;
        
        while (true) {
            // 2. 获取未同步的商品列表
            List<TrainJdProducts> localJdProdList = trainJdProductsService.findByTeamIdAndSyncStatusWithPagination(
                    currTeamId, JdSyncStatus.UN_SYNC.getCode(), null, offset, pageSize);
            
            if (localJdProdList.isEmpty()) {
                log.debug("团队 {} 没有更多未同步的商品", currTeamId);
                break;
            }
            
            log.debug("团队 {} 获取到 {} 个未同步商品", currTeamId, localJdProdList.size());
            
            // 3. 遍历商品列表
            for (TrainJdProducts localJdProd : localJdProdList) {
                try {
                    // 3.1 保存shopId和id备用
                    savedShopId = localJdProd.getShopId();
                    savedId = localJdProd.getId();
                    
                    // 3.2 设置商品状态为同步中
                    localJdProd.setSyncStatus(JdSyncStatus.SYNCING.getCode());
                    
                    // 3.3 更新商品状态（开启事务S1）
                    boolean updateResult = trainJdProductsService.updateByIdSelective(localJdProd);
                    if (!updateResult) {
                        log.warn("更新商品 {} 状态失败，跳过处理", localJdProd.getId());
                        continue;
                    }
                    
                    // 4. 处理商品图片
                    processProductImages(localJdProd, currTeamId);
                    
                } catch (Exception e) {
                    log.error("处理商品 {} 时发生异常", localJdProd.getId(), e);
                    // 回滚商品状态
                    try {
                        localJdProd.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());
                        trainJdProductsService.updateByIdSelective(localJdProd);
                    } catch (Exception rollbackEx) {
                        log.error("回滚商品 {} 状态失败", localJdProd.getId(), rollbackEx);
                    }
                }
            }
            
//            offset += pageSize;
        }
        
        // 5. 更新AccessToken和TeamShops状态
        if (savedShopId != null) {
            updateFinalStatus(savedShopId);
        }
    }

    /**
     * 处理商品图片
     */
    private void processProductImages(TrainJdProducts localJdProd, Long currTeamId) {
        log.debug("开始处理商品 {} 的图片", localJdProd.getId());
        
        int offset = 0;
        List<RstObj> finalResultList = new ArrayList<>();
        
        while (true) {
            // 4. 加载商品图片数据
            List<TrainJdProdImages> localTrJdProdImageList = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                    currTeamId, JdSyncStatus.UN_SYNC.getCode(), localJdProd.getWareId(), offset, batchSize);
            
            if (localTrJdProdImageList.isEmpty()) {
                log.debug("商品 {} 没有更多未同步的图片", localJdProd.getId());
                break;
            }
            
            log.debug("商品 {} 获取到 {} 张未同步图片", localJdProd.getId(), localTrJdProdImageList.size());
            
            // 4.3 使用线程池处理图片
            List<RstObj> batchResults = processImagesBatch(localTrJdProdImageList, localJdProd, currTeamId);
            finalResultList.addAll(batchResults);
            
        }
        
        // 4.5 汇总结果并更新商品详情
        updateProductDetails(localJdProd, finalResultList);
    }

    /**
     * 批量处理图片
     */
    private List<RstObj> processImagesBatch(List<TrainJdProdImages> imageList,
                                          TrainJdProducts localJdProd, Long currTeamId) {
        log.info("开始批量处理 {} 张图片", imageList.size());

        // 记录线程池状态
        log.info("线程池状态: 活跃线程={}, 队列大小={}, 完成任务数={}, 核心线程数={}, 最大线程数={}",
                threadPoolExecutor.getActiveCount(),
                threadPoolExecutor.getQueue().size(),
                threadPoolExecutor.getCompletedTaskCount(),
                threadPoolExecutor.getCorePoolSize(),
                threadPoolExecutor.getMaximumPoolSize());
        
        // 创建有序的Future列表
        List<CompletableFuture<RstObj>> futures = new ArrayList<>();
        
        for (int i = 0; i < imageList.size(); i++) {
            TrainJdProdImages image = imageList.get(i);
            int orderIndex = i; // 保证顺序
            
            CompletableFuture<RstObj> future = CompletableFuture.supplyAsync(() -> {
                return processImageTask(image, localJdProd, currTeamId, orderIndex);
            }, threadPoolExecutor);
            
            futures.add(future);
        }
        
        // 等待所有任务完成并按顺序收集结果
        List<RstObj> results = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get(timeoutSeconds, TimeUnit.SECONDS);

            // 按顺序收集结果
            for (CompletableFuture<RstObj> future : futures) {
                RstObj result = future.get();
                if (result != null) {
                    results.add(result);
                }
            }

        } catch (Exception e) {
            log.error("批量处理图片时发生异常", e);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        log.info("批量处理完成，成功处理 {}/{} 张图片，耗时 {} 毫秒，平均 {} 毫秒/张",
                results.size(), imageList.size(), duration,
                imageList.size() > 0 ? duration / imageList.size() : 0);
        return results;
    }

    /**
     * 处理单个图片任务
     */
    @Transactional(rollbackFor = Exception.class)
    public RstObj processImageTask(TrainJdProdImages trainJdProdImages,
                                  TrainJdProducts localJdProd, Long currTeamId, int orderIndex) {
        RstObj result = new RstObj(orderIndex, trainJdProdImages.getImgUrl());
        long taskStartTime = System.currentTimeMillis();

        try {
            // a) 开启事务S2，更新图片状态为同步中
            trainJdProdImages.setSyncStatus(JdSyncStatus.SYNCING.getCode());
            int updateResult = trainJdProdImagesService.updateById(trainJdProdImages);
            if (updateResult <= 0) {
                result.markFailure("更新图片状态失败");
                return result;
            }
            
            // b) 向豆包发起请求
            long imgMdStartTime = System.currentTimeMillis();
            String recoTextWithMD = getImgMdFromService(trainJdProdImages.getImgUrl());
            long imgMdEndTime = System.currentTimeMillis();
            log.debug("图片识别API调用耗时: {}毫秒, imageUrl={}",
                    imgMdEndTime - imgMdStartTime, trainJdProdImages.getImgUrl());

            // c) 生成向量
            long vectorStartTime = System.currentTimeMillis();
            boolean vectorResult = vectorSearchService.processVectorForWare(
                    localJdProd, recoTextWithMD, trainJdProdImages.getImgUrl(), currTeamId);
            long vectorEndTime = System.currentTimeMillis();
            log.debug("向量生成耗时: {}毫秒, imageUrl={}",
                    vectorEndTime - vectorStartTime, trainJdProdImages.getImgUrl());
            
            if (!vectorResult) {
                log.warn("生成向量失败: imageUrl={}", trainJdProdImages.getImgUrl());
            }
            
            // d) 更新图片状态为已同步
            trainJdProdImages.setSyncStatus(JdSyncStatus.SYNCED.getCode());
            trainJdProdImages.setImgRecoText(recoTextWithMD);
            int syncedUpdateResult = trainJdProdImagesService.updateById(trainJdProdImages);
            if (syncedUpdateResult <= 0) {
                log.warn("更新图片同步完成状态失败: imageUrl={}", trainJdProdImages.getImgUrl());
            }
            
            // e) 设置结果
            result.markSuccess(recoTextWithMD);

            long taskEndTime = System.currentTimeMillis();
            long taskDuration = taskEndTime - taskStartTime;
            log.info("图片处理成功: imageUrl={}, orderIndex={}, 耗时={}毫秒",
                    trainJdProdImages.getImgUrl(), orderIndex, taskDuration);
            
        } catch (Exception e) {
            log.error("处理图片失败: imageUrl={}", trainJdProdImages.getImgUrl(), e);
            
            // 回滚图片状态
            try {
                trainJdProdImages.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());
                int rollbackResult = trainJdProdImagesService.updateById(trainJdProdImages);
                if (rollbackResult <= 0) {
                    log.warn("回滚图片状态失败: imageUrl={}", trainJdProdImages.getImgUrl());
                }
            } catch (Exception rollbackEx) {
                log.error("回滚图片状态失败", rollbackEx);
            }
            
            result.markFailure(e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新商品详情
     */
    private void updateProductDetails(TrainJdProducts localJdProd, List<RstObj> finalResultList) {
        try {
            // 4.5 汇总识别结果
            String jdProdDtl = finalResultList.stream()
                    .filter(item -> item != null && item.hasValidResult())
                    .sorted((a, b) -> Integer.compare(a.getOrderIndex(), b.getOrderIndex())) // 按顺序排序
                    .map(RstObj::getRecoTextWithMD)
                    .collect(Collectors.joining(","));
            
            // 更新商品详情
            localJdProd.setJdProdDtl(jdProdDtl);
            localJdProd.setSyncStatus(JdSyncStatus.SYNCED.getCode());
            
            boolean updateResult = trainJdProductsService.updateByIdSelective(localJdProd);
            if (updateResult) {
                log.debug("商品 {} 详情更新成功，识别结果长度: {}", localJdProd.getId(), jdProdDtl.length());
            } else {
                log.warn("商品 {} 详情更新失败", localJdProd.getId());
            }
            
        } catch (Exception e) {
            log.error("更新商品 {} 详情失败", localJdProd.getId(), e);
        }
    }

    /**
     * 更新最终状态
     */
    private void updateFinalStatus(Long shopId) {
        try {
            log.debug("开始更新最终状态: shopId={}", shopId);

            // 5. 更新AccessToken状态
            TrainJdAccessToken accessToken = trainJdAccessTokenService.findByShopId(shopId);
            if (accessToken != null) {
                accessToken.setIsSyncComplete(JdSyncStatus.SYNCED.getCode());
                trainJdAccessTokenService.updateByXid(accessToken);
                log.debug("AccessToken状态更新成功: shopId={}", shopId);
            }

            // 6. 更新TeamShops状态
            TrainTeamShops teamShops = trainTeamShopsService.findByShopId(shopId);
            if (teamShops != null) {
                teamShops.setIsSyncComplete(JdSyncStatus.SYNCED.getCode());
                trainTeamShopsService.updateEntityByShopId(teamShops);
                log.debug("TeamShops状态更新成功: shopId={}", shopId);
            }

        } catch (Exception e) {
            log.error("更新最终状态失败: shopId={}", shopId, e);
        }
    }

    /**
     * 调用JdProductSyncServiceImpl的convertImageToMarkdown方法
     * 直接调用公共方法，避免反射调用导致的代理问题
     */
    private String getImgMdFromService(String imageUrl) {
        try {
            log.debug("开始调用图片识别服务: imageUrl={}", imageUrl);

            // 检查 jdProductSyncService 是否为 null
            if (jdProductSyncServiceUtil == null) {
                log.error("JdProductSyncServiceImpl 服务未正确注入，jdProductSyncService 为 null");
                throw new RuntimeException("JdProductSyncServiceImpl 服务未正确注入");
            }

            // 记录服务实例信息
            log.debug("JdProductSyncServiceImpl 实例信息: class={}, hashCode={}",
                    jdProductSyncServiceUtil.getClass().getName(), jdProductSyncServiceUtil.hashCode());

            // 直接调用公共方法，避免反射调用导致的代理问题
            String result = jdProductSyncServiceUtil.convertImageToMarkdown(imageUrl);

            log.debug("图片识别服务调用完成: imageUrl={}, 结果长度={}", imageUrl, result != null ? result.length() : 0);
            return result;

        } catch (Exception e) {
            log.error("调用图片识别服务失败: imageUrl={}", imageUrl, e);
            throw new RuntimeException("图片识别服务调用失败: " + e.getMessage(), e);
        }
    }
}
