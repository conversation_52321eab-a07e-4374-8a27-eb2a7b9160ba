package com.yiyi.ai_train_playground.scheduler;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.service.impl.RedisDistributedLockService;
import com.yiyi.ai_train_playground.service.jd.JdTokenRefreshService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 京东Token刷新定时任务
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
    name = "spring.task.scheduling.enabled",
    havingValue = "true",
    matchIfMissing = true
)
public class JdTokenRefreshScheduler {

    private final JdTokenRefreshService jdTokenRefreshService;
    private final RedisDistributedLockService redisDistributedLockService;
    private final JdConfig jdConfig;
    
    /**
     * 分布式锁的键名
     */
    private static final String LOCK_KEY = "jd:token:refresh:lock";

    /**
     * 定时刷新京东访问令牌
     * 执行间隔从配置文件读取，默认60分钟
     */
    @Scheduled(fixedRateString = "#{${jd.token-refresh.interval-minutes:60} * 60 * 1000}")
    public void refreshJdTokens() {
        // 生成唯一的锁值
        String lockValue = UUID.randomUUID().toString();
        
        log.info("京东Token刷新定时任务开始执行，尝试获取分布式锁: lockKey={}, lockValue={}", 
                LOCK_KEY, lockValue);
        
        // 获取锁过期时间（秒）- 从配置文件读取，确保在下次任务执行前释放
        long lockExpireSeconds = (jdConfig.getTokenRefresh() != null && jdConfig.getTokenRefresh().getLockExpireMinutes() != null)
                ? jdConfig.getTokenRefresh().getLockExpireMinutes() * 60L : 50 * 60L;

        // 尝试获取分布式锁
        boolean lockAcquired = redisDistributedLockService.tryLock(LOCK_KEY, lockValue, lockExpireSeconds);
        
        if (!lockAcquired) {
            log.info("获取分布式锁失败，可能有其他实例正在执行京东Token刷新任务，本次跳过");
            return;
        }
        
        try {
            log.info("成功获取分布式锁，开始执行京东Token刷新任务");
            
            // 执行刷新任务
            int refreshedCount = jdTokenRefreshService.refreshAllTokens();
            
            log.info("京东Token刷新定时任务执行完成，成功刷新 {} 个令牌", refreshedCount);
            
        } catch (Exception e) {
            log.error("京东Token刷新定时任务执行时发生异常", e);
        } finally {
            // 释放分布式锁
            boolean unlocked = redisDistributedLockService.unlock(LOCK_KEY, lockValue);
            if (unlocked) {
                log.info("成功释放分布式锁: lockKey={}, lockValue={}", LOCK_KEY, lockValue);
            } else {
                log.warn("释放分布式锁失败（锁可能已过期）: lockKey={}, lockValue={}", LOCK_KEY, lockValue);
            }
        }
    }
}
