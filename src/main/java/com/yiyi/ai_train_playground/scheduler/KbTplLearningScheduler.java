package com.yiyi.ai_train_playground.scheduler;

import com.yiyi.ai_train_playground.service.impl.RedisDistributedLockService;
import com.yiyi.ai_train_playground.service.converkb.KbTplLearningService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 知识库模板学习定时任务
 *
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "kb.tpl-learning.enabled", havingValue = "true", matchIfMissing = true)
public class KbTplLearningScheduler {

    private final KbTplLearningService kbTplLearningService;
    private final RedisDistributedLockService redisDistributedLockService;
    
    /**
     * 分布式锁的键名
     */
    private static final String LOCK_KEY = "kb:tpl:learning:lock";
    
    /**
     * 锁过期时间（秒）- 30分钟
     */
    private static final long LOCK_EXPIRE_SECONDS = 30 * 60L;

    /**
     * 定时处理知识库模板学习任务
     * 执行间隔从配置文件读取，默认10分钟
     */
    @Scheduled(fixedRateString = "#{${kb.tpl-learning.interval-minutes:10} * 60 * 1000}")
    public void processKbTplLearning() {
        // 生成唯一的锁值
        String lockValue = UUID.randomUUID().toString();
        
        log.info("知识库模板学习定时任务开始执行，尝试获取分布式锁: lockKey={}, lockValue={}", 
                LOCK_KEY, lockValue);

        // 尝试获取分布式锁
        boolean lockAcquired = redisDistributedLockService.tryLock(LOCK_KEY, lockValue, LOCK_EXPIRE_SECONDS);
        
        if (!lockAcquired) {
            log.info("获取分布式锁失败，可能有其他实例正在执行知识库模板学习任务，本次跳过");
            return;
        }
        
        try {
            log.info("成功获取分布式锁，开始执行知识库模板学习任务");

            // 执行学习任务
            KbTplLearningService.KbTplLearningResult result = kbTplLearningService.processKbTplLearning();

            log.info("知识库模板学习定时任务执行完成，成功处理 {} 条记录，总token消耗: {}",
                    result.getProcessedCount(), result.getTotalTokens());
            
        } catch (Exception e) {
            log.error("知识库模板学习定时任务执行时发生异常", e);
        } finally {
            // 释放分布式锁
            boolean unlocked = redisDistributedLockService.unlock(LOCK_KEY, lockValue);
            if (unlocked) {
                log.info("成功释放分布式锁: lockKey={}, lockValue={}", LOCK_KEY, lockValue);
            } else {
                log.warn("释放分布式锁失败（锁可能已过期）: lockKey={}, lockValue={}", LOCK_KEY, lockValue);
            }
        }
    }
}
