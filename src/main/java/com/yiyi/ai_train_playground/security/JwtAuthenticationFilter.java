package com.yiyi.ai_train_playground.security;

import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final UserDetailsService userDetailsService;
    private final StaffUserDetailsService staffUserDetailsService;

    public JwtAuthenticationFilter(JwtUtil jwtUtil, 
                                 @Qualifier("customUserDetailsService") UserDetailsService userDetailsService,
                                 StaffUserDetailsService staffUserDetailsService) {
        this.jwtUtil = jwtUtil;
        this.userDetailsService = userDetailsService;
        this.staffUserDetailsService = staffUserDetailsService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String token = extractTokenFromRequest(request);
        
        if (token != null) {
            try {
                // 验证token并获取用户信息
                if (jwtUtil.validateToken(token)) {
                    String username = jwtUtil.getUsernameFromToken(token);
                    Long userId = jwtUtil.getUserIdFromToken(token);
                    Long teamId = jwtUtil.getTeamIdFromToken(token);

                    UserDetails userDetails = null;

                    // 如果username是以ano_开头的，直接构造假的userDetails
                    if (username != null && username.startsWith("ano_")) {
                        userDetails = new CustomUserDetails(
                            userId,      // 使用从token中获取的userId
                            username,    // 使用从token中获取的username
                            null,        // passwordHash设为null
                            teamId,      // 使用从token中获取的teamId
                            true,        // enabled设为true
                            false        // accountNonLocked设为false
                        );
                        log.debug("检测到匿名用户Token，用户名: {}, userId: {}, teamId: {}", username, userId, teamId);
                    } else {
                        // 根据userId判断是员工还是普通用户
                        // 员工登录时，userId实际上是员工ID（通过staff.getId()设置）
                        // 可以通过尝试加载员工信息来判断
                        try {
                            // 首先尝试作为员工加载
                            userDetails = staffUserDetailsService.loadUserByStaffId(userId);
                            log.debug("检测到员工Token，员工ID: {}, 用户名: {}", userId, username);
                        } catch (Exception e) {
                            // 如果员工加载失败，则作为普通用户加载
                            try {
                                userDetails = userDetailsService.loadUserByUsername(username);
                                log.debug("检测到普通用户Token，用户名: {}", username);
                            } catch (Exception ex) {
                                log.warn("无法加载用户信息，用户名: {}, 错误: {}", username, ex.getMessage());
                            }
                        }
                    }

                    if (userDetails != null) {
                        // 使用自定义的JwtAuthenticationToken来存储userId和teamId
                        JwtAuthenticationToken authentication =
                            new JwtAuthenticationToken(userDetails, null, userDetails.getAuthorities(), userId, teamId);
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        log.debug("JWT认证成功，用户: {}, userId: {}, teamId: {}", username, userId, teamId);
                    }
                }
            } catch (ExpiredJwtException e) {
                log.warn("JWT token已过期: {}", e.getMessage());
                // 不设置认证信息，让Spring Security的认证入口点处理
            } catch (JwtException e) {
                log.warn("JWT token无效: {}", e.getMessage());
                // 不设置认证信息，让Spring Security的认证入口点处理
            } catch (Exception e) {
                log.error("JWT认证过程中发生异常: {}", e.getMessage());
                // 不设置认证信息，让Spring Security的认证入口点处理
            }
        }
        
        filterChain.doFilter(request, response);
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
} 