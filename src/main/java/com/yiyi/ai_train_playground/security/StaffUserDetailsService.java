package com.yiyi.ai_train_playground.security;

import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 员工用户详情服务
 * 用于JWT认证时加载员工用户信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Service("staffUserDetailsService")
public class StaffUserDetailsService implements UserDetailsService {

    private final TrainStaffMapper trainStaffMapper;

    public StaffUserDetailsService(TrainStaffMapper trainStaffMapper) {
        this.trainStaffMapper = trainStaffMapper;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 使用员工登录专用方法查询（不限制团队）
        TrainStaff staff = trainStaffMapper.selectByUsernameForLogin(username);
        if (staff == null) {
            throw new UsernameNotFoundException("员工不存在: " + username);
        }

        return new CustomUserDetails(
            staff.getId(),           // 使用员工ID作为userId
            staff.getUsername(),
            staff.getPasswordHash(),
            staff.getTeamId(),
            !Boolean.TRUE.equals(staff.getIsLocked()),  // enabled
            staff.getFailedAttempts() == null || staff.getFailedAttempts() < 5  // accountNonLocked
        );
    }

    /**
     * 根据员工ID加载用户详情
     * @param staffId 员工ID
     * @return UserDetails
     */
    public UserDetails loadUserByStaffId(Long staffId) throws UsernameNotFoundException {
        TrainStaff staff = trainStaffMapper.selectById(staffId);
        if (staff == null) {
            throw new UsernameNotFoundException("员工不存在: ID=" + staffId);
        }

        return new CustomUserDetails(
            staff.getId(),           // 使用员工ID作为userId
            staff.getUsername(),
            staff.getPasswordHash(),
            staff.getTeamId(),
            !Boolean.TRUE.equals(staff.getIsLocked()),  // enabled
            staff.getFailedAttempts() == null || staff.getFailedAttempts() < 5  // accountNonLocked
        );
    }
}