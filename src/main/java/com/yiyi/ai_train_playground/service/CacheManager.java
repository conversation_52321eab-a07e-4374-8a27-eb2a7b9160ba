package com.yiyi.ai_train_playground.service;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 缓存管理器接口
 * 抽象缓存操作，支持Redis等具体实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-13
 */
public interface CacheManager {
    
    // ==================== Value 操作 ====================
    
    /**
     * 存储对象值
     * 
     * @param key 缓存键
     * @param value 缓存值
     */
    void put(String key, Object value);
    
    /**
     * 存储对象值（带过期时间）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void put(String key, Object value, long timeout, TimeUnit unit);
    
    /**
     * 存储对象值（带过期时间）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param duration 过期时间
     */
    void put(String key, Object value, Duration duration);
    
    /**
     * 获取对象值
     * 
     * @param key 缓存键
     * @return 缓存值，可能为null
     */
    Object get(String key);
    
    /**
     * 获取指定类型的对象值
     * 
     * @param <T> 返回类型
     * @param key 缓存键
     * @param type 期望的返回类型
     * @return 缓存值，如果不存在或类型不匹配则为空
     */
    <T> Optional<T> get(String key, Class<T> type);
    
    // ==================== Hash 操作 ====================
    
    /**
     * 批量存储Hash字段
     * 
     * @param key Hash键
     * @param hash 字段映射
     */
    void putHash(String key, Map<String, Object> hash);
    
    /**
     * 批量存储Hash字段（带过期时间）
     * 
     * @param key Hash键
     * @param hash 字段映射
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void putHash(String key, Map<String, Object> hash, long timeout, TimeUnit unit);
    
    /**
     * 获取Hash所有字段
     * 
     * @param key Hash键
     * @return 字段映射，如果不存在则为空Map
     */
    Map<Object, Object> getHash(String key);
    
    /**
     * 获取Hash单个字段
     * 
     * @param key Hash键
     * @param hashKey 字段键
     * @return 字段值，可能为null
     */
    Object getHashField(String key, String hashKey);
    
    // ==================== Key 操作 ====================
    
    /**
     * 检查键是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);
    
    /**
     * 删除键
     * 
     * @param key 缓存键
     * @return 是否删除成功
     */
    boolean delete(String key);
    
    /**
     * 设置键的过期时间
     * 
     * @param key 缓存键
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return 是否设置成功
     */
    boolean expire(String key, long timeout, TimeUnit unit);
    
    /**
     * 设置键的过期时间
     * 
     * @param key 缓存键
     * @param duration 过期时间
     * @return 是否设置成功
     */
    boolean expire(String key, Duration duration);
    
    /**
     * 获取键的剩余生存时间
     *
     * @param key 缓存键
     * @return 剩余生存时间（秒），-1表示永不过期，-2表示键不存在
     */
    long getExpire(String key);

    // ==================== List 操作（栈支持） ====================

    /**
     * 向列表左侧推入元素（栈顶）
     *
     * @param key 列表键
     * @param value 要推入的值
     * @return 推入后列表的长度
     */
    Long leftPush(String key, Object value);

    /**
     * 向列表右侧推入元素（队列尾部）
     *
     * @param key 列表键
     * @param value 要推入的值
     * @return 推入后列表的长度
     */
    Long rightPush(String key, Object value);

    /**
     * 从列表左侧弹出元素（栈顶）
     *
     * @param key 列表键
     * @return 弹出的元素，如果列表为空则返回null
     */
    Object leftPop(String key);

    /**
     * 获取列表长度
     *
     * @param key 列表键
     * @return 列表长度，如果键不存在则返回0
     */
    Long getListSize(String key);

    /**
     * 获取列表指定范围的元素
     *
     * @param key 列表键
     * @param start 开始索引
     * @param end 结束索引
     * @return 元素列表
     */
    List<Object> getListRange(String key, long start, long end);
}