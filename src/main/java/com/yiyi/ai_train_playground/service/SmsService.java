package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;

/**
 * 短信验证码服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface SmsService {
    
    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @return 验证密钥
     */
    SmsCodeResponse sendVerificationCode(String phone);
    
    /**
     * 验证短信验证码
     * 
     * @param verificationKey 验证密钥
     * @param verificationCode 验证码
     * @return 是否验证成功
     */
    boolean verifyCode(String verificationKey, String verificationCode);
    
    /**
     * 短信验证码登录（验证成功后自动创建团队和用户）
     * 
     * @param verificationKey 验证密钥
     * @param verificationCode 验证码
     * @param phone 手机号
     * @return 登录响应信息
     */
    SmsLoginResponse smsLogin(String verificationKey, String verificationCode, String phone);
} 