package com.yiyi.ai_train_playground.service.bm;

import com.yiyi.ai_train_playground.dto.task.ChatRoomTaskInfo4Conv;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;

import java.util.Map;

/**
 * 知识库专用大模型服务接口
 * 专门用于处理知识库相关的对话和会话管理
 */
public interface BmForKbService {

    /**
     * 初始化会话（支持自定义系统提示词）
     *
     * @param sceneName             场景名称
     * @param servicerId            客服ID（可选，如果为空则从JWT获取）
     * @param token                 JWT令牌
     * @param isThinking            是否使用思考模型
     * @param isStreaming           是否使用流式响应
     * @param systemPrompt          自定义系统提示词，如果为null则使用默认的
     * @param externalProductId     第一个产品的ID
     * @param prodType              产品类型
     * @param fakeRobotToken
     * @param qaSimplelDtoWithUUID
     * @param chatRoomTaskInfo4Conv
     * @return 包含sessionId和robotName的Map
     */
    Map<String, Object> initSession(String sceneName, String servicerId, String token,
                                    Boolean isThinking, Boolean isStreaming, String systemPrompt,
                                    String externalProductId, Integer prodType, String fakeRobotToken, QaSimplelDtoWithUUID qaSimplelDtoWithUUID, ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv);

    /**
     * 清除会话资源
     * @param sessionId 会话ID
     */
    void clearSession(String sessionId);

    /**
     * 处理并响应消息（非流式版本）
     * 专用于知识库场景的消息处理
     *
     * @param sessionId   会话ID
     * @param userMessage 用户消息
     * @param msgId
     * @return 非流式响应字符串
     */
    String handlerAndRespWithNTNS(String sessionId, String userMessage, String msgId);
}