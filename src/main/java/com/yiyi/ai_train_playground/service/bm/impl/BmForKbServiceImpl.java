package com.yiyi.ai_train_playground.service.bm.impl;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.dto.bm.IntentDto;
import com.yiyi.ai_train_playground.dto.task.ChatRoomTaskInfo4Conv;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.enums.SceneName;
import com.yiyi.ai_train_playground.enums.TaskPurposeTag;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import com.yiyi.ai_train_playground.service.bm.BmForKbService;
import com.yiyi.ai_train_playground.service.impl.DoubaoBigModelServiceImpl;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatLogService;
import com.yiyi.ai_train_playground.enums.SenderType;
import com.yiyi.ai_train_playground.service.task.FakeRobotService;
import com.yiyi.ai_train_playground.service.task.impl.Qa2StackServiceImpl;
import com.yiyi.ai_train_playground.util.JwtUtil;
import com.yiyi.ai_train_playground.consts.CONSTS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import com.yiyi.ai_train_playground.config.QdrantConfig;


/**
 * 知识库专用大模型服务实现类
 * 基于BigModelManager复制创建，专门用于知识库场景
 */
@Slf4j
@Service
public class BmForKbServiceImpl implements BmForKbService {

    @Autowired
    private DoubaoBigModelServiceImpl doubaoBigModelService;

    @Autowired
    private TrainTeamMapper trainTeamMapper;

    @Autowired
    private JwtUtil jwtUtil;


    @Autowired
    private ObjectMapper objectMapper;


    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private QdrantConfig.QdrantSearchConfig qdrantSearchConfig;

    @Autowired
    private TrainConvWinchatLogService trainConvWinchatLogService;

    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;

    @Autowired
    @Qualifier("fakeRobotServiceImpl")
    private FakeRobotService fakeRobotService;

    @Autowired
    private Qa2StackServiceImpl qa2StackService;

    @Value("${product.processing.collection-name:train_prod_collection}")
    private String collectionName;

    // Redis TTL 设置为24小时
    private static final long REDIS_TTL_HOURS = 24;

    // Redis Key 前缀常量
    private static final String SESSION_KEY_PREFIX = "session:";
    private static final String CHATLOG_KEY_PREFIX = "chatlog:";


    // 固定的角色模板（后期从数据库加载）
    private static final List<String> ROLES = Arrays.asList(
            "买洗碗机的客户，对产品功能、价格、售后服务都有疑问",
            "买冰箱的客户，关心冰箱的容量、能耗、保鲜效果",
            "买电脑的客户，需要咨询配置、性能、适用场景",
            "买小米SU7的客户，对电动车的续航、充电、智能功能感兴趣"
    );


    /**
     * 初始化会话（支持自定义系统提示词）
     *
     * @param sceneName             场景名称
     * @param servicerId            客服ID（可选，如果为空则从JWT获取）
     * @param token                 JWT令牌
     * @param isThinking            是否使用思考模型
     * @param isStreaming           是否使用流式响应
     * @param systemPrompt          自定义系统提示词，如果为null则使用默认的
     * @param externalProductId     第一个产品的ID
     * @param prodType
     * @param fakeRobotToken
     * @param qaSimplelDtoWithUUID
     * @param chatRoomTaskInfo4Conv
     * @return 包含sessionId和robotName的Map
     */
    @Override
    public Map<String, Object> initSession(String sceneName, String servicerId, String token, Boolean isThinking, Boolean isStreaming, String systemPrompt, String externalProductId, Integer prodType, String fakeRobotToken, QaSimplelDtoWithUUID qaSimplelDtoWithUUID, ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv) {
        return initSingleRobot(sceneName, servicerId, token, -1, isThinking, isStreaming, systemPrompt, externalProductId, prodType, fakeRobotToken, qaSimplelDtoWithUUID, chatRoomTaskInfo4Conv);
    }

    /**
     * 获取上下文ID
     *
     * @param systemPrompt 系统提示词
     * @return 上下文结果对象
     */
    private ContextResult getContextId(String systemPrompt) {
        return doubaoBigModelService.generateContextId(systemPrompt);
    }

    /**
     * 清除会话资源
     *
     * @param sessionId 会话ID
     */
    @Override
    public void clearSession(String sessionId) {
        try {
            log.info("开始清除会话资源: sessionId={}", sessionId);

            // 删除聊天记录
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;
            boolean chatLogDeleted = cacheManager.delete(chatLogKey);
            log.info("删除聊天记录: {}, success={}", chatLogKey, chatLogDeleted);

            // 删除会话信息
            String sessionKey = SESSION_KEY_PREFIX + sessionId;
            boolean sessionDeleted = cacheManager.delete(sessionKey);
            log.info("删除会话信息: {}, success={}", sessionKey, sessionDeleted);

            log.info("会话资源清除完成: sessionId={}", sessionId);
        } catch (Exception e) {
            log.error("清除会话资源失败: sessionId={}", sessionId, e);
            // 不抛出异常，避免影响连接关闭流程
        }
    }

    /**
     * 初始化单个机器人会话
     *
     * @param sceneName             场景名称
     * @param servicerId            客服ID（可选，如果为空则从JWT获取）
     * @param token                 JWT令牌
     * @param roleIndex             角色索引，-1表示随机选择
     * @param isThinking            是否使用思考模型
     * @param isStreaming           是否使用流式响应
     * @param systemPrompt          自定义系统提示词，如果为null则使用默认的
     * @param externalProductId     第1个产品的ID
     * @param prodType
     * @param fakeRobotToken
     * @param qaSimplelDtoWithUUID
     * @param chatRoomTaskInfo4Conv
     * @return 包含sessionId和robotName的Map
     */
    private Map<String, Object> initSingleRobot(String sceneName, String servicerId, String token, int roleIndex, Boolean isThinking, Boolean isStreaming, String systemPrompt, String externalProductId, Integer prodType, String fakeRobotToken, QaSimplelDtoWithUUID qaSimplelDtoWithUUID, ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv) {
        try {
            SceneName scene = SceneName.fromCode(sceneName);

            // 获取客服ID
            String realServiceId = servicerId;
            if (realServiceId == null || realServiceId.isEmpty()) {
                Long userId = jwtUtil.getUserIdFromToken(token);
                realServiceId = userId != null ? userId.toString() : "default";
            }

            // 生成机器人ID和会话ID
            String robotId = UUID.randomUUID().toString().substring(0, 8);
            String sessionId = robotId + "_" + realServiceId;

            // 生成机器人名称
            String robotName = generateRobotName(scene, roleIndex);

            // 获取客服名称（团队名称）
            String serviceName = getServiceName(token);

            // 生成系统提示词
            String systemPromptToUse = systemPrompt != null ? systemPrompt : generateSystemPrompt(roleIndex);

            List<ChatMessage> messages = new ArrayList<>();

            // 调用getContextId生成上下文ID
            String contextId = "";
            if (TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                ContextResult contextResult = getContextId(systemPromptToUse);
                contextId = contextResult.getId();
            } else {
                contextId = fakeRobotToken;
            }

            // 获取teamId从token中
            Long teamId = jwtUtil.getTeamIdFromToken(token);

            // 存储会话信息到Redis，包括contextId
            try {
                // 封装后的方法调用，替代直接操作Redis的代码
                saveSessionToRedis(sessionId, systemPromptToUse, robotName, serviceName,
                        isThinking, isStreaming, externalProductId, teamId, new ArrayList<>(), contextId, prodType, qaSimplelDtoWithUUID, chatRoomTaskInfo4Conv, fakeRobotToken);

                log.info("会话信息已存储到Redis: sessionId={}, contextId={}", sessionId, contextId);
            } catch (Exception e) {
                log.error("存储会话信息到Redis失败: sessionId={}", sessionId, e);
                // 不抛出异常，继续执行，保证业务不受Redis影响
            }

            // 生成firstMessage（使用新的逻辑）
            String firstMessageWithJson;
            if (TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
                    //这个地方不能用上游传来的contextId，而是使用一次性的方式获取
                    String prmtOnce=getSysPrmtOnce(qaSimplelDtoWithUUID.getQuestion());

                    // 构建系统消息
                    ChatMessage sysMessage = ChatMessage.builder()
                            .role(ChatMessageRole.SYSTEM)
                            .content(prmtOnce)
                            .build();
                    messages.add(sysMessage);

                    // 构建用户消息
                    /*ChatMessage userMessage = ChatMessage.builder()
                            .role(ChatMessageRole.USER)
                            .content("")
                            .build();
                    messages.add(userMessage);*/

                    // 使用带上下文的方法
//                    firstMessageWithJson = doubaoBigModelService.ntnsWithCtx(messages, contextId);//其实这个时候没有json，就是普通的字符串

                    firstMessageWithJson = doubaoBigModelService.ntnsOnce(prmtOnce, "");//其实这个时候没有json，就是普通的字符串
                } else {
                    // 如果没有自定义系统提示词，使用默认的假消息
                    firstMessageWithJson = generateFirstMessage();
                }
            } else {
                firstMessageWithJson = fakeRobotService.getNextBuyerMessage(contextId);
            }


            //get firstMessage get rid of json
            String firstMessageWithoutJson = processResponseContent(firstMessageWithJson);

            // 将firstMessage添加到会话历史中
            List<ChatMessage> sessionMessages = new ArrayList<>();
            sessionMessages.add(ChatMessage.builder()
                    .role(ChatMessageRole.ASSISTANT)
                    .content(firstMessageWithoutJson)//remove json before save by germmy@20250819
                    .build());
            addChatMessagesToRedis(sessionId, sessionMessages);

            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("robotName", robotName);
            result.put("serviceName", serviceName);
            result.put("firstMessage", firstMessageWithJson);
            result.put("firstMessageWithoutJson", firstMessageWithoutJson);

            //add bygermmy @20250827
            if (TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()) {
                result.put("isFirstFreq", true);
                result.put("msgId", qaSimplelDtoWithUUID.getUuid());
            }

            log.info("初始化会话成功: sessionId={}, robotName={}, serviceName={}",
                    sessionId, robotName, serviceName);

            return result;
        } catch (Exception e) {
            log.error("初始化会话失败", e);
            throw new RuntimeException("初始化会话失败: " + e.getMessage());
        }
    }

    private String getSysPrmtOnce(String question) {
        try {
            // 4.1.1 获取提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_CONVERT_FREQ_FIRST_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到转换频次标题提示词模板，关键词：{}", CONSTS.DEFAULT_CONVERT_FREQ_FIRST_KEYWORD);
                return "";
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到提示词模板：{}", template);

            // 4.1.2 进行变量替换，用question替换{{ques}}
            String result = template.replace("{{ques}}", question != null ? question : "");;
            log.info("变量替换后的提示词：{}", result);

            return result;

        } catch (Exception e) {
            log.error("生成系统提示词失败", e);
            return "";
        }
    }

    /**
     * 将会话信息保存到Redis
     *
     * @param sessionId             会话ID
     * @param systemPrompt          系统提示词
     * @param robotName             机器人名称
     * @param serviceName           客服名称
     * @param isThinking            是否使用思考模型
     * @param isStreaming           是否使用流式响应
     * @param externalProductId     外部产品ID
     * @param teamId                团队ID
     * @param messages              初始消息列表
     * @param contextId             豆包缓存上下文ID
     * @param prodType
     * @param qaSimplelDtoWithUUID
     * @param chatRoomTaskInfo4Conv
     * @param fakeRobotToken
     */
    private void saveSessionToRedis(String sessionId, String systemPrompt, String robotName,
                                    String serviceName, Boolean isThinking, Boolean isStreaming,
                                    String externalProductId, Long teamId, List<ChatMessage> messages, String contextId, Integer prodType, QaSimplelDtoWithUUID qaSimplelDtoWithUUID, ChatRoomTaskInfo4Conv chatRoomTaskInfo4Conv, String fakeRobotToken) {
        // 1. 存储聊天历史到Redis：chatlog:{sessionId}
        String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;
        cacheManager.put(chatLogKey, messages, REDIS_TTL_HOURS, TimeUnit.HOURS);

        // 2. 存储会话配置信息到Redis hash：session:{sessionId}
        String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
        Map<String, Object> sessionConfig = new HashMap<>();

        // 基本会话配置
        sessionConfig.put("sessionSystemPrompts", systemPrompt);
        sessionConfig.put("sessionRobotNames", robotName);
        sessionConfig.put("sessionServiceNames", serviceName);

        // 模型配置
        sessionConfig.put("isThinking", isThinking != null ? isThinking : false);
        sessionConfig.put("isStreaming", isStreaming != null ? isStreaming : false);

        // 可选配置
        if (externalProductId != null) {
            sessionConfig.put("externalProductId", externalProductId);
        }

        // add by djwan 2027.7.30
        if (prodType != null) {
            sessionConfig.put("prodType", prodType);
        }

        if (teamId != null) {
            sessionConfig.put("teamId", teamId.toString());
        }

        //添加上下文缓存ID
        sessionConfig.put("sessionContextId", contextId);

        //添加msgId。这个一定是为首轮服务的吗？好像也不是，只要编号对上，谁都可以  add by germmy@20250827
        if(qaSimplelDtoWithUUID != null && TaskPurposeTag.TRAINING.getCode() == chatRoomTaskInfo4Conv.getTaskPurposeTag()){
            cacheManager.put(qaSimplelDtoWithUUID.getUuid(), qaSimplelDtoWithUUID, REDIS_TTL_HOURS, TimeUnit.HOURS);
        }
        sessionConfig.put("taskPurposeTag", chatRoomTaskInfo4Conv.getTaskPurposeTag());
        sessionConfig.put("fakeRobotToken", fakeRobotToken);


        // 批量保存会话配置（带过期时间）
        cacheManager.putHash(sessionConfigKey, sessionConfig, REDIS_TTL_HOURS, TimeUnit.HOURS);

        log.debug("会话配置已保存到Redis: sessionId={}, configCount={}", sessionId, sessionConfig.size());
    }

    /**
     * 生成机器人名称
     *
     * @param scene     场景
     * @param roleIndex 角色索引，-1表示随机
     */
    private String generateRobotName(SceneName scene, int roleIndex) {
        String roleDescription;
        if (roleIndex >= 0 && roleIndex < ROLES.size()) {
            // 根据角色生成描述性名称
            String[] roleNames = {"洗碗机客户", "冰箱客户", "电脑客户", "小米SU7客户"};
            roleDescription = roleNames[roleIndex];
        } else {
            roleDescription = "随机客户" + (new Random().nextInt(999) + 1);
        }

        if (scene == SceneName.TRIAL_ONE) {
            return "试用-" + roleDescription;
        } else if (scene == SceneName.TRIAL_FOUR) {
            // 这里将从数据库中读取
            return "随机-" + roleDescription;
        } else if (scene == SceneName.FORMAL) {
            // 这里将从数据库中读取
            return "正式-" + roleDescription;
        } else {
            return roleDescription;
        }
    }

    /**
     * 获取客服名称（从团队表获取）
     */
    private String getServiceName(String token) {
        try {
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId != null) {
                TrainTeam team = trainTeamMapper.findById(teamId);
                if (team != null) {
                    return team.getName();
                }
            }
            return "客服";
        } catch (Exception e) {
            log.warn("获取团队名称失败", e);
            return "客服";
        }
    }

    /**
     * 生成系统提示词
     *
     * @param roleIndex 角色索引，-1表示随机选择
     */
    private String generateSystemPrompt(int roleIndex) {
        String role;
        if (roleIndex >= 0 && roleIndex < ROLES.size()) {
            // 使用指定的角色
            role = ROLES.get(roleIndex);
        } else {
            // 随机选择一个角色
            role = ROLES.get(new Random().nextInt(ROLES.size()));
        }
        // 这里将从数据库加载
        return "你模拟一个客户，扮演成以下" + role + "，和客服聊天";
    }

    /**
     * 生成机器人首次发言
     */
    private String generateFirstMessage() {
        List<String> firstMessages = Arrays.asList(
                "你好，我想咨询一下产品的相关问题",
                "请问这个产品有什么特色功能吗？",
                "我对你们的产品很感兴趣，能详细介绍一下吗？",
                "你好，能帮我推荐一款合适的产品吗？"
        );
        return firstMessages.get(new Random().nextInt(firstMessages.size()));
    }

    // ==================== Redis 操作封装方法 ====================

    /**
     * 检查会话是否存在（从Redis中检查）
     *
     * @param sessionId 会话ID
     * @return 是否存在
     */
    private boolean isSessionExistInRedis(String sessionId) {
        try {
            String sessionKey = SESSION_KEY_PREFIX + sessionId;
            return cacheManager.exists(sessionKey);
        } catch (Exception e) {
            log.error("检查会话是否存在失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    /**
     * 从Redis获取聊天历史
     *
     * @param sessionId 会话ID
     * @return 聊天历史消息列表
     */
    @SuppressWarnings("unchecked")
    private List<ChatMessage> getChatHistoryFromRedis(String sessionId) {
        try {
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;
            Object chatHistoryObj = cacheManager.get(chatLogKey);
            if (chatHistoryObj != null) {
                // 如果是List类型，直接转换
                if (chatHistoryObj instanceof List) {
                    return (List<ChatMessage>) chatHistoryObj;
                }
                // 如果是字符串，尝试JSON反序列化
                String jsonStr = chatHistoryObj.toString();
                return objectMapper.readValue(jsonStr, new TypeReference<List<ChatMessage>>() {
                });
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("从Redis获取聊天历史失败: sessionId={}", sessionId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将聊天消息添加到Redis
     *
     * @param sessionId 会话ID
     * @param message   聊天消息
     */
    private void addChatMessageToRedis(String sessionId, ChatMessage message) {
        try {
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;

            // 获取现有的聊天历史
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);

            // 添加新消息
            messages.add(message);

            // 保存回Redis
            cacheManager.put(chatLogKey, messages, REDIS_TTL_HOURS, TimeUnit.HOURS);

            log.debug("成功将消息添加到Redis: sessionId={}, messageRole={}", sessionId, message.getRole());
        } catch (Exception e) {
            log.error("将聊天消息添加到Redis失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 批量将聊天消息添加到Redis
     *
     * @param sessionId   会话ID
     * @param newMessages 新的聊天消息列表
     */
    private void addChatMessagesToRedis(String sessionId, List<ChatMessage> newMessages) {
        try {
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;

            // 获取现有的聊天历史
            List<ChatMessage> existingMessages = getChatHistoryFromRedis(sessionId);

            // 添加新消息
            existingMessages.addAll(newMessages);

            // 保存回Redis
            cacheManager.put(chatLogKey, existingMessages, REDIS_TTL_HOURS, TimeUnit.HOURS);

            log.debug("成功将{}条消息添加到Redis: sessionId={}", newMessages.size(), sessionId);
        } catch (Exception e) {
            log.error("将聊天消息批量添加到Redis失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 处理并响应消息（非流式版本）
     * 复制自handlerAndResponse方法，但返回String而不是Flux<String>
     * 这段代码我感觉还是要重构，写的太啰嗦了，但功能上没有问题
     *
     * @param sessionId   会话ID
     * @param userMessage 用户消息
     * @param msgId
     * @return 非流式响应字符串
     */
    @Override
    public String handlerAndRespWithNTNS(String sessionId, String userMessage, String msgId) {
        try {
            // 检查会话是否存在
            if (!isSessionExistInRedis(sessionId)) {
                throw new RuntimeException("会话不存在: " + sessionId);
            }

            // 2.2、从redis中取出teamId、externalProductId
            String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
            Map<Object, Object> sessionFromRedis = cacheManager.getHash(sessionConfigKey);
            String teamId = (String) sessionFromRedis.get("teamId");
            String externalProductId = (String) sessionFromRedis.get("externalProductId");
            //从session里面获取prodType add by germmy
            Integer prodType = (Integer) sessionFromRedis.get("prodType");
            Integer taskPurposeTag = (Integer) sessionFromRedis.get("taskPurposeTag");
            String fakeRobotToken = (String) sessionFromRedis.get("fakeRobotToken");

            //从session里面获取msgId的对象 bygermmy @20250827
            QaSimplelDtoWithUUID qaSimplelDtoWithUUID = (QaSimplelDtoWithUUID) cacheManager.get(msgId);

            log.info("从Redis获取会话配置: teamId={}, externalProductId={}", teamId, externalProductId);

            // 2.3、进行向量检索获取相关内容
            String trueAnswerFromQdrant = "";

            // 2.4 获取最近的机器人回复消息
            String lastRobotResponse = getLastResponseFromRobot(sessionId);
            String searchText = lastRobotResponse != null ? lastRobotResponse : userMessage;

            if (TaskPurposeTag.TRAINING.getCode() != taskPurposeTag) {//0:非高频知识库才进行向量检索
                // 仅当teamId和externalProductId都存在时才进行向量搜索
                //暂时去掉向量检索 modby germmy@20250817
                if (teamId != null && externalProductId != null) {
                    log.info("开始向量检索，teamId: {}, externalProductId: {}, 搜索文本来源: {}", teamId, externalProductId, lastRobotResponse != null ? "最近机器人回复" : "用户消息");
                    trueAnswerFromQdrant = vectorSearchService.searchByText(searchText, teamId, externalProductId, qdrantSearchConfig.getLimit(), prodType, qdrantSearchConfig.getMinScore());

                    if (!trueAnswerFromQdrant.isEmpty()) {
                        log.info("向量检索完成，找到相关内容长度: {}", trueAnswerFromQdrant.length());
                    } else {
                        log.info("向量检索未找到相关内容");
                    }
                } else {
                    log.info("跳过向量检索，缺少必要参数: teamId={}, externalProductId={}", teamId, externalProductId);
                }
            }

            // 2、从Redis获取现有的会话历史
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);

            // 3、将本轮用户消息存入Redis
            ChatMessage userChatMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(userMessage)
                    .build();
            addChatMessageToRedis(sessionId, userChatMessage);


            // 将用户消息也添加到当前消息列表中用于模型调用
            messages.add(userChatMessage);

            //从redis中获取
            boolean isThinking = (Boolean) sessionFromRedis.get("isThinking");
            boolean isStreaming = (Boolean) sessionFromRedis.get("isStreaming");

            //从redis中获取缓存上下文
            String contextId = (String) sessionFromRedis.get("sessionContextId");

            // 创建完整的消息列表副本用于传递给大模型
            //副本的目的就是剥离知识库中冗长的内容，因为对会话历史来说没有意义，只针对当前会话才有意义 @germmy 2025.08.05
            List<ChatMessage> messagesForModel = new ArrayList<>(messages);

            String contextContent = null, contextMessage = null;
            if (TaskPurposeTag.TRAINING.getCode() != taskPurposeTag) {//0:非高频知识库才进行向量检索
                // 2.2.4、messagesForModel需要新加一个trueAnswerFromQdrant
                contextContent = !trueAnswerFromQdrant.isEmpty() ? trueAnswerFromQdrant : "知识库内容为空";
                contextMessage = " 商品参考答案如下面3个#号括起来的: ### " + contextContent + " ###";
                // 暂时不搞向量了 modby germmy@20250817
                log.info("已添加{}上下文到消息列表", !trueAnswerFromQdrant.isEmpty() ? "向量检索" : "'知识库内容为空'");
                // 强制使用非流式ntns方法
                // TODO 2 - 实现意图识别逻辑
                IntentDto intentDto = processWithIntentRecognition(searchText, userMessage, messagesForModel, contextId);

                // 1.将用户消息+知识库存入数据库（移动到意图识别后面）
                saveUserMessageToDatabase(sessionId, userMessage, contextMessage, Long.parseLong(teamId), intentDto);

                //获取responseWithoutJson
                String responseWithoutJson = processResponseContent(intentDto.getModelResponse());

                // 4、将本轮模型回复消息存入Redis,存的是result的json串
                ChatMessage assistantMessage = ChatMessage.builder()
                        .role(ChatMessageRole.ASSISTANT)
                        .content(responseWithoutJson)
                        .build();
                addChatMessageToRedis(sessionId, assistantMessage);

                // 2 将机器人回复消息存入数据库
                saveRobotResponseToDatabase(sessionId, intentDto.getModelResponse(), Long.parseLong(teamId));

                log.debug("收到普通非流式响应: sessionId={}, response={}", sessionId, intentDto.getModelResponse());
                return intentDto.getModelResponse();

            } else {
                String ques = lastRobotResponse;//获取上一轮机器人的回复
                String respFromBigModel = processBigModel(ques, userMessage, messagesForModel, contextId, qaSimplelDtoWithUUID, fakeRobotToken);

                // 1.将用户消息+知识库存入数据库（移动到意图识别后面）
                saveUserMessageToDatabase(sessionId, userMessage, contextMessage, Long.parseLong(teamId), null);

                //获取responseWithoutJson
//                String responseWithoutJson = processResponseContent(intentDto.getModelResponse());

                // 4、将本轮模型回复消息存入Redis,存的是result的json串
                ChatMessage assistantMessage = ChatMessage.builder()
                        .role(ChatMessageRole.ASSISTANT)
                        .content(respFromBigModel)
                        .build();
                addChatMessageToRedis(sessionId, assistantMessage);

                // 2 将机器人回复消息存入数据库
                saveRobotResponseToDatabase(sessionId, respFromBigModel, Long.parseLong(teamId));

                log.debug("收到普通非流式响应: sessionId={}, response={}", sessionId, respFromBigModel);
                return respFromBigModel;
            }


            // 暂时无不送给大模型了 germmy@20250817
          /*  messagesForModel.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(contextMessage)
                    .build());*/
//            contextMessage = "";


        } catch (Exception e) {
            log.error("发送消息失败: sessionId={}", sessionId, e);
            throw new RuntimeException("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 从历史聊天记录中获取最近的机器人回复消息
     *
     * @param sessionId 会话ID
     * @return 最近的ASSISTANT消息内容，如果没有则返回null
     */
    private String getLastResponseFromRobot(String sessionId) {
        try {
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);
            if (messages == null || messages.isEmpty()) {
                log.debug("会话历史为空，无法获取最近的机器人回复: sessionId={}", sessionId);
                return null;
            }

            // 从最后一条消息开始向前查找最近的ASSISTANT消息
            for (int i = messages.size() - 1; i >= 0; i--) {
                ChatMessage message = messages.get(i);
                if (message != null && ChatMessageRole.ASSISTANT.equals(message.getRole())) {
                    String content = message.getContent().toString();
                    log.debug("找到最近的机器人回复消息: sessionId={}, 消息位置={}, 内容长度={}", sessionId, i + 1, content.length());
                    return content;
                }
            }

            log.debug("历史记录中未找到机器人回复消息: sessionId={}", sessionId);
            return null;
        } catch (Exception e) {
            log.error("获取最近机器人回复失败: sessionId={}", sessionId, e);
            return null;
        }
    }

    /**
     * 保存用户消息到数据库
     *
     * @param sessionId      会话ID
     * @param userMessage    用户消息
     * @param contextMessage 知识库上下文消息
     * @param teamId         团队ID
     * @param intentDto      意图识别结果DTO
     */
    private void saveUserMessageToDatabase(String sessionId, String userMessage, String contextMessage, Long teamId, IntentDto intentDto) {
        try {
            log.info("开始保存用户消息到数据库：sessionId={}, teamId={}", sessionId, teamId);

            // 从Redis获取发送者名称
            String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
            Map<Object, Object> sessionFromRedis = cacheManager.getHash(sessionConfigKey);
            String sender = (String) sessionFromRedis.get("sessionServiceNames");

            if (sender == null || sender.trim().isEmpty()) {
                log.warn("未找到发送者名称，使用默认值：sessionId={}", sessionId);
                sender = "客服";
            }

            // 组合内容：用户消息+知识库内容
//            String content = userMessage + "," + contextMessage;

            // 创建聊天记录，添加intentDto的score和intentResult参数
            var chatLog = trainConvWinchatLogService.createChatLogWithProdAnswer(
                    0L,  // convDtlId取0L
                    sessionId,
                    sender,
                    userMessage,
                    LocalDateTime.now(),
                    SenderType.STAFF.getCode(),  // senderType取'staff'
                    contextMessage,
                    intentDto != null ? intentDto.getScore() : null,  // score
                    intentDto != null ? intentDto.getIntentResult() : null,  // intentResult
                    teamId,
                    "system"
            );

            if (chatLog != null && chatLog.getId() != null) {
                log.info("保存用户消息到数据库成功：chatLogId={}, score={}, intentResult={}",
                        chatLog.getId(),
                        intentDto != null ? intentDto.getScore() : null,
                        intentDto != null && intentDto.getIntentResult() != null ? "有" : "无");
            } else {
                log.error("保存用户消息到数据库失败：返回结果为空");
            }
        } catch (Exception e) {
            log.error("保存用户消息到数据库异常：sessionId={}, teamId={}", sessionId, teamId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 保存机器人回复消息到数据库
     *
     * @param sessionId 会话ID
     * @param response  机器人回复
     * @param teamId    团队ID
     */
    private void saveRobotResponseToDatabase(String sessionId, String response, Long teamId) {
        try {
            log.info("开始保存机器人回复到数据库：sessionId={}, teamId={}", sessionId, teamId);

            // 从Redis获取机器人名称
            String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
            Map<Object, Object> sessionFromRedis = cacheManager.getHash(sessionConfigKey);
            String sender = (String) sessionFromRedis.get("sessionRobotNames");

            if (sender == null || sender.trim().isEmpty()) {
                log.warn("未找到机器人名称，使用默认值：sessionId={}", sessionId);
                sender = "机器人";
            }

            // 处理response内容
            String content = processResponseContent(response);

            // 创建聊天记录
            var chatLog = trainConvWinchatLogService.createChatLog(
                    0L,  // convDtlId取0L
                    sessionId,
                    sender,
                    content,
                    LocalDateTime.now(),
                    SenderType.BUYER.getCode(),  // senderType取'buyer'
                    teamId,
                    "system"
            );

            if (chatLog != null && chatLog.getId() != null) {
                log.info("保存机器人回复到数据库成功：chatLogId={}", chatLog.getId());
            } else {
                log.error("保存机器人回复到数据库失败：返回结果为空");
            }
        } catch (Exception e) {
            log.error("保存机器人回复到数据库异常：sessionId={}, teamId={}", sessionId, teamId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理机器人回复内容
     * 如果是JSON数组格式，则提取result数组转换为字符串
     * 否则直接返回字符串形式
     *
     * @param response 原始回复内容
     * @return 处理后的内容字符串
     */
    private String processResponseContent(Object response) {
        if (response == null) {
            return "";
        }

        try {
            // 如果是字符串，尝试解析为JSON
            if (response instanceof String) {
                String responseStr = (String) response;

                // 尝试解析为JSON对象
                try {
                    Map<String, Object> responseMap = objectMapper.readValue(responseStr, new TypeReference<Map<String, Object>>() {
                    });

                    // 检查是否有result字段且为数组
                    Object result = responseMap.get("result");
                    if (result instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<String> resultList = (List<String>) result;
                        return String.join("\n", resultList);
                    }
                } catch (Exception e) {
                    // 如果不是JSON格式，直接返回原字符串
                    log.debug("回复内容不是JSON格式，直接返回：{}", e.getMessage());
                    return responseStr;
                }

                return responseStr;
            }

            // 如果是Map类型，直接处理
            if (response instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseMap = (Map<String, Object>) response;

                // 检查是否有result字段且为数组
                Object result = responseMap.get("result");
                if (result instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> resultList = (List<String>) result;
                    return String.join("\n", resultList);
                }
            }

            // 其他情况，转换为JSON字符串
            return objectMapper.writeValueAsString(response);

        } catch (Exception e) {
            log.warn("处理机器人回复内容时发生异常，使用toString()方法：{}", e.getMessage());
            return response.toString();
        }
    }

    /**
     * 获取意图识别系统提示词
     *
     * @return 意图识别系统提示词
     */
    private String getIntentRecoSysPmt() {
        try {
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_INTENT_REPO_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                throw new RuntimeException("获取意图识别提示词失败，未找到相关提示词");
            }
            return prompts.get(0);
        } catch (Exception e) {
            log.error("获取意图识别系统提示词失败", e);
            throw new RuntimeException("获取意图识别系统提示词失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基于意图识别处理消息并返回响应
     *
     * @param searchText       搜索文本（客户问题）
     * @param userMessage      用户消息（客服回答）
     * @param messagesForModel 传递给大模型的消息列表
     * @param contextId        上下文ID
     * @return IntentDto对象，包含模型响应、意图识别结果和评分
     */
    private IntentDto processWithIntentRecognition(String searchText, String userMessage,
                                                   List<ChatMessage> messagesForModel, String contextId) {
        try {
            // 1. 调用意图识别判断
            String intentResult = judgeIntent(searchText, userMessage);

            // 1.1.5 解析意图识别结果
            Map<String, Object> intentMap = objectMapper.readValue(intentResult, new TypeReference<Map<String, Object>>() {
            });
            Object scoreObj = intentMap.get("score");
            int score = 0;

            if (scoreObj instanceof Number) {
                score = ((Number) scoreObj).intValue();
            } else if (scoreObj instanceof String) {
                try {
                    score = Integer.parseInt((String) scoreObj);
                } catch (NumberFormatException e) {
                    log.warn("无法解析score字段: {}", scoreObj);
                }
            }

            log.info("意图识别得分: {}", score);

            String modelResponse;
            // 1.1.6.1 如果score>=70，继续调用大模型
            if (score > 10) {
//                modelResponse = doubaoBigModelService.ntnsWithCtx(messagesForModel, contextId);
                modelResponse = fakeRobotService.getNextBuyerMessage(contextId);
            } else {
                // 1.1.6.2 如果score<70，构造提示回复
//                String parsedSearchText = parseSearchTextToString(searchText);
                String promptMessage = "我们就不要聊" + userMessage + "啦， 请您针对性回答哦：" + searchText;
                Map<String, Object> responseMap = new HashMap<>();
                responseMap.put("result", Arrays.asList(promptMessage));
                modelResponse = objectMapper.writeValueAsString(responseMap);

                log.info("意图识别得分过低({}), 返回提示消息", score);
            }

            // 创建并返回IntentDto
            return new IntentDto(modelResponse, intentResult, score);

        } catch (Exception e) {
            log.error("解析意图识别结果失败，降级到直接调用大模型: {}", e.getMessage());
            // 解析失败时降级到原有逻辑
            String fallbackResponse = doubaoBigModelService.ntnsWithCtx(messagesForModel, contextId);
            return new IntentDto(fallbackResponse, null, 0);
        }
    }

    /**
     * 基于意图识别处理消息并返回响应
     *
     * @param ques             搜索文本（客户问题）
     * @param userMessage      用户消息（客服回答）
     * @param messagesForModel 传递给大模型的消息列表
     * @param contextId        上下文ID
     * @param fakeRobotToken
     * @return IntentDto对象，包含模型响应、意图识别结果和评分
     */
    private String processBigModel(String ques, String userMessage,
                                   List<ChatMessage> messagesForModel, String contextId, QaSimplelDtoWithUUID lastOldAnswer, String fakeRobotToken) {
        try {

            // 1.机器人的提问有了


            // 2.找到正确答案
            String correntAnswer = lastOldAnswer.getAnswer();

            // TODO 3.找下一题的问题,要出栈；
            QaSimplelDtoWithUUID newAnswer = qa2StackService.getNextBuyerMessageAsQa(fakeRobotToken);
            log.info("出栈下一题问答数据：{}", newAnswer != null ? newAnswer.getUuid() : "null");

            // TODO 3.1 同时要将完整记录存到redis中
            if (newAnswer != null) {
                cacheManager.put(newAnswer.getUuid(), newAnswer, REDIS_TTL_HOURS, TimeUnit.HOURS);
                log.info("将答案存储到Redis，key：{}，value：{}", newAnswer.getUuid(), newAnswer.getAnswer());
            }

            // TODO 3.2 同时要获取下一题的问题
            String nextQues = newAnswer != null ? newAnswer.getQuestion() : "";
            log.info("获取下一题问题：{}", nextQues);

            // TODO 4.获取用户提示词，并用上面3个变量替换，调用大模型
            String qa2ndUsrPrompt = getQa2ndUsrPrompt(ques, userMessage, correntAnswer, nextQues);
            log.info("生成用户提示词：{}", qa2ndUsrPrompt);

            String modelResponse;
           /* if (usrPmt != null && !usrPmt.trim().isEmpty()) {
                // 使用生成的用户提示词调用大模型
                modelResponse = doubaoBigModelService.ntnsOnce("", usrPmt);
                log.info("使用用户提示词调用大模型成功");
            } else {*/
            // 必须调用ctxId的
            List<ChatMessage> messagesForModelTemp = new ArrayList<>();
            /*messagesForModelTemp.add(ChatMessage.builder().role(ChatMessageRole.USER).content(usrPmt).build());
                modelResponse = doubaoBigModelService.ntnsWithCtx(messagesForModelTemp, contextId);*/

//            modelResponse = doubaoBigModelService.ntnsOnce(qa2ndSysPromptPmt, "");
            messagesForModelTemp.add(ChatMessage.builder().role(ChatMessageRole.USER).content(qa2ndUsrPrompt).build());
            modelResponse = doubaoBigModelService.ntnsWithCtx(messagesForModelTemp, contextId);


            // 对modelResponse进行JSON格式化
            String formattedResponse = formatModelResponse(modelResponse, newAnswer != null ? newAnswer.getUuid() : null);
            log.info("格式化后的响应：{}", formattedResponse);

            return formattedResponse;

        } catch (Exception e) {
            log.error("processBigModel出错: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 格式化模型响应为指定的JSON格式
     *
     * @param modelResponse 大模型原始响应
     * @param msgId         消息ID（来自newAnswer.getUuid()）
     * @return 格式化后的JSON字符串
     */
    private String formatModelResponse(String modelResponse, String msgId) {
        try {
            List<String> results = new ArrayList<>();
            Map<String, Object> judgeResult = null;

            if (modelResponse == null || modelResponse.trim().isEmpty()) {
                // 如果响应为空，使用默认值
                results.add("响应为空");
                log.warn("模型响应为空，使用默认值");
            } else {
                // 预处理：修复JSON格式错误
                String cleanedResponse = cleanJsonFormat(modelResponse);

                // 尝试解析为JSON对象
                try {
                    Map<String, Object> responseMap = objectMapper.readValue(cleanedResponse, new TypeReference<Map<String, Object>>() {
                    });

                    // 检查是否是评判结果格式（包含analysis、score等字段或中文字段）
                    if (isJudgeResultFormat(responseMap)) {
                        log.info("检测到评判结果格式，开始处理");
                        
                        // 检查是否为中文字段格式
                        boolean isChineseFormat = responseMap.containsKey("分析") && responseMap.containsKey("得分");
                        
                        if (isChineseFormat) {
                            // 中文格式：将整个JSON对象作为字符串添加到results数组
                            String jsonString = objectMapper.writeValueAsString(responseMap);
                            results.add(jsonString);
                            log.info("中文评判结果格式：已将完整JSON对象添加到results数组");
                        } else {
                            // 英文格式：保持原有逻辑，提取judgeResult
                            judgeResult = responseMap;
                            results.add("评判结果"); // 可以根据实际需求调整
                            log.info("英文评判结果格式：提取judgeResult");
                        }
                    } else {
                        // 检查是否有results字段
                        Object resultsObj = responseMap.get("results");
                        if (resultsObj instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<String> resultsList = (List<String>) resultsObj;
                            results.addAll(resultsList);
                            log.info("成功解析JSON格式响应，results数组大小：{}", results.size());
                        } else {
                            // 如果没有results字段，将整个响应作为单个结果
                            results.add(modelResponse);
                            log.info("JSON响应中没有results字段，将整个响应作为单个结果");
                        }
                    }
                } catch (Exception e) {
                    // 如果不是JSON格式，将整个字符串作为单个结果
                    results.add(cleanedResponse);
                    log.info("响应不是JSON格式，将清理后的字符串作为单个结果：{}", e.getMessage());
                    log.debug("原始响应：{}", modelResponse);
                    log.debug("清理后响应：{}", cleanedResponse);
                }
            }

            // 构建目标格式的JSON
            Map<String, Object> respMap = new HashMap<>();
            respMap.put("results", results);
            
            // 如果有评判结果（英文格式），加入judgeResult字段
            if (judgeResult != null) {
                respMap.put("judgeResult", judgeResult);
                log.info("已添加judgeResult到响应中，包含字段：{}", judgeResult.keySet());
            }
            
            respMap.put("msgId", msgId != null ? msgId : "");

            Map<String, Object> finalResponse = new HashMap<>();
            finalResponse.put("resp", respMap);

            String formattedJson = objectMapper.writeValueAsString(finalResponse);
            log.info("成功格式化模型响应，msgId：{}，包含judgeResult：{}", msgId, judgeResult != null);

            return formattedJson;

        } catch (Exception e) {
            log.error("格式化模型响应失败，原始响应：{}，msgId：{}", modelResponse, msgId, e);

            // 降级处理：构建最基本的响应格式
            try {
                Map<String, Object> respMap = new HashMap<>();
                respMap.put("results", Arrays.asList("处理响应时发生错误: " + e.getMessage()));
                respMap.put("msgId", msgId != null ? msgId : "");

                Map<String, Object> finalResponse = new HashMap<>();
                finalResponse.put("resp", respMap);

                return objectMapper.writeValueAsString(finalResponse);
            } catch (Exception ex) {
                log.error("降级处理也失败了", ex);
                return "{\"resp\":{\"results\":[\"系统错误\"],\"msgId\":\"" + (msgId != null ? msgId : "") + "\"}}";
            }
        }
    }

    /**
     * 判断响应是否为评判结果格式
     * 评判结果格式包含：
     * 英文字段：analysis、score、deductionPoints、correctAnswer、nextQuestion等字段
     * 中文字段：分析、得分、扣分点、正确答案、下一题等字段
     *
     * @param responseMap 解析后的响应Map
     * @return true如果是评判结果格式
     */
    private boolean isJudgeResultFormat(Map<String, Object> responseMap) {
        if (responseMap == null) {
            return false;
        }

        // 检查英文字段格式
        boolean hasAnalysis = responseMap.containsKey("analysis");
        boolean hasScore = responseMap.containsKey("score");
        
        // 检查中文字段格式
        boolean hasChinese分析 = responseMap.containsKey("分析");
        boolean hasChinese得分 = responseMap.containsKey("得分");

        // 至少要包含分析和得分字段才认为是评判结果（支持中英文）
        boolean isEnglishJudgeFormat = hasAnalysis && hasScore;
        boolean isChineseJudgeFormat = hasChinese分析 && hasChinese得分;

        boolean isJudgeFormat = isEnglishJudgeFormat || isChineseJudgeFormat;

        if (isJudgeFormat) {
            if (isChineseJudgeFormat) {
                log.info("检测到中文评判结果格式，包含字段：分析={}, 得分={}, 扣分点={}, 正确答案={}, 下一题={}, 考察点={}",
                         hasChinese分析, hasChinese得分,
                         responseMap.containsKey("扣分点"),
                         responseMap.containsKey("正确答案"),
                         responseMap.containsKey("下一题"),
                         responseMap.containsKey("考察点"));
            } else {
                log.info("检测到英文评判结果格式，包含字段：analysis={}, score={}, deductionPoints={}, correctAnswer={}, nextQuestion={}",
                         hasAnalysis, hasScore,
                         responseMap.containsKey("deductionPoints"),
                         responseMap.containsKey("correctAnswer"),
                         responseMap.containsKey("nextQuestion"));
            }
        }
        
        return isJudgeFormat;
    }

    /**
     * 获取用户提示词并进行变量替换
     *
     * @param ques          机器人问题
     * @param userMessage   用户消息（客服回答）
     * @param correntAnswer 正确答案
     * @param nextQues      下一题问题
     * @return 替换后的用户提示词
     */
    private String getUserPrompt(String ques, String userMessage, String correntAnswer, String nextQues) {
        try {
            // 获取用户提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_CONVERT_FREQ_TITLE_USER_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到转换频次标题用户提示词模板，关键词：{}", CONSTS.DEFAULT_CONVERT_FREQ_TITLE_USER_KEYWORD);
                return "";
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到用户提示词模板：{}", template);

            // 进行变量替换
            String result = template
                    .replace("{{nextQues}}", nextQues != null ? nextQues : "")
                    .replace("{{csAnsw}}", userMessage != null ? userMessage : "")
                    .replace("{{answ}}", correntAnswer != null ? correntAnswer : "");

            log.info("变量替换后的用户提示词：{}", result);
            return result;

        } catch (Exception e) {
            log.error("生成用户提示词失败，ques：{}，userMessage：{}，correntAnswer：{}，nextQues：{}",
                    ques, userMessage, correntAnswer, nextQues, e);
            return "";
        }
    }


    /**
     * 获取用户提示词并进行变量替换
     *
     * @param ques          机器人问题
     * @param userMessage   用户消息（客服回答）
     * @param correntAnswer 正确答案
     * @param nextQues      下一题问题
     * @return 替换后的用户提示词
     */
    private String getQa2ndUsrPrompt(String ques, String userMessage, String correntAnswer, String nextQues) {
        try {
            // 获取用户提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_CONVERT_FREQ_TITLE_USER_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到转换频次标题用户提示词模板，关键词：{}", CONSTS.DEFAULT_CONVERT_FREQ_TITLE_USER_KEYWORD);
                return "";
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到用户提示词模板：{}", template);

            // 进行变量替换
            String result = template
                    .replace("{{nextQues}}", nextQues != null ? nextQues : "")
                    .replace("{{csAnsw}}", userMessage != null ? userMessage : "")
                    .replace("{{answ}}", correntAnswer != null ? correntAnswer : "");

            log.info("变量替换后的用户提示词：{}", result);
            return result;

        } catch (Exception e) {
            log.error("生成用户提示词失败，ques：{}，userMessage：{}，correntAnswer：{}，nextQues：{}",
                    ques, userMessage, correntAnswer, nextQues, e);
            return "";
        }
    }

    /**
     * 解析searchText中的JSON格式数据为普通字符串
     *
     * @param searchText 可能包含JSON格式的搜索文本
     * @return 解析后的字符串，如果是JSON格式则将result数组用逗号连接，否则返回原字符串
     */
    private String parseSearchTextToString(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return "";
        }

        try {
            // 尝试解析为JSON对象
            Map<String, Object> jsonMap = objectMapper.readValue(searchText, new TypeReference<Map<String, Object>>() {
            });

            // 检查是否有result字段且为数组
            Object result = jsonMap.get("result");
            if (result instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> resultList = (List<String>) result;

                // 将数组各个子项用逗号连接
                String joinedResult = String.join(",", resultList);
                log.debug("成功解析searchText JSON格式，原长度: {}, 解析后: {}", searchText.length(), joinedResult);
                return joinedResult;
            } else {
                // 如果没有result字段或不是数组，返回原字符串
                log.debug("searchText JSON格式中没有result数组字段，返回原字符串");
                return searchText;
            }

        } catch (Exception e) {
            // JSON解析失败，降级使用原字符串
            log.debug("searchText JSON解析失败，降级使用原字符串: {}", e.getMessage());
            return searchText;
        }
    }

    /**
     * 判断客户问题与客服回答的匹配度
     *
     * @param clientProblem 客户问题
     * @param srvAnswer     客服回答
     * @return 意图识别结果JSON字符串
     */
    private String judgeIntent(String clientProblem, String srvAnswer) {
        try {
            // 1.1.1 获取系统提示词
            String intentRecoSysPmtTpl = getIntentRecoSysPmt();

            // 1.1.3 替换模板中的变量
            String intentRecoSysPmt = intentRecoSysPmtTpl
                    .replace("{{clientProblem}}", clientProblem)
                    .replace("{{srvAnswer}}", srvAnswer);

            // 1.1.4 调用大模型进行意图识别
            String result = doubaoBigModelService.ntnsOnce(intentRecoSysPmt, srvAnswer);

            log.info("意图识别完成，客户问题: {}, 客服回答: {}, 识别结果长度: {}",
                    clientProblem, srvAnswer, result.length());

            return result;
        } catch (Exception e) {
            log.error("意图识别失败，客户问题: {}, 客服回答: {}", clientProblem, srvAnswer, e);
            throw new RuntimeException("意图识别失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理JSON格式错误
     * 主要处理字段值中未转义的双引号问题，避免破坏JSON完整性
     *
     * @param jsonString 原始JSON字符串
     * @return 清理后的JSON字符串
     */
    private String cleanJsonFormat(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            // 首先尝试直接解析，如果成功说明格式正确
            objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});
            return jsonString; // JSON格式正确，无需修复
        } catch (Exception e) {
            log.debug("JSON解析失败，尝试修复格式错误: {}", e.getMessage());
        }

        try {
            String cleaned = fixJsonQuotes(jsonString);
            
            // 验证修复后的JSON是否有效
            try {
                objectMapper.readValue(cleaned, new TypeReference<Map<String, Object>>() {});
                log.info("JSON格式修复成功");
                log.debug("原始JSON: {}", jsonString);
                log.debug("修复后JSON: {}", cleaned);
                return cleaned;
            } catch (Exception e) {
                log.warn("JSON格式修复后仍然无效，使用原始字符串: {}", e.getMessage());
                return jsonString;
            }

        } catch (Exception e) {
            log.warn("清理JSON格式时发生异常，返回原始字符串: {}", e.getMessage());
            return jsonString;
        }
    }

    /**
     * 修复JSON字符串中字段值内部的双引号
     * 使用状态机方式逐字符解析，识别并转义字段值中的双引号
     *
     * @param jsonString 原始JSON字符串
     * @return 修复后的JSON字符串
     */
    private String fixJsonQuotes(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        StringBuilder result = new StringBuilder();
        boolean inString = false;      // 是否在字符串内部
        boolean inFieldValue = false;  // 是否在字段值内部
        boolean inArray = false;       // 是否在数组内部
        int bracketDepth = 0;          // 花括号嵌套深度
        int arrayDepth = 0;            // 数组嵌套深度
        
        char prev = 0;
        
        for (int i = 0; i < jsonString.length(); i++) {
            char c = jsonString.charAt(i);
            char next = (i + 1 < jsonString.length()) ? jsonString.charAt(i + 1) : 0;
            
            // 跳过已转义的字符
            if (prev == '\\') {
                result.append(c);
                prev = c;
                continue;
            }
            
            switch (c) {
                case '"':
                    if (!inString) {
                        // 进入字符串
                        inString = true;
                        // 判断是否是字段值的开始（前面是冒号和可选空白）
                        if (isFieldValueStart(jsonString, i)) {
                            inFieldValue = true;
                        }
                    } else {
                        // 可能要退出字符串
                        if (inFieldValue) {
                            // 在字段值内部，检查是否是字段值结束的双引号
                            if (isFieldValueEnd(jsonString, i + 1)) {
                                inString = false;
                                inFieldValue = false;
                            } else {
                                // 字段值内部的双引号，需要转义
                                result.append('\\');
                                log.debug("转义字段值内部双引号，位置: {}", i);
                            }
                        } else {
                            // 不在字段值内部，正常结束字符串
                            inString = false;
                        }
                    }
                    result.append(c);
                    break;
                    
                case '{':
                    if (!inString) {
                        bracketDepth++;
                    }
                    result.append(c);
                    break;
                    
                case '}':
                    if (!inString) {
                        bracketDepth--;
                    }
                    result.append(c);
                    break;
                    
                case '[':
                    if (!inString) {
                        arrayDepth++;
                        inArray = true;
                    }
                    result.append(c);
                    break;
                    
                case ']':
                    if (!inString) {
                        arrayDepth--;
                        if (arrayDepth == 0) {
                            inArray = false;
                        }
                    }
                    result.append(c);
                    break;
                    
                default:
                    result.append(c);
                    break;
            }
            
            prev = c;
        }
        
        return result.toString();
    }
    
    /**
     * 判断指定位置的双引号是否是字段值的开始
     */
    private boolean isFieldValueStart(String json, int quoteIndex) {
        // 向前查找，跳过空白字符，找到冒号
        for (int i = quoteIndex - 1; i >= 0; i--) {
            char c = json.charAt(i);
            if (c == ':') {
                return true;
            } else if (c != ' ' && c != '\t' && c != '\n' && c != '\r') {
                return false;
            }
        }
        return false;
    }
    
    /**
     * 判断指定位置之后是否是字段值的结束
     */
    private boolean isFieldValueEnd(String json, int afterQuoteIndex) {
        if (afterQuoteIndex >= json.length()) {
            return true;
        }
        
        // 向后查找，跳过空白字符，看是否是逗号、右花括号或右方括号
        for (int i = afterQuoteIndex; i < json.length(); i++) {
            char c = json.charAt(i);
            if (c == ',' || c == '}' || c == ']') {
                return true;
            } else if (c != ' ' && c != '\t' && c != '\n' && c != '\r') {
                return false;
            }
        }
        return true;
    }

}