package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.dto.converkb.FileImportResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件导入服务接口
 */
public interface FileImportService {
    
    /**
     * 导入文件并切割成块
     * @param file 上传的文件
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 导入结果
     */
    FileImportResponse importFile(MultipartFile file, Long teamId, String userId);
    
    /**
     * 验证文件格式和大小
     * @param file 上传的文件
     * @return 验证结果，null表示验证通过，否则返回错误信息
     */
    String validateFile(MultipartFile file);
    
    /**
     * 读取文件内容
     * @param file 上传的文件
     * @return 文件内容字符串
     */
    String readFileContent(MultipartFile file);
    
    /**
     * 将文件内容切割成块
     * @param content 文件内容
     * @return 切割后的块列表
     */
    List<String> splitContentIntoChunks(String content);
    
    /**
     * 保存切割后的块到数据库
     * @param tplId 模板ID
     * @param chunks 块列表
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 成功保存的块数量
     */
    int saveChunksToDatabase(Long tplId, List<String> chunks, Long teamId, String userId);
    
    /**
     * 获取文件扩展名
     * @param fileName 文件名
     * @return 文件扩展名
     */
    String getFileExtension(String fileName);
    
    /**
     * 检查内容是否包含完整的会话
     * @param content 内容
     * @return 是否包含完整会话
     */
    boolean hasCompleteConversation(String content);
    
    /**
     * 查找会话结束位置
     * @param content 内容
     * @param startPos 开始位置
     * @return 会话结束位置，-1表示未找到
     */
    int findConversationEnd(String content, int startPos);
    
    /**
     * 生成内容预览
     * @param content 内容
     * @param maxLength 最大长度
     * @return 预览内容
     */
    String generatePreview(String content, int maxLength);
}
