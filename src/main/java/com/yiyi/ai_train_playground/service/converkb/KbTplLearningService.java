package com.yiyi.ai_train_playground.service.converkb;

/**
 * 知识库模板学习服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
public interface KbTplLearningService {
    
    /**
     * 处理知识库模板学习任务
     * 从train_kb_tpl_pre表中加载状态为un_learn的数据，
     * 使用线程池处理并调用LLM生成模板详情
     *
     * @return 包含处理记录数量和总token消耗的结果
     */
    KbTplLearningResult processKbTplLearning();

    /**
     * 知识库模板学习结果
     */
    class KbTplLearningResult {
        private final int processedCount;
        private final long totalTokens;

        public KbTplLearningResult(int processedCount, long totalTokens) {
            this.processedCount = processedCount;
            this.totalTokens = totalTokens;
        }

        public int getProcessedCount() {
            return processedCount;
        }

        public long getTotalTokens() {
            return totalTokens;
        }
    }
}
