package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;

/**
 * 知识库模板学习单个记录处理器服务
 * <p>
 * 该服务用于处理单个学习记录，并确保其处理过程在独立的事务中进行。
 *
 * <AUTHOR> Assistant (Refactored)
 * @since 2025-08-17
 */
public interface SingleRecordProcessorService {

    /**
     * 处理单条记录。
     * <p>
     * 此方法被设计为事务性的。它包含调用LLM、解析结果和更新多个数据库表的逻辑。
     * 整个过程要么完全成功，要么在发生任何异常时完全回滚。
     *
     * @param record    要处理的预处理记录
     * @param contextId 对话上下文ID
     * @return Token消耗结果
     */
    TokenResult processSingleRecord(TrainKbTplPre record, String contextId);

    /**
     * Token统计结果内部类
     */
    class TokenResult {
        private final long totalTokens;
        private final long promptTokens;
        private final long completionTokens;

        public TokenResult(long totalTokens, long promptTokens, long completionTokens) {
            this.totalTokens = totalTokens;
            this.promptTokens = promptTokens;
            this.completionTokens = completionTokens;
        }

        public long getTotalTokens() { return totalTokens; }
        public long getPromptTokens() { return promptTokens; }
        public long getCompletionTokens() { return completionTokens; }
    }

    /**
     * 聊天模板项内部类
     */
    class ChatTemplateItem {
        private String typeCode;
        private String typeName;
        private String content;

        // Getters and Setters
        public String getTypeCode() { return typeCode; }
        public void setTypeCode(String typeCode) { this.typeCode = typeCode; }

        public String getTypeName() { return typeName; }
        public void setTypeName(String typeName) { this.typeName = typeName; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }
}
