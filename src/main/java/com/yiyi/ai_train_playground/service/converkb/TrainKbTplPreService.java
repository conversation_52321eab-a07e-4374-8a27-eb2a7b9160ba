package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;

import java.util.List;

/**
 * 会话知识库模板预处理服务接口
 */
public interface TrainKbTplPreService {
    
    /**
     * 根据ID查询预处理记录
     * @param id 主键ID
     * @param teamId 团队ID
     * @return 预处理记录
     */
    TrainKbTplPre getById(Long id, Long teamId);
    
    /**
     * 根据模板ID查询所有预处理记录
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 预处理记录列表（按index排序）
     */
    List<TrainKbTplPre> getByTplId(Long tplId, Long teamId);
    
    /**
     * 根据模板ID和学习状态查询预处理记录
     * @param tplId 模板ID
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 预处理记录列表（按index排序）
     */
    List<TrainKbTplPre> getByTplIdAndLearnStatus(Long tplId, String learnStatus, Long teamId);
    
    /**
     * 根据学习状态查询预处理记录
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 预处理记录列表
     */
    List<TrainKbTplPre> getByLearnStatus(String learnStatus, Long teamId);
    
    /**
     * 创建预处理记录
     * @param tplId 模板ID
     * @param content 内容
     * @param index 顺序
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 创建的记录ID
     */
    Long createPreRecord(Long tplId, String content, Integer index, String learnStatus, Long teamId, String userId);
    
    /**
     * 批量创建预处理记录
     * @param tplId 模板ID
     * @param contents 内容列表
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 创建的记录数量
     */
    int batchCreatePreRecords(Long tplId, List<String> contents, String learnStatus, Long teamId, String userId);
    
    /**
     * 更新预处理记录
     * @param id 记录ID
     * @param content 内容
     * @param index 顺序
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean updatePreRecord(Long id, String content, Integer index, String learnStatus, Long teamId, String userId);
    
    /**
     * 批量更新学习状态
     * @param tplId 模板ID
     * @param oldLearnStatus 原学习状态
     * @param newLearnStatus 新学习状态
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 更新的记录数量
     */
    int updateLearnStatus(Long tplId, String oldLearnStatus, String newLearnStatus, Long teamId, String userId);
    
    /**
     * 删除预处理记录
     * @param id 记录ID
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deletePreRecord(Long id, Long teamId);
    
    /**
     * 删除模板的所有预处理记录
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 删除的记录数量
     */
    int deleteByTplId(Long tplId, Long teamId);
    
    /**
     * 删除模板指定学习状态的记录
     * @param tplId 模板ID
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 删除的记录数量
     */
    int deleteByTplIdAndLearnStatus(Long tplId, String learnStatus, Long teamId);
    
    /**
     * 统计模板的预处理记录数量
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 记录数量
     */
    Long countByTplId(Long tplId, Long teamId);
    
    /**
     * 统计模板指定学习状态的记录数量
     * @param tplId 模板ID
     * @param learnStatus 学习状态
     * @param teamId 团队ID
     * @return 记录数量
     */
    Long countByTplIdAndLearnStatus(Long tplId, String learnStatus, Long teamId);
    
    /**
     * 获取模板的学习进度
     * @param tplId 模板ID
     * @param teamId 团队ID
     * @return 学习进度信息（包含各状态的数量）
     */
    LearnProgress getLearnProgress(Long tplId, Long teamId);
    
    /**
     * 学习进度信息
     */
    class LearnProgress {
        private Long totalCount;
        private Long unLearnCount;
        private Long learningCount;
        private Long learnedCount;
        
        public LearnProgress() {}
        
        public LearnProgress(Long totalCount, Long unLearnCount, Long learningCount, Long learnedCount) {
            this.totalCount = totalCount;
            this.unLearnCount = unLearnCount;
            this.learningCount = learningCount;
            this.learnedCount = learnedCount;
        }
        
        // Getter和Setter方法
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
        
        public Long getUnLearnCount() { return unLearnCount; }
        public void setUnLearnCount(Long unLearnCount) { this.unLearnCount = unLearnCount; }
        
        public Long getLearningCount() { return learningCount; }
        public void setLearningCount(Long learningCount) { this.learningCount = learningCount; }
        
        public Long getLearnedCount() { return learnedCount; }
        public void setLearnedCount(Long learnedCount) { this.learnedCount = learnedCount; }
        
        /**
         * 计算学习进度百分比
         * @return 学习进度百分比（0-100）
         */
        public double getProgressPercentage() {
            if (totalCount == null || totalCount == 0) {
                return 0.0;
            }
            Long learned = (learnedCount != null ? learnedCount : 0);
            return (learned.doubleValue() / totalCount.doubleValue()) * 100.0;
        }
    }
}
