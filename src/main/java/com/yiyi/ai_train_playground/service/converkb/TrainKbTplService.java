package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.dto.converkb.KbTplCreateRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplQueryRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailResponse;
import com.yiyi.ai_train_playground.dto.converkb.KbTplListResponse;
import com.yiyi.ai_train_playground.dto.PageResult;

import java.util.List;

/**
 * 会话知识库模板服务接口
 */
public interface TrainKbTplService {
    
    /**
     * 创建会话知识库模板
     * @param request 创建请求
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 模板ID
     */
    Long createKbTpl(KbTplCreateRequest request, Long teamId, String userId);
    
    /**
     * 获取模板详情（支持分页）
     * @param id 模板ID
     * @param teamId 团队ID
     * @param request 分页请求
     * @return 模板详情
     */
    KbTplDetailResponse getKbTplDetail(Long id, Long teamId, KbTplDetailRequest request);
    
    /**
     * 分页查询模板列表
     * @param request 查询请求
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<KbTplListResponse> getKbTplPageList(KbTplQueryRequest request, Long teamId);
    
    /**
     * 更新模板（更新主表名称和明细）
     * @param id 模板ID
     * @param request 更新请求
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateKbTpl(Long id, KbTplCreateRequest request, Long teamId, String userId);
    
    /**
     * 删除模板
     * @param id 模板ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deleteKbTpl(Long id, Long teamId);
    
    /**
     * 检查名称是否存在
     * @param name 模板名称
     * @param teamId 团队ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkNameExists(String name, Long teamId, Long excludeId);
    
    /**
     * 更新模板的token数量
     * @param id 模板ID
     * @param tokens 新的token数量
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean updateKbTplTokens(Long id, Long tokens, Long teamId);

    /**
     * 更新模板的学习状态
     * @param id 模板ID
     * @param learnStatus 新的学习状态
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean updateKbTplLearnStatus(Long id, String learnStatus, Long teamId);

    /**
     * 创建文件导入模板（仅包含基本信息）
     * @param fileName 文件名
     * @param fileType 文件类型
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 模板ID
     */
    Long createFileImportTemplate(String fileName, String fileType, Long teamId, String userId);

    /**
     * 根据任务ID获取随机的知识库明细内容
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 随机的知识库明细内容，如果没有找到则返回null
     */
    String getRandomKbDetailByTaskId(Long taskId, Long teamId);
    
    /**
     * 根据知识库模板ID获取所有明细（用于创建任务时批量插入）
     * @param kbTplId 知识库模板ID
     * @param teamId 团队ID
     * @return 明细列表，包含id和content字段
     */
    List<KbTplDetailForTaskCreation> getKbTplDetailsForTaskCreation(Long kbTplId, Long teamId);
    
    /**
     * 知识库模板明细DTO（用于任务创建）
     */
    class KbTplDetailForTaskCreation {
        private Long id;
        private String content;
        
        public KbTplDetailForTaskCreation() {}
        
        public KbTplDetailForTaskCreation(Long id, String content) {
            this.id = id;
            this.content = content;
        }
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
    }
}