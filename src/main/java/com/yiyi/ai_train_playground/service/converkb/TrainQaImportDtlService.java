package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;

import java.util.List;

/**
 * 问答导入明细服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
public interface TrainQaImportDtlService {
    
    /**
     * 根据主表ID分页查询详情列表
     * @param qaMainId 主表ID
     * @param question 问题关键词（可选，用于模糊查询）
     * @param offset 偏移量
     * @param pageSize 每页大小
     */
    List<TrainQaImportDtl> getDtlListByQaMainId(Long qaMainId, String question, int offset, int pageSize);

    /**
     * 根据主表ID统计详情数量
     * @param qaMainId 主表ID
     * @param question 问题关键词（可选，用于模糊查询）
     */
    int countByQaMainId(Long qaMainId, String question);
    
    /**
     * 根据问题模糊查询详情列表
     */
    List<TrainQaImportDtl> getDtlListByQuestionLike(String question, Long teamId, int offset, int pageSize);
    
    /**
     * 根据问题模糊查询统计数量
     */
    int countByQuestionLike(String question, Long teamId);
    
    /**
     * 根据团队ID分页查询所有详情
     */
    List<TrainQaImportDtl> getDtlListByTeamId(Long teamId, int offset, int pageSize);
    
    /**
     * 根据团队ID统计所有详情数量
     */
    int countByTeamId(Long teamId);
    
    /**
     * 根据主表ID查询所有明细（只返回question和answer字段，不分页）
     */
    List<QaSimpleDto> getAllDtlByMainId(Long qaMainId);

    /**
     * 根据主表ID随机获取指定数量的明细（只返回question和answer字段）
     * @param qaMainId 主表ID
     * @param count 需要获取的数量
     * @return 随机获取的问答列表
     */
    List<QaSimpleDto> getRdmListByMain(Long qaMainId, Integer count);
    
    /**
     * 根据ID查询详情
     */
    TrainQaImportDtl getDtlById(Long id);
    
    /**
     * 创建详情记录
     */
    void createDtl(TrainQaImportDtl dtl, Long teamId, String creator);
    
    /**
     * 更新详情记录
     */
    void updateDtl(Long id, TrainQaImportDtl dtl, Long teamId, String updater);
    
    /**
     * 删除详情记录
     */
    void deleteDtl(Long id, Long teamId);
    
    /**
     * 批量删除详情记录
     */
    void batchDeleteDtl(List<Long> ids, Long teamId);
    
    /**
     * 检查问题是否存在
     */
    boolean existsByQuestion(String question, Long teamId);
}