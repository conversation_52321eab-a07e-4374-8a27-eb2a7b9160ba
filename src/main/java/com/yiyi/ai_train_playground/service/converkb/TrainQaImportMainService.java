package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;

import java.util.List;

/**
 * 问答导入主表服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
public interface TrainQaImportMainService {
    
    /**
     * 根据团队ID分页查询知识库列表
     */
    List<TrainQaImportMain> getMainListByTeamId(Long teamId, int offset, int pageSize);
    
    /**
     * 根据团队ID和创建人分页查询知识库列表
     */
    List<TrainQaImportMain> getMainListByTeamId(Long teamId, String creator, int offset, int pageSize);
    
    /**
     * 根据条件分页查询知识库列表
     * @param teamId 团队ID
     * @param creator 创建人
     * @param qaImName 知识库名称（可选，模糊搜索）
     * @param offset 偏移量
     * @param pageSize 页面大小
     * @return 知识库列表
     */
    List<TrainQaImportMain> getMainListByConditions(Long teamId, String creator, String qaImName, int offset, int pageSize);
    
    /**
     * 根据团队ID统计知识库数量
     */
    int countByTeamId(Long teamId);
    
    /**
     * 根据团队ID和创建人统计知识库数量
     */
    int countByTeamId(Long teamId, String creator);
    
    /**
     * 根据条件统计知识库数量
     * @param teamId 团队ID
     * @param creator 创建人
     * @param qaImName 知识库名称（可选，模糊搜索）
     * @return 知识库数量
     */
    int countByConditions(Long teamId, String creator, String qaImName);
    
    /**
     * 根据ID查询知识库详情
     */
    TrainQaImportMain getMainById(Long id);
    
    /**
     * 更新知识库信息（只能更新名称和描述）
     * @param id 知识库ID
     * @param qaImName 知识库名称
     * @param qaImDesc 知识库描述
     * @param teamId 团队ID
     * @param updater 更新人
     */
    void updateMain(Long id, String qaImName, String qaImDesc, Long teamId, String updater);
    
    /**
     * 删除知识库（包括其所有详情）
     */
    void deleteMainWithDetails(Long id, Long teamId);
    
    /**
     * 批量删除知识库（包括其所有详情）
     */
    void batchDeleteMainWithDetails(List<Long> ids, Long teamId);
}