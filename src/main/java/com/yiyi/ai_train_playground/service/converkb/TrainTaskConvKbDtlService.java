package com.yiyi.ai_train_playground.service.converkb;

import com.yiyi.ai_train_playground.dto.converkb.*;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl;

import java.util.List;

/**
 * 任务会话明细服务接口
 */
public interface TrainTaskConvKbDtlService {
    
    /**
     * 创建任务会话明细
     * @param request 创建请求
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 明细ID
     */
    Long createTaskConvKbDtl(TaskConvKbDtlCreateRequest request, Long teamId, String userId);
    
    /**
     * 批量创建任务会话明细
     * @param requests 创建请求列表
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 成功创建的数量
     */
    int batchCreateTaskConvKbDtl(List<TaskConvKbDtlCreateRequest> requests, Long teamId, String userId);
    
    /**
     * 根据ID获取明细详情
     * @param id 明细ID
     * @param teamId 团队ID
     * @return 明细详情
     */
    TaskConvKbDtlDetailDTO getTaskConvKbDtlDetail(Long id, Long teamId);
    
    /**
     * 分页查询明细列表
     * @param request 查询请求
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<TaskConvKbDtlListDTO.TaskConvKbDtlItem> getTaskConvKbDtlPageList(TaskConvKbDtlQueryRequest request, Long teamId);
    
    /**
     * 根据任务ID获取明细列表
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 明细列表
     */
    List<TaskConvKbDtlDetailDTO> getTaskConvKbDtlByTaskId(Long taskId, Long teamId);

    /**
     * 根据任务ID随机获取一个明细
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 随机的明细详情
     */
    TaskConvKbDtlDetailDTO getRdmConvDtlByTaskId(Long taskId, Long teamId);
    
    /**
     * 更新任务会话明细
     * @param id 明细ID
     * @param request 更新请求
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateTaskConvKbDtl(Long id, TaskConvKbDtlUpdateRequest request, Long teamId, String userId);
    
    /**
     * 更新学习状态
     * @param id 明细ID
     * @param learningStatus 学习状态
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateLearningStatus(Long id, String learningStatus, Long teamId, String userId);
    
    /**
     * 更新初始聊天记录
     * @param id 明细ID
     * @param f1stRawChatlog 初始聊天记录
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateF1stRawChatlog(Long id, String f1stRawChatlog, Long teamId, String userId);
    
    /**
     * 更新最终聊天记录
     * @param id 明细ID
     * @param finalChatLog 最终聊天记录
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateFinalChatLog(Long id, String finalChatLog, Long teamId, String userId);
    
    /**
     * 删除任务会话明细
     * @param id 明细ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deleteTaskConvKbDtl(Long id, Long teamId);
    
    /**
     * 根据任务ID删除所有明细
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 删除的数量
     */
    int deleteByTaskId(Long taskId, Long teamId);
    
    /**
     * 统计任务的明细数量
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 明细数量
     */
    int countByTaskId(Long taskId, Long teamId);
    
    /**
     * 统计指定学习状态的数量
     * @param taskId 任务ID
     * @param learningStatus 学习状态
     * @param teamId 团队ID
     * @return 数量
     */
    int countByTaskIdAndStatus(Long taskId, String learningStatus, Long teamId);
}