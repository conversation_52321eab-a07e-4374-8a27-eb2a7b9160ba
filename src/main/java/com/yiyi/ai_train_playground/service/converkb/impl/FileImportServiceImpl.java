package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.config.FileProcessingConfig;
import com.yiyi.ai_train_playground.dto.converkb.FileImportResponse;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.service.converkb.FileImportService;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplPreService;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 文件导入服务实现类
 */
@Service
public class FileImportServiceImpl implements FileImportService {
    
    private static final Logger log = LoggerFactory.getLogger(FileImportServiceImpl.class);
    
    @Autowired
    private FileProcessingConfig fileProcessingConfig;

    @Autowired
    private TrainKbTplPreService trainKbTplPreService;

    @Autowired
    private TrainKbTplService trainKbTplService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileImportResponse importFile(MultipartFile file, Long teamId, String userId) {
        log.info("开始导入文件，fileName: {}, fileSize: {}, teamId: {}",
                file.getOriginalFilename(), file.getSize(), teamId);
        
        LocalDateTime startTime = LocalDateTime.now();
        FileImportResponse response = new FileImportResponse();
        response.setOriginalFileName(file.getOriginalFilename());
        response.setFileSize(file.getSize());
        response.setFileType(getFileExtension(file.getOriginalFilename()));
        response.setStartTime(startTime);
        response.setErrorMessages(new ArrayList<>());
        response.setChunkInfos(new ArrayList<>());

        Long tplId = null;

        try {
            // 1. 验证文件
            String validationError = validateFile(file);
            if (validationError != null) {
                response.getErrorMessages().add(validationError);
                response.setTotalChunks(0);
                response.setSuccessChunks(0);
                response.setFailedChunks(0);
                return response;
            }

            // 2. 创建模板记录，状态为file_uploading
            String fileName = generateUniqueFileName(file.getOriginalFilename());
            String fileType = getFileExtension(fileName);
            tplId = trainKbTplService.createFileImportTemplate(fileName, fileType, teamId, userId);
            response.setTplId(tplId);

            log.info("创建文件导入模板成功，tplId: {}", tplId);
            
            // 3. 读取文件内容
            String content = readFileContent(file);
            if (!StringUtils.hasText(content)) {
                response.getErrorMessages().add("文件内容为空");
                response.setTotalChunks(0);
                response.setSuccessChunks(0);
                response.setFailedChunks(0);
                return response;
            }

            // 4. 切割内容
            List<String> chunks = splitContentIntoChunks(content);
            response.setTotalChunks(chunks.size());

            // 5. 保存到数据库
            int successCount = saveChunksToDatabase(tplId, chunks, teamId, userId);
            response.setSuccessChunks(successCount);
            response.setFailedChunks(chunks.size() - successCount);

            // 6. 文件导入完成后，更新模板状态为learning,站在业务思维
            boolean updateSuccess = trainKbTplService.updateKbTplLearnStatus(tplId, LearnStatus.LEARNING.getCode(), teamId);
            if (!updateSuccess) {
                log.warn("更新模板学习状态失败，tplId: {}", tplId);
            } else {
                log.info("更新模板学习状态成功，tplId: {}, status: {}", tplId, LearnStatus.LEARNING.getCode());
            }
            
            // 5. 生成块信息
            for (int i = 0; i < chunks.size(); i++) {
                String chunk = chunks.get(i);
                FileImportResponse.ChunkInfo chunkInfo = new FileImportResponse.ChunkInfo();
                chunkInfo.setIndex(i);
                chunkInfo.setSize(chunk.getBytes(StandardCharsets.UTF_8).length);
                chunkInfo.setPreview(generatePreview(chunk, 100));
                chunkInfo.setHasCompleteConversation(hasCompleteConversation(chunk));
                response.getChunkInfos().add(chunkInfo);
            }
            
            log.info("文件导入完成，总块数: {}, 成功: {}, 失败: {}", 
                    chunks.size(), successCount, chunks.size() - successCount);
            
        } catch (Exception e) {
            log.error("文件导入失败", e);
            response.getErrorMessages().add("文件导入失败: " + e.getMessage());
            response.setTotalChunks(0);
            response.setSuccessChunks(0);
            response.setFailedChunks(0);
        } finally {
            LocalDateTime endTime = LocalDateTime.now();
            response.setEndTime(endTime);
            response.setProcessingTimeMs(java.time.Duration.between(startTime, endTime).toMillis());
        }
        
        return response;
    }
    
    @Override
    public String validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "文件不能为空";
        }
        
        // 检查文件大小
        if (fileProcessingConfig.isFileSizeExceeded(file.getSize())) {
            return String.format("文件大小超过限制，最大允许 %d 字节", fileProcessingConfig.getMaxFileSize());
        }
        
        // 检查文件格式
        String extension = getFileExtension(file.getOriginalFilename());
        if (!fileProcessingConfig.isSupportedFormat(extension)) {
            return String.format("不支持的文件格式: %s，支持的格式: %s", 
                    extension, fileProcessingConfig.getSupportedFormats());
        }
        
        return null; // 验证通过
    }
    
    @Override
    public String readFileContent(MultipartFile file) {
        String extension = getFileExtension(file.getOriginalFilename());
        
        try {
            switch (extension.toLowerCase()) {
                case "txt":
                case "csv":
                    return readTextFile(file);
                case "xlsx":
                    return readXlsxFile(file);
                case "xls":
                    return readXlsFile(file);
                default:
                    throw new IllegalArgumentException("不支持的文件格式: " + extension);
            }
        } catch (Exception e) {
            log.error("读取文件内容失败: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("读取文件内容失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 读取文本文件
     */
    private String readTextFile(MultipartFile file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (InputStream inputStream = file.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
    
    /**
     * 读取Excel文件(.xlsx)
     */
    private String readXlsxFile(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            return readExcelContent(workbook);
        }
    }
    
    /**
     * 读取Excel文件(.xls)
     */
    private String readXlsFile(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new HSSFWorkbook(inputStream)) {
            return readExcelContent(workbook);
        }
    }
    
    /**
     * 读取Excel内容
     */
    private String readExcelContent(Workbook workbook) {
        StringBuilder content = new StringBuilder();
        
        // 读取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            StringBuilder rowContent = new StringBuilder();
            for (Cell cell : row) {
                String cellValue = getCellValueAsString(cell);
                if (StringUtils.hasText(cellValue)) {
                    if (rowContent.length() > 0) {
                        rowContent.append("\t");
                    }
                    rowContent.append(cellValue);
                }
            }
            if (rowContent.length() > 0) {
                content.append(rowContent).append("\n");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    @Override
    public List<String> splitContentIntoChunks(String content) {
        List<String> chunks = new ArrayList<>();
        
        if (!StringUtils.hasText(content)) {
            return chunks;
        }
        
        int chunkSize = fileProcessingConfig.getChunkSize();
        int contentLength = content.length();
        int currentPos = 0;
        
        while (currentPos < contentLength) {
            int endPos = Math.min(currentPos + chunkSize, contentLength);
            
            // 如果不是最后一块，尝试找到完整的会话结束位置
            if (endPos < contentLength) {
                int conversationEnd = findConversationEnd(content, endPos);
                if (conversationEnd > endPos && conversationEnd <= contentLength) {
                    endPos = conversationEnd;
                }
            }
            
            String chunk = content.substring(currentPos, endPos);
            if (StringUtils.hasText(chunk.trim())) {
                chunks.add(chunk);
            }
            
            currentPos = endPos;
        }
        
        log.info("内容切割完成，总长度: {}, 块数: {}, 平均块大小: {}", 
                contentLength, chunks.size(), 
                chunks.isEmpty() ? 0 : contentLength / chunks.size());
        
        return chunks;
    }
    
    @Override
    public int saveChunksToDatabase(Long tplId, List<String> chunks, Long teamId, String userId) {
        if (chunks == null || chunks.isEmpty()) {
            return 0;
        }
        
        try {
            // 先删除该模板的现有预处理记录
            trainKbTplPreService.deleteByTplId(tplId, teamId);
            
            // 批量创建新的预处理记录
            return trainKbTplPreService.batchCreatePreRecords(tplId, chunks, LearnStatus.UN_LEARN.getCode(), teamId, userId);
            
        } catch (Exception e) {
            log.error("保存块到数据库失败", e);
            throw new RuntimeException("保存块到数据库失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }
    
    @Override
    public boolean hasCompleteConversation(String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }
        
        String startMarker = fileProcessingConfig.getConversationStartMarker();
        String endMarker = fileProcessingConfig.getConversationEndMarker();
        
        return content.contains(startMarker) && content.contains(endMarker);
    }
    
    @Override
    public int findConversationEnd(String content, int startPos) {
        String endMarker = fileProcessingConfig.getConversationEndMarker();
        
        // 从startPos开始向后查找会话结束标志
        int endMarkerPos = content.indexOf(endMarker, startPos);
        if (endMarkerPos != -1) {
            // 找到结束标志后，继续查找到行尾
            int lineEndPos = content.indexOf('\n', endMarkerPos);
            if (lineEndPos != -1) {
                return lineEndPos + 1; // 包含换行符
            } else {
                return content.length(); // 文件末尾
            }
        }
        
        return -1; // 未找到
    }
    
    @Override
    public String generatePreview(String content, int maxLength) {
        if (!StringUtils.hasText(content)) {
            return "";
        }
        
        if (content.length() <= maxLength) {
            return content;
        }
        
        return content.substring(0, maxLength) + "...";
    }
    
    /**
     * 生成唯一的文件名
     * 格式：原文件名_yyyyMMddHHmmssSSS_3位随机数.扩展名
     * 例如：myfile_20250810123456789_123.txt
     *
     * @param originalFileName 原始文件名
     * @return 唯一的文件名
     */
    private String generateUniqueFileName(String originalFileName) {
        if (!StringUtils.hasText(originalFileName)) {
            return "file_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + "_" + 
                   String.format("%03d", new Random().nextInt(999) + 1);
        }
        
        // 分离文件名和扩展名
        int lastDotIndex = originalFileName.lastIndexOf('.');
        String nameWithoutExt;
        String extension;
        
        if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
            nameWithoutExt = originalFileName.substring(0, lastDotIndex);
            extension = originalFileName.substring(lastDotIndex); // 包含点号
        } else {
            nameWithoutExt = originalFileName;
            extension = "";
        }
        
        // 生成时间戳（精确到毫秒）
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        
        // 生成3位随机数（001-999）
        String randomNumber = String.format("%03d", new Random().nextInt(999) + 1);
        
        // 组合生成唯一文件名
        return nameWithoutExt + "_" + timestamp + "_" + randomNumber + extension;
    }
}
