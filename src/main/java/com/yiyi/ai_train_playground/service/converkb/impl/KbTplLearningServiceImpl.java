package com.yiyi.ai_train_playground.service.converkb.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.consts.CONSTS;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplMainMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplPreMapper;
import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.converkb.KbTplLearningService;
import com.yiyi.ai_train_playground.service.converkb.SingleRecordProcessorService;
import com.yiyi.ai_train_playground.service.impl.BigmodelPromptsServiceImpl;
import com.yiyi.ai_train_playground.service.impl.DoubaoBigModelServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 知识库模板学习服务实现类 (Refactored)
 *
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KbTplLearningServiceImpl implements KbTplLearningService {

    private final TrainKbTplPreMapper trainKbTplPreMapper;
    private final TrainKbTplMainMapper trainKbTplMainMapper;
    private final BigmodelPromptsServiceImpl bigmodelPromptsService;
    private final DoubaoBigModelServiceImpl doubaoBigModelServiceImpl;
    private final CacheManager cacheManager;
    private final SingleRecordProcessorService singleRecordProcessorService;

    @Value("${kb.llm-output-size:3}")
    private Integer llmOutputSize;

    private static final int THREAD_POOL_SIZE = 20;
    private static final int BATCH_SIZE = 20;
    private static final String CONTEXT_CACHE_PREFIX = "kb:context:";
    private static final long CONTEXT_CACHE_TTL_HOURS = 160;

    @Override
    public KbTplLearningResult processKbTplLearning() {
        log.info("开始处理知识库模板学习任务");

        List<TrainKbTplPre> unLearnedRecords = trainKbTplPreMapper.selectByLearnStatusWithLimit(
                LearnStatus.UN_LEARN.getCode(), BATCH_SIZE);

        if (unLearnedRecords.isEmpty()) {
            log.info("没有找到未学习状态的pre记录，跳过处理");
            return new KbTplLearningResult(0, 0);
        }

        log.info("找到 {} 条pre未学习记录，开始处理", unLearnedRecords.size());

        String systemPrompt = getSystemPrompt();
        ContextResult contextResult = getOrCreateContextId(systemPrompt);
        String contextId = contextResult.getId();
        log.info("获取contextId: {}", contextId);

        Executor executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<CompletableFuture<SingleRecordProcessorService.TokenResult>> futures = new ArrayList<>();

        for (TrainKbTplPre record : unLearnedRecords) {
            CompletableFuture<SingleRecordProcessorService.TokenResult> future = CompletableFuture.supplyAsync(() ->
                    singleRecordProcessorService.processSingleRecord(record, contextId), executor);
            futures.add(future);
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long totalTokens = processResults(unLearnedRecords, futures);

        log.info("知识库模板学习任务处理完成，共处理 {} 条记录，总token消耗: {}",
                unLearnedRecords.size(), totalTokens);
        return new KbTplLearningResult(unLearnedRecords.size(), totalTokens);
    }

    private long processResults(List<TrainKbTplPre> unLearnedRecords, List<CompletableFuture<SingleRecordProcessorService.TokenResult>> futures) {
        AtomicLong totalTokens = new AtomicLong(0);
        Map<Long, List<TrainKbTplPre>> groupedByTplId = unLearnedRecords.stream()
                .collect(Collectors.groupingBy(TrainKbTplPre::getTplId));

        for (Map.Entry<Long, List<TrainKbTplPre>> entry : groupedByTplId.entrySet()) {
            Long tplId = entry.getKey();
            List<TrainKbTplPre> recordsInGroup = entry.getValue();

            long[] templateTokens = calculateTemplateTokens(recordsInGroup, unLearnedRecords, futures);
            totalTokens.addAndGet(templateTokens[0]);

            updateMainTableStatus(tplId, LearnStatus.LEARNED, templateTokens[0], templateTokens[1], templateTokens[2]);
            log.info("模板 tplId: {} 处理完成，包含 {} 条记录，token消耗: {}", tplId, recordsInGroup.size(), templateTokens[0]);
        }
        return totalTokens.get();
    }

    private long[] calculateTemplateTokens(List<TrainKbTplPre> recordsInGroup, List<TrainKbTplPre> allRecords, List<CompletableFuture<SingleRecordProcessorService.TokenResult>> futures) {
        long total = 0, prompt = 0, completion = 0;
        for (TrainKbTplPre record : recordsInGroup) {
            for (int i = 0; i < allRecords.size(); i++) {
                if (allRecords.get(i).getId().equals(record.getId())) {
                    try {
                        SingleRecordProcessorService.TokenResult tokenResult = futures.get(i).get();
                        total += tokenResult.getTotalTokens();
                        prompt += tokenResult.getPromptTokens();
                        completion += tokenResult.getCompletionTokens();
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("获取记录 {} token消耗失败", record.getId(), e);
                    }
                    break; // Found the record, break inner loop
                }
            }
        }
        return new long[]{total, prompt, completion};
    }

    @Transactional(rollbackFor = Exception.class)
    public Long processTemplate(Long tplId, List<TrainKbTplPre> records, String contextId, AtomicLong totalTokens) {
        long templateTokens = 0;
        try {
            log.info("开始处理模板 tplId: {}, 记录数: {}", tplId, records.size());
            updateMainTableStatus(tplId, LearnStatus.LEARNING, 0L);

            long totalPromptTokens = 0;
            long totalCompletionTokens = 0;
            for (TrainKbTplPre record : records) {
                SingleRecordProcessorService.TokenResult tokenResult = singleRecordProcessorService.processSingleRecord(record, contextId);
                templateTokens += tokenResult.getTotalTokens();
                totalPromptTokens += tokenResult.getPromptTokens();
                totalCompletionTokens += tokenResult.getCompletionTokens();
            }

            updateMainTableStatus(tplId, LearnStatus.LEARNED, templateTokens, totalPromptTokens, totalCompletionTokens);
            log.info("模板 tplId: {} 处理完成，token消耗: {}", tplId, templateTokens);
            return templateTokens;
        } catch (Exception e) {
            log.error("处理模板 tplId: {} 时发生异常", tplId, e);
            throw e;
        }
    }

    private String getSystemPrompt() {
        try {
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_GENE_1ST_CHAT_TPL_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                throw new RuntimeException("未找到系统提示词模板");
            }
            String respPrmt = prompts.get(0);
            return respPrmt.replace("{{number}}", String.valueOf(llmOutputSize));
        } catch (Exception e) {
            log.error("获取系统提示词失败", e);
            throw new RuntimeException("获取系统提示词失败", e);
        }
    }

    private void updateMainTableStatus(Long tplId, LearnStatus status, Long tokens) {
        updateMainTableStatus(tplId, status, tokens, 0L, 0L);
    }

    private void updateMainTableStatus(Long tplId, LearnStatus status, Long tokens, Long tokenIn, Long tokenOut) {
        try {
            TrainKbTplMain mainRecord = trainKbTplMainMapper.selectById(tplId);
            if (mainRecord != null) {
                mainRecord.setLearnStatus(status.getCode());
                mainRecord.setUpdater("system");
                if (status == LearnStatus.LEARNED && tokens != null && tokens > 0) {
                    mainRecord.setTokens(mainRecord.getTokens() + tokens);
                    if (tokenIn != null && tokenIn > 0) {
                        mainRecord.setTokenIn((mainRecord.getTokenIn() != null ? mainRecord.getTokenIn() : 0L) + tokenIn);
                    }
                    if (tokenOut != null && tokenOut > 0) {
                        mainRecord.setTokenOut((mainRecord.getTokenOut() != null ? mainRecord.getTokenOut() : 0L) + tokenOut);
                    }
                    log.debug("更新模板 tplId: {} 状态为: {}，累加token: {}，tokenIn: {}，tokenOut: {}",
                            tplId, status.getDescription(), tokens, tokenIn, tokenOut);
                } else {
                    log.debug("更新模板 tplId: {} 状态为: {}", tplId, status.getDescription());
                }
                trainKbTplMainMapper.updateById(mainRecord);
            }
        } catch (Exception e) {
            log.error("更新模板 tplId: {} 状态失败", tplId, e);
            throw e;
        }
    }

    private ContextResult getOrCreateContextId(String systemPrompt) {
        try {
            String cacheKey = generateCacheKey(systemPrompt);
            var cachedResult = cacheManager.get(cacheKey, ContextResult.class);
            if (cachedResult.isPresent()) {
                log.info("从缓存中获取到ContextId: {}", cachedResult.get().getId());
                return cachedResult.get();
            }
            log.info("缓存中未找到ContextId，调用豆包大模型生成");
            ContextResult contextResult = doubaoBigModelServiceImpl.generateContextId(systemPrompt);
            cacheManager.put(cacheKey, contextResult, CONTEXT_CACHE_TTL_HOURS, TimeUnit.HOURS);
            log.info("ContextId已存入缓存: {}, cacheKey: {}", contextResult.getId(), cacheKey);
            return contextResult;
        } catch (Exception e) {
            log.error("获取或创建ContextId失败", e);
            log.warn("缓存操作失败，直接调用豆包大模型生成ContextId");
            return doubaoBigModelServiceImpl.generateContextId(systemPrompt);
        }
    }

    private String generateCacheKey(String systemPrompt) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(systemPrompt.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            String hash = sb.toString();
            return CONTEXT_CACHE_PREFIX + hash;
        } catch (Exception e) {
            log.error("生成缓存key失败", e);
            return CONTEXT_CACHE_PREFIX + "len_" + systemPrompt.length();
        }
    }
}