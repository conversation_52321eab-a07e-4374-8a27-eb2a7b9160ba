package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.converkb.QaImportResponse;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportDtlMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportMainMapper;
import com.yiyi.ai_train_playground.service.OssService;
import com.yiyi.ai_train_playground.service.converkb.QaImportService;

import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;


import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 问答导入服务实现类
 */
@Slf4j
@Service
public class QaImportServiceImpl implements QaImportService {
    
    @Autowired
    private TrainQaImportDtlMapper trainQaImportDtlMapper;

    @Autowired
    private TrainQaImportMainMapper trainQaImportMainMapper;

    @Autowired
    private OssService ossService;
    

    
    @Override
    @Transactional
    public QaImportResponse importQaExcel(MultipartFile file, Long teamId, String userId) {
        log.info("开始导入问答Excel，文件名: {}, 团队ID: {}, 用户ID: {}", 
                file.getOriginalFilename(), teamId, userId);
        
        QaImportResponse response = new QaImportResponse();
        List<QaImportResponse.QaItem> successItems = new ArrayList<>();
        List<QaImportResponse.FailedItem> failedItems = new ArrayList<>();
        List<String[]> allRowData = new ArrayList<>();

        try {
            // 1. 先插入主表记录
            Long qaMainId = insertMainRecord(file.getOriginalFilename(), teamId, userId);
            log.info("插入主表记录成功，qaMainId: {}", qaMainId);
            // 读取Excel文件
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            // 检查是否有数据
            if (sheet.getPhysicalNumberOfRows() <= 1) {
                response.setTotalCount(0);
                response.setSuccessCount(0);
                response.setFailCount(0);
                response.setSuccessItems(successItems);
                response.setFailedItems(failedItems);
                return response;
            }
            
            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("Excel文件格式不正确，缺少表头");
            }
            
            // 表头验证
            String[] expectedHeaders = {"问题", "答案"};
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null || !expectedHeaders[i].equals(cell.getStringCellValue().trim())) {
                    throw new RuntimeException("表头格式不正确，应为：问题、答案");
                }
            }
            
            // 处理数据行
            int dataRows = sheet.getPhysicalNumberOfRows() - 1; // 减去表头
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                try {
                    // 检查是否为空行
                    boolean isEmpty = true;
                    for (int j = 0; j < 2; j++) { // 检查前两列是否全为空
                        Cell cell = row.getCell(j);
                        if (cell != null && cell.getCellType() != CellType.BLANK) {
                            isEmpty = false;
                            break;
                        }
                    }
                    if (isEmpty) continue;
                    
                    String question = getCellValueAsString(row.getCell(0));
                    String answer = getCellValueAsString(row.getCell(1));
                    
                    // 存储行数据
                    String[] rowData = {question, answer};
                    allRowData.add(rowData);
                    
                    String errorReason = null;
                    
                    // 验证必填字段
                    if (question.trim().isEmpty() || answer.trim().isEmpty()) {
                        errorReason = "问题和答案不能为空";
                    }
                    // 验证问题长度
                    else if (question.length() > 500) {
                        errorReason = "问题长度不能超过500个字符";
                    }
                    // 检查问题唯一性
                    else if (trainQaImportDtlMapper.existsByQuestion(question.trim(), teamId) > 0) {
                        errorReason = "问题已存在：" + question;
                    }
                    
                    if (errorReason != null) {
                        QaImportResponse.FailedItem failedItem = new QaImportResponse.FailedItem();
                        failedItem.setRow(i + 1);
                        failedItem.setQuestion(question.isEmpty() ? "缺失" : question);
                        failedItem.setReason(errorReason);
                        failedItems.add(failedItem);
                        continue;
                    }
                    
                    // 插入详情表
                    TrainQaImportDtl qaImportDtl = new TrainQaImportDtl();
                    qaImportDtl.setQaMainId(qaMainId);
                    qaImportDtl.setQuestion(question.trim());
                    qaImportDtl.setAnswer(answer.trim());
                    qaImportDtl.setTeamId(teamId);
                    qaImportDtl.setCreator(SecurityUtil.getCurrentUsername());
                    qaImportDtl.setUpdater(userId);

                    trainQaImportDtlMapper.insert(qaImportDtl);
                    
                    // 添加成功项
                    QaImportResponse.QaItem successItem = new QaImportResponse.QaItem();
                    successItem.setQuestion(question.trim());
                    successItem.setAnswer(answer.trim());
                    successItems.add(successItem);
                    
                } catch (Exception e) {
                    log.error("解析第{}行数据出错: {}", i + 1, e.getMessage());
                    QaImportResponse.FailedItem failedItem = new QaImportResponse.FailedItem();
                    failedItem.setRow(i + 1);
                    failedItem.setQuestion(getCellValueAsString(row.getCell(0)));
                    failedItem.setReason("数据格式错误: " + e.getMessage());
                    failedItems.add(failedItem);
                }
            }
            
            // 生成成功和失败的Excel文件并上传到OSS
            String successExcelUrl = null;
            String failedExcelUrl = null;

            try {
                // 生成成功导入的Excel文件
                if (!successItems.isEmpty()) {
                    successExcelUrl = generateSuccessExcel(successItems, teamId, userId);
                }

                // 生成失败明细Excel文件
                if (!failedItems.isEmpty()) {
                    failedExcelUrl = generateFailedExcel(failedItems, allRowData, teamId, userId);
                }
            } catch (Exception e) {
                log.warn("生成Excel文件失败: {}", e.getMessage());
                // 不影响主流程，继续执行
            }

            // 构建响应
            response.setTotalCount(dataRows);
            response.setSuccessCount(successItems.size());
            response.setFailCount(failedItems.size());
            response.setSuccessItems(successItems);
            response.setFailedItems(failedItems);
            response.setSuccessExcelUrl(successExcelUrl);
            response.setFailedExcelUrl(failedExcelUrl);
            
            workbook.close();
            
            log.info("问答Excel导入完成，总数: {}, 成功: {}, 失败: {}", 
                    dataRows, successItems.size(), failedItems.size());
            
            return response;
            
        } catch (IOException e) {
            log.error("解析Excel文件失败", e);
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage());
        }
    }
    
    @Override
    public String validateQaExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "请选择要上传的Excel文件";
        }
        
        // 检查文件格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
            return "文件格式不正确，请上传Excel文件（.xlsx或.xls）";
        }
        
        try {
            // 读取Excel文件进行格式验证
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            // 检查是否有数据
            if (sheet.getPhysicalNumberOfRows() <= 1) {
                return "Excel文件中没有数据";
            }
            
            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return "Excel文件格式不正确，缺少表头";
            }
            
            // 表头验证
            String[] expectedHeaders = {"问题", "答案"};
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null || !expectedHeaders[i].equals(cell.getStringCellValue().trim())) {
                    return "表头格式不正确，应为：问题、答案";
                }
            }
            
            workbook.close();
            return null; // 验证通过
            
        } catch (IOException e) {
            log.error("验证Excel文件失败", e);
            return "Excel文件格式错误: " + e.getMessage();
        }
    }
    
    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 生成成功导入的Excel文件并上传到OSS
     */
    private String generateSuccessExcel(List<QaImportResponse.QaItem> successItems, Long teamId, String userId) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("成功导入的问答");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("问题");
        headerRow.createCell(1).setCellValue("答案");

        // 填充数据
        for (int i = 0; i < successItems.size(); i++) {
            Row row = sheet.createRow(i + 1);
            QaImportResponse.QaItem item = successItems.get(i);
            row.createCell(0).setCellValue(item.getQuestion());
            row.createCell(1).setCellValue(item.getAnswer());
        }

        // 自动调整列宽
        sheet.autoSizeColumn(0);
        sheet.autoSizeColumn(1);

        // 转换为MultipartFile并上传
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        byte[] excelBytes = outputStream.toByteArray();
        workbook.close();
        outputStream.close();

        String fileName = "qa_import_success_" + System.currentTimeMillis() + ".xlsx";
        MultipartFile excelFile = createMultipartFile(fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                excelBytes);

        // 上传到OSS，使用EXCEL类型(0)
        return ossService.upload(excelFile, 0, teamId);
    }

    /**
     * 生成失败明细Excel文件并上传到OSS
     */
    private String generateFailedExcel(List<QaImportResponse.FailedItem> failedItems,
                                     List<String[]> allRowData, Long teamId, String userId) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("导入失败明细");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("行号");
        headerRow.createCell(1).setCellValue("问题");
        headerRow.createCell(2).setCellValue("答案");
        headerRow.createCell(3).setCellValue("失败原因");

        // 填充数据
        for (int i = 0; i < failedItems.size(); i++) {
            Row row = sheet.createRow(i + 1);
            QaImportResponse.FailedItem item = failedItems.get(i);

            row.createCell(0).setCellValue(item.getRow());
            row.createCell(1).setCellValue(item.getQuestion());

            // 从原始数据中获取答案
            String answer = "";
            int dataIndex = item.getRow() - 2; // 减去表头和1-based索引
            if (dataIndex >= 0 && dataIndex < allRowData.size()) {
                String[] rowData = allRowData.get(dataIndex);
                if (rowData.length > 1) {
                    answer = rowData[1];
                }
            }
            row.createCell(2).setCellValue(answer);
            row.createCell(3).setCellValue(item.getReason());
        }

        // 自动调整列宽
        for (int i = 0; i < 4; i++) {
            sheet.autoSizeColumn(i);
        }

        // 转换为MultipartFile并上传
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        byte[] excelBytes = outputStream.toByteArray();
        workbook.close();
        outputStream.close();

        String fileName = "qa_import_failed_" + System.currentTimeMillis() + ".xlsx";
        MultipartFile excelFile = createMultipartFile(fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                excelBytes);

        // 上传到OSS，使用EXCEL类型(0)
        return ossService.upload(excelFile, 0, teamId);
    }

    /**
     * 创建MultipartFile实例
     */
    private MultipartFile createMultipartFile(String fileName, String contentType, byte[] content) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return content == null || content.length == 0;
            }

            @Override
            public long getSize() {
                return content == null ? 0 : content.length;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return content;
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new ByteArrayInputStream(content);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                throw new UnsupportedOperationException("transferTo not supported");
            }
        };
    }

    /**
     * 插入主表记录
     * @param fileName 文件名
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 主表ID
     */
    private Long insertMainRecord(String fileName, Long teamId, String userId) {
        // 生成批次号：年月日时分秒毫秒
        String batchNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));

        // 从文件名中提取知识库名称（去掉扩展名）
        String qaImName = fileName;
        if (fileName != null && fileName.contains(".")) {
            qaImName = fileName.substring(0, fileName.lastIndexOf("."));
        }

        TrainQaImportMain mainRecord = new TrainQaImportMain();
        mainRecord.setBatchNo(batchNo);
        mainRecord.setQaImName(qaImName);
        mainRecord.setQaImDesc(qaImName); // 描述和名称相同
        mainRecord.setTeamId(teamId);
        mainRecord.setCreator(SecurityUtil.getCurrentUsername());
        mainRecord.setUpdater(userId);

        trainQaImportMainMapper.insert(mainRecord);

        log.info("插入主表记录成功，批次号: {}, 知识库名称: {}, 主表ID: {}",
                batchNo, qaImName, mainRecord.getId());

        return mainRecord.getId();
    }

}
