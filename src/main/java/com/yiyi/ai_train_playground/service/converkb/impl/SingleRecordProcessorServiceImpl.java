package com.yiyi.ai_train_playground.service.converkb.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.doubao.DoubaoResponseDto;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplDetail;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.enums.SalesPhase;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplDetailMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplPreMapper;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import com.yiyi.ai_train_playground.service.converkb.SingleRecordProcessorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识库模板学习单个记录处理器服务实现类
 *
 * <AUTHOR> Assistant (Refactored)
 * @since 2025-08-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SingleRecordProcessorServiceImpl implements SingleRecordProcessorService {

    private final TrainKbTplPreMapper trainKbTplPreMapper;
    private final TrainKbTplDetailMapper trainKbTplDetailMapper;
    private final SuperBigModelInterface doubaoBigModelService;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TokenResult processSingleRecord(TrainKbTplPre record, String contextId) {
        try {
            log.debug("开始处理单条pre记录 id: {}, tplId: {}", record.getId(), record.getTplId());

            // 1. 处理记录并获取token结果 (移除了更新主表状态的逻辑)
            TokenResult tokenResult = processRecord(record, contextId);

            // 2. 更新预处理记录状态为已学习
            updatePreRecordStatus(record.getId(), LearnStatus.LEARNED);

            log.debug("单条pre记录 id: {} 处理完成，token消耗: {},prompt消耗:{},completion消耗:{}", record.getId(), tokenResult.getTotalTokens()
                    ,tokenResult.getPromptTokens(),tokenResult.getCompletionTokens());
            return tokenResult;

        } catch (Exception e) {
            log.error("处理单条pre记录 id: {} 时发生异常", record.getId(), e);
            // 发生异常时，将预处理记录状态回滚为未学习
            try {
                updatePreRecordStatus(record.getId(), LearnStatus.UN_LEARN);
            } catch (Exception rollbackException) {
                log.error("回滚pre记录 id: {} 状态时发生异常", record.getId(), rollbackException);
            }
            // 由于此方法是事务性的，Spring会处理主逻辑的回滚。此处的异常需要向上抛出以触发回滚。
            throw e;
        }
    }

    private TokenResult processRecord(TrainKbTplPre record, String contextId) {
        try {
            log.debug("处理pre id: {}, tplId: {}", record.getId(), record.getTplId());

            String userPrompt = record.getContent();

            List<com.volcengine.ark.runtime.model.completion.chat.ChatMessage> messages = new ArrayList<>();
            messages.add(com.volcengine.ark.runtime.model.completion.chat.ChatMessage.builder()
                    .role(com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole.USER)
                    .content(userPrompt)
                    .build());

            DoubaoResponseDto responseDto = doubaoBigModelService.ntnsWithCtxWithToken(messages, contextId, null);
            String response = responseDto.getMessage();

            long totalTokens = 0, promptTokens = 0, completionTokens = 0;
            if (responseDto.getUsage() != null) {
                totalTokens = responseDto.getUsage().getTotalTokens() != null ? responseDto.getUsage().getTotalTokens() : 0L;
                promptTokens = responseDto.getUsage().getPromptTokens() != null ? responseDto.getUsage().getPromptTokens() : 0L;
                completionTokens = responseDto.getUsage().getCompletionTokens() != null ? responseDto.getUsage().getCompletionTokens() : 0L;
            }

            log.debug("LLM响应: {}, token消耗 - total: {}, prompt: {}, completion: {}",
                    response, totalTokens, promptTokens, completionTokens);

            List<ChatTemplateItem> templateItems = parseResponse(response);
            insertTemplateDetails(record.getTplId(), record.getTeamId(), templateItems, record.getId());

            log.debug("pre记录 id: {} 处理完成，token消耗 - total: {}, prompt: {}, completion: {}",
                    record.getId(), totalTokens, promptTokens, completionTokens);
            return new TokenResult(totalTokens, promptTokens, completionTokens);

        } catch (Exception e) {
            log.error("处理pre id: {} 时发生异常", record.getId(), e);
            throw e;
        }
    }

    private void updatePreRecordStatus(Long recordId, LearnStatus status) {
        try {
            TrainKbTplPre preRecord = trainKbTplPreMapper.selectById(recordId);
            if (preRecord != null) {
                preRecord.setLearnStatus(status.getCode());
                preRecord.setUpdater("system");
                trainKbTplPreMapper.updateById(preRecord);
                log.debug("更新预处理记录 id: {} 状态为: {}", recordId, status.getDescription());
            }
        } catch (Exception e) {
            log.error("更新预处理记录 id: {} 状态失败", recordId, e);
            throw e;
        }
    }

    private List<ChatTemplateItem> parseResponse(String response) {
        try {
            String sanitizedResponse = sanitizeJsonString(response);
            log.debug("原始响应长度: {}, 清理后长度: {}", response.length(), sanitizedResponse.length());

            JsonNode rootNode = objectMapper.readTree(sanitizedResponse);

            if (rootNode.has("results") && rootNode.get("results").isArray()) {
                JsonNode resultsNode = rootNode.get("results");
                return objectMapper.convertValue(resultsNode, new TypeReference<List<ChatTemplateItem>>() {});
            } else if (rootNode.isArray()) {
                return objectMapper.convertValue(rootNode, new TypeReference<List<ChatTemplateItem>>() {});
            } else {
                log.error("未识别的响应格式: {}", sanitizedResponse);
                throw new RuntimeException("未识别的响应格式");
            }

        } catch (Exception e) {
            log.error("解析LLM响应失败: {}", response, e);
            throw new RuntimeException("解析LLM响应失败", e);
        }
    }

    private String sanitizeJsonString(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) return jsonString;
        try {
            StringBuilder result = new StringBuilder();
            boolean inString = false, escaped = false;
            for (int i = 0; i < jsonString.length(); i++) {
                char c = jsonString.charAt(i);
                if (!inString) {
                    if (c == '"') inString = true;
                    result.append(c);
                } else {
                    if (escaped) {
                        result.append(c);
                        escaped = false;
                    } else if (c == '\\') {
                        result.append(c);
                        escaped = true;
                    } else if (c == '"') {
                        result.append(c);
                        inString = false;
                    } else if (isControlCharacter(c)) {
                        result.append(escapeControlCharacter(c));
                    } else {
                        result.append(c);
                    }
                }
            }
            return result.toString();
        } catch (Exception e) {
            log.warn("清理JSON字符串时发生异常，返回原字符串", e);
            return jsonString;
        }
    }

    private boolean isControlCharacter(char c) { return c >= 0x00 && c <= 0x1F; }

    private String escapeControlCharacter(char c) {
        switch (c) {
            case '\n': return "\\n";
            case '\r': return "\\r";
            case '\t': return "\\t";
            case '\b': return "\\b";
            case '\f': return "\\f";
            case '\0': return "\\u0000";
            default: return String.format("\\u%04x", (int) c);
        }
    }

    private void insertTemplateDetails(Long tplId, Long teamId, List<ChatTemplateItem> templateItems, Long preId) {
        try {
            List<TrainKbTplDetail> details = new ArrayList<>();
            for (ChatTemplateItem item : templateItems) {
                TrainKbTplDetail detail = new TrainKbTplDetail();
                detail.setTplId(tplId);
                detail.setContent(item.getContent());
                detail.setTplType(mapTypeCodeToSalesPhase(item.getTypeCode()));
                detail.setPreId(preId);
                detail.setTeamId(teamId);
                detail.setCreator("system");
                detail.setUpdater("system");
                details.add(detail);
            }
            if (!details.isEmpty()) {
                trainKbTplDetailMapper.batchInsert(details);
                log.debug("插入 {} 条模板详情记录，preId: {}", details.size(), preId);
            }
        } catch (Exception e) {
            log.error("插入模板详情失败", e);
            throw e;
        }
    }

    private String mapTypeCodeToSalesPhase(String typeCode) {
        switch (typeCode) {
            case "0": return SalesPhase.PRE_SALES.getCode();
            case "1": return SalesPhase.SALING.getCode();
            case "2": return SalesPhase.AFTER_SALE.getCode();
            default: return SalesPhase.OTHER.getCode();
        }
    }
}