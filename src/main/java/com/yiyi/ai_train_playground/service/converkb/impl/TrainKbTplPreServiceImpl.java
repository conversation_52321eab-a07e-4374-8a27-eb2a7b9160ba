package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplPreMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplPreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 会话知识库模板预处理服务实现类
 */
@Service
public class TrainKbTplPreServiceImpl implements TrainKbTplPreService {
    
    private static final Logger log = LoggerFactory.getLogger(TrainKbTplPreServiceImpl.class);
    
    @Autowired
    private TrainKbTplPreMapper trainKbTplPreMapper;
    
    @Override
    public TrainKbTplPre getById(Long id, Long teamId) {
        if (id == null || teamId == null) {
            return null;
        }
        return trainKbTplPreMapper.selectByIdAndTeamId(id, teamId);
    }
    
    @Override
    public List<TrainKbTplPre> getByTplId(Long tplId, Long teamId) {
        if (tplId == null || teamId == null) {
            return new ArrayList<>();
        }
        return trainKbTplPreMapper.selectByTplId(tplId, teamId);
    }
    
    @Override
    public List<TrainKbTplPre> getByTplIdAndLearnStatus(Long tplId, String learnStatus, Long teamId) {
        if (tplId == null || learnStatus == null || teamId == null) {
            return new ArrayList<>();
        }
        return trainKbTplPreMapper.selectByTplIdAndLearnStatus(tplId, learnStatus, teamId);
    }
    
    @Override
    public List<TrainKbTplPre> getByLearnStatus(String learnStatus, Long teamId) {
        if (learnStatus == null || teamId == null) {
            return new ArrayList<>();
        }
        return trainKbTplPreMapper.selectByLearnStatus(learnStatus, teamId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPreRecord(Long tplId, String content, Integer index, String learnStatus, Long teamId, String userId) {
        log.info("创建预处理记录，tplId: {}, index: {}, learnStatus: {}, teamId: {}", tplId, index, learnStatus, teamId);
        
        if (tplId == null || content == null || index == null || learnStatus == null || teamId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        TrainKbTplPre record = new TrainKbTplPre();
        record.setTplId(tplId);
        record.setContent(content);
        record.setIndex(index);
        record.setLearnStatus(learnStatus);
        record.setTeamId(teamId);
        record.setCreator(userId != null ? userId : "0");
        record.setUpdater(userId != null ? userId : "0");
        
        int result = trainKbTplPreMapper.insert(record);
        if (result <= 0 || record.getId() == null) {
            throw new RuntimeException("创建预处理记录失败");
        }
        
        log.info("创建预处理记录成功，id: {}", record.getId());
        return record.getId();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreatePreRecords(Long tplId, List<String> contents, String learnStatus, Long teamId, String userId) {
        log.info("批量创建预处理记录，tplId: {}, count: {}, learnStatus: {}, teamId: {}", 
                tplId, contents != null ? contents.size() : 0, learnStatus, teamId);
        
        if (tplId == null || CollectionUtils.isEmpty(contents) || learnStatus == null || teamId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        List<TrainKbTplPre> records = new ArrayList<>();
        for (int i = 0; i < contents.size(); i++) {
            TrainKbTplPre record = new TrainKbTplPre();
            record.setTplId(tplId);
            record.setContent(contents.get(i));
            record.setIndex(i + 1); // 从1开始编号
            record.setLearnStatus(learnStatus);
            record.setTeamId(teamId);
            record.setCreator(userId != null ? userId : "0");
            record.setUpdater(userId != null ? userId : "0");
            records.add(record);
        }
        
        int result = trainKbTplPreMapper.batchInsert(records);
        log.info("批量创建预处理记录完成，创建数量: {}", result);
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePreRecord(Long id, String content, Integer index, String learnStatus, Long teamId, String userId) {
        log.info("更新预处理记录，id: {}, index: {}, learnStatus: {}, teamId: {}", id, index, learnStatus, teamId);

        if (id == null || teamId == null) {
            return false;
        }

        // 查询原始记录以获取version
        TrainKbTplPre original = trainKbTplPreMapper.selectByIdAndTeamId(id, teamId);
        if (original == null) {
            log.warn("预处理记录不存在，id: {}, teamId: {}", id, teamId);
            return false;
        }

        // 使用动态SQL，直接传递参数值（包括null）
        TrainKbTplPre record = new TrainKbTplPre();
        record.setId(id);
        record.setContent(content); // 直接传递，让动态SQL处理null
        record.setIndex(index); // 直接传递，让动态SQL处理null
        record.setLearnStatus(learnStatus); // 直接传递，让动态SQL处理null
        record.setTeamId(teamId);
        record.setUpdater(userId != null ? userId : "0");
        record.setVersion(original.getVersion());

        int result = trainKbTplPreMapper.updateById(record);
        boolean success = result > 0;

        if (success) {
            log.info("更新预处理记录成功，id: {}", id);
        } else {
            log.warn("更新预处理记录失败，id: {}", id);
        }

        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateLearnStatus(Long tplId, String oldLearnStatus, String newLearnStatus, Long teamId, String userId) {
        log.info("批量更新学习状态，tplId: {}, {} -> {}, teamId: {}", tplId, oldLearnStatus, newLearnStatus, teamId);
        
        if (tplId == null || oldLearnStatus == null || newLearnStatus == null || teamId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        int result = trainKbTplPreMapper.updateLearnStatusByTplId(tplId, oldLearnStatus, newLearnStatus, teamId, 
                userId != null ? userId : "0");
        
        log.info("批量更新学习状态完成，更新数量: {}", result);
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePreRecord(Long id, Long teamId) {
        log.info("删除预处理记录，id: {}, teamId: {}", id, teamId);
        
        if (id == null || teamId == null) {
            return false;
        }
        
        int result = trainKbTplPreMapper.deleteById(id, teamId);
        boolean success = result > 0;
        
        if (success) {
            log.info("删除预处理记录成功，id: {}", id);
        } else {
            log.warn("删除预处理记录失败，id: {}", id);
        }
        
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByTplId(Long tplId, Long teamId) {
        log.info("删除模板的所有预处理记录，tplId: {}, teamId: {}", tplId, teamId);
        
        if (tplId == null || teamId == null) {
            return 0;
        }
        
        int result = trainKbTplPreMapper.deleteByTplId(tplId, teamId);
        log.info("删除模板的所有预处理记录完成，删除数量: {}", result);
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByTplIdAndLearnStatus(Long tplId, String learnStatus, Long teamId) {
        log.info("删除模板指定学习状态的记录，tplId: {}, learnStatus: {}, teamId: {}", tplId, learnStatus, teamId);
        
        if (tplId == null || learnStatus == null || teamId == null) {
            return 0;
        }
        
        int result = trainKbTplPreMapper.deleteByTplIdAndLearnStatus(tplId, learnStatus, teamId);
        log.info("删除模板指定学习状态的记录完成，删除数量: {}", result);
        return result;
    }
    
    @Override
    public Long countByTplId(Long tplId, Long teamId) {
        if (tplId == null || teamId == null) {
            return 0L;
        }
        return trainKbTplPreMapper.countByTplId(tplId, teamId);
    }
    
    @Override
    public Long countByTplIdAndLearnStatus(Long tplId, String learnStatus, Long teamId) {
        if (tplId == null || learnStatus == null || teamId == null) {
            return 0L;
        }
        return trainKbTplPreMapper.countByTplIdAndLearnStatus(tplId, learnStatus, teamId);
    }
    
    @Override
    public LearnProgress getLearnProgress(Long tplId, Long teamId) {
        if (tplId == null || teamId == null) {
            return new LearnProgress(0L, 0L, 0L, 0L);
        }
        
        Long totalCount = countByTplId(tplId, teamId);
        Long unLearnCount = countByTplIdAndLearnStatus(tplId, "un_learn", teamId);
        Long learningCount = countByTplIdAndLearnStatus(tplId, "learning", teamId);
        Long learnedCount = countByTplIdAndLearnStatus(tplId, "learned", teamId);
        
        return new LearnProgress(totalCount, unLearnCount, learningCount, learnedCount);
    }
}
