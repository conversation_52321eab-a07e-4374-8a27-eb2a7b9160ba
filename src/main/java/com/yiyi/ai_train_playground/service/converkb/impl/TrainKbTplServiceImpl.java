package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.converkb.KbTplCreateRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplQueryRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailResponse;
import com.yiyi.ai_train_playground.dto.converkb.KbTplListResponse;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplDetail;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplMainMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplDetailMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会话知识库模板服务实现类
 */
@Service
public class TrainKbTplServiceImpl implements TrainKbTplService {
    
    private static final Logger log = LoggerFactory.getLogger(TrainKbTplServiceImpl.class);
    
    @Autowired
    private TrainKbTplMainMapper trainKbTplMainMapper;
    
    @Autowired
    private TrainKbTplDetailMapper trainKbTplDetailMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createKbTpl(KbTplCreateRequest request, Long teamId, String userId) {
        log.info("开始创建会话知识库模板，teamId: {}, userId: {}, name: {}", teamId, userId, request.getName());
        
        // 检查名称是否已存在
        if (checkNameExists(request.getName(), teamId, null)) {
            throw new IllegalArgumentException("模板名称已存在");
        }
        
        // 创建主表记录
        TrainKbTplMain main = new TrainKbTplMain();
        main.setName(request.getName());
        // 如果description为空，使用name填充
        main.setDescription(request.getDescription() != null && !request.getDescription().trim().isEmpty() 
            ? request.getDescription() : request.getName());
        main.setTokens(0L); // 初始化为0
        main.setTeamId(teamId);
        main.setCreator(userId);
        main.setUpdater(userId);
        main.setFileType(request.getFileType());
        main.setLearnStatus(request.getLearnStatus());
        
        int mainResult = trainKbTplMainMapper.insert(main);
        if (mainResult <= 0 || main.getId() == null) {
            throw new RuntimeException("创建模板主表失败");
        }
        
        Long tplId = main.getId();
        log.info("创建模板主表成功，tplId: {}", tplId);
        
        // 创建明细记录
        if (!CollectionUtils.isEmpty(request.getDetails())) {
            List<TrainKbTplDetail> details = request.getDetails().stream().map(item -> {
                TrainKbTplDetail detail = new TrainKbTplDetail();
                detail.setTplId(tplId);
                detail.setContent(item.getContent());
                detail.setTplType(item.getTplType());
                detail.setTeamId(teamId);
                detail.setCreator(userId);
                detail.setUpdater(userId);
                return detail;
            }).collect(Collectors.toList());
            
            int detailResult = trainKbTplDetailMapper.batchInsert(details);
            if (detailResult <= 0) {
                throw new RuntimeException("创建模板明细失败");
            }
            log.info("创建模板明细成功，数量: {}", details.size());
        }
        
        log.info("创建会话知识库模板完成，tplId: {}", tplId);
        return tplId;
    }
    
    @Override
    public KbTplDetailResponse getKbTplDetail(Long id, Long teamId, KbTplDetailRequest request) {
        log.info("查询模板详情，id: {}, teamId: {}, page: {}, pageSize: {}", id, teamId, request.getPage(), request.getPageSize());
        
        // 查询主表
        TrainKbTplMain main = trainKbTplMainMapper.selectByIdAndTeamId(id, teamId);
        if (main == null) {
            return null;
        }
        
        // 分页查询明细
        int offset = (request.getPage() - 1) * request.getPageSize();
        
        List<KbTplDetailResponse.KbTplDetailItem> detailItems = 
            trainKbTplDetailMapper.selectPageByTplId(id, teamId, request, offset);
        
        Long total = trainKbTplDetailMapper.selectPageCountByTplId(id, teamId, request);
        
        PageResult<KbTplDetailResponse.KbTplDetailItem> detailPageResult = 
            new PageResult<>(detailItems, total, request.getPage(), request.getPageSize());
        
        // 组装响应
        KbTplDetailResponse response = new KbTplDetailResponse();
        response.setId(main.getId());
        response.setName(main.getName());
        response.setDescription(main.getDescription());
        response.setTokens(main.getTokens());
        response.setFileType(main.getFileType());
        response.setLearnStatus(main.getLearnStatus());
        response.setCreateTime(main.getCreateTime());
        response.setUpdateTime(main.getUpdateTime());
        response.setVersion(main.getVersion());
        response.setDetails(detailPageResult);
        
        return response;
    }
    
    @Override
    public PageResult<KbTplListResponse> getKbTplPageList(KbTplQueryRequest request, Long teamId) {
        log.info("分页查询模板列表，teamId: {}, page: {}, pageSize: {}", teamId, request.getPage(), request.getPageSize());
        
        // 计算偏移量
        int offset = (request.getPage() - 1) * request.getPageSize();
        
        // 查询总数
        Long total = trainKbTplMainMapper.selectPageCount(request, teamId);
        if (total == 0) {
            return new PageResult<>(new ArrayList<>(), total, request.getPage(), request.getPageSize());
        }
        
        // 查询数据
        List<KbTplListResponse> records = trainKbTplMainMapper.selectPageList(request, teamId, offset);
        
        return new PageResult<>(records, total, request.getPage(), request.getPageSize());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateKbTpl(Long id, KbTplCreateRequest request, Long teamId, String userId) {
        log.info("更新模板，id: {}, teamId: {}, userId: {}", id, teamId, userId);
        
        // 查询原始记录
        TrainKbTplMain original = trainKbTplMainMapper.selectByIdAndTeamId(id, teamId);
        if (original == null) {
            log.warn("模板不存在，id: {}, teamId: {}", id, teamId);
            return false;
        }
        
        // 检查名称是否已存在（排除当前记录）
        if (checkNameExists(request.getName(), teamId, id)) {
            throw new IllegalArgumentException("模板名称已存在");
        }
        
        // 更新主表
        TrainKbTplMain main = new TrainKbTplMain();
        main.setId(id);
        main.setName(request.getName());
        main.setDescription(request.getDescription()); // 使用request的description
        main.setTokens(original.getTokens()); // 保持原有token数量
        main.setTeamId(teamId);
        main.setUpdater(userId);
        main.setFileType(request.getFileType());
        main.setLearnStatus(request.getLearnStatus());
        main.setVersion(original.getVersion());
        
        int mainResult = trainKbTplMainMapper.updateById(main);
        if (mainResult <= 0) {
            log.warn("更新模板主表失败，id: {}", id);
            return false;
        }
        
        // 删除原有明细
        trainKbTplDetailMapper.deleteByTplId(id, teamId);
        
        // 插入新明细
        if (!CollectionUtils.isEmpty(request.getDetails())) {
            List<TrainKbTplDetail> details = request.getDetails().stream().map(item -> {
                TrainKbTplDetail detail = new TrainKbTplDetail();
                detail.setTplId(id);
                detail.setContent(item.getContent());
                detail.setTplType(item.getTplType());
                detail.setTeamId(teamId);
                detail.setCreator(userId);
                detail.setUpdater(userId);
                return detail;
            }).collect(Collectors.toList());
            
            int detailResult = trainKbTplDetailMapper.batchInsert(details);
            if (detailResult <= 0) {
                throw new RuntimeException("更新模板明细失败");
            }
            log.info("更新模板明细成功，数量: {}", details.size());
        }
        
        log.info("更新模板完成，id: {}", id);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteKbTpl(Long id, Long teamId) {
        log.info("删除模板，id: {}, teamId: {}", id, teamId);
        
        // 删除明细
        trainKbTplDetailMapper.deleteByTplId(id, teamId);
        log.info("删除模板明细完成，id: {}", id);
        
        // 删除主表
        int result = trainKbTplMainMapper.deleteById(id, teamId);
        if (result <= 0) {
            log.warn("删除模板主表失败，id: {}, teamId: {}", id, teamId);
            return false;
        }
        
        log.info("删除模板完成，id: {}", id);
        return true;
    }
    
    @Override
    public boolean checkNameExists(String name, Long teamId, Long excludeId) {
        int count = trainKbTplMainMapper.checkNameExists(name, teamId, excludeId);
        return count > 0;
    }
    
    @Override
    public boolean updateKbTplTokens(Long id, Long tokens, Long teamId) {
        log.info("更新模板token数量，id: {}, tokens: {}, teamId: {}", id, tokens, teamId);

        int result = trainKbTplMainMapper.updateTokens(id, tokens, teamId);
        if (result <= 0) {
            log.warn("更新模板token数量失败，id: {}, tokens: {}, teamId: {}", id, tokens, teamId);
            return false;
        }

        return true;
    }

    @Override
    public boolean updateKbTplLearnStatus(Long id, String learnStatus, Long teamId) {
        log.info("更新模板学习状态，id: {}, learnStatus: {}, teamId: {}", id, learnStatus, teamId);

        int result = trainKbTplMainMapper.updateLearnStatus(id, learnStatus, teamId);
        if (result <= 0) {
            log.warn("更新模板学习状态失败，id: {}, learnStatus: {}, teamId: {}", id, learnStatus, teamId);
            return false;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFileImportTemplate(String fileName, String fileType, Long teamId, String userId) {
        log.info("创建文件导入模板，fileName: {}, fileType: {}, teamId: {}, userId: {}", fileName, fileType, teamId, userId);

        // 创建主表记录
        TrainKbTplMain main = new TrainKbTplMain();
        main.setName(fileName);
        main.setDescription(fileName); // 使用fileName填充description
        main.setTokens(0L); // 初始化为0
        main.setTeamId(teamId);
        main.setCreator(userId);
        main.setUpdater(userId);
        main.setFileType(fileType);
        main.setLearnStatus(com.yiyi.ai_train_playground.enums.LearnStatus.FILE_UPLOADING.getCode()); // 设置为文件上传中

        int mainResult = trainKbTplMainMapper.insert(main);
        if (mainResult <= 0 || main.getId() == null) {
            throw new RuntimeException("创建文件导入模板失败");
        }

        Long tplId = main.getId();
        log.info("创建文件导入模板成功，tplId: {}", tplId);
        return tplId;
    }

    @Override
    public String getRandomKbDetailByTaskId(Long taskId, Long teamId) {
        log.info("根据任务ID获取随机知识库明细内容，taskId: {}, teamId: {}", taskId, teamId);

        if (taskId == null || teamId == null) {
            log.warn("任务ID或团队ID为空，taskId: {}, teamId: {}", taskId, teamId);
            return null;
        }

        try {
            String content = trainKbTplDetailMapper.selectRandomContentByTaskId(taskId, teamId);
            if (content == null) {
                log.warn("未找到任务对应的知识库明细内容，taskId: {}, teamId: {}", taskId, teamId);
            } else {
                log.info("成功获取随机知识库明细内容，taskId: {}, teamId: {}, contentLength: {}",
                        taskId, teamId, content.length());
            }
            return content;
        } catch (Exception e) {
            log.error("获取随机知识库明细内容失败，taskId: {}, teamId: {}", taskId, teamId, e);
            return null;
        }
    }
    
    @Override
    public List<KbTplDetailForTaskCreation> getKbTplDetailsForTaskCreation(Long kbTplId, Long teamId) {
        log.info("根据知识库模板ID获取所有明细，kbTplId: {}, teamId: {}", kbTplId, teamId);
        
        if (kbTplId == null || teamId == null) {
            log.warn("知识库模板ID或团队ID为空，kbTplId: {}, teamId: {}", kbTplId, teamId);
            return new ArrayList<>();
        }
        
        try {
            List<TrainKbTplDetail> details = trainKbTplDetailMapper.selectByTplId(kbTplId, teamId);
            if (CollectionUtils.isEmpty(details)) {
                log.warn("未找到知识库模板下的明细数据，kbTplId: {}, teamId: {}", kbTplId, teamId);
                return new ArrayList<>();
            }
            
            List<KbTplDetailForTaskCreation> result = details.stream()
                    .map(detail -> new KbTplDetailForTaskCreation(detail.getId(), detail.getContent()))
                    .collect(Collectors.toList());
                    
            log.info("成功获取知识库模板明细数据，kbTplId: {}, teamId: {}, count: {}", kbTplId, teamId, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取知识库模板明细数据失败，kbTplId: {}, teamId: {}", kbTplId, teamId, e);
            return new ArrayList<>();
        }
    }
}