package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportDtlMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportDtlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 问答导入明细服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
@Slf4j
@Service
public class TrainQaImportDtlServiceImpl implements TrainQaImportDtlService {

    @Autowired
    private TrainQaImportDtlMapper trainQaImportDtlMapper;

    @Override
    public List<TrainQaImportDtl> getDtlListByQaMainId(Long qaMainId, String question, int offset, int pageSize) {
        log.info("根据主表ID分页查询详情列表：qaMainId={}, question={}, offset={}, pageSize={}", qaMainId, question, offset, pageSize);
        return trainQaImportDtlMapper.selectByQaMainId(qaMainId, question, offset, pageSize);
    }

    @Override
    public int countByQaMainId(Long qaMainId, String question) {
        log.info("根据主表ID统计详情数量：qaMainId={}, question={}", qaMainId, question);
        return trainQaImportDtlMapper.countByQaMainId(qaMainId, question);
    }

    @Override
    public List<TrainQaImportDtl> getDtlListByQuestionLike(String question, Long teamId, int offset, int pageSize) {
        log.info("根据问题模糊查询详情列表：question={}, teamId={}, offset={}, pageSize={}", question, teamId, offset, pageSize);
        return trainQaImportDtlMapper.selectByQuestionLike(question, teamId, offset, pageSize);
    }

    @Override
    public int countByQuestionLike(String question, Long teamId) {
        log.info("根据问题模糊查询统计数量：question={}, teamId={}", question, teamId);
        return trainQaImportDtlMapper.countByQuestionLike(question, teamId);
    }

    @Override
    public List<TrainQaImportDtl> getDtlListByTeamId(Long teamId, int offset, int pageSize) {
        log.info("根据团队ID分页查询所有详情：teamId={}, offset={}, pageSize={}", teamId, offset, pageSize);
        return trainQaImportDtlMapper.selectByTeamId(teamId, offset, pageSize);
    }

    @Override
    public int countByTeamId(Long teamId) {
        log.info("根据团队ID统计所有详情数量：teamId={}", teamId);
        return trainQaImportDtlMapper.countByTeamId(teamId);
    }

    @Override
    public List<QaSimpleDto> getAllDtlByMainId(Long qaMainId) {
        log.info("根据主表ID查询所有明细（只返回question和answer字段）：qaMainId={}", qaMainId);
        List<QaSimpleDto> result = trainQaImportDtlMapper.selectAllByMainId(qaMainId);
        log.info("查询结果数量：{}", result.size());
        return result;
    }

    @Override
    public List<QaSimpleDto> getRdmListByMain(Long qaMainId, Integer count) {
        log.info("根据主表ID随机获取指定数量的明细：qaMainId={}, count={}", qaMainId, count);
        List<QaSimpleDto> result = trainQaImportDtlMapper.selectRandomByMainId(qaMainId, count);
        log.info("随机查询结果数量：{}", result.size());
        return result;
    }

    @Override
    public TrainQaImportDtl getDtlById(Long id) {
        log.info("根据ID查询详情：id={}", id);
        return trainQaImportDtlMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDtl(TrainQaImportDtl dtl, Long teamId, String creator) {
        log.info("创建详情记录：question={}, teamId={}, creator={}", dtl.getQuestion(), teamId, creator);
        
        // 检查问题是否已存在
        if (existsByQuestion(dtl.getQuestion(), teamId)) {
            throw new RuntimeException("问题已存在：" + dtl.getQuestion());
        }
        
        dtl.setTeamId(teamId);
        dtl.setCreator(creator);
        dtl.setUpdater(creator);
        
        trainQaImportDtlMapper.insert(dtl);
        log.info("创建详情记录成功：id={}", dtl.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDtl(Long id, TrainQaImportDtl dtl, Long teamId, String updater) {
        log.info("更新详情记录：id={}, question={}, teamId={}, updater={}", id, dtl.getQuestion(), teamId, updater);
        
        // 查询原记录
        TrainQaImportDtl original = trainQaImportDtlMapper.selectById(id);
        if (original == null || !original.getTeamId().equals(teamId)) {
            throw new RuntimeException("记录不存在或无权限");
        }
        
        // 如果问题发生变化，检查新问题是否重复
        if (!original.getQuestion().equals(dtl.getQuestion())) {
            if (existsByQuestion(dtl.getQuestion(), teamId)) {
                throw new RuntimeException("问题已存在：" + dtl.getQuestion());
            }
        }
        
        dtl.setId(id);
        dtl.setTeamId(teamId);
        dtl.setUpdater(updater);
        
        trainQaImportDtlMapper.updateById(dtl);
        log.info("更新详情记录成功：id={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDtl(Long id, Long teamId) {
        log.info("删除详情记录：id={}, teamId={}", id, teamId);
        
        // 验证权限
        TrainQaImportDtl detail = trainQaImportDtlMapper.selectById(id);
        if (detail == null || !detail.getTeamId().equals(teamId)) {
            throw new RuntimeException("记录不存在或无权限");
        }
        
        trainQaImportDtlMapper.deleteById(id);
        log.info("删除详情记录成功：id={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteDtl(List<Long> ids, Long teamId) {
        log.info("批量删除详情记录：ids={}, teamId={}", ids, teamId);
        
        // 验证所有记录的权限
        for (Long id : ids) {
            TrainQaImportDtl detail = trainQaImportDtlMapper.selectById(id);
            if (detail == null || !detail.getTeamId().equals(teamId)) {
                throw new RuntimeException("记录ID " + id + " 不存在或无权限");
            }
        }
        
        // 批量删除
        trainQaImportDtlMapper.deleteByIds(ids);
        log.info("批量删除详情记录成功：ids={}", ids);
    }

    @Override
    public boolean existsByQuestion(String question, Long teamId) {
        log.info("检查问题是否存在：question={}, teamId={}", question, teamId);
        return trainQaImportDtlMapper.existsByQuestion(question, teamId) > 0;
    }
}