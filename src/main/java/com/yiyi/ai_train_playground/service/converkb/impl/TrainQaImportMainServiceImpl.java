package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportMainMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportDtlMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 问答导入主表服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
@Slf4j
@Service
public class TrainQaImportMainServiceImpl implements TrainQaImportMainService {

    @Autowired
    private TrainQaImportMainMapper trainQaImportMainMapper;
    
    @Autowired
    private TrainQaImportDtlMapper trainQaImportDtlMapper;

    @Override
    public List<TrainQaImportMain> getMainListByTeamId(Long teamId, int offset, int pageSize) {
        log.info("根据团队ID分页查询知识库列表：teamId={}, offset={}, pageSize={}", teamId, offset, pageSize);
        return trainQaImportMainMapper.selectByTeamId(teamId, offset, pageSize);
    }

    @Override
    public List<TrainQaImportMain> getMainListByTeamId(Long teamId, String creator, int offset, int pageSize) {
        log.info("根据团队ID和创建人分页查询知识库列表：teamId={}, creator={}, offset={}, pageSize={}", teamId, creator, offset, pageSize);
        return trainQaImportMainMapper.selectByTeamIdAndCreator(teamId, creator, offset, pageSize);
    }

    @Override
    public List<TrainQaImportMain> getMainListByConditions(Long teamId, String creator, String qaImName, int offset, int pageSize) {
        log.info("根据条件分页查询知识库列表：teamId={}, creator={}, qaImName={}, offset={}, pageSize={}", teamId, creator, qaImName, offset, pageSize);
        return trainQaImportMainMapper.selectByConditions(teamId, creator, qaImName, offset, pageSize);
    }

    @Override
    public int countByTeamId(Long teamId) {
        log.info("根据团队ID统计知识库数量：teamId={}", teamId);
        return trainQaImportMainMapper.countByTeamId(teamId);
    }

    @Override
    public int countByTeamId(Long teamId, String creator) {
        log.info("根据团队ID和创建人统计知识库数量：teamId={}, creator={}", teamId, creator);
        return trainQaImportMainMapper.countByTeamIdAndCreator(teamId, creator);
    }

    @Override
    public int countByConditions(Long teamId, String creator, String qaImName) {
        log.info("根据条件统计知识库数量：teamId={}, creator={}, qaImName={}", teamId, creator, qaImName);
        return trainQaImportMainMapper.countByConditions(teamId, creator, qaImName);
    }

    @Override
    public TrainQaImportMain getMainById(Long id) {
        log.info("根据ID查询知识库详情：id={}", id);
        return trainQaImportMainMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(Long id, String qaImName, String qaImDesc, Long teamId, String updater) {
        log.info("更新知识库信息：id={}, qaImName={}, qaImDesc={}, teamId={}, updater={}", id, qaImName, qaImDesc, teamId, updater);
        
        // 验证权限
        TrainQaImportMain main = trainQaImportMainMapper.selectById(id);
        if (main == null || !main.getTeamId().equals(teamId)) {
            throw new RuntimeException("知识库不存在或无权限");
        }
        
        // 更新知识库信息
        trainQaImportMainMapper.updateMain(id, qaImName, qaImDesc, updater);
        
        log.info("更新知识库成功：id={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMainWithDetails(Long id, Long teamId) {
        log.info("删除知识库及其详情：id={}, teamId={}", id, teamId);
        
        // 验证权限
        TrainQaImportMain main = trainQaImportMainMapper.selectById(id);
        if (main == null || !main.getTeamId().equals(teamId)) {
            throw new RuntimeException("知识库不存在或无权限");
        }
        
        // 先删除详情记录
        trainQaImportDtlMapper.deleteByQaMainId(id);
        
        // 再删除主表记录
        trainQaImportMainMapper.deleteById(id);
        
        log.info("删除知识库成功：id={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteMainWithDetails(List<Long> ids, Long teamId) {
        log.info("批量删除知识库及其详情：ids={}, teamId={}", ids, teamId);
        
        // 验证权限并删除详情
        for (Long id : ids) {
            TrainQaImportMain main = trainQaImportMainMapper.selectById(id);
            if (main == null || !main.getTeamId().equals(teamId)) {
                throw new RuntimeException("知识库ID " + id + " 不存在或无权限");
            }
            
            // 删除详情记录
            trainQaImportDtlMapper.deleteByQaMainId(id);
        }
        
        // 批量删除主表记录
        trainQaImportMainMapper.deleteByIds(ids);
        
        log.info("批量删除知识库成功：ids={}", ids);
    }
}