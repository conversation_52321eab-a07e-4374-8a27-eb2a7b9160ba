package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.converkb.*;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.mapper.converkb.TrainTaskConvKbDtlMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务会话明细服务实现类
 */
@Service
public class TrainTaskConvKbDtlServiceImpl implements TrainTaskConvKbDtlService {
    
    private static final Logger log = LoggerFactory.getLogger(TrainTaskConvKbDtlServiceImpl.class);
    
    @Autowired
    private TrainTaskConvKbDtlMapper trainTaskConvKbDtlMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTaskConvKbDtl(TaskConvKbDtlCreateRequest request, Long teamId, String userId) {
        log.info("开始创建任务会话明细，teamId: {}, userId: {}, taskId: {}, kbDtlId: {}", 
                teamId, userId, request.getTaskId(), request.getKbDtlId());
        
        TrainTaskConvKbDtl entity = new TrainTaskConvKbDtl();
        entity.setTaskId(request.getTaskId());
        entity.setKbDtlId(request.getKbDtlId());
        entity.setLearningStatus(request.getLearningStatus() != null ? request.getLearningStatus() : LearnStatus.UN_LEARN.getCode());
        entity.setF1stRawChatlog(request.getF1stRawChatlog());
        entity.setFinalChatLog(request.getFinalChatLog());
        entity.setTeamId(teamId);
        entity.setCreator(userId);
        entity.setUpdater(userId);
        
        int result = trainTaskConvKbDtlMapper.insert(entity);
        if (result > 0) {
            log.info("创建任务会话明细成功，id: {}", entity.getId());
            return entity.getId();
        } else {
            throw new RuntimeException("创建任务会话明细失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateTaskConvKbDtl(List<TaskConvKbDtlCreateRequest> requests, Long teamId, String userId) {
        if (CollectionUtils.isEmpty(requests)) {
            return 0;
        }
        
        log.info("开始批量创建任务会话明细，teamId: {}, userId: {}, count: {}", teamId, userId, requests.size());
        
        List<TrainTaskConvKbDtl> entities = requests.stream().map(request -> {
            TrainTaskConvKbDtl entity = new TrainTaskConvKbDtl();
            entity.setTaskId(request.getTaskId());
            entity.setKbDtlId(request.getKbDtlId());
            entity.setLearningStatus(request.getLearningStatus() != null ? request.getLearningStatus() : LearnStatus.UN_LEARN.getCode());
            entity.setF1stRawChatlog(request.getF1stRawChatlog());
            entity.setFinalChatLog(request.getFinalChatLog());
            entity.setTeamId(teamId);
            entity.setCreator(userId);
            entity.setUpdater(userId);
            return entity;
        }).collect(Collectors.toList());
        
        int result = trainTaskConvKbDtlMapper.batchInsert(entities);
        log.info("批量创建任务会话明细完成，成功数量: {}", result);
        return result;
    }
    
    @Override
    public TaskConvKbDtlDetailDTO getTaskConvKbDtlDetail(Long id, Long teamId) {
        log.info("获取任务会话明细详情，id: {}, teamId: {}", id, teamId);
        
        TrainTaskConvKbDtl entity = trainTaskConvKbDtlMapper.selectById(id);
        if (entity == null || !entity.getTeamId().equals(teamId)) {
            return null;
        }
        
        TaskConvKbDtlDetailDTO dto = new TaskConvKbDtlDetailDTO();
        dto.setId(entity.getId());
        dto.setTaskId(entity.getTaskId());
        dto.setKbDtlId(entity.getKbDtlId());
        dto.setLearningStatus(entity.getLearningStatus());
        dto.setLearningStatusDesc(getLearnStatusDesc(entity.getLearningStatus()));
        dto.setFinalChatLog(entity.getFinalChatLog());
        dto.setTeamId(entity.getTeamId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setCreator(entity.getCreator());
        dto.setUpdater(entity.getUpdater());
        dto.setVersion(entity.getVersion());
        
        return dto;
    }
    
    @Override
    public PageResult<TaskConvKbDtlListDTO.TaskConvKbDtlItem> getTaskConvKbDtlPageList(TaskConvKbDtlQueryRequest request, Long teamId) {
        log.info("分页查询任务会话明细列表，teamId: {}, page: {}, pageSize: {}", teamId, request.getPage(), request.getPageSize());
        
        // 这里需要在mapper中添加分页查询方法，暂时返回空结果
        // TODO: 实现分页查询逻辑
        return new PageResult<>(new ArrayList<>(), 0L, request.getPage(), request.getPageSize());
    }
    
    @Override
    public List<TaskConvKbDtlDetailDTO> getTaskConvKbDtlByTaskId(Long taskId, Long teamId) {
        log.info("根据任务ID获取明细列表，taskId: {}, teamId: {}", taskId, teamId);

        List<TrainTaskConvKbDtl> entities = trainTaskConvKbDtlMapper.selectByTaskId(taskId, teamId);

        return entities.stream().map(entity -> {
            TaskConvKbDtlDetailDTO dto = new TaskConvKbDtlDetailDTO();
            dto.setId(entity.getId());
            dto.setTaskId(entity.getTaskId());
            dto.setKbDtlId(entity.getKbDtlId());
            dto.setLearningStatus(entity.getLearningStatus());
            dto.setLearningStatusDesc(getLearnStatusDesc(entity.getLearningStatus()));
            dto.setFinalChatLog(entity.getFinalChatLog());
            dto.setTeamId(entity.getTeamId());
            dto.setCreateTime(entity.getCreateTime());
            dto.setUpdateTime(entity.getUpdateTime());
            dto.setCreator(entity.getCreator());
            dto.setUpdater(entity.getUpdater());
            dto.setVersion(entity.getVersion());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public TaskConvKbDtlDetailDTO getRdmConvDtlByTaskId(Long taskId, Long teamId) {
        log.info("根据任务ID随机获取明细，taskId: {}, teamId: {}", taskId, teamId);

        TrainTaskConvKbDtl entity = trainTaskConvKbDtlMapper.selectRandomByTaskId(taskId, teamId);
        if (entity == null) {
            log.warn("未找到任务会话明细，taskId: {}, teamId: {}", taskId, teamId);
            return null;
        }

        TaskConvKbDtlDetailDTO dto = new TaskConvKbDtlDetailDTO();
        dto.setId(entity.getId());
        dto.setTaskId(entity.getTaskId());
        dto.setKbDtlId(entity.getKbDtlId());
        dto.setLearningStatus(entity.getLearningStatus());
        dto.setLearningStatusDesc(getLearnStatusDesc(entity.getLearningStatus()));
        dto.setF1stRawChatlog(entity.getF1stRawChatlog());
        dto.setFinalChatLog(entity.getFinalChatLog());
        dto.setTeamId(entity.getTeamId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setCreator(entity.getCreator());
        dto.setUpdater(entity.getUpdater());
        dto.setVersion(entity.getVersion());

        log.info("随机获取到明细，id: {}, finalChatLog长度: {}",
                entity.getId(), entity.getFinalChatLog() != null ? entity.getFinalChatLog().length() : 0);

        return dto;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskConvKbDtl(Long id, TaskConvKbDtlUpdateRequest request, Long teamId, String userId) {
        log.info("更新任务会话明细，id: {}, teamId: {}, userId: {}", id, teamId, userId);
        
        TrainTaskConvKbDtl entity = new TrainTaskConvKbDtl();
        entity.setId(id);
        entity.setLearningStatus(request.getLearningStatus());
        entity.setFinalChatLog(request.getFinalChatLog());
        entity.setTeamId(teamId);
        entity.setUpdater(userId);
        
        int result = trainTaskConvKbDtlMapper.updateById(entity);
        return result > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLearningStatus(Long id, String learningStatus, Long teamId, String userId) {
        log.info("更新学习状态，id: {}, learningStatus: {}, teamId: {}, userId: {}", id, learningStatus, teamId, userId);
        
        int result = trainTaskConvKbDtlMapper.updateLearningStatus(id, learningStatus, userId, teamId);
        return result > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateF1stRawChatlog(Long id, String f1stRawChatlog, Long teamId, String userId) {
        log.info("更新初始聊天记录，id: {}, teamId: {}, userId: {}", id, teamId, userId);
        
        int result = trainTaskConvKbDtlMapper.updateF1stRawChatlog(id, f1stRawChatlog, userId, teamId);
        return result > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFinalChatLog(Long id, String finalChatLog, Long teamId, String userId) {
        log.info("更新最终聊天记录，id: {}, teamId: {}, userId: {}", id, teamId, userId);
        
        int result = trainTaskConvKbDtlMapper.updateFinalChatLog(id, finalChatLog, userId, teamId);
        return result > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTaskConvKbDtl(Long id, Long teamId) {
        log.info("删除任务会话明细，id: {}, teamId: {}", id, teamId);
        
        int result = trainTaskConvKbDtlMapper.deleteById(id, teamId);
        return result > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByTaskId(Long taskId, Long teamId) {
        log.info("根据任务ID删除所有明细，taskId: {}, teamId: {}", taskId, teamId);
        
        return trainTaskConvKbDtlMapper.deleteByTaskId(taskId, teamId);
    }
    
    @Override
    public int countByTaskId(Long taskId, Long teamId) {
        return trainTaskConvKbDtlMapper.countByTaskId(taskId, teamId);
    }
    
    @Override
    public int countByTaskIdAndStatus(Long taskId, String learningStatus, Long teamId) {
        return trainTaskConvKbDtlMapper.countByTaskIdAndStatus(taskId, learningStatus, teamId);
    }
    
    /**
     * 获取学习状态描述
     */
    private String getLearnStatusDesc(String learningStatus) {
        if (learningStatus == null) {
            return null;
        }
        
        try {
            LearnStatus status = LearnStatus.fromCode(learningStatus);
            return status.getDescription();
        } catch (Exception e) {
            return learningStatus;
        }
    }
}