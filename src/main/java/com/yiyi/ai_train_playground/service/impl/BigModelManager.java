package com.yiyi.ai_train_playground.service.impl;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.enums.SceneName;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import com.yiyi.ai_train_playground.config.QdrantConfig;


/**
 * 大模型管理器 - 管理所有的会话和历史消息
 */
@Slf4j
@Service
public class BigModelManager {

    @Autowired
    private SuperBigModelInterface bigModelService;

    @Autowired
    private DoubaoBigModelServiceImpl doubaoBigModelService;

    @Autowired
    private TrainTeamMapper trainTeamMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private QdrantConfig.QdrantSearchConfig qdrantSearchConfig;

    @Value("${product.processing.collection-name:train_prod_collection}")
    private String collectionName;

    // Redis TTL 设置为24小时
    private static final long REDIS_TTL_HOURS = 24;

    // Redis Key 前缀常量
    private static final String SESSION_KEY_PREFIX = "session:";
    private static final String CHATLOG_KEY_PREFIX = "chatlog:";


    // 固定的角色模板（后期从数据库加载）
    private static final List<String> ROLES = Arrays.asList(
            "买洗碗机的客户，对产品功能、价格、售后服务都有疑问",
            "买冰箱的客户，关心冰箱的容量、能耗、保鲜效果",
            "买电脑的客户，需要咨询配置、性能、适用场景",
            "买小米SU7的客户，对电动车的续航、充电、智能功能感兴趣"
    );




    /**
     * 初始化会话（支持自定义系统提示词）
     *
     * @param sceneName         场景名称
     * @param servicerId        客服ID（可选，如果为空则从JWT获取）
     * @param token             JWT令牌
     * @param isThinking        是否使用思考模型
     * @param isStreaming       是否使用流式响应
     * @param systemPrompt      自定义系统提示词，如果为null则使用默认的
     * @param externalProductId 第一个产品的ID
     * @param prodType
     * @return 包含sessionId和robotName的Map
     */
    public Map<String, Object> initSession(String sceneName, String servicerId, String token, Boolean isThinking, Boolean isStreaming, String systemPrompt, String externalProductId, Integer prodType) {
        return initSingleRobot(sceneName, servicerId, token, -1, isThinking, isStreaming, systemPrompt, externalProductId,prodType);
    }




    /**
     *
     * 此方法没有在业务中被使用，可以废弃
     * 批量初始化机器人会话（支持自定义系统提示词和数量）
     *
     * @param sceneName     场景名称
     * @param servicerId    客服ID（可选，如果为空则从JWT获取）
     * @param token         JWT令牌
     * @param isThinking    是否使用思考模型
     * @param isStreaming   是否使用流式响应
     * @param systemPrompts 自定义系统提示词数组，如果为null则使用默认的
     * @param robotCount    机器人数量，如果为null则根据场景决定
     * @return 包含机器人信息的列表
     */
    public List<Map<String, Object>> initMultipleRobots(String sceneName, String servicerId, String token, Boolean isThinking, Boolean isStreaming, String[] systemPrompts, Integer robotCount) {
        List<Map<String, Object>> robotList = new ArrayList<>();

        // 确定创建的机器人数量
        int actualRobotCount;
        SceneName scene = SceneName.fromCode(sceneName);

        if (robotCount != null && robotCount > 0) {
            // 使用指定的机器人数量
            actualRobotCount = robotCount;
        } else {
            // 根据场景决定机器人数量
            if (scene == SceneName.TRIAL_ONE) {
                actualRobotCount = 1;
            } else {
                actualRobotCount = ROLES.size(); // 默认4个
            }
        }

        // 创建指定数量的机器人
        for (int i = 0; i < actualRobotCount; i++) {
            try {
                // 获取系统提示词
                String systemPrompt = null;
                if (systemPrompts != null && i < systemPrompts.length) {
                    systemPrompt = systemPrompts[i];
                }

                // 确定角色索引
                int roleIndex = -1; // 默认随机
                if (scene != SceneName.TRIAL_ONE && i < ROLES.size()) {
                    roleIndex = i; // 使用预定义角色
                }

                Map<String, Object> robot = initSingleRobot(sceneName, servicerId, token, roleIndex, isThinking, isStreaming, systemPrompt, null,0 );
                robotList.add(robot);
            } catch (Exception e) {
                log.error("初始化第{}个机器人失败", i + 1, e);
                // 继续创建其他机器人，不因一个失败而停止
            }
        }

        return robotList;
    }

    /**
     * 获取上下文ID
     * @param systemPrompt 系统提示词
     * @return 上下文结果对象
     */
    private ContextResult getContextId(String systemPrompt) {
        return doubaoBigModelService.generateContextId(systemPrompt);
    }

    /**
     * 清除会话资源
     * @param sessionId 会话ID
     */
    public void clearSession(String sessionId) {
        try {
            log.info("开始清除会话资源: sessionId={}", sessionId);

            // 删除聊天记录
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;
            boolean chatLogDeleted = cacheManager.delete(chatLogKey);
            log.info("删除聊天记录: {}, success={}", chatLogKey, chatLogDeleted);

            // 删除会话信息
            String sessionKey = SESSION_KEY_PREFIX + sessionId;
            boolean sessionDeleted = cacheManager.delete(sessionKey);
            log.info("删除会话信息: {}, success={}", sessionKey, sessionDeleted);

            log.info("会话资源清除完成: sessionId={}", sessionId);
        } catch (Exception e) {
            log.error("清除会话资源失败: sessionId={}", sessionId, e);
            // 不抛出异常，避免影响连接关闭流程
        }
    }

    /**
     * 初始化单个机器人会话
     *
     * @param sceneName         场景名称
     * @param servicerId        客服ID（可选，如果为空则从JWT获取）
     * @param token             JWT令牌
     * @param roleIndex         角色索引，-1表示随机选择
     * @param isThinking        是否使用思考模型
     * @param isStreaming       是否使用流式响应
     * @param systemPrompt      自定义系统提示词，如果为null则使用默认的
     * @param externalProductId 第1个产品的ID
     * @param prodType
     * @return 包含sessionId和robotName的Map
     */
    private Map<String, Object> initSingleRobot(String sceneName, String servicerId, String token, int roleIndex, Boolean isThinking, Boolean isStreaming, String systemPrompt, String externalProductId, Integer prodType) {
        try {
            SceneName scene = SceneName.fromCode(sceneName);

            // 获取客服ID
            String realServiceId = servicerId;
            if (realServiceId == null || realServiceId.isEmpty()) {
                Long userId = jwtUtil.getUserIdFromToken(token);
                realServiceId = userId != null ? userId.toString() : "default";
            }

            // 生成机器人ID和会话ID
            String robotId = UUID.randomUUID().toString().substring(0, 8);
            String sessionId = robotId + "_" + realServiceId;

            // 生成机器人名称
            String robotName = generateRobotName(scene, roleIndex);

            // 获取客服名称（团队名称）
            String serviceName = getServiceName(token);

            // 生成系统提示词
            String systemPromptToUse = systemPrompt != null ? systemPrompt : generateSystemPrompt(roleIndex);

            List<ChatMessage> messages = new ArrayList<>();

            // 调用getContextId生成上下文ID
            ContextResult contextResult = getContextId(systemPromptToUse);
            String contextId = contextResult.getId();

            // 获取teamId从token中
            Long teamId = jwtUtil.getTeamIdFromToken(token);

            // 存储会话信息到Redis，包括contextId
            try {
                // 封装后的方法调用，替代直接操作Redis的代码
                saveSessionToRedis(sessionId, systemPromptToUse, robotName, serviceName,
                                  isThinking, isStreaming, externalProductId, teamId, new ArrayList<>(), contextId, prodType);

                log.info("会话信息已存储到Redis: sessionId={}, contextId={}", sessionId, contextId);
            } catch (Exception e) {
                log.error("存储会话信息到Redis失败: sessionId={}", sessionId, e);
                // 不抛出异常，继续执行，保证业务不受Redis影响
            }

            // 生成firstMessage（使用新的逻辑）
            String firstMessage;
            if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
                // 构建用户消息
                ChatMessage userMessage = ChatMessage.builder()
                        .role(ChatMessageRole.USER)
                        .content("你先发言")
                        .build();
                messages.add(userMessage);

                // 使用带上下文的方法
                firstMessage = doubaoBigModelService.ntnsWithCtx(messages, contextId);
            } else {
                // 如果没有自定义系统提示词，使用默认的假消息
                firstMessage = generateFirstMessage();
            }

            // 将firstMessage添加到会话历史中
            List<ChatMessage> sessionMessages = new ArrayList<>();
            sessionMessages.add(ChatMessage.builder()
                    .role(ChatMessageRole.ASSISTANT)
                    .content(firstMessage)
                    .build());
            addChatMessagesToRedis(sessionId, sessionMessages);

            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("robotName", robotName);
            result.put("serviceName", serviceName);
            result.put("firstMessage", firstMessage);

            log.info("初始化会话成功: sessionId={}, robotName={}, serviceName={}",
                    sessionId, robotName, serviceName);

            return result;
        } catch (Exception e) {
            log.error("初始化会话失败", e);
            throw new RuntimeException("初始化会话失败: " + e.getMessage());
        }
    }

    /**
     * 将会话信息保存到Redis
     *
     * @param sessionId         会话ID
     * @param systemPrompt      系统提示词
     * @param robotName         机器人名称
     * @param serviceName       客服名称
     * @param isThinking        是否使用思考模型
     * @param isStreaming       是否使用流式响应
     * @param externalProductId 外部产品ID
     * @param teamId            团队ID
     * @param messages          初始消息列表
     * @param contextId         豆包缓存上下文ID
     * @param prodType
     */
    private void saveSessionToRedis(String sessionId, String systemPrompt, String robotName,
                                    String serviceName, Boolean isThinking, Boolean isStreaming,
                                    String externalProductId, Long teamId, List<ChatMessage> messages, String contextId, Integer prodType) {
        // 1. 存储聊天历史到Redis：chatlog:{sessionId}
        String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;
        cacheManager.put(chatLogKey, messages, REDIS_TTL_HOURS, TimeUnit.HOURS);

        // 2. 存储会话配置信息到Redis hash：session:{sessionId}
        String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
        Map<String, Object> sessionConfig = new HashMap<>();
        
        // 基本会话配置
        sessionConfig.put("sessionSystemPrompts", systemPrompt);
        sessionConfig.put("sessionRobotNames", robotName);
        sessionConfig.put("sessionServiceNames", serviceName);
        
        // 模型配置
        sessionConfig.put("isThinking", isThinking != null ? isThinking : false);
        sessionConfig.put("isStreaming", isStreaming != null ? isStreaming : false);
        
        // 可选配置
        if (externalProductId != null) {
            sessionConfig.put("externalProductId", externalProductId);
        }

        // add by djwan 2027.7.30
        if (prodType != null) {
            sessionConfig.put("prodType", prodType);
        }
        
        if (teamId != null) {
            sessionConfig.put("teamId", teamId.toString());
        }

        //添加上下文缓存ID
        sessionConfig.put("sessionContextId", contextId);

        // 批量保存会话配置（带过期时间）
        cacheManager.putHash(sessionConfigKey, sessionConfig, REDIS_TTL_HOURS, TimeUnit.HOURS);
        
        log.debug("会话配置已保存到Redis: sessionId={}, configCount={}", sessionId, sessionConfig.size());
    }

    /**
     *
     * 接受客户端消息
     * 并回复消息
     *
     * @param sessionId   会话ID
     * @param userMessage 用户消息
     * @return 流式响应
     */
    public Flux<String> handlerAndResponse(String sessionId, String userMessage) {
        try {
            // 检查会话是否存在
            if (!isSessionExistInRedis(sessionId)) {
                return Flux.error(new RuntimeException("会话不存在: " + sessionId));
            }

            // 2.2、从redis中取出teamId、externalProductId
            String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
            Map<Object, Object> sessionFromRedis = cacheManager.getHash(sessionConfigKey);
            String teamId = (String) sessionFromRedis.get("teamId");
            String externalProductId = (String) sessionFromRedis.get("externalProductId");
            Integer prodType = (Integer) sessionFromRedis.get("prodType");
            log.info("从Redis获取会话配置: teamId={}, externalProductId={}", teamId, externalProductId);

            // 2.3、进行向量检索获取相关内容
            String trueAnswerFromQdrant = "";
            
            // 仅当teamId和externalProductId都存在时才进行向量搜索
            if (teamId != null && externalProductId != null) {
                log.info("开始向量检索，teamId: {}, externalProductId: {}", teamId, externalProductId);
                trueAnswerFromQdrant = vectorSearchService.searchByText(userMessage, teamId, externalProductId, qdrantSearchConfig.getLimit(), prodType, qdrantSearchConfig.getMinScore());
                
                if (!trueAnswerFromQdrant.isEmpty()) {
                    log.info("向量检索完成，找到相关内容长度: {}", trueAnswerFromQdrant.length());
                } else {
                    log.info("向量检索未找到相关内容");
                }
            } else {
                log.info("跳过向量检索，缺少必要参数: teamId={}, externalProductId={}", teamId, externalProductId);
            }



            // 2、从Redis获取现有的会话历史
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);

            // 3、将本轮用户消息存入Redis
            ChatMessage userChatMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(userMessage)
                    .build();
            addChatMessageToRedis(sessionId, userChatMessage);

            // 将用户消息也添加到当前消息列表中用于模型调用
            messages.add(userChatMessage);


            //从redis中获取
            boolean isThinking = (Boolean) sessionFromRedis.get("isThinking");
            boolean isStreaming = (Boolean) sessionFromRedis.get("isStreaming");

            //从redis中获取缓存上下文
            String contextId = (String) sessionFromRedis.get("sessionContextId");


            // 创建完整的消息列表副本用于传递给大模型
            List<ChatMessage> messagesForModel = new ArrayList<>(messages);

            // 2.2.4、messagesForModel需要新加一个trueAnswerFromQdrant
            String contextContent = !trueAnswerFromQdrant.isEmpty() ? trueAnswerFromQdrant : "知识库内容为空";
            String contextMessage = " 知识库详情A如下面3个#号括起来的: ### " + contextContent + " ###";
            messagesForModel.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(contextMessage)
                    .build());
            log.info("已添加{}上下文到消息列表", !trueAnswerFromQdrant.isEmpty() ? "向量检索" : "'知识库内容为空'");

            // 根据配置调用不同的方法
            if (isThinking && isStreaming) {
                // 思考模型 + 流式 = ts方法
                StringBuilder fullResponse = new StringBuilder();
                return bigModelService.ts(messagesForModel)
                        .doOnNext(response -> {
                            fullResponse.append(response);
                            log.debug("收到思考流式响应: sessionId={}, response={}", sessionId, response);
                        })
                        .doOnComplete(() -> {
                            // 4、将本轮模型回复消息存入Redis
                            ChatMessage assistantMessage = ChatMessage.builder()
                                    .role(ChatMessageRole.ASSISTANT)
                                    .content(fullResponse.toString())
                                    .build();
                            addChatMessageToRedis(sessionId, assistantMessage);
                            log.debug("思考流式响应完成: sessionId={}, fullResponse={}", sessionId, fullResponse.toString());


                        });
            } else if (isThinking && !isStreaming) {
                // 思考模型 + 非流式 = tns方法
                String response = bigModelService.tns(messagesForModel);
                // 将回复添加到会话历史

                // 4、将本轮模型回复消息存入Redis
                ChatMessage assistantMessage = ChatMessage.builder()
                        .role(ChatMessageRole.ASSISTANT)
                        .content(response)
                        .build();
                addChatMessageToRedis(sessionId, assistantMessage);

                return Flux.just(response)
                        .doOnNext(resp -> {
                            log.debug("收到思考非流式响应: sessionId={}, response={}", sessionId, resp);
                        });
            } else if (!isThinking && isStreaming) {
                // 普通模型 + 流式 = nts方法
                StringBuilder fullResponse = new StringBuilder();
                return bigModelService.nts(messagesForModel)
                        .doOnNext(response -> {
                            fullResponse.append(response);
                            log.debug("收到普通流式响应: sessionId={}, response={}", sessionId, response);
                        })

                        .doOnComplete(() -> {
                            // 4、将本轮模型回复消息存入Redis
                            ChatMessage assistantMessage = ChatMessage.builder()
                                    .role(ChatMessageRole.ASSISTANT)
                                    .content(fullResponse.toString())
                                    .build();
                            addChatMessageToRedis(sessionId, assistantMessage);
                            log.debug("普通流式响应完成: sessionId={}, fullResponse={}", sessionId, fullResponse.toString());
                        });

            } else {
                // 普通模型 + 非流式 = ntns方法
                /*String response = bigModelService.ntns(messagesForModel);*/
                String response =doubaoBigModelService.ntnsWithCtx(messagesForModel, contextId);
                // 将回复添加到会话历史


                // 4、将本轮模型回复消息存入Redis
                ChatMessage assistantMessage = ChatMessage.builder()
                        .role(ChatMessageRole.ASSISTANT)
                        .content(response)
                        .build();
                addChatMessageToRedis(sessionId, assistantMessage);

                return Flux.just(response)
                        .doOnNext(resp -> {
                            log.debug("收到普通非流式响应: sessionId={}, response={}", sessionId, resp);
                        });
            }
        } catch (Exception e) {
            log.error("发送消息失败: sessionId={}", sessionId, e);
            return Flux.error(new RuntimeException("发送消息失败: " + e.getMessage()));
        }
    }


    /**
     * 生成机器人名称
     *
     * @param scene     场景
     * @param roleIndex 角色索引，-1表示随机
     */
    private String generateRobotName(SceneName scene, int roleIndex) {
        String roleDescription;
        if (roleIndex >= 0 && roleIndex < ROLES.size()) {
            // 根据角色生成描述性名称
            String[] roleNames = {"洗碗机客户", "冰箱客户", "电脑客户", "小米SU7客户"};
            roleDescription = roleNames[roleIndex];
        } else {
            roleDescription = "随机客户" + (new Random().nextInt(999) + 1);
        }

        if (scene == SceneName.TRIAL_ONE) {
            return "试用-" + roleDescription;
        } else if (scene == SceneName.TRIAL_FOUR) {
            // 这里将从数据库中读取 TODO db
            return "随机-" + roleDescription;
        } else if (scene == SceneName.FORMAL) {
            // 这里将从数据库中读取 TODO db
            return "正式-" + roleDescription;
        } else {
            return roleDescription;
        }
    }

    /**
     * 获取客服名称（从团队表获取）
     */
    private String getServiceName(String token) {
        try {
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId != null) {
                TrainTeam team = trainTeamMapper.findById(teamId);
                if (team != null) {
                    return team.getName();
                }
            }
            return "客服";
        } catch (Exception e) {
            log.warn("获取团队名称失败", e);
            return "客服";
        }
    }

    /**
     * 生成系统提示词
     *
     * @param roleIndex 角色索引，-1表示随机选择
     */
    private String generateSystemPrompt(int roleIndex) {
        String role;
        if (roleIndex >= 0 && roleIndex < ROLES.size()) {
            // 使用指定的角色
            role = ROLES.get(roleIndex);
        } else {
            // 随机选择一个角色
            role = ROLES.get(new Random().nextInt(ROLES.size()));
        }
        // 这里将从数据库加载 TODO db
        return "你模拟一个客户，扮演成以下" + role + "，和客服聊天";
    }

    /**
     * 生成机器人首次发言
     */
    private String generateFirstMessage() {
        List<String> firstMessages = Arrays.asList(
                "你好，我想咨询一下产品的相关问题",
                "请问这个产品有什么特色功能吗？",
                "我对你们的产品很感兴趣，能详细介绍一下吗？",
                "你好，能帮我推荐一款合适的产品吗？"
        );
        return firstMessages.get(new Random().nextInt(firstMessages.size()));
    }





    // ==================== Redis 操作封装方法 ====================

    /**
     * 检查会话是否存在（从Redis中检查）
     *
     * @param sessionId 会话ID
     * @return 是否存在
     */
    private boolean isSessionExistInRedis(String sessionId) {
        try {
            String sessionKey = SESSION_KEY_PREFIX + sessionId;
            return cacheManager.exists(sessionKey);
        } catch (Exception e) {
            log.error("检查会话是否存在失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    /**
     * 从Redis获取聊天历史
     *
     * @param sessionId 会话ID
     * @return 聊天历史消息列表
     */
    @SuppressWarnings("unchecked")
    private List<ChatMessage> getChatHistoryFromRedis(String sessionId) {
        try {
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;
            Object chatHistoryObj = cacheManager.get(chatLogKey);
            if (chatHistoryObj != null) {
                // 如果是List类型，直接转换
                if (chatHistoryObj instanceof List) {
                    return (List<ChatMessage>) chatHistoryObj;
                }
                // 如果是字符串，尝试JSON反序列化
                String jsonStr = chatHistoryObj.toString();
                return objectMapper.readValue(jsonStr, new TypeReference<List<ChatMessage>>() {
                });
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("从Redis获取聊天历史失败: sessionId={}", sessionId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将聊天消息添加到Redis
     *
     * @param sessionId 会话ID
     * @param message   聊天消息
     */
    private void addChatMessageToRedis(String sessionId, ChatMessage message) {
        try {
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;

            // 获取现有的聊天历史
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);

            // 添加新消息
            messages.add(message);

            // 保存回Redis
            cacheManager.put(chatLogKey, messages, REDIS_TTL_HOURS, TimeUnit.HOURS);

            log.debug("成功将消息添加到Redis: sessionId={}, messageRole={}", sessionId, message.getRole());
        } catch (Exception e) {
            log.error("将聊天消息添加到Redis失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 批量将聊天消息添加到Redis
     *
     * @param sessionId   会话ID
     * @param newMessages 新的聊天消息列表
     */
    private void addChatMessagesToRedis(String sessionId, List<ChatMessage> newMessages) {
        try {
            String chatLogKey = CHATLOG_KEY_PREFIX + sessionId;

            // 获取现有的聊天历史
            List<ChatMessage> existingMessages = getChatHistoryFromRedis(sessionId);

            // 添加新消息
            existingMessages.addAll(newMessages);

            // 保存回Redis
            cacheManager.put(chatLogKey, existingMessages, REDIS_TTL_HOURS, TimeUnit.HOURS);

            log.debug("成功将{}条消息添加到Redis: sessionId={}", newMessages.size(), sessionId);
        } catch (Exception e) {
            log.error("将聊天消息批量添加到Redis失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 处理并响应消息（非流式版本）
     * 复制自handlerAndResponse方法，但返回String而不是Flux<String>
     * 这段代码我感觉还是要重构，写的太啰嗦了，但功能上没有问题
     * @param sessionId   会话ID
     * @param userMessage 用户消息
     * @return 非流式响应字符串
     */
    public String handlerAndRespWithNTNS(String sessionId, String userMessage) {
        try {
            // 检查会话是否存在
            if (!isSessionExistInRedis(sessionId)) {
                throw new RuntimeException("会话不存在: " + sessionId);
            }

            // 2.2、从redis中取出teamId、externalProductId
            String sessionConfigKey = SESSION_KEY_PREFIX + sessionId;
            Map<Object, Object> sessionFromRedis = cacheManager.getHash(sessionConfigKey);
            String teamId = (String) sessionFromRedis.get("teamId");
            String externalProductId = (String) sessionFromRedis.get("externalProductId");
            //从session里面获取prodType add by germmy
            Integer prodType = (Integer) sessionFromRedis.get("prodType");
            log.info("从Redis获取会话配置: teamId={}, externalProductId={}", teamId, externalProductId);

            // 2.3、进行向量检索获取相关内容
            String trueAnswerFromQdrant = "";

            // 2.4 获取最近的机器人回复消息
            String lastRobotResponse = getLastResponseFromRobot(sessionId);
            String searchText = lastRobotResponse != null ? lastRobotResponse : userMessage;

            // 仅当teamId和externalProductId都存在时才进行向量搜索
            if (teamId != null && externalProductId != null) {
                log.info("开始向量检索，teamId: {}, externalProductId: {}, 搜索文本来源: {}", teamId, externalProductId, lastRobotResponse != null ? "最近机器人回复" : "用户消息");
                trueAnswerFromQdrant = vectorSearchService.searchByText(searchText, teamId, externalProductId, qdrantSearchConfig.getLimit(), prodType, qdrantSearchConfig.getMinScore());
                
                if (!trueAnswerFromQdrant.isEmpty()) {
                    log.info("向量检索完成，找到相关内容长度: {}", trueAnswerFromQdrant.length());
                } else {
                    log.info("向量检索未找到相关内容");
                }
            } else {
                log.info("跳过向量检索，缺少必要参数: teamId={}, externalProductId={}", teamId, externalProductId);
            }

            // 2、从Redis获取现有的会话历史
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);

            // 3、将本轮用户消息存入Redis
            ChatMessage userChatMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(userMessage)
                    .build();
            addChatMessageToRedis(sessionId, userChatMessage);

            // 将用户消息也添加到当前消息列表中用于模型调用
            messages.add(userChatMessage);

            //从redis中获取
            boolean isThinking = (Boolean) sessionFromRedis.get("isThinking");
            boolean isStreaming = (Boolean) sessionFromRedis.get("isStreaming");

            //从redis中获取缓存上下文
            String contextId = (String) sessionFromRedis.get("sessionContextId");

            // 创建完整的消息列表副本用于传递给大模型
            //副本的目的就是剥离知识库中冗长的内容，因为对会话历史来说没有意义，只针对当前会话才有意义 @germmy 2025.08.05
            List<ChatMessage> messagesForModel = new ArrayList<>(messages);

            // 2.2.4、messagesForModel需要新加一个trueAnswerFromQdrant
            String contextContent = !trueAnswerFromQdrant.isEmpty() ? trueAnswerFromQdrant : "知识库内容为空";
            String contextMessage = " 知识库详情A如下面3个#号括起来的: ### " + contextContent + " ###";
            messagesForModel.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(contextMessage)
                    .build());
            log.info("已添加{}上下文到消息列表", !trueAnswerFromQdrant.isEmpty() ? "向量检索" : "'知识库内容为空'");

            //将向量中的可能的正确答案也添加到redis中 add by germmy@20250805
            //其实也可以不加，因为这个里面的值对多轮上下文是没有帮助的，只针对当前会话本身有效。
            //但是对于后期agent打分，是需要落库的。否则无法综合打分
            /*addChatMessageToRedis(sessionId, ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(contextMessage)
                    .build());*/

            // 强制使用非流式ntns方法
            String response = doubaoBigModelService.ntnsWithCtx(messagesForModel, contextId);

            // 4、将本轮模型回复消息存入Redis,存的是result的json串
            ChatMessage assistantMessage = ChatMessage.builder()
                    .role(ChatMessageRole.ASSISTANT)
                    .content(response)
                    .build();
            addChatMessageToRedis(sessionId, assistantMessage);

            log.debug("收到普通非流式响应: sessionId={}, response={}", sessionId, response);
            return response;

        } catch (Exception e) {
            log.error("发送消息失败: sessionId={}", sessionId, e);
            throw new RuntimeException("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 从历史聊天记录中获取最近的机器人回复消息
     * @param sessionId 会话ID
     * @return 最近的ASSISTANT消息内容，如果没有则返回null
     */
    private String getLastResponseFromRobot(String sessionId) {
        try {
            List<ChatMessage> messages = getChatHistoryFromRedis(sessionId);
            if (messages == null || messages.isEmpty()) {
                log.debug("会话历史为空，无法获取最近的机器人回复: sessionId={}", sessionId);
                return null;
            }

            // 从最后一条消息开始向前查找最近的ASSISTANT消息
            for (int i = messages.size() - 1; i >= 0; i--) {
                ChatMessage message = messages.get(i);
                if (message != null && ChatMessageRole.ASSISTANT.equals(message.getRole())) {
                    String content = message.getContent().toString();
                    log.debug("找到最近的机器人回复消息: sessionId={}, 消息位置={}, 内容长度={}", sessionId, i + 1, content.length());
                    return content;
                }
            }

            log.debug("历史记录中未找到机器人回复消息: sessionId={}", sessionId);
            return null;
        } catch (Exception e) {
            log.error("获取最近机器人回复失败: sessionId={}", sessionId, e);
            return null;
        }
    }

} 