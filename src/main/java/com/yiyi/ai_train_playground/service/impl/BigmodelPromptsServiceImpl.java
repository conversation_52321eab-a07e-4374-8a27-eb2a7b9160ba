package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.mapper.BigmodelPromptsMapper;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.CacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class BigmodelPromptsServiceImpl implements BigmodelPromptsService {
    
    private static final String CACHE_KEY_PREFIX = "bigmodel_prompts:";
    private static final Duration CACHE_EXPIRATION = Duration.ofHours(1); // 缓存1小时
    
    @Autowired
    private BigmodelPromptsMapper bigmodelPromptsMapper;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Override
    public List<String> getPromptsByKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            throw new IllegalArgumentException("关键词不能为空");
        }
        
        // 1. 先尝试从缓存获取
        String cacheKey = CACHE_KEY_PREFIX + keyword;
        Optional<List> cachedResult = cacheManager.get(cacheKey, List.class);
        if (cachedResult.isPresent()) {
            List<String> prompts = (List<String>) cachedResult.get();
            log.info("缓存命中：keyword={}, 返回{}条提示词", keyword, prompts.size());
            return prompts;
        }
        
        log.info("缓存未命中，查询数据库：keyword={}", keyword);
        
        // 2. 缓存未命中，从数据库查询
        List<String> result = queryFromDatabase(keyword);
        
        // 3. 将查询结果存入缓存
        try {
            cacheManager.put(cacheKey, result, CACHE_EXPIRATION);
            log.info("查询结果已缓存：keyword={}, 返回{}条提示词，缓存过期时间{}小时", 
                    keyword, result.size(), CACHE_EXPIRATION.toHours());
        } catch (Exception e) {
            log.warn("缓存存储失败：keyword={}, 继续返回查询结果", keyword, e);
        }
        
        return result;
    }
    
    /**
     * 从数据库查询提示词
     * @param keyword 关键词
     * @return 提示词列表
     */
    private List<String> queryFromDatabase(String keyword) {
        // 检查是否包含冒号
        if (!keyword.contains(":")) {
            throw new IllegalArgumentException("关键词格式错误，应为：keyword:SU");
        }
        
        String[] parts = keyword.split(":", 2);
        if (parts.length != 2) {
            throw new IllegalArgumentException("关键词格式错误，应为：keyword:SU");
        }
        
        String keywordPart = parts[0].trim();
        String typePart = parts[1].trim();
        
        if (keywordPart.isEmpty()) {
            throw new IllegalArgumentException("关键词不能为空");
        }
        
        if (typePart.isEmpty()) {
            throw new IllegalArgumentException("提示词类型不能为空，应包含S或U");
        }
        
        // 验证类型参数，必须包含S或U中至少一个
        boolean hasS = typePart.contains("S");
        boolean hasU = typePart.contains("U");
        
        if (!hasS && !hasU) {
            throw new IllegalArgumentException("提示词类型错误，必须包含S（系统提示词）或U（用户提示词）中的至少一个");
        }
        
        log.info("查询提示词：keyword={}, hasS={}, hasU={}", keywordPart, hasS, hasU);
        
        List<String> result = new ArrayList<>();
        
        try {
            if (hasS && hasU) {
                // 查询系统和用户提示词
                result = bigmodelPromptsMapper.findBothPromptsByKeyword(keywordPart);
            } else if (hasS) {
                // 只查询系统提示词
                result = bigmodelPromptsMapper.findSysPromptsByKeyword(keywordPart);
            } else {
                // 只查询用户提示词
                result = bigmodelPromptsMapper.findUsrPromptsByKeyword(keywordPart);
            }
            
            log.info("数据库查询结果：keyword={}, 返回{}条提示词", keywordPart, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询提示词失败：keyword={}", keywordPart, e);
            throw new RuntimeException("查询提示词失败：" + e.getMessage());
        }
    }
} 