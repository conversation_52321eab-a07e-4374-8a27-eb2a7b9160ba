package com.yiyi.ai_train_playground.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.model.MediaUploadResponse;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.MultiMediaService;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import com.yiyi.ai_train_playground.util.JwtUtil;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

@Slf4j
@Service
public class MultiMediaServiceImpl implements MultiMediaService {

    private final OssServiceImpl ossService;
    private final SuperBigModelInterface doubaiBigModelService;
    private final BigmodelPromptsService bigmodelPromptsService;
    private final ObjectMapper objectMapper;

    @Value("${my.doubao.image.estimateToken:500}")
    private Integer estimateToken;

    private static final Pattern IMAGE_PATTERN = Pattern.compile("^image/.*$");
    private static final Pattern VIDEO_PATTERN = Pattern.compile("^video/.*$");

    @Autowired
    public MultiMediaServiceImpl(OssServiceImpl ossService, 
                                SuperBigModelInterface doubaiBigModelService,
                                BigmodelPromptsService bigmodelPromptsService,
                                ObjectMapper objectMapper) {
        this.ossService = ossService;
        this.doubaiBigModelService = doubaiBigModelService;
        this.bigmodelPromptsService = bigmodelPromptsService;
        this.objectMapper = objectMapper;
    }

    @Override
    public Flux<String> processMultiMediaUpload(MultipartFile file, String uploadType, String fileUrl, String promKW, String jwtToken, JwtUtil jwtUtil) {
        return Flux.create(sink -> {
            try {
                // 验证JWT token并获取团队ID
                Long teamId = null;

                try {
                    if (jwtToken != null && !jwtToken.trim().isEmpty()) {
                        teamId = jwtUtil.getTeamIdFromToken(jwtToken);
                    }
                } catch (Exception e) {
                    log.warn("JWT token验证失败: {}", e.getMessage());
                    sink.error(new RuntimeException("认证失败，请重新登录"));
                    return;
                }
                
                if (teamId == null) {
                    sink.error(new RuntimeException("无法获取团队ID，请检查登录状态"));
                    return;
                }

                // 验证提示词关键词
                if (promKW == null || promKW.trim().isEmpty()) {
                    sink.error(new RuntimeException("提示词关键词不能为空"));
                    return;
                }

                // 处理上传并获取媒体信息
                MediaInfo mediaInfo;
                try {
                    mediaInfo = processUpload(file, uploadType, fileUrl, teamId);
                    if (mediaInfo == null) {
                        sink.error(new RuntimeException("文件上传失败"));
                        return;
                    }
                } catch (Exception e) {
                    log.error("处理上传失败", e);
                    sink.error(new RuntimeException("处理上传失败: " + e.getMessage()));
                    return;
                }

                // 如果是图片，处理识别
                if (mediaInfo.getMediaType() == 1) {
                    processImageRecognitionAsync(mediaInfo.getUrl(), promKW, uploadType, mediaInfo, sink);
                } else {
                    // 视频文件直接返回最终响应
                    sendFinalResponse(mediaInfo, "", uploadType, sink);
                }

            } catch (Exception e) {
                log.error("处理上传请求失败", e);
                sink.error(new RuntimeException("处理请求失败: " + e.getMessage()));
            }
        });
    }

    /**
     * 异步处理图片识别
     */
    private void processImageRecognitionAsync(String imageUrl, String promKW, String uploadType, MediaInfo mediaInfo, reactor.core.publisher.FluxSink<String> sink) {
        try {
            // 使用BigmodelPromptsService获取提示词
            String prompt;
            try {
                List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(promKW);
                if (prompts == null || prompts.isEmpty()) {
                    prompt = "图中讲的是什么，同时列出此图片发送场景。"; // 默认提示词
                    log.warn("未找到关键词对应的提示词，使用默认提示词：promKW={}", promKW);
                } else {
                    prompt = prompts.get(0); // 取第一个提示词
                    log.info("使用提示词：promKW={}, prompt={}", promKW, prompt);
                }
            } catch (Exception e) {
                log.error("获取提示词失败，使用默认提示词：promKW={}", promKW, e);
                prompt = "图中讲的是什么，同时列出此图片发送场景。"; // 默认提示词
            }
            
            // 用于累加完整的识别文本
            AtomicReference<StringBuilder> textBuilder = new AtomicReference<>(new StringBuilder());
            AtomicInteger progress = new AtomicInteger(0);
            
            // 调用大模型流式识别图片
            doubaiBigModelService.imageChatWithStream(imageUrl, prompt, null)
                .subscribe(
                    content -> {
                        try {
                            // 累加识别文本
                            textBuilder.get().append(content);
                            
                            // 计算进度
                            int currentProgress = Math.min(95, progress.addAndGet(5));
                            
                            // 发送进度和单块内容
                            Map<String, Object> progressData = new HashMap<>();
                            progressData.put("type", "progress");
                            progressData.put("progress", currentProgress);
                            progressData.put("content", content);
                            
                            String progressJson = objectMapper.writeValueAsString(progressData);
                            sink.next(progressJson);
                            
                        } catch (Exception e) {
                            log.error("处理图片识别响应失败", e);
                        }
                    },
                    error -> {
                        log.error("调用大模型识别图片失败", error);
                        sink.error(new RuntimeException("识别图片内容失败: " + error.getMessage()));
                    },
                    () -> {
                        // 流完成时，发送最终响应，包含完整的识别文本
                        try {
                            sendFinalResponse(mediaInfo, textBuilder.get().toString(), uploadType, sink);
                        } catch (Exception e) {
                            log.error("发送最终响应失败", e);
                            sink.error(new RuntimeException("发送最终响应失败: " + e.getMessage()));
                        }
                    }
                );
            
        } catch (Exception e) {
            log.error("处理图片识别失败", e);
            sink.error(new RuntimeException("处理图片识别失败: " + e.getMessage()));
        }
    }

    /**
     * 处理上传逻辑并返回媒体信息
     */
    private MediaInfo processUpload(MultipartFile file, String uploadType, String fileUrl, Long teamId) throws IOException {
        // 媒体类型（1:图片，2:视频）
        Integer mediaType;
        // 文件URL
        String url;

        // 处理不同的上传类型
        if ("1".equals(uploadType)) {
            // 本地上传
            if (file == null || file.isEmpty()) {
                throw new RuntimeException("请选择要上传的文件");
            }

            // 判断文件类型
            String contentType = file.getContentType();
            if (contentType == null) {
                throw new RuntimeException("无法确定文件类型");
            }

            if (IMAGE_PATTERN.matcher(contentType).matches()) {
                mediaType = 1; // 图片
            } else if (VIDEO_PATTERN.matcher(contentType).matches()) {
                mediaType = 2; // 视频
            } else {
                throw new RuntimeException("不支持的文件类型，仅支持图片或视频");
            }

            // 上传到OSS
            try {
                url = ossService.upload(file, mediaType, teamId);
            } catch (Exception e) {
                log.error("上传文件到OSS失败", e);
                throw new RuntimeException("上传文件失败: " + e.getMessage());
            }
        } else if ("2".equals(uploadType)) {
            // URL上传
            if (fileUrl == null || fileUrl.isEmpty()) {
                throw new RuntimeException("文件URL不能为空");
            }
            url = fileUrl;

            // 根据URL后缀判断文件类型
            if (isImageUrl(fileUrl)) {
                mediaType = 1; // 图片
            } else if (isVideoUrl(fileUrl)) {
                mediaType = 2; // 视频
            } else {
                throw new RuntimeException("无法确定URL文件类型，仅支持图片或视频");
            }
        } else {
            throw new RuntimeException("无效的上传类型");
        }
        
        return new MediaInfo(mediaType, url);
    }
    
    /**
     * 发送最终响应
     */
    private void sendFinalResponse(MediaInfo mediaInfo, String recognizedText, String uploadType, reactor.core.publisher.FluxSink<String> sink) {
        try {
            MediaUploadResponse response = new MediaUploadResponse();
            response.setMediaType(mediaInfo.getMediaType());
            response.setUploadType(Integer.parseInt(uploadType));
            response.setRecognizedText(recognizedText);
            response.setUrl(mediaInfo.getUrl());

            Result<MediaUploadResponse> result = Result.success("解析成功", response);
            String resultJson = objectMapper.writeValueAsString(result);
            sink.next(resultJson);
            sink.complete();
        } catch (Exception e) {
            log.error("发送最终响应失败", e);
            sink.error(new RuntimeException("发送最终响应失败: " + e.getMessage()));
        }
    }

    /**
     * 根据URL判断是否为图片
     * @param url URL
     * @return 是否为图片
     */
    private boolean isImageUrl(String url) {
        String lowerUrl = url.toLowerCase();
        return lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg") || 
               lowerUrl.endsWith(".png") || lowerUrl.endsWith(".gif") ||
               lowerUrl.endsWith(".bmp") || lowerUrl.endsWith(".webp");
    }

    /**
     * 根据URL判断是否为视频
     * @param url URL
     * @return 是否为视频
     */
    private boolean isVideoUrl(String url) {
        String lowerUrl = url.toLowerCase();
        return lowerUrl.endsWith(".mp4") || lowerUrl.endsWith(".avi") || 
               lowerUrl.endsWith(".mov") || lowerUrl.endsWith(".wmv") ||
               lowerUrl.endsWith(".flv") || lowerUrl.endsWith(".mkv") ||
               lowerUrl.endsWith(".webm");
    }
    
    /**
     * 媒体信息内部类
     */
    private static class MediaInfo {
        private final Integer mediaType;
        private final String url;
        
        public MediaInfo(Integer mediaType, String url) {
            this.mediaType = mediaType;
            this.url = url;
        }
        
        public Integer getMediaType() {
            return mediaType;
        }
        
        public String getUrl() {
            return url;
        }
    }
} 