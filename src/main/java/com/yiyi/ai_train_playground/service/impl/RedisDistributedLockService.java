package com.yiyi.ai_train_playground.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁服务
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisDistributedLockService {
    
    private final StringRedisTemplate stringRedisTemplate;
    
    /**
     * Lua脚本：原子性释放锁
     * 只有当锁的值匹配时才删除，避免误删其他线程的锁
     */
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    private final DefaultRedisScript<Long> unlockScript = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);
    
    /**
     * 尝试获取分布式锁
     * 
     * @param lockKey 锁的键
     * @param lockValue 锁的值（通常使用UUID等唯一标识）
     * @param expireSeconds 锁的过期时间（秒）
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, String lockValue, long expireSeconds) {
        try {
            // 使用SET命令的NX（不存在才设置）和EX（设置过期时间）选项
            Boolean result = stringRedisTemplate.opsForValue()
                    .setIfAbsent(lockKey, lockValue, expireSeconds, TimeUnit.SECONDS);
            
            boolean success = Boolean.TRUE.equals(result);
            
            if (success) {
                log.debug("成功获取分布式锁: key={}, value={}, expireSeconds={}", 
                         lockKey, lockValue, expireSeconds);
            } else {
                log.debug("获取分布式锁失败: key={}, value={}", lockKey, lockValue);
            }
            
            return success;
        } catch (Exception e) {
            log.error("获取分布式锁时发生异常: key={}, value={}", lockKey, lockValue, e);
            return false;
        }
    }
    
    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁的键
     * @param lockValue 锁的值
     * @return 是否成功释放锁
     */
    public boolean unlock(String lockKey, String lockValue) {
        try {
            // 使用Lua脚本确保原子性：只有当锁的值匹配时才删除
            Long result = stringRedisTemplate.execute(
                    unlockScript, 
                    Collections.singletonList(lockKey), 
                    lockValue
            );
            
            boolean success = Long.valueOf(1).equals(result);
            
            if (success) {
                log.debug("成功释放分布式锁: key={}, value={}", lockKey, lockValue);
            } else {
                log.debug("释放分布式锁失败（锁可能已过期或被其他线程持有）: key={}, value={}", 
                         lockKey, lockValue);
            }
            
            return success;
        } catch (Exception e) {
            log.error("释放分布式锁时发生异常: key={}, value={}", lockKey, lockValue, e);
            return false;
        }
    }
    
    /**
     * 检查锁是否存在
     * 
     * @param lockKey 锁的键
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.hasKey(lockKey));
        } catch (Exception e) {
            log.error("检查锁状态时发生异常: key={}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 获取锁的值
     * 
     * @param lockKey 锁的键
     * @return 锁的值，如果锁不存在则返回null
     */
    public String getLockValue(String lockKey) {
        try {
            return stringRedisTemplate.opsForValue().get(lockKey);
        } catch (Exception e) {
            log.error("获取锁值时发生异常: key={}", lockKey, e);
            return null;
        }
    }
}
