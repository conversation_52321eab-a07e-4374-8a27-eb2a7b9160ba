package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.dto.ScriptQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptGroupQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptUpdateRequest;
import com.yiyi.ai_train_playground.entity.TrainScript;
import com.yiyi.ai_train_playground.entity.TrainProduct;
import com.yiyi.ai_train_playground.entity.TrainScriptProducts;
import com.yiyi.ai_train_playground.entity.TrainRelatedImage;
import com.yiyi.ai_train_playground.entity.TrainFlowNode;
import com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts;
import com.yiyi.ai_train_playground.mapper.TrainScriptMapper;
import com.yiyi.ai_train_playground.mapper.TrainProductMapper;
import com.yiyi.ai_train_playground.mapper.TrainScriptProductsMapper;
import com.yiyi.ai_train_playground.mapper.TrainRelatedImageMapper;
import com.yiyi.ai_train_playground.mapper.TrainFlowNodeMapper;
import com.yiyi.ai_train_playground.service.TrainScriptService;
import com.yiyi.ai_train_playground.service.jd.TrainScriptJdProductsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.ArrayList;

/**
 * 剧本服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Slf4j
@Service
public class TrainScriptServiceImpl implements TrainScriptService {
    
    @Autowired
    private TrainScriptMapper trainScriptMapper;

    @Autowired
    private TrainProductMapper trainProductMapper;

    @Autowired
    private TrainScriptProductsMapper trainScriptProductsMapper;

    @Autowired
    private TrainRelatedImageMapper trainRelatedImageMapper;

    @Autowired
    private TrainFlowNodeMapper trainFlowNodeMapper;

    @Autowired
    private TrainScriptJdProductsService trainScriptJdProductsService;
    
    @Override
    public PageResult<ScriptListDTO> getScriptList(ScriptQueryRequest request, Long teamId) {
        log.info("分页查询剧本列表：teamId={}, request={}", teamId, request);
        
        // 计算偏移量
        Integer offset = (request.getPage() - 1) * request.getPageSize();
        
        // 查询总数
        Long total = trainScriptMapper.countScripts(request, teamId);
        
        // 查询分页数据
        List<ScriptListDTO> records = trainScriptMapper.selectScriptList(request, teamId, offset, request.getPageSize());
        
        log.info("查询结果：total={}, records.size={}", total, records.size());
        
        return new PageResult<>(records, total, request.getPage(), request.getPageSize());
    }

    @Override
    public PageResult<ScriptListDTO> getScriptListByGroup(ScriptGroupQueryRequest request, Long teamId) {
        log.info("根据分组ID分页查询剧本列表：teamId={}, request={}", teamId, request);

        // 解析分组ID列表
        List<Long> groupIds = parseScriptGroupIds(request.getScriptGroupIds());
        log.info("解析后的分组ID列表：{}", groupIds);

        // 计算偏移量
        Integer offset = (request.getPage() - 1) * request.getPageSize();

        // 查询总数
        Long total = trainScriptMapper.countScriptsByGroup(request, teamId);

        // 查询分页数据
        List<ScriptListDTO> records = trainScriptMapper.selectScriptListByGroup(request, teamId, offset, request.getPageSize());

        log.info("查询结果：total={}, records.size={}", total, records.size());

        return new PageResult<>(records, total, request.getPage(), request.getPageSize());
    }

    /**
     * 解析scriptGroupIds字符串为Long列表
     *
     * @param scriptGroupIds 逗号分隔的分组ID字符串
     * @return 分组ID列表
     */
    private List<Long> parseScriptGroupIds(String scriptGroupIds) {
        if (scriptGroupIds == null || scriptGroupIds.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> groupIds = new ArrayList<>();
        String[] idArray = scriptGroupIds.split(",");

        for (String idStr : idArray) {
            try {
                Long id = Long.parseLong(idStr.trim());
                groupIds.add(id);
            } catch (NumberFormatException e) {
                log.warn("无效的分组ID格式：{}", idStr);
            }
        }

        return groupIds;
    }

    @Override
    public TrainScript getScriptById(Long id, Long teamId) {
        log.info("根据ID查询剧本：id={}, teamId={}", id, teamId);
        
        if (id == null) {
            throw new RuntimeException("剧本ID不能为空");
        }
        
        TrainScript script = trainScriptMapper.selectById(id, teamId);
        if (script == null) {
            throw new RuntimeException("剧本不存在或无权限访问");
        }
        
        return script;
    }
    
    @Override
    @Transactional
    public boolean createScript(TrainScript script) {
        log.info("创建剧本：name={}, teamId={}", script.getName(), script.getTeamId());
        
        if (script.getName() == null || script.getName().trim().isEmpty()) {
            throw new RuntimeException("剧本名称不能为空");
        }
        
        if (script.getTeamId() == null) {
            throw new RuntimeException("团队ID不能为空");
        }
        
        // 设置默认值
        if (script.getRetryBuyerRequirementCounts() == null) {
            script.setRetryBuyerRequirementCounts(0);
        }
        if (script.getRetryFlowNodeCounts() == null) {
            script.setRetryFlowNodeCounts(0);
        }
        if (script.getOrderPriority() == null) {
            script.setOrderPriority(0);
        }
        if (script.getVersion() == null) {
            script.setVersion(0L);
        }
        if (script.getIsOfficial() == null) {
            script.setIsOfficial(false);
        }
        
        int result = trainScriptMapper.insert(script);
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean updateScript(TrainScript script) {
        log.info("更新剧本：id={}, name={}, teamId={}", script.getId(), script.getName(), script.getTeamId());
        
        if (script.getId() == null) {
            throw new RuntimeException("剧本ID不能为空");
        }
        
        if (script.getName() == null || script.getName().trim().isEmpty()) {
            throw new RuntimeException("剧本名称不能为空");
        }
        
        if (script.getTeamId() == null) {
            throw new RuntimeException("团队ID不能为空");
        }
        
        // 检查剧本是否存在
        TrainScript existingScript = trainScriptMapper.selectById(script.getId(), script.getTeamId());
        if (existingScript == null) {
            throw new RuntimeException("剧本不存在或无权限访问");
        }
        
        int result = trainScriptMapper.update(script);
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean deleteScript(Long id, Long teamId) {
        log.info("删除剧本：id={}, teamId={}", id, teamId);

        if (id == null) {
            throw new RuntimeException("剧本ID不能为空");
        }

        if (teamId == null) {
            throw new RuntimeException("团队ID不能为空");
        }

        // 检查剧本是否存在
        TrainScript existingScript = trainScriptMapper.selectById(id, teamId);
        if (existingScript == null) {
            throw new RuntimeException("剧本不存在或无权限访问");
        }

        int result = trainScriptMapper.deleteById(id, teamId);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean batchDeleteScripts(String ids, Long teamId) {
        log.info("批量删除剧本：ids={}, teamId={}", ids, teamId);

        if (ids == null || ids.trim().isEmpty()) {
            throw new RuntimeException("剧本ID列表不能为空");
        }

        if (teamId == null) {
            throw new RuntimeException("团队ID不能为空");
        }

        // 解析ID字符串为Long列表
        List<Long> idList = new ArrayList<>();
        try {
            String[] idArray = ids.split(",");
            for (String idStr : idArray) {
                idStr = idStr.trim();
                if (!idStr.isEmpty()) {
                    idList.add(Long.parseLong(idStr));
                }
            }
        } catch (NumberFormatException e) {
            throw new RuntimeException("剧本ID格式不正确：" + ids);
        }

        if (idList.isEmpty()) {
            throw new RuntimeException("没有有效的剧本ID");
        }

        log.info("解析后的剧本ID列表：{}", idList);

        // 批量删除剧本
        int result = trainScriptMapper.batchDeleteByIds(idList, teamId);
        log.info("批量删除剧本结果：删除了{}条记录", result);

        return result > 0;
    }

    @Override
    public ScriptDetailDTO getScriptDetail(Long id, Long teamId) {
        log.info("查询剧本详情：id={}, teamId={}", id, teamId);

        if (id == null) {
            throw new RuntimeException("剧本ID不能为空");
        }

        if (teamId == null) {
            throw new RuntimeException("团队ID不能为空");
        }

        ScriptDetailDTO scriptDetail = trainScriptMapper.getScriptDetailById(id, teamId);
        if (scriptDetail == null) {
            throw new RuntimeException("剧本不存在或无权限访问");
        }

        return scriptDetail;
    }

    @Override
    public ScriptDetailDTO getScriptDetailByType(Long id, Long teamId) {
        log.info("根据商品类型查询剧本详情：id={}, teamId={}", id, teamId);

        if (id == null) {
            throw new RuntimeException("剧本ID不能为空");
        }

        if (teamId == null) {
            throw new RuntimeException("团队ID不能为空");
        }

        // 先查询剧本的prodType
        Integer prodType = trainScriptMapper.getScriptProdType(id, teamId);
        if (prodType == null) {
            throw new RuntimeException("剧本不存在或无权限访问");
        }

        log.info("剧本商品类型：id={}, prodType={}", id, prodType);

        // 根据prodType决定使用哪种查询方式
        if (prodType == 0) {
            // 自主导入商品，使用原有逻辑
            return getScriptDetail(id, teamId);
        } else if (prodType == 1) {
            // JD商品，使用新的查询逻辑
            return getScriptDetailWithJd(id, teamId);
        } else {
            throw new RuntimeException("暂不支持的商品类型：" + prodType);
        }
    }

    /**
     * 查询JD商品类型的剧本详情
     */
    private ScriptDetailDTO getScriptDetailWithJd(Long id, Long teamId) {
        log.info("查询JD商品剧本详情：id={}, teamId={}", id, teamId);

        ScriptDetailDTO scriptDetail = trainScriptMapper.getScriptDetailWithJdById(id, teamId);
        if (scriptDetail == null) {
            throw new RuntimeException("剧本不存在或无权限访问");
        }

        return scriptDetail;
    }

    @Override
    @Transactional
    public Long createScriptWithRelatedData(ScriptCreateRequest request, Long teamId, String creator) {
        log.info("创建剧本及关联数据：name={}, teamId={}, creator={}",
                request != null ? request.getName() : null, teamId, creator);

        // 参数验证
        if (request == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        if (teamId == null) {
            throw new RuntimeException("团队ID不能为空");
        }
        if (creator == null || creator.trim().isEmpty()) {
            throw new RuntimeException("创建人不能为空");
        }
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            throw new RuntimeException("剧本名称不能为空");
        }

        try {
            // 1. 创建剧本主记录
            TrainScript script = new TrainScript();
            script.setTeamId(teamId);
            script.setName(request.getName());
            script.setGenerationType(request.getGenerationTypeCode());
            // 如果groupId为1，则存储为NULL
            script.setGroupId(request.getGroupId() != null && request.getGroupId().equals(1L) ? null : request.getGroupId());
            script.setIntentId(request.getIntentId());
            script.setEvaluationId(request.getEvaluationPlanId());
            script.setBuyerRequirement(request.getBuyerRequirement());
            script.setOrderIsRemarked(request.getOrderIsRemarked());
            script.setOrderPriority(request.getOrderPriority());
            script.setOrderRemark(request.getOrderRemark());
            script.setSimulationTool(request.getSimulationTool());
            script.setProdType(request.getProdType());
            // 设置默认值
            script.setRetryBuyerRequirementCounts(0);
            script.setRetryFlowNodeCounts(0);
            script.setCreator(creator);
            script.setUpdater(creator);
            script.setVersion(1L);
            script.setIsOfficial(false);

            int scriptResult = trainScriptMapper.insert(script);
            if (scriptResult <= 0) {
                throw new RuntimeException("创建剧本失败");
            }

            Long scriptId = script.getId();
            log.info("剧本创建成功，scriptId={}", scriptId);

            // 2. 处理商品列表
            if (request.getProductList() != null && !request.getProductList().isEmpty()) {
                createProducts(request.getProductList(), scriptId, teamId, creator, request.getProdType());
            }

            // 3. 处理关联图片
            if (request.getRelateImgs() != null && !request.getRelateImgs().isEmpty()) {
                createRelatedImages(request.getRelateImgs(), scriptId, teamId, creator);
            }

            // 4. 处理流程节点
            if (request.getFlowNodes() != null && !request.getFlowNodes().isEmpty()) {
                createFlowNodes(request.getFlowNodes(), scriptId, teamId, creator);
            }

            log.info("剧本及关联数据创建完成，scriptId={}", scriptId);
            return scriptId;

        } catch (Exception e) {
            log.error("创建剧本失败", e);
            throw new RuntimeException("创建剧本失败：" + e.getMessage());
        }
    }

    /**
     * 创建商品及关联关系
     */
    private void createProducts(List<ScriptCreateRequest.ProductCreateDTO> productList, Long scriptId, Long teamId, String creator, Integer prodType) {
        log.info("创建商品列表，数量：{}，商品类型：{}", productList.size(), prodType);

        // 根据商品类型决定处理逻辑
        if (prodType == null || prodType == 0) {
            // 自主导入，使用原有逻辑
            createTraditionalProducts(productList, scriptId, teamId, creator);
        } else if (prodType == 1) {
            // JD商品，插入到train_script_jd_products表
            createJdProducts(productList, scriptId, teamId, creator);
        } else {
            // 其他类型暂不支持
            log.warn("暂不支持的商品类型：{}", prodType);
            throw new RuntimeException("暂不支持的商品类型：" + prodType);
        }
    }

    /**
     * 创建传统商品及关联关系（原有逻辑）
     */
    private void createTraditionalProducts(List<ScriptCreateRequest.ProductCreateDTO> productList, Long scriptId, Long teamId, String creator) {
        log.info("创建传统商品列表，数量：{}", productList.size());

        List<TrainScriptProducts> scriptProductsList = new ArrayList<>();

        for (ScriptCreateRequest.ProductCreateDTO productDTO : productList) {
            // 1. 先查找是否已存在相同的商品
            TrainProduct existingProduct = trainProductMapper.findByExternalProductId(
                    productDTO.getExternalProductId(), teamId);

            Long trainProductId;
            if (existingProduct != null) {
                // 商品已存在，使用现有商品ID
                trainProductId = existingProduct.getId();
                log.info("使用现有商品：externalProductId={}, trainProductId={}",
                        productDTO.getExternalProductId(), trainProductId);
            } else {
                // 商品不存在，创建新商品
                int insertResult = trainProductMapper.insertProduct(
                        teamId,
                        productDTO.getExternalProductId(),
                        productDTO.getExternalProductName(),
                        productDTO.getExternalProductLink(),
                        productDTO.getExternalProductImage(),
                        "active", // 默认状态
                        "", // category
                        "", // tags
                        "", // platform
                        "", // shopId
                        "", // shopName
                        creator,
                        creator // updater
                );

                if (insertResult <= 0) {
                    throw new RuntimeException("创建商品失败：" + productDTO.getExternalProductName());
                }

                // 获取新创建的商品ID
                TrainProduct newProduct = trainProductMapper.findByExternalProductId(
                        productDTO.getExternalProductId(), teamId);
                if (newProduct == null) {
                    throw new RuntimeException("获取新创建商品ID失败");
                }
                trainProductId = newProduct.getId();
                log.info("创建新商品：externalProductId={}, trainProductId={}",
                        productDTO.getExternalProductId(), trainProductId);
            }

            // 2. 创建剧本商品关联记录
            TrainScriptProducts scriptProducts = new TrainScriptProducts();
            scriptProducts.setTeamId(teamId);
            scriptProducts.setScriptId(scriptId);
//            scriptProducts.setTrainProductId(trainProductId);
            scriptProducts.setTrainProductId(Long.valueOf(productDTO.getExternalProductId()));
            scriptProducts.setExternalProductId(productDTO.getExternalProductId());
            scriptProducts.setExternalProductName(productDTO.getExternalProductName());
            scriptProducts.setExternalProductLink(productDTO.getExternalProductLink());
            scriptProducts.setExternalProductImage(productDTO.getExternalProductImage());
            scriptProducts.setCreator(creator);
            scriptProducts.setUpdater(creator);

            scriptProductsList.add(scriptProducts);
        }

        // 3. 批量插入剧本商品关联记录
        if (!scriptProductsList.isEmpty()) {
            int batchResult = trainScriptProductsMapper.batchInsert(scriptProductsList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建剧本商品关联失败");
            }
            log.info("批量创建剧本商品关联成功，数量：{}", scriptProductsList.size());
        }
    }

    /**
     * 创建JD商品关联关系
     */
    private void createJdProducts(List<ScriptCreateRequest.ProductCreateDTO> productList, Long scriptId, Long teamId, String creator) {
        log.info("创建JD商品关联列表，数量：{}", productList.size());

        List<TrainScriptJdProducts> scriptJdProductsList = new ArrayList<>();

        for (ScriptCreateRequest.ProductCreateDTO productDTO : productList) {
            // 将externalProductId作为tr_jd_sku_id插入到train_script_jd_products表
            TrainScriptJdProducts scriptJdProducts = new TrainScriptJdProducts();
            scriptJdProducts.setTeamId(teamId);
            scriptJdProducts.setScriptId(scriptId);

            // 将externalProductId转换为Long类型作为tr_jd_sku_id
            try {
                Long trJdSkuId = Long.parseLong(productDTO.getExternalProductId());
                scriptJdProducts.setTrJdSkuId(trJdSkuId);
            } catch (NumberFormatException e) {
                log.error("无效的JD SKU ID：{}", productDTO.getExternalProductId());
                throw new RuntimeException("无效的JD SKU ID：" + productDTO.getExternalProductId());
            }

            scriptJdProducts.setCreator(creator);
            scriptJdProducts.setUpdater(creator);
            scriptJdProducts.setVersion(0L);

            scriptJdProductsList.add(scriptJdProducts);
        }

        // 批量插入剧本JD商品关联记录
        if (!scriptJdProductsList.isEmpty()) {
            int batchResult = trainScriptJdProductsService.batchInsert(scriptJdProductsList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建剧本JD商品关联失败");
            }
            log.info("批量创建剧本JD商品关联成功，数量：{}", scriptJdProductsList.size());
        }
    }

    /**
     * 创建关联图片
     */
    private void createRelatedImages(List<ScriptCreateRequest.RelatedImageCreateDTO> relateImgs, Long scriptId, Long teamId, String creator) {
        log.info("创建关联图片，数量：{}", relateImgs.size());

        List<TrainRelatedImage> relatedImageList = new ArrayList<>();

        for (ScriptCreateRequest.RelatedImageCreateDTO imageDTO : relateImgs) {
            TrainRelatedImage relatedImage = new TrainRelatedImage();
            relatedImage.setTeamId(teamId);
            relatedImage.setScriptId(scriptId);
            relatedImage.setRecognizedText(imageDTO.getRecognizedText());
            relatedImage.setUploadType(imageDTO.getUploadType());
            relatedImage.setMediaType(imageDTO.getMediaType());
            relatedImage.setUrl(imageDTO.getUrl());
            relatedImage.setCreator(creator);
            relatedImage.setUpdater(creator);

            relatedImageList.add(relatedImage);
        }

        // 批量插入关联图片
        if (!relatedImageList.isEmpty()) {
            int batchResult = trainRelatedImageMapper.batchInsert(relatedImageList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建关联图片失败");
            }
            log.info("批量创建关联图片成功，数量：{}", relatedImageList.size());
        }
    }

    /**
     * 创建流程节点
     */
    private void createFlowNodes(List<ScriptCreateRequest.FlowNodeCreateDTO> flowNodes, Long scriptId, Long teamId, String creator) {
        log.info("创建流程节点，数量：{}", flowNodes.size());

        List<TrainFlowNode> flowNodeList = new ArrayList<>();

        for (ScriptCreateRequest.FlowNodeCreateDTO nodeDTO : flowNodes) {
            TrainFlowNode flowNode = new TrainFlowNode();
            flowNode.setTeamId(teamId);
            flowNode.setScriptId(scriptId);
            flowNode.setNodeName(nodeDTO.getNodeName());
            flowNode.setNodeBuyerRequirement(nodeDTO.getNodeBuyerRequirement());
            flowNode.setCreator(creator);
            flowNode.setUpdater(creator);

            flowNodeList.add(flowNode);
        }

        // 批量插入流程节点
        if (!flowNodeList.isEmpty()) {
            int batchResult = trainFlowNodeMapper.batchInsert(flowNodeList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建流程节点失败");
            }
            log.info("批量创建流程节点成功，数量：{}", flowNodeList.size());
        }
    }

    @Override
    @Transactional
    public boolean updateScriptWithRelatedData(ScriptUpdateRequest request, Long teamId, String updater) {
        try {
            log.info("开始更新剧本及关联数据：scriptId={}, teamId={}, updater={}", request.getId(), teamId, updater);

            // 1. 更新剧本基本信息
            TrainScript script = new TrainScript();
            script.setId(request.getId());
            script.setName(request.getName());
            script.setGenerationType(request.getGenerationTypeCode());
            // 如果groupId为1，则存储为NULL
            script.setGroupId(request.getGroupId() != null && request.getGroupId().equals(1L) ? null : request.getGroupId());
            script.setIntentId(request.getIntentId());
            script.setEvaluationId(request.getEvaluationPlanId());
            script.setBuyerRequirement(request.getBuyerRequirement());
            script.setOrderIsRemarked(request.getOrderIsRemarked());
            script.setOrderPriority(request.getOrderPriority());
            script.setOrderRemark(request.getOrderRemark());
            script.setRetryBuyerRequirementCounts(request.getRetryBuyerRequirementCounts() != null ? request.getRetryBuyerRequirementCounts() : 0);
            script.setRetryFlowNodeCounts(request.getRetryFlowNodeCounts() != null ? request.getRetryFlowNodeCounts() : 0);
            script.setSimulationTool(request.getSimulationTool());
            script.setVersion(request.getVersion() != null ? request.getVersion() : 1L);
            script.setIsOfficial(request.getIsOfficial() != null ? request.getIsOfficial() : false);
            script.setTeamId(teamId);
            script.setUpdater(updater);

            //save prod type
            script.setProdType(request.getProdType());

            boolean updateResult = updateScript(script);
            if (!updateResult) {
                throw new RuntimeException("更新剧本基本信息失败");
            }

            // 2. 处理商品列表
            updateProducts(request.getProductList(), request.getId(), teamId, updater, request.getProdType());

            // 3. 处理关联图片
            updateRelatedImages(request.getRelateImgs(), request.getId(), teamId, updater);

            // 4. 处理流程节点
            updateFlowNodes(request.getFlowNodes(), request.getId(), teamId, updater);

            log.info("剧本及关联数据更新完成，scriptId={}", request.getId());
            return true;

        } catch (Exception e) {
            log.error("更新剧本失败", e);
            throw new RuntimeException("更新剧本失败：" + e.getMessage());
        }
    }

    /**
     * 更新商品及关联关系
     */
    private void updateProducts(List<ScriptUpdateRequest.ProductUpdateDTO> productList, Long scriptId, Long teamId, String updater, Integer prodType) {
        log.info("更新商品列表，scriptId={}, 数量：{}, 商品类型：{}", scriptId, productList != null ? productList.size() : 0, prodType);

        // 根据商品类型决定处理逻辑
        if (prodType == null || prodType == 0) {
            // 自主导入商品，使用原有逻辑
            updateTraditionalProducts(productList, scriptId, teamId, updater);
        } else if (prodType == 1) {
            // JD商品，处理train_script_jd_products表
            updateJdProducts(productList, scriptId, teamId, updater);
        } else {
            // 其他类型暂不支持
            log.warn("暂不支持的商品类型：{}", prodType);
            throw new RuntimeException("暂不支持的商品类型：" + prodType);
        }
    }

    /**
     * 更新传统商品及关联关系（自主导入商品）
     */
    private void updateTraditionalProducts(List<ScriptUpdateRequest.ProductUpdateDTO> productList, Long scriptId, Long teamId, String updater) {
        log.info("更新传统商品列表，scriptId={}, 数量：{}", scriptId, productList != null ? productList.size() : 0);

        // 1. 先查询现有的商品ID列表
        List<Long> existingProductIds = trainScriptProductsMapper.selectTrainProductIdsByScriptIdAndTeamId(scriptId, teamId);
        log.info("现有商品ID列表：{}", existingProductIds);

        // 2. 删除现有的剧本商品关联记录
        int deleteScriptProductsResult = trainScriptProductsMapper.deleteByScriptId(scriptId);
        log.info("删除剧本商品关联记录数：{}", deleteScriptProductsResult);

        // 3. 删除现有的商品记录
        if (!CollectionUtils.isEmpty(existingProductIds)) {
            int deleteProductsResult = trainProductMapper.batchDeleteByIds(existingProductIds, teamId);
            log.info("删除商品记录数：{}", deleteProductsResult);
        }

        // 4. 创建新的商品及关联记录
        if (productList != null && !productList.isEmpty()) {
            createProductsFromUpdate(productList, scriptId, teamId, updater);
        }
    }

    /**
     * 更新JD商品及关联关系
     */
    private void updateJdProducts(List<ScriptUpdateRequest.ProductUpdateDTO> productList, Long scriptId, Long teamId, String updater) {
        log.info("更新JD商品列表，scriptId={}, 数量：{}", scriptId, productList != null ? productList.size() : 0);

        // 1. 删除现有的JD商品关联记录
        boolean deleteResult = trainScriptJdProductsService.deleteByScriptIdAndTeamId(scriptId, teamId);
        log.info("删除JD商品关联记录结果：{}", deleteResult);

        // 2. 创建新的JD商品关联记录
        if (productList != null && !productList.isEmpty()) {
            createJdProductsFromUpdate(productList, scriptId, teamId, updater);
        }
    }

    /**
     * 从更新请求创建JD商品关联关系
     */
    private void createJdProductsFromUpdate(List<ScriptUpdateRequest.ProductUpdateDTO> productList, Long scriptId, Long teamId, String updater) {
        log.info("从更新请求创建JD商品关联列表，数量：{}", productList.size());

        List<TrainScriptJdProducts> scriptJdProductsList = new ArrayList<>();

        for (ScriptUpdateRequest.ProductUpdateDTO productDTO : productList) {
            // 将externalProductId作为tr_jd_sku_id插入到train_script_jd_products表
            TrainScriptJdProducts scriptJdProducts = new TrainScriptJdProducts();
            scriptJdProducts.setTeamId(teamId);
            scriptJdProducts.setScriptId(scriptId);

            // 将externalProductId转换为Long类型作为tr_jd_sku_id
            try {
                Long trJdSkuId = Long.parseLong(productDTO.getExternalProductId());
                scriptJdProducts.setTrJdSkuId(trJdSkuId);
            } catch (NumberFormatException e) {
                log.error("无效的JD SKU ID：{}", productDTO.getExternalProductId());
                throw new RuntimeException("无效的JD SKU ID：" + productDTO.getExternalProductId());
            }

            scriptJdProducts.setCreator(updater);
            scriptJdProducts.setUpdater(updater);
            scriptJdProducts.setVersion(1L);

            scriptJdProductsList.add(scriptJdProducts);
        }

        // 批量插入JD商品关联记录
        if (!scriptJdProductsList.isEmpty()) {
            int batchResult = trainScriptJdProductsService.batchInsert(scriptJdProductsList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建JD商品关联失败");
            }
            log.info("批量创建JD商品关联成功，数量：{}", scriptJdProductsList.size());
        }
    }

    /**
     * 更新关联图片
     */
    private void updateRelatedImages(List<ScriptUpdateRequest.RelatedImageUpdateDTO> relateImgs, Long scriptId, Long teamId, String updater) {
        log.info("更新关联图片，scriptId={}, 数量：{}", scriptId, relateImgs != null ? relateImgs.size() : 0);

        // 1. 删除现有的关联图片
        int deleteResult = trainRelatedImageMapper.deleteByScriptIdAndTeamId(scriptId, teamId);
        log.info("删除关联图片记录数：{}", deleteResult);

        // 2. 创建新的关联图片
        if (relateImgs != null && !relateImgs.isEmpty()) {
            createRelatedImagesFromUpdate(relateImgs, scriptId, teamId, updater);
        }
    }

    /**
     * 更新流程节点
     */
    private void updateFlowNodes(List<ScriptUpdateRequest.FlowNodeUpdateDTO> flowNodes, Long scriptId, Long teamId, String updater) {
        log.info("更新流程节点，scriptId={}, 数量：{}", scriptId, flowNodes != null ? flowNodes.size() : 0);

        // 1. 删除现有的流程节点
        int deleteResult = trainFlowNodeMapper.deleteByScriptIdAndTeamId(scriptId, teamId);
        log.info("删除流程节点记录数：{}", deleteResult);

        // 2. 创建新的流程节点
        if (flowNodes != null && !flowNodes.isEmpty()) {
            createFlowNodesFromUpdate(flowNodes, scriptId, teamId, updater);
        }
    }

    /**
     * 从更新请求创建商品及关联关系
     */
    private void createProductsFromUpdate(List<ScriptUpdateRequest.ProductUpdateDTO> productList, Long scriptId, Long teamId, String updater) {
        log.info("从更新请求创建商品列表，数量：{}", productList.size());

        List<TrainScriptProducts> scriptProductsList = new ArrayList<>();

        for (ScriptUpdateRequest.ProductUpdateDTO productDTO : productList) {
            // 1. 先查找是否已存在相同的商品
            TrainProduct existingProduct = trainProductMapper.findByExternalProductId(
                    productDTO.getExternalProductId(), teamId);

            Long trainProductId;
            if (existingProduct != null) {
                // 商品已存在，使用现有商品ID
                trainProductId = existingProduct.getId();
                log.info("使用现有商品：externalProductId={}, trainProductId={}",
                        productDTO.getExternalProductId(), trainProductId);
            } else {
                // 商品不存在，创建新商品
                int insertResult = trainProductMapper.insertProduct(
                        teamId,
                        productDTO.getExternalProductId(),
                        productDTO.getExternalProductName(),
                        productDTO.getExternalProductLink(),
                        productDTO.getExternalProductImage(),
                        "active", // 默认状态
                        "", // category
                        "", // tags
                        "", // platform
                        "", // shopId
                        "", // shopName
                        updater,
                        updater // updater
                );

                if (insertResult <= 0) {
                    throw new RuntimeException("创建商品失败：" + productDTO.getExternalProductName());
                }

                // 获取新创建的商品ID
                TrainProduct newProduct = trainProductMapper.findByExternalProductId(
                        productDTO.getExternalProductId(), teamId);
                if (newProduct == null) {
                    throw new RuntimeException("获取新创建商品ID失败");
                }
                trainProductId = newProduct.getId();
                log.info("创建新商品：externalProductId={}, trainProductId={}",
                        productDTO.getExternalProductId(), trainProductId);
            }

            // 2. 创建剧本商品关联记录
            TrainScriptProducts scriptProducts = new TrainScriptProducts();
            scriptProducts.setTeamId(teamId);
            scriptProducts.setScriptId(scriptId);
            scriptProducts.setTrainProductId(trainProductId);
            scriptProducts.setExternalProductId(productDTO.getExternalProductId());
            scriptProducts.setExternalProductName(productDTO.getExternalProductName());
            scriptProducts.setExternalProductLink(productDTO.getExternalProductLink());
            scriptProducts.setExternalProductImage(productDTO.getExternalProductImage());
            scriptProducts.setCreator(updater);
            scriptProducts.setUpdater(updater);

            scriptProductsList.add(scriptProducts);
        }

        // 3. 批量插入剧本商品关联记录
        if (!scriptProductsList.isEmpty()) {
            int batchResult = trainScriptProductsMapper.batchInsert(scriptProductsList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建剧本商品关联失败");
            }
            log.info("批量创建剧本商品关联成功，数量：{}", scriptProductsList.size());
        }
    }

    /**
     * 从更新请求创建关联图片
     */
    private void createRelatedImagesFromUpdate(List<ScriptUpdateRequest.RelatedImageUpdateDTO> relateImgs, Long scriptId, Long teamId, String updater) {
        log.info("从更新请求创建关联图片，数量：{}", relateImgs.size());

        List<TrainRelatedImage> relatedImageList = new ArrayList<>();

        for (ScriptUpdateRequest.RelatedImageUpdateDTO imageDTO : relateImgs) {
            TrainRelatedImage relatedImage = new TrainRelatedImage();
            relatedImage.setTeamId(teamId);
            relatedImage.setScriptId(scriptId);
            relatedImage.setRecognizedText(imageDTO.getRecognizedText());
            relatedImage.setUploadType(imageDTO.getUploadType());
            relatedImage.setMediaType(imageDTO.getMediaType());
            relatedImage.setUrl(imageDTO.getUrl());
            relatedImage.setCreator(updater);
            relatedImage.setUpdater(updater);

            relatedImageList.add(relatedImage);
        }

        // 批量插入关联图片
        if (!relatedImageList.isEmpty()) {
            int batchResult = trainRelatedImageMapper.batchInsert(relatedImageList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建关联图片失败");
            }
            log.info("批量创建关联图片成功，数量：{}", relatedImageList.size());
        }
    }

    /**
     * 从更新请求创建流程节点
     */
    private void createFlowNodesFromUpdate(List<ScriptUpdateRequest.FlowNodeUpdateDTO> flowNodes, Long scriptId, Long teamId, String updater) {
        log.info("从更新请求创建流程节点，数量：{}", flowNodes.size());

        List<TrainFlowNode> flowNodeList = new ArrayList<>();

        for (ScriptUpdateRequest.FlowNodeUpdateDTO nodeDTO : flowNodes) {
            TrainFlowNode flowNode = new TrainFlowNode();
            flowNode.setTeamId(teamId);
            flowNode.setScriptId(scriptId);
            flowNode.setNodeName(nodeDTO.getNodeName());
            flowNode.setNodeBuyerRequirement(nodeDTO.getNodeBuyerRequirement());
            flowNode.setCreator(updater);
            flowNode.setUpdater(updater);

            flowNodeList.add(flowNode);
        }

        // 批量插入流程节点
        if (!flowNodeList.isEmpty()) {
            int batchResult = trainFlowNodeMapper.batchInsert(flowNodeList);
            if (batchResult <= 0) {
                throw new RuntimeException("批量创建流程节点失败");
            }
            log.info("批量创建流程节点成功，数量：{}", flowNodeList.size());
        }
    }
}
