package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.config.ProductProcessingConfig;
import com.yiyi.ai_train_playground.config.QdrantConfig;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.mapper.TrainProductMapper;
import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.model.VectorSearchRequest;
import com.yiyi.ai_train_playground.model.VectorSearchResult;
import com.yiyi.ai_train_playground.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 向量搜索服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VectorSearchServiceImpl implements VectorSearchService {

    private final ProductProcessingConfig config;
    private final HanLpService hanLpService;
    private final HtmlSanitizerService htmlSanitizerService;
    private final SuperBigModelInterface bigModelService;
    private final QdrantService qdrantService;
    private final TrainProductMapper trainProductMapper;
    private final DoubaoChunkService doubaoChunkService;
    private final QdrantConfig.QdrantSearchConfig qdrantSearchConfig;

    @Value("${product.processing.collection-name:train_prod_collection}")
    private String collectionName;

    @Value("${product.processing.jd-collection-name:train_prod_jd_collection}")
    private String jdCollectionName;

    @Override
    public String searchByText(String queryText, String teamId, String externalProductId, int limit, Integer prodType, Float minScore) {
        log.info("开始文本向量搜索: queryText={}, teamId={}, externalProductId={}, limit={}, minScore={}",
                queryText, teamId, externalProductId, limit, minScore);

        try {
            // 1. 将查询文本向量化
            List<List<Double>> embeddings = embedTexts(Arrays.asList(queryText));
            if (embeddings.isEmpty()) {
                log.warn("文本向量化失败: {}", queryText);
                return "";
            }

            List<Double> queryVector = embeddings.get(0);
            List<Float> searchVector = queryVector.stream()
                    .map(Double::floatValue)
                    .collect(Collectors.toList());

            // 2. 进行向量搜索
            VectorSearchResult result = searchByVector(searchVector, teamId, externalProductId, limit, prodType, minScore);

            // 3. 提取文本内容
            return extractContentFromSearchResult(result);

        } catch (Exception e) {
            log.error("文本向量搜索失败: queryText={}, teamId={}, externalProductId={}",
                    queryText, teamId, externalProductId, e);
            return "";
        }
    }

    @Override
    public VectorSearchResult searchByVector(List<Float> vector, String teamId, String externalProductId, int limit, Integer prodType, Float minScore) {
        // 使用配置文件中的limit值，如果传入的limit <= 0
        int actualLimit = (limit > 0) ? limit : qdrantSearchConfig.getLimit();
        // 使用配置文件中的minScore值，如果传入的minScore为null
        float actualMinScore = (minScore != null) ? minScore : qdrantSearchConfig.getMinScore();
        
        log.info("开始向量搜索: teamId={}, externalProductId={}, limit={}, minScore={}, vectorDim={}",
                teamId, externalProductId, actualLimit, actualMinScore, vector.size());

        try {
            // 构建过滤条件
            Map<String, Object> filter = new HashMap<>();
            if (teamId != null) {
                try {
                    // 将字符串teamId转换为数字类型，以匹配向量库中的数字格式
                    Long teamIdLong = Long.parseLong(teamId);
                    filter.put("teamId", teamIdLong);
                    log.debug("转换teamId为数字类型: {} -> {}", teamId, teamIdLong);
                } catch (NumberFormatException e) {
                    // 如果转换失败，使用原始字符串
                    filter.put("teamId", teamId);
                    log.warn("teamId转换为数字失败，使用原始字符串: {}", teamId);
                }
            }

            if (prodType == 0) {
                if (externalProductId != null) {
                    filter.put("externalProductId", Long.valueOf(externalProductId));
                }

            } else {
                if (externalProductId != null) {
                    try {
                        // 将字符串productId转换为数字类型，以匹配向量库中的数字格式
                        Long productIdLong = Long.parseLong(externalProductId);
                        filter.put("productId", productIdLong);
                        log.debug("转换productId为数字类型: {} -> {}", externalProductId, productIdLong);
                    } catch (NumberFormatException e) {
                        // 如果转换失败，使用原始字符串
                        filter.put("productId", externalProductId);
                        log.warn("productId转换为数字失败，使用原始字符串: {}", externalProductId);
                    }
                }

                collectionName = this.jdCollectionName;
            }

            // 构建搜索请求
            VectorSearchRequest request = VectorSearchRequest.builder()
                    .collectionName(collectionName)
                    .vector(vector)
                    .limit(actualLimit)
                    .scoreThreshold(actualMinScore)
                    .filter(filter)
                    .withPayload(true)
                    .build();

            log.info("开始向量检索，集合: {}, 过滤条件: {}, 得分阈值: {}", collectionName, filter, actualMinScore);
            VectorSearchResult result = qdrantService.searchVectors(request);

            if (result != null && result.getResults() != null && !result.getResults().isEmpty()) {
                log.info("向量检索完成，找到相关内容: {} 条", result.getResults().size());
                VectorData vectorData = null;
                for(int i=0;i<result.getResults().size();i++){{
                    vectorData=result.getResults().get(i);
                    log.info(",============第{}个向量start======================================================================================================================",i);
                    log.info("向量内容为:{},向量分数为:{}",vectorData.getPayload().get("content"),vectorData.getScore());
                    log.info(",============第{}个向量end=========================================================================================================================",i);
                }}
                /*for(VectorData vectorData :result.getResults()){
                    log.info("============某个向量start=========================================================================");
                    log.info("向量内容为:{},向量分数为:{}",vectorData.getPayload().get("content"),vectorData.getScore());
                    log.info("============某个向量end============================================================================");
                }*/
            } else {
                log.info("向量检索未找到相关内容");
            }

            return result;

        } catch (Exception e) {
            log.error("向量搜索失败: teamId={}, externalProductId={}", teamId, externalProductId, e);
            return null;
        }
    }

    @Override
    public void processAndStoreProductVectors(String externalProductId, String teamId, String productDetail) {
        log.info("开始处理和存储产品向量: externalProductId={}, teamId={}", externalProductId, teamId);

        Long teamIdLong = null;
        try {
            teamIdLong = Long.valueOf(teamId);
        } catch (NumberFormatException e) {
            log.error("无效的团队ID格式: {}", teamId);
            return;
        }

        try {
            // 1. 清理和提取文本
            log.info("步骤1: 开始清理产品详情文本，产品ID: {}", externalProductId);
            String cleanText = htmlSanitizerService.sanitizeAndExtractText(productDetail);
            log.info("步骤1: 文本清理完成，产品ID: {}, 原始长度: {}, 清理后长度: {}",
                    externalProductId, productDetail.length(), cleanText.length());

            // 2. 语义分块
            log.info("步骤2: 开始语义分块，产品ID: {}, 分块大小: {}, 重叠大小: {}",
                    externalProductId, config.getChunkSize(), config.getOverlapSize());
            List<String> chunkTexts = doubaoChunkService.chunkText(cleanText, config.getChunkSize(), config.getOverlapSize());
            log.info("步骤2: 语义分块完成，产品ID: {}, 分块数量: {}", externalProductId, chunkTexts.size());

            // 3. 文本向量化
            log.info("步骤3: 开始文本向量化，产品ID: {}, 文本块数量: {}", externalProductId, chunkTexts.size());
            List<List<Double>> embeddings = embedTexts(chunkTexts);
            log.info("步骤3: 文本向量化完成，产品ID: {}, 向量数量: {}, 向量维度: {}",
                    externalProductId, embeddings.size(),
                    embeddings.isEmpty() ? 0 : embeddings.get(0).size());

            // 4. 确保集合存在
            if (!ensureCollectionExists(collectionName)) {
                throw new RuntimeException("向量集合创建或验证失败: " + collectionName);
            }

            // 5. 删除已存在的向量
            log.info("步骤5: 检查并删除已存在的向量，产品ID: {}", externalProductId);
            deleteProductVectors(externalProductId, teamId);

            // 6. 准备向量数据
            log.info("步骤6: 开始准备向量数据列表，产品ID: {}", externalProductId);
            List<VectorData> vectorDataList = prepareVectorDataList(
                    chunkTexts, embeddings, externalProductId, teamId
            );
            log.info("步骤6: 向量数据准备完成，数量: {}", vectorDataList.size());

            // 7. 批量插入向量
            log.info("步骤7: 开始批量插入向量库，产品ID: {}, 向量数量: {}", externalProductId, vectorDataList.size());
            boolean inserted = insertVectors(collectionName, vectorDataList);
            if (!inserted) {
                throw new RuntimeException("向量库插入失败");
            }
            log.info("步骤7: 向量库插入成功，产品ID: {}", externalProductId);

            // 8. 更新产品状态
            log.info("步骤8: 开始更新产品学习状态，产品ID: {}", externalProductId);
            updateProductLearningStatus(externalProductId, teamIdLong, cleanText);
            log.info("步骤8: 产品学习状态更新完成，产品ID: {}", externalProductId);

            log.info("产品向量处理完成: externalProductId={}, teamId={}", externalProductId, teamId);

        } catch (Exception e) {
            log.error("产品向量处理失败: externalProductId={}, teamId={}", externalProductId, teamId, e);
            throw new RuntimeException("产品向量处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteProductVectors(String externalProductId, String teamId) {
        log.info("开始删除产品向量: externalProductId={}, teamId={}", externalProductId, teamId);

        try {
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("externalProductId", externalProductId);
            conditions.put("teamId", teamId);

            List<String> existingVectorIds = getVectorIdsByConditions(collectionName, conditions);
            if (!existingVectorIds.isEmpty()) {
                log.info("发现已存在的向量，数量: {}, 准备删除", existingVectorIds.size());
                boolean deleted = qdrantService.deleteVectors(collectionName, existingVectorIds);
                if (deleted) {
                    log.info("成功删除已存在的向量，数量: {}", existingVectorIds.size());
                } else {
                    log.warn("删除已存在向量失败");
                }
                return deleted;
            } else {
                log.info("未发现已存在的向量，无需删除");
                return true;
            }
        } catch (Exception e) {
            log.error("删除产品向量失败: externalProductId={}, teamId={}", externalProductId, teamId, e);
            return false;
        }
    }

    @Override
    public List<List<Double>> embedTexts(List<String> texts) {
        log.debug("开始文本向量化，文本数量: {}", texts.size());

        try {
            List<List<Double>> embeddings = bigModelService.embed(texts);
            log.debug("文本向量化完成，向量数量: {}, 向量维度: {}",
                    embeddings.size(),
                    embeddings.isEmpty() ? 0 : embeddings.get(0).size());
            return embeddings;
        } catch (Exception e) {
            log.error("文本向量化失败", e);
            throw new RuntimeException("文本向量化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean ensureCollectionExists(String collectionName) {
        log.debug("检查向量集合是否存在: {}", collectionName);

        try {
            if (!qdrantService.collectionExists(collectionName)) {
                log.info("创建向量集合: {}, 维度: {}", collectionName, config.getVectorDimension());
                boolean created = qdrantService.createCollection(collectionName, config.getVectorDimension(), "COSINE");
                if (created) {
                    log.info("向量集合创建成功: {}", collectionName);
                } else {
                    log.error("向量集合创建失败: {}", collectionName);
                }
                return created;
            } else {
                log.debug("向量集合已存在: {}", collectionName);
                return true;
            }
        } catch (Exception e) {
            log.error("检查或创建向量集合失败: {}", collectionName, e);
            return false;
        }
    }

    @Override
    public boolean insertVectors(String collectionName, List<VectorData> vectorDataList) {
        log.debug("开始批量插入向量，集合: {}, 向量数量: {}", collectionName, vectorDataList.size());

        try {
            boolean result = qdrantService.insertVectors(collectionName, vectorDataList);
            if (result) {
                log.debug("批量插入向量成功，集合: {}, 向量数量: {}", collectionName, vectorDataList.size());
            } else {
                log.error("批量插入向量失败，集合: {}, 向量数量: {}", collectionName, vectorDataList.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入向量异常，集合: {}, 向量数量: {}", collectionName, vectorDataList.size(), e);
            return false;
        }
    }

    @Override
    public List<String> getVectorIdsByConditions(String collectionName, Map<String, Object> conditions) {
        log.debug("根据条件查询向量ID，集合: {}, 条件: {}", collectionName, conditions);

        try {
            List<String> vectorIds = qdrantService.getVectorIdsByPayload(collectionName, conditions);
            log.debug("查询到向量ID数量: {}", vectorIds.size());
            return vectorIds;
        } catch (Exception e) {
            log.error("根据条件查询向量ID失败，集合: {}, 条件: {}", collectionName, conditions, e);
            return Collections.emptyList();
        }
    }

    /**
     * 从搜索结果中提取内容文本
     *
     * @param result 搜索结果
     * @return 提取的文本内容
     */
    private String extractContentFromSearchResult(VectorSearchResult result) {
        if (result == null || result.getResults() == null || result.getResults().isEmpty()) {
            return "";
        }

        StringBuilder answerBuilder = new StringBuilder();
        for (VectorData vectorData : result.getResults()) {
            if (vectorData.getPayload() != null && vectorData.getPayload().containsKey("content")) {
                String content = (String) vectorData.getPayload().get("content");
                answerBuilder.append(content).append("\n");
            }
        }
        return answerBuilder.toString().trim();
    }

    /**
     * 准备向量数据列表
     *
     * @param chunkTexts        文本块列表
     * @param embeddings        向量列表
     * @param externalProductId 外部产品ID
     * @param teamId            团队ID
     * @return 向量数据列表
     */
    private List<VectorData> prepareVectorDataList(List<String> chunkTexts, List<List<Double>> embeddings,
                                                   String externalProductId, String teamId) {
        List<VectorData> vectorDataList = new ArrayList<>();

        for (int i = 0; i < chunkTexts.size() && i < embeddings.size(); i++) {
//            String chunkId = externalProductId + "_chunk_" + i + "_" + System.currentTimeMillis();

            // 转换向量格式
            List<Float> vector = embeddings.get(i).stream()
                    .map(Double::floatValue)
                    .collect(Collectors.toList());

            // 构建payload
            Map<String, Object> payload = new HashMap<>();
            payload.put("content", chunkTexts.get(i));
            payload.put("externalProductId", externalProductId);
            payload.put("teamId", teamId);
            payload.put("chunkIndex", i);
            payload.put("timestamp", System.currentTimeMillis());

            String vectorId = java.util.UUID.randomUUID().toString();

            VectorData vectorData = VectorData.builder()
//                    .id(chunkId)
                    .id(vectorId)
                    .vector(vector)
                    .payload(payload)
                    .build();

            vectorDataList.add(vectorData);
        }

        return vectorDataList;
    }

    /**
     * 更新产品学习状态
     *
     * @param externalProductId 外部产品ID
     * @param teamId            团队ID
     * @param cleanText         清理后的文本
     */
    private void updateProductLearningStatus(String externalProductId, Long teamId, String cleanText) {
        try {
            // 向量学习完成，状态设为已学习(2)
            Integer learnStatus = 2;
            String updater = "VectorSearchService";

            int affectedRows = trainProductMapper.updateLearningStatus(
                    externalProductId,
                    teamId,
                    learnStatus,
                    cleanText,
                    updater
            );

            if (affectedRows > 0) {
                log.info("产品学习状态更新成功: externalProductId={}, teamId={}, status={}",
                        externalProductId, teamId, learnStatus);
            } else {
                log.warn("产品学习状态更新失败，未找到匹配记录: externalProductId={}, teamId={}",
                        externalProductId, teamId);
            }
        } catch (Exception e) {
            log.error("更新产品学习状态失败: externalProductId={}, teamId={}", externalProductId, teamId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理京东商品向量数据
     *
     * @param trainJdProducts 京东商品对象
     * @param recoTextWithMD  推荐文本（Markdown格式）
     * @param imgUrl          图片URL
     * @param teamId          团队ID
     * @return 是否插入成功
     */
    public boolean processVectorForWare(TrainJdProducts trainJdProducts, String recoTextWithMD, String imgUrl, Long teamId) {
        log.info("开始处理京东商品向量数据: productId={}, teamId={}, imgUrl={}",
                trainJdProducts.getWareId(), teamId, imgUrl);

        try {
            // 2.1 先查询有没有相同的存在，存在则删除
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("productId", trainJdProducts.getWareId());
            conditions.put("teamId", teamId);
            conditions.put("imgUrl", imgUrl);

            // 确保集合存在
            if (!ensureCollectionExists(jdCollectionName)) {
                log.error("京东商品向量集合创建或验证失败: {}", jdCollectionName);
                return false;
            }

            // 查询现有向量ID
            List<String> existingVectorIds = qdrantService.getVectorIdsByPayload(jdCollectionName, conditions);

            // 如果存在相同的向量，先删除
            if (!existingVectorIds.isEmpty()) {
                log.info("发现已存在的向量，准备删除: productId={}, teamId={}, 向量数量={}",
                        trainJdProducts.getWareId(), teamId, existingVectorIds.size());

                for (String vectorId : existingVectorIds) {
                    boolean deleted = qdrantService.deleteVector(jdCollectionName, vectorId);
                    if (!deleted) {
                        log.warn("删除向量失败: vectorId={}", vectorId);
                    }
                }
                log.info("已删除现有向量: productId={}, teamId={}, 删除数量={}",
                        trainJdProducts.getWareId(), teamId, existingVectorIds.size());
            }

            // 2.2 如果没有相同的存在，或删除完成后，插入新向量
            if (recoTextWithMD == null || recoTextWithMD.trim().isEmpty()) {
                log.warn("推荐文本为空，跳过向量化: productId={}", trainJdProducts.getWareId());
                return false;
            }

            // 文本向量化
            log.info("开始向量化推荐文本: productId={}, 文本长度={}",
                    trainJdProducts.getWareId(), recoTextWithMD.length());
            List<List<Double>> embeddings = embedTexts(Arrays.asList(recoTextWithMD));

            if (embeddings.isEmpty()) {
                log.error("文本向量化失败: productId={}", trainJdProducts.getWareId());
                return false;
            }

            // 转换向量格式
            List<Float> vector = embeddings.get(0).stream()
                    .map(Double::floatValue)
                    .collect(Collectors.toList());

            // 2.3 Point的id用uuid
            String vectorId = UUID.randomUUID().toString();

            // 2.4 payLoad是TrainJdProducts.getWareId、teamId、imgUrl
            Map<String, Object> payload = new HashMap<>();
            payload.put("productId", trainJdProducts.getWareId());
            payload.put("teamId", teamId);
            payload.put("imgUrl", imgUrl);
            payload.put("content", recoTextWithMD);
            payload.put("timestamp", System.currentTimeMillis());

            // 构建向量数据
            VectorData vectorData = VectorData.builder()
                    .id(vectorId)
                    .vector(vector)
                    .payload(payload)
                    .build();

            // 插入向量库
            log.info("开始插入向量到京东商品集合: productId={}, vectorId={}",
                    trainJdProducts.getWareId(), vectorId);
            boolean inserted = qdrantService.insertVector(jdCollectionName, vectorData);

            if (inserted) {
                log.info("京东商品向量插入成功: productId={}, teamId={}, vectorId={}",
                        trainJdProducts.getWareId(), teamId, vectorId);
                return true;
            } else {
                log.error("京东商品向量插入失败: productId={}, teamId={}",
                        trainJdProducts.getWareId(), teamId);
                return false;
            }

        } catch (Exception e) {
            log.error("处理京东商品向量数据失败: productId={}, teamId={}",
                    trainJdProducts.getWareId(), teamId, e);
            return false;
        }
    }

    @Override
    public boolean deleteVectorForWare(TrainJdProducts trainJdProducts, String imgUrl, Long teamId) {
        log.info("开始删除京东商品向量数据: productId={}, teamId={}, imgUrl={}",
                trainJdProducts.getWareId(), teamId, imgUrl);

        try {
            // 构建查询条件
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("productId", trainJdProducts.getWareId());
            conditions.put("teamId", teamId);
            conditions.put("imgUrl", imgUrl);

            // 确保集合存在
            if (!ensureCollectionExists(jdCollectionName)) {
                log.error("京东商品向量集合创建或验证失败: {}", jdCollectionName);
                return false;
            }

            // 查询现有向量ID
            List<String> existingVectorIds = qdrantService.getVectorIdsByPayload(jdCollectionName, conditions);

            // 如果没有找到向量，返回true（认为删除成功）
            if (existingVectorIds.isEmpty()) {
                log.info("未找到需要删除的向量: productId={}, teamId={}, imgUrl={}",
                        trainJdProducts.getWareId(), teamId, imgUrl);
                return true;
            }

            // 删除找到的向量
            log.info("发现需要删除的向量，数量: {}, productId={}, teamId={}",
                    existingVectorIds.size(), trainJdProducts.getWareId(), teamId);

            boolean allDeleted = true;
            for (String vectorId : existingVectorIds) {
                boolean deleted = qdrantService.deleteVector(jdCollectionName, vectorId);
                if (!deleted) {
                    log.warn("删除向量失败: vectorId={}", vectorId);
                    allDeleted = false;
                } else {
                    log.debug("成功删除向量: vectorId={}", vectorId);
                }
            }

            if (allDeleted) {
                log.info("成功删除所有向量: productId={}, teamId={}, 删除数量={}",
                        trainJdProducts.getWareId(), teamId, existingVectorIds.size());
            } else {
                log.warn("部分向量删除失败: productId={}, teamId={}, 总数量={}",
                        trainJdProducts.getWareId(), teamId, existingVectorIds.size());
            }

            return allDeleted;

        } catch (Exception e) {
            log.error("删除京东商品向量数据失败: productId={}, teamId={}, imgUrl={}",
                    trainJdProducts.getWareId(), teamId, imgUrl, e);
            return false;
        }
    }
}