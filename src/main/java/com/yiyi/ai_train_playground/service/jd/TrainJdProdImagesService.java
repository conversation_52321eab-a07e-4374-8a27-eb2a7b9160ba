package com.yiyi.ai_train_playground.service.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages;
import java.util.List;

/**
 * 京东商品图片信息服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
public interface TrainJdProdImagesService {
    
    /**
     * 插入商品图片信息
     *
     * @param image 商品图片信息
     * @return 影响行数
     */
    int insert(TrainJdProdImages image);
    
    /**
     * 批量插入商品图片信息
     *
     * @param imageList 商品图片信息列表
     * @return 影响行数
     */
    int batchInsert(List<TrainJdProdImages> imageList);
    
    /**
     * 根据ID更新商品图片信息
     *
     * @param image 商品图片信息
     * @return 影响行数
     */
    int updateById(TrainJdProdImages image);
    
    /**
     * 根据ID删除商品图片信息
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);
    
    /**
     * 根据teamId和jdProdId查询商品图片列表
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamIdAndJdProdId(Long teamId, Long jdProdId);
    
    /**
     * 根据ID查询商品图片信息
     *
     * @param id 主键ID
     * @return 商品图片信息
     */
    TrainJdProdImages findById(Long id);
    
    /**
     * 根据teamId查询商品图片列表
     *
     * @param teamId 团队ID
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamId(Long teamId);
    
    /**
     * 根据同步状态查询商品图片列表
     *
     * @param teamId 团队ID
     * @param syncStatus 同步状态
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamIdAndSyncStatus(Long teamId, Integer syncStatus);
    
    /**
     * 根据teamId和jdProdId删除商品图片信息
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @return 影响行数
     */
    int deleteByTeamIdAndJdProdId(Long teamId, Long jdProdId);
    
    /**
     * 统计团队商品图片数量
     *
     * @param teamId 团队ID
     * @return 图片数量
     */
    Long countByTeamId(Long teamId);
    
    /**
     * 统计指定商品的图片数量
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @return 图片数量
     */
    Long countByTeamIdAndJdProdId(Long teamId, Long jdProdId);
    
    /**
     * 保存或更新商品图片信息
     * 如果图片已存在（根据teamId、jdProdId、imgUrl判断），则更新；否则插入
     *
     * @param image 商品图片信息
     * @return 影响行数
     */
    int saveOrUpdate(TrainJdProdImages image);

    /**
     * 根据team_id、sync_status分页查询商品图片列表
     *
     * @param teamId 团队ID
     * @param syncStatus 同步状态（可为null，表示查询所有状态）
     * @param jdProdId 京东商品ID（可为null，表示查询所有商品）
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamIdAndSyncStatusWithPagination(Long teamId, Integer syncStatus, Long jdProdId, Integer offset, Integer pageSize);
}
