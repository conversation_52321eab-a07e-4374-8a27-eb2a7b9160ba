package com.yiyi.ai_train_playground.service.jd;

import com.yiyi.ai_train_playground.dto.jd.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListResponse;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import java.util.List;

/**
 * 京东商品服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface TrainJdProductsService {
    
    /**
     * 根据wareId和teamId查询商品信息
     * 
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @return 商品信息，如果不存在返回null
     */
    TrainJdProducts findByWareIdAndTeamId(Long wareId, Long teamId);
    
    /**
     * 插入商品信息
     * 
     * @param product 商品信息
     * @return 插入成功返回true，失败返回false
     */
    boolean insert(TrainJdProducts product);
    
    /**
     * 根据wareId和teamId更新商品信息
     * 
     * @param product 商品信息
     * @return 更新成功返回true，失败返回false
     */
    boolean updateByWareIdAndTeamId(TrainJdProducts product);
    
    /**
     * 批量插入商品信息
     * 
     * @param productList 商品信息列表
     * @return 插入成功的数量
     */
    int batchInsert(List<TrainJdProducts> productList);
    
    /**
     * 保存或更新商品信息
     * 如果商品已存在则更新，不存在则插入
     * 
     * @param product 商品信息
     * @return 操作成功返回true，失败返回false
     */
    boolean saveOrUpdate(TrainJdProducts product);
    
    /**
     * 根据团队ID查询商品列表
     * 
     * @param teamId 团队ID
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamId(Long teamId);
    
    /**
     * 根据wareId和teamId删除商品信息
     * 
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteByWareIdAndTeamId(Long wareId, Long teamId);
    
    /**
     * 检查商品是否存在
     * 
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @return 存在返回true，不存在返回false
     */
    boolean existsByWareIdAndTeamId(Long wareId, Long teamId);
    
    /**
     * 统计团队商品数量
     * 
     * @param teamId 团队ID
     * @return 商品数量
     */
    long countByTeamId(Long teamId);
    
    /**
     * 根据wareId列表批量查询商品
     * 
     * @param wareIds wareId列表
     * @param teamId 团队ID
     * @return 商品列表
     */
    List<TrainJdProducts> findByWareIds(List<Long> wareIds, Long teamId);
    
    /**
     * 分页查询团队商品
     * 
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdWithPage(Long teamId, int offset, int limit);
    
    /**
     * 根据商品标题模糊查询
     * 
     * @param teamId 团队ID
     * @param title 商品标题关键词
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndTitleLike(Long teamId, String title);
    
    /**
     * 根据品牌查询商品
     * 
     * @param teamId 团队ID
     * @param brandName 品牌名称
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndBrandName(Long teamId, String brandName);
    
    /**
     * 根据价格区间查询商品
     *
     * @param teamId 团队ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndPriceRange(Long teamId, Double minPrice, Double maxPrice);

    /**
     * 分页查询京东商品列表（关联SKU表）
     *
     * @param teamId 团队ID
     * @param request 查询请求参数
     * @return 商品列表响应结果
     */
    JdPrdListResponse findJdProductList(Long teamId, JdPrdListRequest request);

    /**
     * 根据wareId查询商品详情
     *
     * @param teamId 团队ID
     * @param wareId 京东商品ID
     * @return 商品详情
     */
    JdPrdDtlResponse findJdProductDetail(Long teamId, Long wareId);

    /**
     * 根据team_id、sync_status分页查询商品列表
     *
     * @param teamId 团队ID
     * @param syncStatus 同步状态（可为null，表示查询所有状态）
     * @param jdProdId 京东商品ID（可为null，表示查询所有商品）
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndSyncStatusWithPagination(Long teamId, Integer syncStatus, Long jdProdId, Integer offset, Integer pageSize);

    /**
     * 根据ID动态更新商品信息
     *
     * @param product 商品信息（只更新非null字段）
     * @return 更新成功返回true，失败返回false
     */
    boolean updateByIdSelective(TrainJdProducts product);
}
