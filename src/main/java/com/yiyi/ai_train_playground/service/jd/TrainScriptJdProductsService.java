package com.yiyi.ai_train_playground.service.jd;

import com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts;
import java.util.List;

/**
 * 剧本京东商品关联服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-23
 */
public interface TrainScriptJdProductsService {
    
    /**
     * 插入剧本京东商品关联记录
     * 
     * @param scriptJdProducts 剧本京东商品关联信息
     * @return 插入成功返回true，失败返回false
     */
    boolean insert(TrainScriptJdProducts scriptJdProducts);
    
    /**
     * 批量插入剧本京东商品关联记录
     * 
     * @param scriptJdProductsList 剧本京东商品关联信息列表
     * @return 插入成功的数量
     */
    int batchInsert(List<TrainScriptJdProducts> scriptJdProductsList);
    
    /**
     * 根据剧本ID删除剧本京东商品关联
     * 
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteByScriptIdAndTeamId(Long scriptId, Long teamId);
    
    /**
     * 根据剧本ID查询剧本京东商品关联
     * 
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 剧本京东商品关联列表
     */
    List<TrainScriptJdProducts> selectByScriptIdAndTeamId(Long scriptId, Long teamId);
    
    /**
     * 根据剧本ID和团队ID查询关联的京东SKU ID列表
     * 
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 京东SKU ID列表
     */
    List<Long> selectJdSkuIdsByScriptIdAndTeamId(Long scriptId, Long teamId);
    
    /**
     * 根据ID查询剧本京东商品关联
     * 
     * @param id 主键ID
     * @return 剧本京东商品关联信息，如果不存在返回null
     */
    TrainScriptJdProducts selectById(Long id);
    
    /**
     * 根据ID更新剧本京东商品关联
     * 
     * @param scriptJdProducts 剧本京东商品关联信息
     * @return 更新成功返回true，失败返回false
     */
    boolean updateById(TrainScriptJdProducts scriptJdProducts);
    
    /**
     * 根据ID删除剧本京东商品关联
     * 
     * @param id 主键ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteById(Long id);
    
    /**
     * 根据京东SKU ID查询关联的剧本列表
     * 
     * @param trJdSkuId 京东SKU ID
     * @param teamId 团队ID
     * @return 剧本京东商品关联列表
     */
    List<TrainScriptJdProducts> selectByJdSkuIdAndTeamId(Long trJdSkuId, Long teamId);
    
    /**
     * 批量删除剧本京东商品关联
     * 
     * @param ids ID列表
     * @param teamId 团队ID
     * @return 删除成功返回true，失败返回false
     */
    boolean batchDeleteByIds(List<Long> ids, Long teamId);
}
