package com.yiyi.ai_train_playground.service.jd.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku;
import com.yiyi.ai_train_playground.service.jd.GetSkuFromJsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 从JSON文件读取并转换为Sku的服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
public class GetSkuFromJsonServiceImpl implements GetSkuFromJsonService {
    
    private final ObjectMapper objectMapper;
    
    /**
     * JSON文件路径，可通过配置文件配置
     */
    @Value("${jd.sku.json.file.path:C:/Users/<USER>/Downloads/temp.md/bot_product_sku.json}")
    private String jsonFilePath;
    
    /**
     * 构造函数，初始化ObjectMapper并配置忽略未知字段
     */
    public GetSkuFromJsonServiceImpl() {
        this.objectMapper = new ObjectMapper();
        // 配置忽略未知字段，避免JSON中有额外字段时解析失败
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    @Override
    public List<Sku> getSkuListFromJson() {
        log.info("开始从JSON文件读取数据并转换为Sku列表，文件路径: {}", jsonFilePath);

        try {
            // 1. 读取JSON文件
            File jsonFile = new File(jsonFilePath);
            if (!jsonFile.exists()) {
                log.error("JSON文件不存在: {}", jsonFilePath);
                return new ArrayList<>();
            }

            // 2. 使用Jackson的ObjectMapper反序列化为List<Map>，避免日期格式问题
            List<Map<String, Object>> rawDataList = objectMapper.readValue(jsonFile, new TypeReference<List<Map<String, Object>>>() {});
            log.info("成功读取JSON文件，共{}条原始SKU记录", rawDataList.size());

            // 3. 转换为List<Sku>
            List<Sku> skuList = new ArrayList<>();
            for (Map<String, Object> rawData : rawDataList) {
                try {
                    // 直接从Map转换为Sku
                    Sku sku = convertMapToSku(rawData);
                    if (sku != null) {
                        skuList.add(sku);
                    }
                } catch (Exception e) {
                    log.error("转换原始SKU数据时出错，skuId: {}, 错误信息: {}",
                             rawData.get("sku_id"), e.getMessage());
                }
            }

            log.info("成功转换{}条Sku记录", skuList.size());
            return skuList;

        } catch (Exception e) {
            log.error("从JSON文件读取并转换数据时出错", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Sku> getSkuListFromJson(Long wareId) {
        log.info("开始从JSON文件读取数据并根据wareId筛选转换为Sku列表，文件路径: {}，wareId: {}", jsonFilePath, wareId);

        try {
            // 1. 先获取所有SKU数据
            List<Sku> allSkuList = getSkuListFromJson();

            // 2. 如果wareId为null，返回所有数据
            if (wareId == null) {
                log.info("wareId为null，返回所有{}条Sku记录", allSkuList.size());
                return allSkuList;
            }

            // 3. 根据wareId筛选SKU
            List<Sku> filteredSkuList = allSkuList.stream()
                    .filter(sku -> wareId.equals(sku.getWareId()))
                    .collect(Collectors.toList());

            log.info("根据wareId: {} 筛选后，共{}条Sku记录", wareId, filteredSkuList.size());
            return filteredSkuList;

        } catch (Exception e) {
            log.error("从JSON文件读取并根据wareId筛选数据时出错，wareId: {}", wareId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将Map数据转换为Sku对象
     *
     * @param rawData 原始Map数据
     * @return Sku对象
     */
    private Sku convertMapToSku(Map<String, Object> rawData) {
        try {
            Sku sku = new Sku();

            // 设置基本字段，支持驼峰命名和下划线命名
            sku.setSkuId(getLongValue(rawData, "skuId") != null ? getLongValue(rawData, "skuId") : getLongValue(rawData, "sku_id"));
            sku.setWareId(getLongValue(rawData, "wareId") != null ? getLongValue(rawData, "wareId") : getLongValue(rawData, "ware_id"));
            sku.setSkuName(getStringValue(rawData, "skuName") != null ? getStringValue(rawData, "skuName") : getStringValue(rawData, "sku_name"));
            sku.setWareTitle(getStringValue(rawData, "wareTitle") != null ? getStringValue(rawData, "wareTitle") : getStringValue(rawData, "ware_title"));
            sku.setBarCode(getStringValue(rawData, "barCode") != null ? getStringValue(rawData, "barCode") : getStringValue(rawData, "bar_code"));
            sku.setCategoryId(getLongValue(rawData, "categoryId") != null ? getLongValue(rawData, "categoryId") : getLongValue(rawData, "category_id"));
            sku.setJdPrice(getBigDecimalValue(rawData, "jdPrice") != null ? getBigDecimalValue(rawData, "jdPrice") : getBigDecimalValue(rawData, "jd_price"));
            sku.setLogo(getStringValue(rawData, "logo"));
            sku.setStatus(getIntegerValue(rawData, "status"));
            sku.setEnable(getIntegerValue(rawData, "enable"));
            sku.setStockNum(getLongValue(rawData, "stockNum") != null ? getLongValue(rawData, "stockNum") : getLongValue(rawData, "stock_num"));

            // 处理日期字段 - 这里是问题的关键
            // JSON中的日期格式可能是 "2025-04-02" 或其他格式
            // 尝试解析日期字段，看看具体错误
            sku.setCreated(parseDateFromString(getStringValue(rawData, "created")));
            sku.setModified(parseDateFromString(getStringValue(rawData, "modified")));

            return sku;

        } catch (Exception e) {
            log.error("转换Map数据为Sku时出错: {}", e.getMessage());
            return null;
        }
    }

    // 辅助方法：安全获取Long值
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取Integer值
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取BigDecimal值
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取Double值
    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取String值
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    // 辅助方法：解析日期字符串
    private Date parseDateFromString(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        log.debug("尝试解析日期字符串: '{}'", dateStr);

        // 尝试多种日期格式
        String[] dateFormats = {
            "yyyy-MM-dd",           // 2025-04-02
            "yyyy-MM-dd HH:mm:ss",  // 2025-04-02 10:30:00
            "yyyy/MM/dd",           // 2025/04/02
            "yyyy/MM/dd HH:mm:ss"   // 2025/04/02 10:30:00
        };

        for (String format : dateFormats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                Date result = sdf.parse(dateStr);
                log.debug("成功解析日期: '{}' -> {} (使用格式: {})", dateStr, result, format);
                return result;
            } catch (ParseException e) {
                log.debug("日期格式 '{}' 解析失败: {}", format, e.getMessage());
            }
        }

        log.warn("无法解析日期字符串: '{}'", dateStr);
        return null;
    }
}
