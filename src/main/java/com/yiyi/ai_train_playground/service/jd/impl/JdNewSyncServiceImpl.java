package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.entity.jd.TrainJdAccessToken;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.entity.jd.TrainJdSku;
import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.enums.JdSyncStatus;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdProdImagesMapper;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdProductsMapper;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdSkuMapper;
import com.yiyi.ai_train_playground.mapper.TrainTeamShopsMapper;
import com.yiyi.ai_train_playground.service.jd.GetSkuFromJsonService;
import com.yiyi.ai_train_playground.service.jd.GetTdFromJsonService;
import com.yiyi.ai_train_playground.service.jd.JdNewSyncService;
import com.yiyi.ai_train_playground.service.jd.TrainJdProdImagesService;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import com.yiyi.ai_train_playground.util.Md5Util;
import com.yiyi.ai_train_playground.util.ResolveUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 京东新同步服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JdNewSyncServiceImpl implements JdNewSyncService {
    
    private final JdConfig jdConfig;
    private final GetTdFromJsonService getTdFromJsonService;
    private final GetSkuFromJsonService getSkuFromJsonService;
    private final YiYiJdService yiYiJdService;
    private final TrainJdProductsMapper trainJdProductsMapper;
    private final TrainJdProdImagesMapper trainJdProdImagesMapper;
    private final TrainJdProdImagesService trainJdProdImagesService;
    private final TrainJdSkuMapper trainJdSkuMapper;
    private final VectorSearchService vectorSearchService;
    private final TrainJdAccessTokenMapper trainJdAccessTokenMapper;
    private final TrainTeamShopsMapper trainTeamShopsMapper;
    
    @Override
    @Async("taskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void sync(String xid, String accessToken, Long teamId, String creator) {
        log.info("开始异步同步京东商品数据，xid: {}, teamId: {}, creator: {}", xid, teamId, creator);
        
        try {
            // 1. 获取远程商品列表
            List<TrainJdProducts> remoteJdProdList = getRemoteJdProdList(accessToken);
            log.info("获取到远程商品数据 {} 条", remoteJdProdList.size());
            
            // 2. 处理远程商品列表
            processRemoteJdProdList(remoteJdProdList, xid, accessToken, teamId, creator);
            
            log.info("京东商品数据同步完成，xid: {}, teamId: {}", xid, teamId);
            
        } catch (Exception e) {
            log.error("同步京东商品数据失败，xid: {}, teamId: {}", xid, teamId, e);
            throw new RuntimeException("同步京东商品数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取远程京东商品列表
     * 
     * @param accessToken 访问令牌
     * @return 商品列表
     */
    private List<TrainJdProducts> getRemoteJdProdList(String accessToken) {
        log.info("开始获取远程京东商品列表");
        
        // 获取分页大小配置
        Integer pageSize = (jdConfig.getSync() != null && jdConfig.getSync().getPageSize() != null) 
            ? jdConfig.getSync().getPageSize() : 10;
        
        // 检查Mock开关
        boolean isMockSwitch = (jdConfig.getSync() != null && jdConfig.getSync().getIsMockSwitch() != null)
            ? jdConfig.getSync().getIsMockSwitch() : false;
        
        List<TrainJdProducts> allProducts = new ArrayList<>();
        
        if (isMockSwitch) {
            log.info("使用本地Mock数据获取商品列表");
            allProducts = getTdFromJsonService.getTrainJdProductsFromJson();
        } else {
            log.info("使用远程API获取商品列表，pageSize: {}", pageSize);
            
            int pageNo = 1;
            while (true) {
                log.debug("正在获取第 {} 页数据，每页 {} 条", pageNo, pageSize);
                
                List<TrainJdProducts> pageProducts = yiYiJdService.getWare4ValidProductList(accessToken, pageNo, pageSize);
                
                if (pageProducts == null || pageProducts.isEmpty()) {
                    log.debug("第 {} 页没有获取到数据，停止分页", pageNo);
                    break;
                }
                
                allProducts.addAll(pageProducts);
                log.debug("第 {} 页获取到 {} 条数据，累计 {} 条", pageNo, pageProducts.size(), allProducts.size());
                
                // 如果当前页数据量小于pageSize，说明已经到最后一页
                if (pageProducts.size() < pageSize) {
                    log.debug("当前页数据量({})小于分页大小({})，已到最后一页", pageProducts.size(), pageSize);
                    break;
                }
                
                pageNo++;
            }
        }
        
        log.info("获取远程商品列表完成，总计 {} 条", allProducts.size());
        return allProducts;
    }
    
    /**
     * 处理远程京东商品列表
     *
     * @param remoteJdProdList 远程商品列表
     * @param xid 京东应用标识
     * @param accessToken 访问令牌
     * @param teamId 团队ID
     * @param creator 创建者
     */
    private void processRemoteJdProdList(List<TrainJdProducts> remoteJdProdList, String xid, String accessToken, Long teamId, String creator) {
        log.info("开始处理远程商品列表，数量: {}", remoteJdProdList.size());
        
        int processedCount = 0;
        int insertedCount = 0;
        int updatedCount = 0;
        int skippedCount = 0;
        
        for (TrainJdProducts rmtJdProduct : remoteJdProdList) {
            try {
                if (rmtJdProduct.getWareId() == null) {
                    log.warn("商品wareId为空，跳过处理");
                    skippedCount++;
                    continue;
                }
                
                // 检查商品是否已存在
                TrainJdProducts existingLocalProduct = trainJdProductsMapper.findByWareIdAndTeamId(
                    rmtJdProduct.getWareId(), teamId);
                
                if (existingLocalProduct == null) {
                    // 不存在，插入新商品
                    insertNewProduct(rmtJdProduct, teamId, creator);
                    insertedCount++;
                } else {
                    // 存在，检查是否需要更新
                    boolean updated = updateExistingProduct(rmtJdProduct, existingLocalProduct, teamId, creator);
                    if (updated) {
                        updatedCount++;
                    } else {
                        skippedCount++;
                    }
                }

                // 同步该商品的SKU数据
                insertOrUpdateNewSku(accessToken, rmtJdProduct.getWareId(), teamId, creator);
                
                processedCount++;

                // 首次同步时需要更新店铺、accessToken等信息
                if (processedCount == 1) {
                    log.info("首次同步，更新店铺和AccessToken信息");
                    // 使用第一个商品的shopId作为默认值
                    if (!remoteJdProdList.isEmpty()) {
                        TrainJdProducts firstProduct = remoteJdProdList.get(0);
                        modifyShopAndACT(xid, firstProduct, teamId, creator);
                    }
                }
                
                if (processedCount % 100 == 0) {
                    log.info("已处理 {} 条商品数据", processedCount);
                }
                
            } catch (Exception e) {
                log.error("处理商品失败，wareId: {}", rmtJdProduct.getWareId(), e);
                skippedCount++;
            }
        }
        
        log.info("商品列表处理完成，总计: {}, 新增: {}, 更新: {}, 跳过: {}",
                processedCount, insertedCount, updatedCount, skippedCount);


    }

    /**
     * 插入新商品
     *
     * @param rmtJdProduct 远程商品数据
     * @param teamId 团队ID
     * @param creator 创建者
     */
    private void insertNewProduct(TrainJdProducts rmtJdProduct, Long teamId, String creator) {
        log.debug("插入新商品，wareId: {}", rmtJdProduct.getWareId());

        try {
            // 设置基本信息
            rmtJdProduct.setTeamId(teamId);
            rmtJdProduct.setCreator(creator);
            rmtJdProduct.setUpdater(creator);
            rmtJdProduct.setCreateTime(LocalDateTime.now());
            rmtJdProduct.setUpdateTime(LocalDateTime.now());

            // 生成introduction的MD5签名
            String introduction = rmtJdProduct.getIntroduction();
            String descSignature = Md5Util.generateMd5(introduction);
            rmtJdProduct.setDescSignature(descSignature);

            // 设置同步状态为未同步
            rmtJdProduct.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());

            // 插入商品主表
            int insertResult = trainJdProductsMapper.insert(rmtJdProduct);
            if (insertResult <= 0) {
                throw new RuntimeException("插入商品主表失败");
            }

            log.debug("商品主表插入成功，wareId: {}, id: {}", rmtJdProduct.getWareId(), rmtJdProduct.getId());

            // 提取并插入图片
            extractAndInsertImages(introduction, teamId, rmtJdProduct.getWareId(), creator);

        } catch (Exception e) {
            log.error("插入新商品失败，wareId: {}", rmtJdProduct.getWareId(), e);
            throw new RuntimeException("插入新商品失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新现有商品
     *
     * @param rmtJdProduct 远程商品数据
     * @param existingLocalProduct 本地现有商品数据
     * @param teamId 团队ID
     * @param creator 创建者
     * @return 是否执行了更新
     */
    private boolean updateExistingProduct(TrainJdProducts rmtJdProduct, TrainJdProducts existingLocalProduct,
                                        Long teamId, String creator) {
        log.debug("检查商品是否需要更新，wareId: {}", rmtJdProduct.getWareId());

        try {
            // 比较modified字段
            if (rmtJdProduct.getModified() == null || existingLocalProduct.getModified() == null ||
                !rmtJdProduct.getModified().isAfter(existingLocalProduct.getModified())) {
                log.debug("商品无需更新，wareId: {}", rmtJdProduct.getWareId());
                return false;
            }

            log.debug("商品需要更新，wareId: {}", rmtJdProduct.getWareId());

            // 保存当前签名
            String currentDescSignature = existingLocalProduct.getDescSignature();

            // 生成新的签名
            String introduction = rmtJdProduct.getIntroduction();
            String newDescSignature = Md5Util.generateMd5(introduction);

            // 比较签名
            if (!Md5Util.compareMd5(currentDescSignature, newDescSignature)) {
                log.debug("商品描述已变更，需要更新图片，wareId: {}", rmtJdProduct.getWareId());

                // 删除旧图片
                trainJdProdImagesMapper.deleteByTeamIdAndJdProdId(teamId, existingLocalProduct.getId());

                // 删除旧的向量数据
                deleteImgVectors(introduction, teamId, rmtJdProduct, creator);

                // 插入新图片
                extractAndInsertImages(introduction, teamId, existingLocalProduct.getId(), creator);

                // 更新签名
                currentDescSignature = newDescSignature;
            }

            // 更新商品主表
            rmtJdProduct.setId(existingLocalProduct.getId());
            rmtJdProduct.setTeamId(teamId);
            rmtJdProduct.setUpdater(creator);
            rmtJdProduct.setUpdateTime(LocalDateTime.now());
            rmtJdProduct.setDescSignature(currentDescSignature);
            rmtJdProduct.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());

            int updateResult = trainJdProductsMapper.updateByWareIdAndTeamId(rmtJdProduct);
            if (updateResult <= 0) {
                throw new RuntimeException("更新商品主表失败");
            }

            log.debug("商品更新成功，wareId: {}", rmtJdProduct.getWareId());
            return true;

        } catch (Exception e) {
            log.error("更新现有商品失败，wareId: {}", rmtJdProduct.getWareId(), e);
            throw new RuntimeException("更新现有商品失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提取图片并插入到图片表
     *
     * @param introduction 商品介绍HTML
     * @param teamId 团队ID
     * @param jdProdId 商品ID
     * @param creator 创建者
     */
    private void extractAndInsertImages(String introduction, Long teamId, Long jdProdId, String creator) {
        if (introduction == null || introduction.trim().isEmpty()) {
            log.debug("商品介绍为空，跳过图片提取，jdProdId: {}", jdProdId);
            return;
        }

        try {
            log.debug("开始提取图片，jdProdId: {}", jdProdId);

            // 提取图片URL列表
            List<String> imgList = ResolveUtil.extractImageSources(introduction);

            if (imgList.isEmpty()) {
                log.debug("未提取到图片，jdProdId: {}", jdProdId);
                return;
            }

            log.debug("提取到 {} 张图片，jdProdId: {}", imgList.size(), jdProdId);

            // 构建图片实体列表
            List<TrainJdProdImages> imageList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (String imgUrl : imgList) {
                TrainJdProdImages image = new TrainJdProdImages();
                image.setTeamId(teamId);
                image.setJdProdId(jdProdId);
                image.setImgUrl(imgUrl);
                image.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());
                image.setCreateTime(now);
                image.setUpdateTime(now);
                image.setCreator(creator);
                image.setUpdater(creator);
                image.setImgRecoText("todo");

                imageList.add(image);
            }

            // 批量插入图片
            int insertResult = trainJdProdImagesMapper.batchInsert(imageList);
            log.debug("批量插入图片完成，jdProdId: {}, 插入数量: {}", jdProdId, insertResult);

        } catch (Exception e) {
            log.error("提取并插入图片失败，jdProdId: {}", jdProdId, e);
            throw new RuntimeException("提取并插入图片失败: " + e.getMessage(), e);
        }
    }

    /**
     * 插入或更新SKU数据
     *
     * @param accessToken 访问令牌
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @param creator 创建者
     */
    private void insertOrUpdateNewSku(String accessToken, Long wareId, Long teamId, String creator) {
        log.info("开始同步SKU数据，wareId: {}, teamId: {}", wareId, teamId);

        try {
            // 1. 获取远程SKU列表
            List<TrainJdSku> rmtSkuList = getRemoteSkuList(accessToken, wareId, teamId, creator);

            if (rmtSkuList.isEmpty()) {
                log.info("未获取到SKU数据，wareId: {}", wareId);
                return;
            }

            log.info("获取到 {} 个SKU，wareId: {}", rmtSkuList.size(), wareId);

            // 2. 处理每个SKU
            int processedCount = 0;
            int insertedCount = 0;
            int updatedCount = 0;
            int skippedCount = 0;

            for (TrainJdSku rmtSku : rmtSkuList) {
                try {
                    if (rmtSku.getSkuId() == null) {
                        log.warn("SKU ID为空，跳过处理");
                        skippedCount++;
                        continue;
                    }

                    // 设置基本信息
                    rmtSku.setTeamId(teamId);
                    rmtSku.setCreator(creator);
                    rmtSku.setUpdater(creator);
                    rmtSku.setCreateTime(LocalDateTime.now());
                    rmtSku.setUpdateTime(LocalDateTime.now());

                    // 检查SKU是否已存在
                    TrainJdSku existingSku = trainJdSkuMapper.findBySkuId(rmtSku.getSkuId());

                    if (existingSku == null) {
                        // 不存在，直接插入
                        trainJdSkuMapper.insert(rmtSku);
                        insertedCount++;
                        log.debug("插入新SKU: {}", rmtSku.getSkuId());
                    } else {
                        // 存在，检查是否需要更新
                        LocalDateTime skuModified = convertDateToLocalDateTime(rmtSku.getModified());
                        boolean isRemoteNewer = trainJdSkuMapper.isRemoteNewer(rmtSku.getSkuId(), skuModified);

                        if (isRemoteNewer) {
                            // 远程数据更新，执行更新
                            rmtSku.setUpdater(creator);
                            trainJdSkuMapper.updateBySkuId(rmtSku);
                            updatedCount++;
                            log.debug("更新SKU: {}", rmtSku.getSkuId());
                        } else {
                            // 本地数据已是最新，跳过
                            skippedCount++;
                            log.debug("跳过SKU（本地已是最新）: {}", rmtSku.getSkuId());
                        }
                    }

                    processedCount++;

                } catch (Exception e) {
                    log.error("处理SKU失败，skuId: {}", rmtSku.getSkuId(), e);
                    skippedCount++;
                }
            }

            log.info("SKU数据同步完成，wareId: {}, 总计: {}, 新增: {}, 更新: {}, 跳过: {}",
                    wareId, processedCount, insertedCount, updatedCount, skippedCount);

        } catch (Exception e) {
            log.error("同步SKU数据失败，wareId: {}, teamId: {}", wareId, teamId, e);
            throw new RuntimeException("同步SKU数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取远程SKU列表
     *
     * @param accessToken 访问令牌
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @param creator 创建者
     * @return SKU列表
     */
    private List<TrainJdSku> getRemoteSkuList(String accessToken, Long wareId, Long teamId, String creator) {
        log.info("开始获取远程SKU列表，wareId: {}", wareId);

        // 获取分页大小配置
        Integer pageSize = (jdConfig.getSync() != null && jdConfig.getSync().getPageSize() != null)
            ? jdConfig.getSync().getPageSize() : 10;

        // 检查Mock开关
        boolean isMockSwitch = (jdConfig.getSync() != null && jdConfig.getSync().getIsMockSwitch() != null)
            ? jdConfig.getSync().getIsMockSwitch() : false;

        List<TrainJdSku> allSkus = new ArrayList<>();

        if (isMockSwitch) {
            log.info("使用本地Mock数据获取SKU列表，wareId: {}", wareId);
            // Mock模式下需要转换Sku对象为TrainJdSku对象
            List<com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku> mockSkus =
                getSkuFromJsonService.getSkuListFromJson(wareId);
            allSkus = convertSkusToTrainJdSkus(mockSkus, teamId, creator);
        } else {
            log.info("使用远程API获取SKU列表，wareId: {}, pageSize: {}", wareId, pageSize);

            int page = 1;
            while (true) {
                log.debug("正在获取第 {} 页SKU数据，wareId: {}, 每页 {} 条", page, wareId, pageSize);

                List<TrainJdSku> pageSkus = yiYiJdService.getTrainJdSkuList(accessToken, wareId, page, pageSize);

                if (pageSkus == null || pageSkus.isEmpty()) {
                    log.debug("第 {} 页没有获取到SKU数据，wareId: {}, 停止分页", page, wareId);
                    break;
                }

                allSkus.addAll(pageSkus);
                log.debug("第 {} 页获取到 {} 条SKU数据，wareId: {}, 累计 {} 条",
                        page, pageSkus.size(), wareId, allSkus.size());

                // 如果当前页数据量小于pageSize，说明已经到最后一页
                if (pageSkus.size() < pageSize) {
                    log.debug("当前页SKU数据量({})小于分页大小({})，wareId: {}, 已到最后一页",
                            pageSkus.size(), pageSize, wareId);
                    break;
                }

                page++;
            }
        }

        log.info("获取远程SKU列表完成，wareId: {}, 总计 {} 条", wareId, allSkus.size());
        return allSkus;
    }

    /**
     * 将LocalDateTime转换为LocalDateTime（实际上是直接返回，保持兼容性）
     *
     * @param dateTime LocalDateTime对象
     * @return LocalDateTime对象
     */
    private LocalDateTime convertDateToLocalDateTime(LocalDateTime dateTime) {
        return dateTime;
    }

    /**
     * 将Sku对象列表转换为TrainJdSku对象列表
     *
     * @param skus Sku对象列表
     * @param teamId 团队ID
     * @param creator 创建者
     * @return TrainJdSku对象列表
     */
    private List<TrainJdSku> convertSkusToTrainJdSkus(
            List<com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku> skus,
            Long teamId, String creator) {

        List<TrainJdSku> trainJdSkus = new ArrayList<>();

        if (skus == null || skus.isEmpty()) {
            return trainJdSkus;
        }

        for (com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku sku : skus) {
            try {
                TrainJdSku trainJdSku = new TrainJdSku();

                // 设置基本信息
                trainJdSku.setTeamId(teamId);
                trainJdSku.setCreator(creator);
                trainJdSku.setCreateTime(LocalDateTime.now());
                trainJdSku.setUpdateTime(LocalDateTime.now());

                // 设置SKU字段
                trainJdSku.setSkuId(sku.getSkuId());
                trainJdSku.setWareId(sku.getWareId());
                trainJdSku.setSkuName(sku.getSkuName());
                trainJdSku.setWareTitle(sku.getWareTitle());
                trainJdSku.setBarCode(sku.getBarCode());
                trainJdSku.setCategoryId(sku.getCategoryId() != null ? Integer.valueOf(sku.getCategoryId().intValue()) : null);
                trainJdSku.setJdPrice(sku.getJdPrice() != null ? new java.math.BigDecimal(sku.getJdPrice().toString()) : null);
                trainJdSku.setLogo(sku.getLogo());
                trainJdSku.setStatus(sku.getStatus());
                trainJdSku.setEnable(sku.getEnable());
                trainJdSku.setStockNum(sku.getStockNum());

                // 转换日期字段
                trainJdSku.setCreated(convertJavaDateToLocalDateTime(sku.getCreated()));
                trainJdSku.setModified(convertJavaDateToLocalDateTime(sku.getModified()));

                trainJdSkus.add(trainJdSku);

            } catch (Exception e) {
                log.error("转换Sku对象为TrainJdSku失败，skuId: {}", sku.getSkuId(), e);
            }
        }

        log.info("转换完成，原始SKU数量: {}, 转换后数量: {}", skus.size(), trainJdSkus.size());
        return trainJdSkus;
    }

    /**
     * 将java.util.Date转换为LocalDateTime
     *
     * @param date java.util.Date对象
     * @return LocalDateTime对象
     */
    private LocalDateTime convertJavaDateToLocalDateTime(java.util.Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDateTime();
    }

    /**
     * 删除图片向量数据
     *
     * @param introduction 商品介绍HTML内容
     * @param teamId 团队ID
     * @param rmtJdProduct 远程商品数据
     * @param creator 创建者
     */
    private void deleteImgVectors(String introduction, Long teamId, TrainJdProducts rmtJdProduct, String creator) {
        log.info("开始删除图片向量数据，wareId: {}, teamId: {}", rmtJdProduct.getWareId(), teamId);

        try {
            // 1. 提取图片URL列表
            List<String> imgList = ResolveUtil.extractImageSources(introduction);

            if (imgList == null || imgList.isEmpty()) {
                log.info("未找到图片URL，无需删除向量，wareId: {}", rmtJdProduct.getWareId());
                return;
            }

            log.info("提取到 {} 个图片URL，wareId: {}", imgList.size(), rmtJdProduct.getWareId());

            // 2. 遍历图片URL列表，删除对应的向量
            int deletedCount = 0;
            int failedCount = 0;

            for (String imgUrl : imgList) {
                try {
                    if (imgUrl == null || imgUrl.trim().isEmpty()) {
                        log.warn("图片URL为空，跳过删除，wareId: {}", rmtJdProduct.getWareId());
                        failedCount++;
                        continue;
                    }

                    // 3. 调用VectorSearchService删除向量
                    boolean deleted = vectorSearchService.deleteVectorForWare(rmtJdProduct, imgUrl, teamId);

                    if (deleted) {
                        deletedCount++;
                        log.debug("成功删除图片向量，wareId: {}, imgUrl: {}", rmtJdProduct.getWareId(), imgUrl);
                    } else {
                        failedCount++;
                        log.warn("删除图片向量失败，wareId: {}, imgUrl: {}", rmtJdProduct.getWareId(), imgUrl);
                    }

                } catch (Exception e) {
                    failedCount++;
                    log.error("删除图片向量异常，wareId: {}, imgUrl: {}", rmtJdProduct.getWareId(), imgUrl, e);
                }
            }

            log.info("图片向量删除完成，wareId: {}, 总计: {}, 成功: {}, 失败: {}",
                    rmtJdProduct.getWareId(), imgList.size(), deletedCount, failedCount);

        } catch (Exception e) {
            log.error("删除图片向量数据失败，wareId: {}, teamId: {}", rmtJdProduct.getWareId(), teamId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 修改店铺和AccessToken信息
     *
     * @param xid 京东应用标识
     * @param rmtJdProduct 远程商品数据
     * @param teamId 团队ID
     * @param creator 创建者
     */
    private void modifyShopAndACT(String xid, TrainJdProducts rmtJdProduct, Long teamId, String creator) {
        log.info("开始更新店铺和AccessToken信息，xid: {}, teamId: {}", xid, teamId);

        try {
            // 获取shopId，优先使用商品的shopId，如果没有则使用默认值
            Long shopId = rmtJdProduct.getShopId() != null ? rmtJdProduct.getShopId() : 0L;

            // 更新AccessToken实体
            updateTATEntity(xid, shopId, JdSyncStatus.UN_SYNC);

            // 插入或更新团队店铺实体
            insertTeamShopEntity(teamId, shopId, creator);

            log.info("店铺和AccessToken信息更新完成，xid: {}, shopId: {}, teamId: {}", xid, shopId, teamId);

        } catch (Exception e) {
            log.error("更新店铺和AccessToken信息失败，xid: {}, teamId: {}", xid, teamId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新TrainJdAccessToken实体
     *
     * @param xid 京东应用标识
     * @param shopId 店铺ID
     * @param jdSyncStatus 同步状态
     */
    private void updateTATEntity(String xid, Long shopId, JdSyncStatus jdSyncStatus) {
        log.debug("更新AccessToken实体，xid: {}, shopId: {}", xid, shopId);

        try {
            TrainJdAccessToken localTrainJdATEntity = trainJdAccessTokenMapper.findByXid(xid);

            if (localTrainJdATEntity == null) {
                log.warn("未找到xid对应的AccessToken记录: {}", xid);
                return;
            }

            localTrainJdATEntity.setXid(xid);
            localTrainJdATEntity.setShopId(shopId);
            localTrainJdATEntity.setIsSyncComplete(JdSyncStatus.UN_SYNC.getCode());

            int updateResult = trainJdAccessTokenMapper.updateByXid(localTrainJdATEntity);
            if (updateResult > 0) {
                log.debug("AccessToken实体更新成功，xid: {}", xid);
            } else {
                log.warn("AccessToken实体更新失败，xid: {}", xid);
            }

        } catch (Exception e) {
            log.error("更新AccessToken实体异常，xid: {}, shopId: {}", xid, shopId, e);
            throw e;
        }
    }

    /**
     * 插入团队店铺实体
     *
     * @param teamId 团队ID
     * @param shopId 店铺ID
     * @param creator 创建者
     */
    private void insertTeamShopEntity(Long teamId, Long shopId, String creator) {
        log.debug("插入团队店铺实体，teamId: {}, shopId: {}", teamId, shopId);

        try {
            TrainTeamShops localTSM = trainTeamShopsMapper.findByShopId(shopId);

            if (localTSM == null) {
                log.debug("店铺不存在，创建新的团队店铺记录，shopId: {}", shopId);

                TrainTeamShops teamShop = new TrainTeamShops();
                teamShop.setTeamId(teamId);
                teamShop.setShopId(shopId); // 默认店铺ID
                teamShop.setShopType(0); // 0:京东
                teamShop.setCreator(creator);
                teamShop.setUpdater(creator);
                teamShop.setIsAuthorize(true); // 设置为已授权
                teamShop.setIsSyncComplete(JdSyncStatus.UN_SYNC.getCode()); // 0：未同步
                teamShop.setCreateTime(LocalDateTime.now());
                teamShop.setUpdateTime(LocalDateTime.now());

                int insertResult = trainTeamShopsMapper.insert(teamShop);
                if (insertResult > 0) {
                    log.debug("团队店铺实体插入成功，teamId: {}, shopId: {}", teamId, shopId);
                } else {
                    log.warn("团队店铺实体插入失败，teamId: {}, shopId: {}", teamId, shopId);
                }
            } else {
                log.debug("店铺已存在，跳过插入，shopId: {}", shopId);
            }

        } catch (Exception e) {
            log.error("插入团队店铺实体异常，teamId: {}, shopId: {}", teamId, shopId, e);
            throw e;
        }
    }
}
