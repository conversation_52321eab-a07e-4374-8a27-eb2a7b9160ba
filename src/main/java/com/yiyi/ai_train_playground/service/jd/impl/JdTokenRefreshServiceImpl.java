package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.entity.jd.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.service.jd.JdTokenRefreshService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 京东Token刷新服务实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JdTokenRefreshServiceImpl implements JdTokenRefreshService {
    
    private final TrainJdAccessTokenMapper trainJdAccessTokenMapper;
    private final WebClient.Builder webClientBuilder;
    private final JdConfig jdConfig;
    
    @Override
    public int refreshAllTokens() {
        log.info("开始刷新所有京东访问令牌");
        
        // 1. 获取所有访问令牌记录
        List<TrainJdAccessToken> accList = trainJdAccessTokenMapper.findAll();
        
        if (accList == null || accList.isEmpty()) {
            log.info("没有找到需要刷新的京东访问令牌");
            return 0;
        }
        
        log.info("找到 {} 个京东访问令牌记录，开始逐个刷新", accList.size());
        
        int successCount = 0;
        
        // 2. 逐个刷新令牌
        for (TrainJdAccessToken acc : accList) {
            try {
                boolean refreshed = refreshSingleToken(acc);
                if (refreshed) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("刷新令牌失败: id={}, xid={}", acc.getId(), acc.getXid(), e);
            }
        }
        
        log.info("京东访问令牌刷新完成，成功刷新 {}/{} 个令牌", successCount, accList.size());
        return successCount;
    }
    
    /**
     * 刷新单个访问令牌
     * 
     * @param acc 访问令牌记录
     * @return 是否刷新成功
     */
    private boolean refreshSingleToken(TrainJdAccessToken acc) {
        log.debug("开始刷新令牌: id={}, xid={}, teamId={}", acc.getId(), acc.getXid(), acc.getTeamId());
        
        try {
            // 1. 检查配置是否完整
            String refreshTokenUrl = jdConfig.getRefreshTokenUrl();
            if (refreshTokenUrl == null || refreshTokenUrl.trim().isEmpty()) {
                log.error("京东refreshTokenUrl配置为空: id={}, xid={}", acc.getId(), acc.getXid());
                return false;
            }

            // 2. 构建刷新令牌的URL
            String refreshUrl = String.format(
                "%s?app_key=%s&app_secret=%s&grant_type=refresh_token&refresh_token=%s",
                refreshTokenUrl,
                jdConfig.getAppKey(),
                jdConfig.getAppSecret(),
                acc.getRefreshToken()
            );
            
            // 3. 调用京东API
            WebClient webClient = webClientBuilder.build();
            Mono<Map<String, Object>> responseMono = webClient.get()
                    .uri(refreshUrl)
                    .retrieve()
                    .bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {});

            Map<String, Object> tokenResponse = responseMono.block();
            
            if (tokenResponse == null) {
                log.error("京东刷新令牌API返回空响应: id={}, xid={}", acc.getId(), acc.getXid());
                return false;
            }
            
            // 4. 检查响应是否包含错误
            if (tokenResponse.containsKey("error")) {
                log.error("京东刷新令牌API返回错误: id={}, xid={}, error={}", 
                         acc.getId(), acc.getXid(), tokenResponse.get("error"));
                return false;
            }
            
            // 5. 解析响应数据
            String newAccessToken = (String) tokenResponse.get("access_token");
            Integer expiresIn = (Integer) tokenResponse.get("expires_in");
            String newRefreshToken = (String) tokenResponse.get("refresh_token");
            String scope = (String) tokenResponse.get("scope");
            
            if (newAccessToken == null || expiresIn == null) {
                log.error("京东刷新令牌API响应缺少必要字段: id={}, xid={}, response={}", 
                         acc.getId(), acc.getXid(), tokenResponse);
                return false;
            }
            
            // 6. 更新数据库记录
            TrainJdAccessToken updateToken = new TrainJdAccessToken();
            updateToken.setId(acc.getId());
            updateToken.setAccessToken(newAccessToken);
            updateToken.setExpiresTime(LocalDateTime.now().plusSeconds(expiresIn));
            updateToken.setRefreshToken(newRefreshToken != null ? newRefreshToken : acc.getRefreshToken());
            updateToken.setScope(scope != null ? scope : acc.getScope());
            updateToken.setUpdater("system"); // 系统自动更新
            
            int updateResult = trainJdAccessTokenMapper.updateTokenInfo(updateToken);
            
            if (updateResult > 0) {
                log.info("成功刷新京东访问令牌: id={}, xid={}, teamId={}, newExpiresTime={}", 
                        acc.getId(), acc.getXid(), acc.getTeamId(), updateToken.getExpiresTime());
                return true;
            } else {
                log.error("更新数据库失败: id={}, xid={}", acc.getId(), acc.getXid());
                return false;
            }
            
        } catch (Exception e) {
            log.error("刷新单个令牌时发生异常: id={}, xid={}", acc.getId(), acc.getXid(), e);
            return false;
        }
    }
}
