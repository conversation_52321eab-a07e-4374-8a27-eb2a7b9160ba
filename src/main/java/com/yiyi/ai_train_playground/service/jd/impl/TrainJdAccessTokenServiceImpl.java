package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.service.jd.TrainJdAccessTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 京东访问令牌服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainJdAccessTokenServiceImpl implements TrainJdAccessTokenService {
    
    private final TrainJdAccessTokenMapper trainJdAccessTokenMapper;
    
    @Override
    public boolean insert(TrainJdAccessToken token) {
        if (token == null) {
            log.warn("访问令牌信息不能为空");
            return false;
        }
        
        try {
            log.debug("插入京东访问令牌: xid={}", token.getXid());
            int result = trainJdAccessTokenMapper.insert(token);
            boolean success = result > 0;
            log.debug("插入京东访问令牌完成: xid={}, 结果={}", token.getXid(), success);
            return success;
        } catch (Exception e) {
            log.error("插入京东访问令牌失败: xid={}", token.getXid(), e);
            return false;
        }
    }
    
    @Override
    public TrainJdAccessToken findByXid(String xid) {
        if (xid == null || xid.trim().isEmpty()) {
            log.warn("xid不能为空");
            return null;
        }
        
        try {
            log.debug("根据xid查询访问令牌: xid={}", xid);
            TrainJdAccessToken token = trainJdAccessTokenMapper.findByXid(xid);
            log.debug("根据xid查询访问令牌完成: xid={}, 找到={}", xid, token != null);
            return token;
        } catch (Exception e) {
            log.error("根据xid查询访问令牌失败: xid={}", xid, e);
            return null;
        }
    }
    
    @Override
    public TrainJdAccessToken findByUserIdAndTeamId(Long userId, Long teamId) {
        if (userId == null || teamId == null) {
            log.warn("用户ID和团队ID不能为空: userId={}, teamId={}", userId, teamId);
            return null;
        }
        
        try {
            log.debug("根据用户ID和团队ID查询访问令牌: userId={}, teamId={}", userId, teamId);
            TrainJdAccessToken token = trainJdAccessTokenMapper.findByUserIdAndTeamId(userId, teamId);
            log.debug("根据用户ID和团队ID查询访问令牌完成: userId={}, teamId={}, 找到={}", 
                    userId, teamId, token != null);
            return token;
        } catch (Exception e) {
            log.error("根据用户ID和团队ID查询访问令牌失败: userId={}, teamId={}", userId, teamId, e);
            return null;
        }
    }
    
    @Override
    public TrainJdAccessToken findByShopId(Long shopId) {
        if (shopId == null) {
            log.warn("店铺ID不能为空");
            return null;
        }
        
        try {
            log.debug("根据店铺ID查询访问令牌: shopId={}", shopId);
            TrainJdAccessToken token = trainJdAccessTokenMapper.findByShopId(shopId);
            log.debug("根据店铺ID查询访问令牌完成: shopId={}, 找到={}", shopId, token != null);
            return token;
        } catch (Exception e) {
            log.error("根据店铺ID查询访问令牌失败: shopId={}", shopId, e);
            return null;
        }
    }
    
    @Override
    public boolean updateByXid(TrainJdAccessToken token) {
        if (token == null || token.getXid() == null) {
            log.warn("访问令牌信息或xid不能为空");
            return false;
        }
        
        try {
            log.debug("根据xid更新访问令牌: xid={}", token.getXid());
            int result = trainJdAccessTokenMapper.updateByXid(token);
            boolean success = result > 0;
            log.debug("根据xid更新访问令牌完成: xid={}, 结果={}", token.getXid(), success);
            return success;
        } catch (Exception e) {
            log.error("根据xid更新访问令牌失败: xid={}", token.getXid(), e);
            return false;
        }
    }
    
    @Override
    public List<TrainJdAccessToken> findAll() {
        try {
            log.debug("查询所有访问令牌记录");
            List<TrainJdAccessToken> tokens = trainJdAccessTokenMapper.findAll();
            log.debug("查询所有访问令牌记录完成: 数量={}", tokens.size());
            return tokens;
        } catch (Exception e) {
            log.error("查询所有访问令牌记录失败", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean updateTokenInfo(TrainJdAccessToken token) {
        if (token == null || token.getId() == null) {
            log.warn("访问令牌信息或ID不能为空");
            return false;
        }
        
        try {
            log.debug("更新令牌信息: id={}", token.getId());
            int result = trainJdAccessTokenMapper.updateTokenInfo(token);
            boolean success = result > 0;
            log.debug("更新令牌信息完成: id={}, 结果={}", token.getId(), success);
            return success;
        } catch (Exception e) {
            log.error("更新令牌信息失败: id={}", token.getId(), e);
            return false;
        }
    }
    
    @Override
    public boolean existsByXid(String xid) {
        TrainJdAccessToken token = findByXid(xid);
        return token != null;
    }
    
    @Override
    public boolean isTokenExpired(TrainJdAccessToken token) {
        if (token == null || token.getExpiresTime() == null) {
            log.warn("访问令牌信息或过期时间不能为空");
            return true; // 如果信息不完整，认为已过期
        }
        
        LocalDateTime now = LocalDateTime.now();
        boolean expired = now.isAfter(token.getExpiresTime());
        log.debug("检查令牌是否过期: xid={}, 过期时间={}, 当前时间={}, 已过期={}", 
                token.getXid(), token.getExpiresTime(), now, expired);
        return expired;
    }
    
    @Override
    public boolean saveOrUpdate(TrainJdAccessToken token) {
        if (token == null || token.getXid() == null) {
            log.warn("访问令牌信息或xid不能为空");
            return false;
        }
        
        try {
            // 检查是否已存在
            TrainJdAccessToken existingToken = findByXid(token.getXid());
            if (existingToken != null) {
                // 更新现有记录
                log.debug("更新现有访问令牌: xid={}", token.getXid());
                return updateByXid(token);
            } else {
                // 插入新记录
                log.debug("插入新访问令牌: xid={}", token.getXid());
                return insert(token);
            }
        } catch (Exception e) {
            log.error("保存或更新访问令牌失败: xid={}", token.getXid(), e);
            return false;
        }
    }
}
