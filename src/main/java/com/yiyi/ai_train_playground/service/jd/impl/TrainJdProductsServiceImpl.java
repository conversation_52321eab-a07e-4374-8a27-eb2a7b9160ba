package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.dto.jd.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListResponse;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.enums.JdProductStatus;
import com.yiyi.ai_train_playground.enums.JdSyncStatus;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdProductsMapper;
import com.yiyi.ai_train_playground.mapper.TrainTeamShopsMapper;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import com.yiyi.ai_train_playground.vo.JdProductListVO;
import com.yiyi.ai_train_playground.vo.JdProductImageDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 京东商品服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainJdProductsServiceImpl implements TrainJdProductsService {

    private final TrainJdProductsMapper trainJdProductsMapper;
    private final TrainTeamShopsMapper trainTeamShopsMapper;
    
    @Override
    public TrainJdProducts findByWareIdAndTeamId(Long wareId, Long teamId) {
        if (wareId == null || teamId == null) {
            log.warn("查询参数不能为空: wareId={}, teamId={}", wareId, teamId);
            return null;
        }
        
        try {
            log.debug("查询京东商品: wareId={}, teamId={}", wareId, teamId);
            return trainJdProductsMapper.findByWareIdAndTeamId(wareId, teamId);
        } catch (Exception e) {
            log.error("查询京东商品失败: wareId={}, teamId={}", wareId, teamId, e);
            throw new RuntimeException("查询京东商品失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean insert(TrainJdProducts product) {
        if (product == null) {
            log.warn("插入商品信息不能为空");
            return false;
        }
        
        // 参数验证
        if (product.getWareId() == null || product.getTeamId() == null) {
            log.warn("商品wareId和teamId不能为空: wareId={}, teamId={}", 
                    product.getWareId(), product.getTeamId());
            return false;
        }
        
        try {
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            if (product.getCreateTime() == null) {
                product.setCreateTime(now);
            }
            if (product.getUpdateTime() == null) {
                product.setUpdateTime(now);
            }
            if (product.getVersion() == null) {
                product.setVersion(0L);
            }
            
            log.info("插入京东商品: wareId={}, teamId={}, title={}", 
                    product.getWareId(), product.getTeamId(), product.getTitle());
            
            int result = trainJdProductsMapper.insert(product);
            boolean success = result > 0;
            
            if (success) {
                log.info("插入京东商品成功: id={}, wareId={}", product.getId(), product.getWareId());
            } else {
                log.warn("插入京东商品失败: wareId={}", product.getWareId());
            }
            
            return success;
        } catch (Exception e) {
            log.error("插入京东商品失败: wareId={}, teamId={}", 
                    product.getWareId(), product.getTeamId(), e);
            throw new RuntimeException("插入京东商品失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean updateByWareIdAndTeamId(TrainJdProducts product) {
        if (product == null) {
            log.warn("更新商品信息不能为空");
            return false;
        }
        
        // 参数验证
        if (product.getWareId() == null || product.getTeamId() == null) {
            log.warn("商品wareId和teamId不能为空: wareId={}, teamId={}", 
                    product.getWareId(), product.getTeamId());
            return false;
        }
        
        try {
            // 设置更新时间
            product.setUpdateTime(LocalDateTime.now());
            
            log.info("更新京东商品: wareId={}, teamId={}, title={}", 
                    product.getWareId(), product.getTeamId(), product.getTitle());
            
            int result = trainJdProductsMapper.updateByWareIdAndTeamId(product);
            boolean success = result > 0;
            
            if (success) {
                log.info("更新京东商品成功: wareId={}", product.getWareId());
            } else {
                log.warn("更新京东商品失败，可能商品不存在: wareId={}, teamId={}", 
                        product.getWareId(), product.getTeamId());
            }
            
            return success;
        } catch (Exception e) {
            log.error("更新京东商品失败: wareId={}, teamId={}", 
                    product.getWareId(), product.getTeamId(), e);
            throw new RuntimeException("更新京东商品失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int batchInsert(List<TrainJdProducts> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            log.warn("批量插入商品列表为空");
            return 0;
        }
        
        try {
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            for (TrainJdProducts product : productList) {
                if (product.getCreateTime() == null) {
                    product.setCreateTime(now);
                }
                if (product.getUpdateTime() == null) {
                    product.setUpdateTime(now);
                }
                if (product.getVersion() == null) {
                    product.setVersion(0L);
                }
            }
            
            log.info("批量插入京东商品: 数量={}", productList.size());
            
            int result = trainJdProductsMapper.batchInsert(productList);
            
            log.info("批量插入京东商品完成: 插入数量={}", result);
            
            return result;
        } catch (Exception e) {
            log.error("批量插入京东商品失败: 数量={}", productList.size(), e);
            throw new RuntimeException("批量插入京东商品失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean saveOrUpdate(TrainJdProducts product) {
        if (product == null) {
            log.warn("保存或更新商品信息不能为空");
            return false;
        }
        
        // 参数验证
        if (product.getWareId() == null || product.getTeamId() == null) {
            log.warn("商品wareId和teamId不能为空: wareId={}, teamId={}", 
                    product.getWareId(), product.getTeamId());
            return false;
        }
        
        try {
            // 先查询是否存在
            TrainJdProducts existingProduct = findByWareIdAndTeamId(product.getWareId(), product.getTeamId());
            
            if (existingProduct != null) {
                // 存在则更新
                log.debug("商品已存在，执行更新: wareId={}, teamId={}", 
                        product.getWareId(), product.getTeamId());
                return updateByWareIdAndTeamId(product);
            } else {
                // 不存在则插入
                log.debug("商品不存在，执行插入: wareId={}, teamId={}", 
                        product.getWareId(), product.getTeamId());
                return insert(product);
            }
        } catch (Exception e) {
            log.error("保存或更新京东商品失败: wareId={}, teamId={}", 
                    product.getWareId(), product.getTeamId(), e);
            throw new RuntimeException("保存或更新京东商品失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainJdProducts> findByTeamId(Long teamId) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }
        
        try {
            log.debug("查询团队商品列表: teamId={}", teamId);
            List<TrainJdProducts> products = trainJdProductsMapper.findByTeamId(teamId);
            log.debug("查询团队商品列表完成: teamId={}, 数量={}", teamId, products.size());
            return products;
        } catch (Exception e) {
            log.error("查询团队商品列表失败: teamId={}", teamId, e);
            throw new RuntimeException("查询团队商品列表失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean deleteByWareIdAndTeamId(Long wareId, Long teamId) {
        if (wareId == null || teamId == null) {
            log.warn("删除参数不能为空: wareId={}, teamId={}", wareId, teamId);
            return false;
        }
        
        try {
            log.info("删除京东商品: wareId={}, teamId={}", wareId, teamId);
            
            int result = trainJdProductsMapper.deleteByWareIdAndTeamId(wareId, teamId);
            boolean success = result > 0;
            
            if (success) {
                log.info("删除京东商品成功: wareId={}", wareId);
            } else {
                log.warn("删除京东商品失败，可能商品不存在: wareId={}, teamId={}", wareId, teamId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("删除京东商品失败: wareId={}, teamId={}", wareId, teamId, e);
            throw new RuntimeException("删除京东商品失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean existsByWareIdAndTeamId(Long wareId, Long teamId) {
        if (wareId == null || teamId == null) {
            log.warn("检查参数不能为空: wareId={}, teamId={}", wareId, teamId);
            return false;
        }
        
        try {
            TrainJdProducts product = findByWareIdAndTeamId(wareId, teamId);
            return product != null;
        } catch (Exception e) {
            log.error("检查京东商品是否存在失败: wareId={}, teamId={}", wareId, teamId, e);
            return false;
        }
    }
    
    @Override
    public long countByTeamId(Long teamId) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return 0;
        }
        
        try {
            log.debug("统计团队商品数量: teamId={}", teamId);
            long count = trainJdProductsMapper.countByTeamId(teamId);
            log.debug("统计团队商品数量完成: teamId={}, 数量={}", teamId, count);
            return count;
        } catch (Exception e) {
            log.error("统计团队商品数量失败: teamId={}", teamId, e);
            throw new RuntimeException("统计团队商品数量失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainJdProducts> findByWareIds(List<Long> wareIds, Long teamId) {
        if (CollectionUtils.isEmpty(wareIds) || teamId == null) {
            log.warn("查询参数不能为空: wareIds={}, teamId={}", wareIds, teamId);
            return Collections.emptyList();
        }
        
        try {
            log.debug("批量查询京东商品: wareIds数量={}, teamId={}", wareIds.size(), teamId);
            List<TrainJdProducts> products = trainJdProductsMapper.findByWareIds(wareIds, teamId);
            log.debug("批量查询京东商品完成: 查询数量={}, 返回数量={}", wareIds.size(), products.size());
            return products;
        } catch (Exception e) {
            log.error("批量查询京东商品失败: wareIds数量={}, teamId={}", wareIds.size(), teamId, e);
            throw new RuntimeException("批量查询京东商品失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TrainJdProducts> findByTeamIdWithPage(Long teamId, int offset, int limit) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }

        if (offset < 0 || limit <= 0) {
            log.warn("分页参数无效: offset={}, limit={}", offset, limit);
            return Collections.emptyList();
        }

        try {
            log.debug("分页查询团队商品: teamId={}, offset={}, limit={}", teamId, offset, limit);
            List<TrainJdProducts> products = trainJdProductsMapper.findByTeamIdWithPage(teamId, offset, limit);
            log.debug("分页查询团队商品完成: teamId={}, 返回数量={}", teamId, products.size());
            return products;
        } catch (Exception e) {
            log.error("分页查询团队商品失败: teamId={}, offset={}, limit={}", teamId, offset, limit, e);
            throw new RuntimeException("分页查询团队商品失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TrainJdProducts> findByTeamIdAndTitleLike(Long teamId, String title) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }

        try {
            log.debug("根据标题模糊查询商品: teamId={}, title={}", teamId, title);
            List<TrainJdProducts> products = trainJdProductsMapper.findByTeamIdAndTitleLike(teamId, title);
            log.debug("根据标题模糊查询商品完成: teamId={}, 返回数量={}", teamId, products.size());
            return products;
        } catch (Exception e) {
            log.error("根据标题模糊查询商品失败: teamId={}, title={}", teamId, title, e);
            throw new RuntimeException("根据标题模糊查询商品失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TrainJdProducts> findByTeamIdAndBrandName(Long teamId, String brandName) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }

        try {
            log.debug("根据品牌查询商品: teamId={}, brandName={}", teamId, brandName);
            List<TrainJdProducts> products = trainJdProductsMapper.findByTeamIdAndBrandName(teamId, brandName);
            log.debug("根据品牌查询商品完成: teamId={}, 返回数量={}", teamId, products.size());
            return products;
        } catch (Exception e) {
            log.error("根据品牌查询商品失败: teamId={}, brandName={}", teamId, brandName, e);
            throw new RuntimeException("根据品牌查询商品失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TrainJdProducts> findByTeamIdAndPriceRange(Long teamId, Double minPrice, Double maxPrice) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }

        if (minPrice != null && maxPrice != null && minPrice > maxPrice) {
            log.warn("价格区间无效: minPrice={}, maxPrice={}", minPrice, maxPrice);
            return Collections.emptyList();
        }

        try {
            log.debug("根据价格区间查询商品: teamId={}, minPrice={}, maxPrice={}", teamId, minPrice, maxPrice);
            List<TrainJdProducts> products = trainJdProductsMapper.findByTeamIdAndPriceRange(teamId, minPrice, maxPrice);
            log.debug("根据价格区间查询商品完成: teamId={}, 返回数量={}", teamId, products.size());
            return products;
        } catch (Exception e) {
            log.error("根据价格区间查询商品失败: teamId={}, minPrice={}, maxPrice={}", teamId, minPrice, maxPrice, e);
            throw new RuntimeException("根据价格区间查询商品失败: " + e.getMessage(), e);
        }
    }

    @Override
    public JdPrdListResponse findJdProductList(Long teamId, JdPrdListRequest request) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return createEmptyResponse();
        }

        if (request == null) {
            log.warn("查询请求参数不能为空");
            return createEmptyResponse();
        }

        // 参数校验和默认值设置
        Integer page = request.getPage() != null && request.getPage() > 0 ? request.getPage() : 1;
        Integer pageSize = request.getPageSize() != null && request.getPageSize() > 0 ? request.getPageSize() : 10;
        String searchKeyword = request.getSearchKeyword();

        // 计算偏移量
        Integer offset = (page - 1) * pageSize;

        try {
            log.debug("分页查询京东商品列表: teamId={}, searchKeyword={}, page={}, pageSize={}",
                    teamId, searchKeyword, page, pageSize);

            // 查询总数
            Long total = trainJdProductsMapper.countJdProductList(teamId, searchKeyword);

            // 查询列表数据
            List<JdProductListVO> voList = trainJdProductsMapper.findJdProductListWithPagination(
                    teamId, searchKeyword, offset, pageSize);

            // 转换为响应DTO
            JdPrdListResponse response = new JdPrdListResponse();
            response.setTotal(total);
            response.setRows(convertToResponseItems(voList));

            // 设置shopId和店铺状态信息 - 从第一个商品中获取，如果列表为空则设置为null
            if (!CollectionUtils.isEmpty(voList)) {
                Long shopId = voList.get(0).getShopId();
                response.setShopId(shopId);

                // 根据shopId查询店铺授权状态和同步状态
                setShopStatusInfo(response, shopId);
            }

            log.debug("分页查询京东商品列表完成: teamId={}, 总数={}, 返回数量={}",
                    teamId, total, voList.size());

            return response;
        } catch (Exception e) {
            log.error("分页查询京东商品列表失败: teamId={}, searchKeyword={}, page={}, pageSize={}",
                    teamId, searchKeyword, page, pageSize, e);
            throw new RuntimeException("分页查询京东商品列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建空的响应结果
     */
    private JdPrdListResponse createEmptyResponse() {
        JdPrdListResponse response = new JdPrdListResponse();
        response.setTotal(0L);
        response.setRows(Collections.emptyList());
        return response;
    }

    /**
     * 转换VO列表为响应DTO列表
     */
    private List<JdPrdListResponse.JdProductItem> convertToResponseItems(List<JdProductListVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        return voList.stream().map(this::convertToResponseItem).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 转换单个VO为响应DTO
     */
    private JdPrdListResponse.JdProductItem convertToResponseItem(JdProductListVO vo) {
        JdPrdListResponse.JdProductItem item = new JdPrdListResponse.JdProductItem();
        item.setId(vo.getId());
        item.setBrandId(vo.getBrandId());
        item.setWareId(vo.getWareId());
        item.setBrandName(vo.getBrandName());

        // 处理logo URL - 添加前缀
        String logoUrl = buildLogoUrl(vo.getLogo());
        item.setLogo(logoUrl);

        item.setTitle(vo.getTitle());

        // 转换状态码为描述文本
        String statusDesc = JdProductStatus.getDescriptionByCode(vo.getStatus());
        item.setStatus(statusDesc);

        item.setOnlineTime(vo.getOnlineTime());
        item.setOffLineTime(vo.getOfflineTime());

        return item;
    }

    /**
     * 构建完整的logo URL
     */
    private String buildLogoUrl(String logo) {
        if (logo == null || logo.trim().isEmpty()) {
            return "";
        }

        // 如果已经是完整URL，直接返回
        if (logo.startsWith("http://") || logo.startsWith("https://")) {
            return logo;
        }

        // 添加前缀
        return "https://img11.360buyimg.com/devfe/" + logo;
    }

    @Override
    public JdPrdDtlResponse findJdProductDetail(Long teamId, Long wareId) {
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return null;
        }

        if (wareId == null) {
            log.warn("商品ID不能为空");
            return null;
        }

        try {
            log.debug("查询京东商品详情: teamId={}, wareId={}", teamId, wareId);

            // 通过 join 查询获取商品图片详情列表
            List<JdProductImageDetailVO> trJdProdImgList = trainJdProductsMapper.findProductImageDetailsByTeamIdAndWareId(teamId, wareId);

            if (trJdProdImgList == null || trJdProdImgList.isEmpty()) {
                log.warn("未找到京东商品图片信息: teamId={}, wareId={}", teamId, wareId);
                return null;
            }

            // 获取第一项作为主要信息来源
            JdProductImageDetailVO firstTrJdProdImg = trJdProdImgList.get(0);

            // 遍历 trJdProdImgList，获取每一项的 img_url，然后拼接起来，每一项中间用逗号分隔
            StringBuilder imgUrlBuilder = new StringBuilder();
            for (int i = 0; i < trJdProdImgList.size(); i++) {
                JdProductImageDetailVO trJdProdImg = trJdProdImgList.get(i);
                if (trJdProdImg.getImgUrl() != null) {
                    imgUrlBuilder.append(trJdProdImg.getImgUrl());
                    if (i < trJdProdImgList.size() - 1) {
                        imgUrlBuilder.append(",");
                    }
                }
            }

            // 转换为响应DTO
            JdPrdDtlResponse response = new JdPrdDtlResponse();
            response.setId(firstTrJdProdImg.getId());
            response.setWareId(firstTrJdProdImg.getWareId());
            response.setJdProdDtl(firstTrJdProdImg.getJdProdDtl());
            response.setJdProdImgList(imgUrlBuilder.toString());

            log.debug("查询京东商品详情成功: teamId={}, wareId={}, 图片数量={}", teamId, wareId, trJdProdImgList.size());

            return response;

        } catch (Exception e) {
            log.error("查询京东商品详情失败: teamId={}, wareId={}", teamId, wareId, e);
            throw new RuntimeException("查询京东商品详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置店铺状态信息（授权状态和同步状态）
     *
     * @param response 响应对象
     * @param shopId 店铺ID
     */
    private void setShopStatusInfo(JdPrdListResponse response, Long shopId) {
        try {
            if (shopId == null) {
                log.debug("shopId为空，设置默认状态");
                response.setIsAuthorize(false);
                response.setIsSyncComplete(null);
                response.setIsSyncCompleteDesc("未知状态");
                return;
            }

            // 根据shopId查询店铺信息
            TrainTeamShops teamShop = trainTeamShopsMapper.findByShopId(shopId);

            if (teamShop == null) {
                log.debug("未找到shopId={}的店铺信息，设置默认状态", shopId);
                response.setIsAuthorize(false);
                response.setIsSyncComplete(null);
                response.setIsSyncCompleteDesc("未知状态");
                return;
            }

            // 设置授权状态：数据库中1表示true，其他表示false
            response.setIsAuthorize(teamShop.getIsAuthorize() != null && teamShop.getIsAuthorize());

            // 设置同步状态：数字状态码和描述信息
            Integer syncStatusCode = teamShop.getIsSyncComplete();
            String syncStatusDesc = JdSyncStatus.getDescriptionByCode(syncStatusCode);
            response.setIsSyncComplete(syncStatusCode);
            response.setIsSyncCompleteDesc(syncStatusDesc);

            log.debug("设置店铺状态信息成功: shopId={}, isAuthorize={}, isSyncComplete={}, isSyncCompleteDesc={}",
                     shopId, response.getIsAuthorize(), response.getIsSyncComplete(), response.getIsSyncCompleteDesc());

        } catch (Exception e) {
            log.error("设置店铺状态信息失败: shopId={}", shopId, e);
            // 设置默认值，避免返回null
            response.setIsAuthorize(false);
            response.setIsSyncComplete(null);
            response.setIsSyncCompleteDesc("未知状态");
        }
    }

    @Override
    public List<TrainJdProducts> findByTeamIdAndSyncStatusWithPagination(Long teamId, Integer syncStatus, Long jdProdId, Integer offset, Integer pageSize) {
        // 参数验证
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }

        if (offset == null || offset < 0) {
            log.warn("偏移量参数无效: offset={}", offset);
            return Collections.emptyList();
        }

        if (pageSize == null || pageSize <= 0) {
            log.warn("分页大小参数无效: pageSize={}", pageSize);
            return Collections.emptyList();
        }

        try {
            log.debug("根据team_id、sync_status分页查询商品列表: teamId={}, syncStatus={}, jdProdId={}, offset={}, pageSize={}",
                    teamId, syncStatus, jdProdId, offset, pageSize);

            List<TrainJdProducts> products = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                    teamId, syncStatus, jdProdId, offset, pageSize);

            log.debug("根据team_id、sync_status分页查询完成: teamId={}, 返回数量={}", teamId, products.size());
            return products;

        } catch (Exception e) {
            log.error("根据team_id、sync_status分页查询失败: teamId={}, syncStatus={}, jdProdId={}, offset={}, pageSize={}",
                    teamId, syncStatus, jdProdId, offset, pageSize, e);
            throw new RuntimeException("根据team_id、sync_status分页查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean updateByIdSelective(TrainJdProducts product) {
        // 参数验证
        if (product == null) {
            log.warn("更新商品信息不能为空");
            return false;
        }

        if (product.getId() == null) {
            log.warn("商品ID不能为空");
            return false;
        }

        try {
            // 设置更新时间
            if (product.getUpdateTime() == null) {
                product.setUpdateTime(LocalDateTime.now());
            }

            log.info("根据ID动态更新商品信息: id={}, title={}",
                    product.getId(), product.getTitle());

            int result = trainJdProductsMapper.updateByIdSelective(product);
            boolean success = result > 0;

            if (success) {
                log.info("根据ID动态更新商品成功: id={}", product.getId());
            } else {
                log.warn("根据ID动态更新商品失败，可能商品不存在: id={}", product.getId());
            }

            return success;

        } catch (Exception e) {
            log.error("根据ID动态更新商品失败: id={}", product.getId(), e);
            throw new RuntimeException("根据ID动态更新商品失败: " + e.getMessage(), e);
        }
    }
}
