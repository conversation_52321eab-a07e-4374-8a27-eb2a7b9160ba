package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts;
import com.yiyi.ai_train_playground.mapper.jd.TrainScriptJdProductsMapper;
import com.yiyi.ai_train_playground.service.jd.TrainScriptJdProductsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 剧本京东商品关联服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-23
 */
@Slf4j
@Service
public class TrainScriptJdProductsServiceImpl implements TrainScriptJdProductsService {
    
    @Autowired
    private TrainScriptJdProductsMapper trainScriptJdProductsMapper;
    
    @Override
    public boolean insert(TrainScriptJdProducts scriptJdProducts) {
        if (scriptJdProducts == null) {
            log.warn("剧本京东商品关联信息不能为空");
            return false;
        }
        
        if (scriptJdProducts.getTeamId() == null || scriptJdProducts.getScriptId() == null) {
            log.warn("团队ID和剧本ID不能为空: teamId={}, scriptId={}", 
                    scriptJdProducts.getTeamId(), scriptJdProducts.getScriptId());
            return false;
        }
        
        try {
            log.debug("插入剧本京东商品关联: teamId={}, scriptId={}, trJdSkuId={}", 
                    scriptJdProducts.getTeamId(), scriptJdProducts.getScriptId(), scriptJdProducts.getTrJdSkuId());
            
            int result = trainScriptJdProductsMapper.insert(scriptJdProducts);
            boolean success = result > 0;
            
            if (success) {
                log.debug("插入剧本京东商品关联成功: id={}", scriptJdProducts.getId());
            } else {
                log.warn("插入剧本京东商品关联失败");
            }
            
            return success;
        } catch (Exception e) {
            log.error("插入剧本京东商品关联失败: teamId={}, scriptId={}, trJdSkuId={}", 
                    scriptJdProducts.getTeamId(), scriptJdProducts.getScriptId(), scriptJdProducts.getTrJdSkuId(), e);
            throw new RuntimeException("插入剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public int batchInsert(List<TrainScriptJdProducts> scriptJdProductsList) {
        if (CollectionUtils.isEmpty(scriptJdProductsList)) {
            log.warn("批量插入列表不能为空");
            return 0;
        }
        
        try {
            log.debug("批量插入剧本京东商品关联: 数量={}", scriptJdProductsList.size());
            
            int result = trainScriptJdProductsMapper.batchInsert(scriptJdProductsList);
            
            log.debug("批量插入剧本京东商品关联完成: 插入数量={}", result);
            return result;
        } catch (Exception e) {
            log.error("批量插入剧本京东商品关联失败: 数量={}", scriptJdProductsList.size(), e);
            throw new RuntimeException("批量插入剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean deleteByScriptIdAndTeamId(Long scriptId, Long teamId) {
        if (scriptId == null || teamId == null) {
            log.warn("剧本ID和团队ID不能为空: scriptId={}, teamId={}", scriptId, teamId);
            return false;
        }
        
        try {
            log.debug("删除剧本京东商品关联: scriptId={}, teamId={}", scriptId, teamId);
            
            int result = trainScriptJdProductsMapper.deleteByScriptIdAndTeamId(scriptId, teamId);
            boolean success = result >= 0; // 允许删除0条记录
            
            log.debug("删除剧本京东商品关联完成: scriptId={}, teamId={}, 删除数量={}", scriptId, teamId, result);
            return success;
        } catch (Exception e) {
            log.error("删除剧本京东商品关联失败: scriptId={}, teamId={}", scriptId, teamId, e);
            throw new RuntimeException("删除剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainScriptJdProducts> selectByScriptIdAndTeamId(Long scriptId, Long teamId) {
        if (scriptId == null || teamId == null) {
            log.warn("剧本ID和团队ID不能为空: scriptId={}, teamId={}", scriptId, teamId);
            return Collections.emptyList();
        }
        
        try {
            log.debug("查询剧本京东商品关联: scriptId={}, teamId={}", scriptId, teamId);
            
            List<TrainScriptJdProducts> result = trainScriptJdProductsMapper.selectByScriptIdAndTeamId(scriptId, teamId);
            
            log.debug("查询剧本京东商品关联完成: scriptId={}, teamId={}, 数量={}", scriptId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询剧本京东商品关联失败: scriptId={}, teamId={}", scriptId, teamId, e);
            throw new RuntimeException("查询剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<Long> selectJdSkuIdsByScriptIdAndTeamId(Long scriptId, Long teamId) {
        if (scriptId == null || teamId == null) {
            log.warn("剧本ID和团队ID不能为空: scriptId={}, teamId={}", scriptId, teamId);
            return Collections.emptyList();
        }
        
        try {
            log.debug("查询剧本关联的京东SKU ID列表: scriptId={}, teamId={}", scriptId, teamId);
            
            List<Long> result = trainScriptJdProductsMapper.selectJdSkuIdsByScriptIdAndTeamId(scriptId, teamId);
            
            log.debug("查询剧本关联的京东SKU ID列表完成: scriptId={}, teamId={}, 数量={}", scriptId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询剧本关联的京东SKU ID列表失败: scriptId={}, teamId={}", scriptId, teamId, e);
            throw new RuntimeException("查询剧本关联的京东SKU ID列表失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public TrainScriptJdProducts selectById(Long id) {
        if (id == null) {
            log.warn("ID不能为空");
            return null;
        }
        
        try {
            log.debug("根据ID查询剧本京东商品关联: id={}", id);
            
            TrainScriptJdProducts result = trainScriptJdProductsMapper.selectById(id);
            
            if (result != null) {
                log.debug("根据ID查询剧本京东商品关联成功: id={}", id);
            } else {
                log.debug("根据ID查询剧本京东商品关联不存在: id={}", id);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据ID查询剧本京东商品关联失败: id={}", id, e);
            throw new RuntimeException("根据ID查询剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean updateById(TrainScriptJdProducts scriptJdProducts) {
        if (scriptJdProducts == null || scriptJdProducts.getId() == null) {
            log.warn("剧本京东商品关联信息或ID不能为空");
            return false;
        }
        
        try {
            log.debug("根据ID更新剧本京东商品关联: id={}", scriptJdProducts.getId());
            
            int result = trainScriptJdProductsMapper.updateById(scriptJdProducts);
            boolean success = result > 0;
            
            if (success) {
                log.debug("根据ID更新剧本京东商品关联成功: id={}", scriptJdProducts.getId());
            } else {
                log.warn("根据ID更新剧本京东商品关联失败: id={}", scriptJdProducts.getId());
            }
            
            return success;
        } catch (Exception e) {
            log.error("根据ID更新剧本京东商品关联失败: id={}", scriptJdProducts.getId(), e);
            throw new RuntimeException("根据ID更新剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean deleteById(Long id) {
        if (id == null) {
            log.warn("ID不能为空");
            return false;
        }
        
        try {
            log.debug("根据ID删除剧本京东商品关联: id={}", id);
            
            int result = trainScriptJdProductsMapper.deleteById(id);
            boolean success = result > 0;
            
            if (success) {
                log.debug("根据ID删除剧本京东商品关联成功: id={}", id);
            } else {
                log.warn("根据ID删除剧本京东商品关联失败: id={}", id);
            }
            
            return success;
        } catch (Exception e) {
            log.error("根据ID删除剧本京东商品关联失败: id={}", id, e);
            throw new RuntimeException("根据ID删除剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainScriptJdProducts> selectByJdSkuIdAndTeamId(Long trJdSkuId, Long teamId) {
        if (trJdSkuId == null || teamId == null) {
            log.warn("京东SKU ID和团队ID不能为空: trJdSkuId={}, teamId={}", trJdSkuId, teamId);
            return Collections.emptyList();
        }
        
        try {
            log.debug("根据京东SKU ID查询关联的剧本列表: trJdSkuId={}, teamId={}", trJdSkuId, teamId);
            
            List<TrainScriptJdProducts> result = trainScriptJdProductsMapper.selectByJdSkuIdAndTeamId(trJdSkuId, teamId);
            
            log.debug("根据京东SKU ID查询关联的剧本列表完成: trJdSkuId={}, teamId={}, 数量={}", trJdSkuId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据京东SKU ID查询关联的剧本列表失败: trJdSkuId={}, teamId={}", trJdSkuId, teamId, e);
            throw new RuntimeException("根据京东SKU ID查询关联的剧本列表失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean batchDeleteByIds(List<Long> ids, Long teamId) {
        if (CollectionUtils.isEmpty(ids) || teamId == null) {
            log.warn("ID列表和团队ID不能为空: ids={}, teamId={}", ids, teamId);
            return false;
        }
        
        try {
            log.debug("批量删除剧本京东商品关联: ids数量={}, teamId={}", ids.size(), teamId);
            
            int result = trainScriptJdProductsMapper.batchDeleteByIds(ids, teamId);
            boolean success = result >= 0; // 允许删除0条记录
            
            log.debug("批量删除剧本京东商品关联完成: ids数量={}, teamId={}, 删除数量={}", ids.size(), teamId, result);
            return success;
        } catch (Exception e) {
            log.error("批量删除剧本京东商品关联失败: ids数量={}, teamId={}", ids.size(), teamId, e);
            throw new RuntimeException("批量删除剧本京东商品关联失败: " + e.getMessage(), e);
        }
    }
}
