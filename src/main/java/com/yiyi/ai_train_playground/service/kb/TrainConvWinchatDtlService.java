package com.yiyi.ai_train_playground.service.kb;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatDtl;

import java.util.List;

/**
 * 模拟聊天室窗口明细表 Service接口
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface TrainConvWinchatDtlService {

    /**
     * 创建聊天窗口明细记录
     *
     * @param convWinchatMainId 聊天窗口主表ID
     * @param taskId 任务ID
     * @param sessionId 会话ID
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 创建的记录
     */
    TrainConvWinchatDtl createWinchatDtl(Long convWinchatMainId, Long taskId, String sessionId, Long teamId, String creator);

    /**
     * 创建聊天窗口明细记录（包含conv_kb_id字段）
     *
     * @param convWinchatMainId 聊天窗口主表ID
     * @param taskId 任务ID
     * @param sessionId 会话ID
     * @param convKbId train_task_conv_kb_dtl表的主键ID
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 创建的记录
     */
    TrainConvWinchatDtl createWinchatDtlWithConvKbId(Long convWinchatMainId, Long taskId, String sessionId, Long convKbId, Long teamId, String creator);

    /**
     * 创建聊天窗口明细记录（包含新增字段）
     *
     * @param convWinchatMainId  聊天窗口主表ID
     * @param taskId             任务ID
     * @param sessionId          会话ID
     * @param f1stRawChatlog     最原始的聊天记录
     * @param s2ndAggSysPrompt   聚合过后的系统提示词
     * @param t3rdRewrite        重写过后的聊天记录
     * @param f4thFinalSysPrompt 最终的系统提示词
     * @param teamId             团队ID
     * @param creator            创建人
     * @param convKbId
     * @return 创建的记录
     */
    TrainConvWinchatDtl createWinchatDtlWithFields(Long convWinchatMainId, Long taskId, String sessionId,
                                                   String f1stRawChatlog, String s2ndAggSysPrompt,
                                                   String t3rdRewrite, String f4thFinalSysPrompt,
                                                   Long teamId, String creator, Long convKbId);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    TrainConvWinchatDtl getById(Long id);

    /**
     * 根据sessionId查询记录
     *
     * @param sessionId 会话ID
     * @return 记录
     */
    TrainConvWinchatDtl getBySessionId(String sessionId);

    /**
     * 更新记录
     *
     * @param winchatDtl 记录
     * @return 是否成功
     */
    boolean updateWinchatDtl(TrainConvWinchatDtl winchatDtl);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据聊天窗口主表ID查询记录列表
     *
     * @param convWinchatMainId 聊天窗口主表ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatDtl> getByConvWinchatMainId(Long convWinchatMainId, Long teamId);

    /**
     * 根据任务ID查询记录列表
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatDtl> getByTaskId(Long taskId, Long teamId);

    /**
     * 根据条件查询记录列表
     *
     * @param convWinchatMainId 聊天窗口主表ID（可选）
     * @param taskId 任务ID（可选）
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatDtl> getByConditions(Long convWinchatMainId, Long taskId, Long teamId);

    /**
     * 更新原始聊天记录
     *
     * @param id 主键ID
     * @param rawChatlog 原始聊天记录
     * @param teamId 团队ID
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateRawChatlog(Long id, String rawChatlog, Long teamId, String updater);

    /**
     * 更新系统提示词
     *
     * @param id 主键ID
     * @param sysPrompt 系统提示词
     * @param teamId 团队ID
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateSysPrompt(Long id, String sysPrompt, Long teamId, String updater);

    /**
     * 更新重写后的聊天记录
     *
     * @param id 主键ID
     * @param rewrite 重写后的聊天记录
     * @param teamId 团队ID
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateRewrite(Long id, String rewrite, Long teamId, String updater);

    /**
     * 更新最终系统提示词
     *
     * @param id 主键ID
     * @param finalSysPrompt 最终系统提示词
     * @param teamId 团队ID
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateFinalSysPrompt(Long id, String finalSysPrompt, Long teamId, String updater);
}