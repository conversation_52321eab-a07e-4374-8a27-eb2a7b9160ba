package com.yiyi.ai_train_playground.service.kb;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模拟聊天室窗口聊天记录表 Service接口
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface TrainConvWinchatLogService {

    /**
     * 创建聊天记录
     *
     * @param convDtlId 明细ID
     * @param sessionId 会话ID
     * @param sender 发送者
     * @param content 内容
     * @param sendTime 发送时间
     * @param senderType 发送者类型
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 创建的记录
     */
    TrainConvWinchatLog createChatLog(Long convDtlId, String sessionId, String sender, String content, 
                                      LocalDateTime sendTime, String senderType, Long teamId, String creator);

    /**
     * 创建带商品参考答案和意图识别的聊天记录
     *
     * @param convDtlId 明细ID
     * @param sessionId 会话ID
     * @param sender 发送者
     * @param content 内容
     * @param sendTime 发送时间
     * @param senderType 发送者类型
     * @param prodReferAnswer 商品参考答案
     * @param score 意图识别评分
     * @param intentResult 意图识别结果
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 创建的记录
     */
    TrainConvWinchatLog createChatLogWithProdAnswer(Long convDtlId, String sessionId, String sender, String content, 
                                                    LocalDateTime sendTime, String senderType, String prodReferAnswer, 
                                                    Integer score, String intentResult, Long teamId, String creator);

    /**
     * 批量创建聊天记录
     *
     * @param chatLogs 聊天记录列表
     * @return 是否成功
     */
    boolean batchCreateChatLog(List<TrainConvWinchatLog> chatLogs);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    TrainConvWinchatLog getById(Long id);

    /**
     * 更新记录
     *
     * @param chatLog 记录
     * @return 是否成功
     */
    boolean updateChatLog(TrainConvWinchatLog chatLog);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据明细ID查询聊天记录列表
     *
     * @param convDtlId 明细ID
     * @param teamId 团队ID
     * @return 聊天记录列表
     */
    List<TrainConvWinchatLog> getByConvDtlId(Long convDtlId, Long teamId);

    /**
     * 根据sessionId查询聊天记录列表
     *
     * @param sessionId 会话ID
     * @param teamId 团队ID
     * @return 聊天记录列表
     */
    List<TrainConvWinchatLog> getBySessionId(String sessionId, Long teamId);

    /**
     * 根据条件查询记录列表
     *
     * @param convDtlId 明细ID（可选）
     * @param sessionId 会话ID（可选）
     * @param senderType 发送者类型（可选）
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatLog> getByConditions(Long convDtlId, String sessionId, String senderType, Long teamId);

    /**
     * 根据明细ID删除所有聊天记录
     *
     * @param convDtlId 明细ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deleteByConvDtlId(Long convDtlId, Long teamId);
}