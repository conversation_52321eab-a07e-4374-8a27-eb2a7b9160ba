package com.yiyi.ai_train_playground.service.kb;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatMain;

import java.util.List;

/**
 * 模拟聊天室窗口主表 Service接口
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
public interface TrainConvWinchatMainService {

    /**
     * 创建聊天窗口主记录
     *
     * @param chatroomId 聊天室ID
     * @param staffId 员工ID
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 创建的记录
     */
    TrainConvWinchatMain createWinchatMain(Long chatroomId, Long staffId, Long teamId, String creator);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    TrainConvWinchatMain getById(Long id);

    /**
     * 更新记录
     *
     * @param winchatMain 记录
     * @return 是否成功
     */
    boolean updateWinchatMain(TrainConvWinchatMain winchatMain);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据聊天室ID和员工ID查询记录
     *
     * @param chatroomId 聊天室ID
     * @param staffId 员工ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatMain> getByCharoomIdAndStaffId(Long chatroomId, Long staffId, Long teamId);

    /**
     * 根据条件查询记录列表
     *
     * @param chatroomId 聊天室ID（可选）
     * @param staffId 员工ID（可选）
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainConvWinchatMain> getByConditions(Long chatroomId, Long staffId, Long teamId);
}