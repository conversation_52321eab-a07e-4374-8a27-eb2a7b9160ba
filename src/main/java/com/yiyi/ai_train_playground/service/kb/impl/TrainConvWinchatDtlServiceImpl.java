package com.yiyi.ai_train_playground.service.kb.impl;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatDtl;
import com.yiyi.ai_train_playground.mapper.kb.TrainConvWinchatDtlMapper;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatDtlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 模拟聊天室窗口明细表 Service实现类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Service
public class TrainConvWinchatDtlServiceImpl implements TrainConvWinchatDtlService {

    @Autowired
    private TrainConvWinchatDtlMapper trainConvWinchatDtlMapper;

    @Override
    @Transactional
    public TrainConvWinchatDtl createWinchatDtl(Long convWinchatMainId, Long taskId, String sessionId, Long teamId, String creator) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setConvWinchatMainId(convWinchatMainId);
        winchatDtl.setTaskId(taskId);
        winchatDtl.setSessionId(sessionId);
        winchatDtl.setTeamId(teamId);
        winchatDtl.setCreator(creator);
        winchatDtl.setUpdater(creator);
        winchatDtl.setVersion(0L);

        try {
            int result = trainConvWinchatDtlMapper.insert(winchatDtl);
            if (result > 0) {
                log.info("创建聊天窗口明细记录成功, ID: {}, convWinchatMainId: {}, taskId: {}, sessionId: {}, teamId: {}",
                    winchatDtl.getId(), convWinchatMainId, taskId, sessionId, teamId);
                return winchatDtl;
            } else {
                log.error("创建聊天窗口明细记录失败, convWinchatMainId: {}, taskId: {}, sessionId: {}, teamId: {}",
                    convWinchatMainId, taskId, sessionId, teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建聊天窗口明细记录异常, convWinchatMainId: {}, taskId: {}, sessionId: {}, teamId: {}",
                convWinchatMainId, taskId, sessionId, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public TrainConvWinchatDtl createWinchatDtlWithFields(Long convWinchatMainId, Long taskId, String sessionId,
                                                          String f1stRawChatlog, String s2ndAggSysPrompt,
                                                          String t3rdRewrite, String f4thFinalSysPrompt,
                                                          Long teamId, String creator, Long convKbId) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setConvWinchatMainId(convWinchatMainId);

        winchatDtl.setConvKbId(convKbId);//add by germmy@20250825

        winchatDtl.setTaskId(taskId);
        winchatDtl.setSessionId(sessionId);
        winchatDtl.setF1stRawChatlog(f1stRawChatlog);
        winchatDtl.setS2ndAggSysPrompt(s2ndAggSysPrompt);
        winchatDtl.setT3rdRewrite(t3rdRewrite);
        winchatDtl.setF4thFinalSysPrompt(f4thFinalSysPrompt);
        winchatDtl.setTeamId(teamId);
        winchatDtl.setCreator(creator);
        winchatDtl.setUpdater(creator);
        winchatDtl.setVersion(0L);

        try {
            int result = trainConvWinchatDtlMapper.insert(winchatDtl);
            if (result > 0) {
                log.info("创建聊天窗口明细记录成功（包含新字段）, ID: {}, convWinchatMainId: {}, taskId: {}, sessionId: {}, teamId: {}",
                    winchatDtl.getId(), convWinchatMainId, taskId, sessionId, teamId);
                return winchatDtl;
            } else {
                log.error("创建聊天窗口明细记录失败（包含新字段）, convWinchatMainId: {}, taskId: {}, sessionId: {}, teamId: {}",
                    convWinchatMainId, taskId, sessionId, teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建聊天窗口明细记录异常（包含新字段）, convWinchatMainId: {}, taskId: {}, sessionId: {}, teamId: {}",
                convWinchatMainId, taskId, sessionId, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public TrainConvWinchatDtl createWinchatDtlWithConvKbId(Long convWinchatMainId, Long taskId, String sessionId, Long convKbId, Long teamId, String creator) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setConvWinchatMainId(convWinchatMainId);
        winchatDtl.setTaskId(taskId);
        winchatDtl.setSessionId(sessionId);
        winchatDtl.setConvKbId(convKbId); // 设置conv_kb_id字段
        winchatDtl.setTeamId(teamId);
        winchatDtl.setCreator(creator);
        winchatDtl.setUpdater(creator);
        winchatDtl.setVersion(0L);

        try {
            int result = trainConvWinchatDtlMapper.insert(winchatDtl);
            if (result > 0) {
                log.info("创建聊天窗口明细记录成功（包含conv_kb_id）, ID: {}, convWinchatMainId: {}, taskId: {}, sessionId: {}, convKbId: {}, teamId: {}",
                    winchatDtl.getId(), convWinchatMainId, taskId, sessionId, convKbId, teamId);
                return winchatDtl;
            } else {
                log.error("创建聊天窗口明细记录失败（包含conv_kb_id）, convWinchatMainId: {}, taskId: {}, sessionId: {}, convKbId: {}, teamId: {}",
                    convWinchatMainId, taskId, sessionId, convKbId, teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建聊天窗口明细记录异常（包含conv_kb_id）, convWinchatMainId: {}, taskId: {}, sessionId: {}, convKbId: {}, teamId: {}",
                convWinchatMainId, taskId, sessionId, convKbId, teamId, e);
            throw e;
        }
    }

    @Override
    public TrainConvWinchatDtl getById(Long id) {
        if (id == null) {
            log.warn("查询聊天窗口明细记录时，ID为空");
            return null;
        }
        
        try {
            TrainConvWinchatDtl result = trainConvWinchatDtlMapper.selectByPrimaryKey(id);
            log.debug("查询聊天窗口明细记录, ID: {}, 结果: {}", id, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("查询聊天窗口明细记录异常, ID: {}", id, e);
            throw e;
        }
    }

    @Override
    public TrainConvWinchatDtl getBySessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            log.warn("根据sessionId查询时，sessionId为空");
            return null;
        }
        
        try {
            TrainConvWinchatDtl result = trainConvWinchatDtlMapper.selectBySessionId(sessionId);
            log.debug("根据sessionId查询聊天窗口明细记录, sessionId: {}, 结果: {}", sessionId, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("根据sessionId查询聊天窗口明细记录异常, sessionId: {}", sessionId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateWinchatDtl(TrainConvWinchatDtl winchatDtl) {
        if (winchatDtl == null || winchatDtl.getId() == null) {
            log.warn("更新聊天窗口明细记录时，记录或ID为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatDtlMapper.updateByPrimaryKey(winchatDtl);
            boolean success = result > 0;
            if (success) {
                log.info("更新聊天窗口明细记录成功, ID: {}", winchatDtl.getId());
            } else {
                log.warn("更新聊天窗口明细记录失败, ID: {}", winchatDtl.getId());
            }
            return success;
        } catch (Exception e) {
            log.error("更新聊天窗口明细记录异常, ID: {}", winchatDtl.getId(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        if (id == null) {
            log.warn("删除聊天窗口明细记录时，ID为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatDtlMapper.deleteByPrimaryKey(id);
            boolean success = result > 0;
            if (success) {
                log.info("删除聊天窗口明细记录成功, ID: {}", id);
            } else {
                log.warn("删除聊天窗口明细记录失败，记录不存在, ID: {}", id);
            }
            return success;
        } catch (Exception e) {
            log.error("删除聊天窗口明细记录异常, ID: {}", id, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatDtl> getByConvWinchatMainId(Long convWinchatMainId, Long teamId) {
        if (convWinchatMainId == null || teamId == null) {
            log.warn("根据聊天窗口主表ID查询时，参数为空: convWinchatMainId={}, teamId={}", convWinchatMainId, teamId);
            return List.of();
        }
        
        try {
            List<TrainConvWinchatDtl> result = trainConvWinchatDtlMapper.selectByConvWinchatMainId(convWinchatMainId, teamId);
            log.debug("根据聊天窗口主表ID查询聊天窗口明细记录, convWinchatMainId: {}, teamId: {}, 结果数量: {}", 
                convWinchatMainId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据聊天窗口主表ID查询聊天窗口明细记录异常, convWinchatMainId: {}, teamId: {}", convWinchatMainId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatDtl> getByTaskId(Long taskId, Long teamId) {
        if (taskId == null || teamId == null) {
            log.warn("根据任务ID查询时，参数为空: taskId={}, teamId={}", taskId, teamId);
            return List.of();
        }
        
        try {
            List<TrainConvWinchatDtl> result = trainConvWinchatDtlMapper.selectByTaskId(taskId, teamId);
            log.debug("根据任务ID查询聊天窗口明细记录, taskId: {}, teamId: {}, 结果数量: {}", taskId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据任务ID查询聊天窗口明细记录异常, taskId: {}, teamId: {}", taskId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatDtl> getByConditions(Long convWinchatMainId, Long taskId, Long teamId) {
        if (teamId == null) {
            log.warn("根据条件查询时，teamId为空");
            return List.of();
        }
        
        try {
            List<TrainConvWinchatDtl> result = trainConvWinchatDtlMapper.selectByConditions(convWinchatMainId, taskId, teamId);
            log.debug("根据条件查询聊天窗口明细记录, convWinchatMainId: {}, taskId: {}, teamId: {}, 结果数量: {}", 
                convWinchatMainId, taskId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据条件查询聊天窗口明细记录异常, convWinchatMainId: {}, taskId: {}, teamId: {}", convWinchatMainId, taskId, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateRawChatlog(Long id, String rawChatlog, Long teamId, String updater) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setId(id);
        winchatDtl.setF1stRawChatlog(rawChatlog);
        winchatDtl.setTeamId(teamId);
        winchatDtl.setUpdater(updater);
        
        return updateWinchatDtl(winchatDtl);
    }

    @Override
    @Transactional
    public boolean updateSysPrompt(Long id, String sysPrompt, Long teamId, String updater) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setId(id);
        winchatDtl.setS2ndAggSysPrompt(sysPrompt);
        winchatDtl.setTeamId(teamId);
        winchatDtl.setUpdater(updater);
        
        return updateWinchatDtl(winchatDtl);
    }

    @Override
    @Transactional
    public boolean updateRewrite(Long id, String rewrite, Long teamId, String updater) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setId(id);
        winchatDtl.setT3rdRewrite(rewrite);
        winchatDtl.setTeamId(teamId);
        winchatDtl.setUpdater(updater);
        
        return updateWinchatDtl(winchatDtl);
    }

    @Override
    @Transactional
    public boolean updateFinalSysPrompt(Long id, String finalSysPrompt, Long teamId, String updater) {
        TrainConvWinchatDtl winchatDtl = new TrainConvWinchatDtl();
        winchatDtl.setId(id);
        winchatDtl.setF4thFinalSysPrompt(finalSysPrompt);
        winchatDtl.setTeamId(teamId);
        winchatDtl.setUpdater(updater);
        
        return updateWinchatDtl(winchatDtl);
    }
}