package com.yiyi.ai_train_playground.service.kb.impl;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatLog;
import com.yiyi.ai_train_playground.mapper.kb.TrainConvWinchatLogMapper;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模拟聊天室窗口聊天记录表 Service实现类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Service
public class TrainConvWinchatLogServiceImpl implements TrainConvWinchatLogService {

    @Autowired
    private TrainConvWinchatLogMapper trainConvWinchatLogMapper;

    @Override
    @Transactional
    public TrainConvWinchatLog createChatLog(Long convDtlId, String sessionId, String sender, String content, 
                                             LocalDateTime sendTime, String senderType, Long teamId, String creator) {
        TrainConvWinchatLog chatLog = new TrainConvWinchatLog();
        chatLog.setConvDtlId(convDtlId);
        chatLog.setSessionId(sessionId);
        chatLog.setSender(sender);
        chatLog.setContent(content);
        chatLog.setSendTime(sendTime);
        chatLog.setSenderType(senderType);
        chatLog.setTeamId(teamId);
        chatLog.setCreator(creator);
        chatLog.setUpdater(creator);
        chatLog.setVersion(0L);
        
        try {
            int result = trainConvWinchatLogMapper.insert(chatLog);
            if (result > 0) {
                log.info("创建聊天记录成功, ID: {}, convDtlId: {}, sessionId: {}, sender: {}, senderType: {}, teamId: {}", 
                    chatLog.getId(), convDtlId, sessionId, sender, senderType, teamId);
                return chatLog;
            } else {
                log.error("创建聊天记录失败, convDtlId: {}, sessionId: {}, sender: {}, senderType: {}, teamId: {}", 
                    convDtlId, sessionId, sender, senderType, teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建聊天记录异常, convDtlId: {}, sessionId: {}, sender: {}, senderType: {}, teamId: {}", 
                convDtlId, sessionId, sender, senderType, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public TrainConvWinchatLog createChatLogWithProdAnswer(Long convDtlId, String sessionId, String sender, String content, 
                                                          LocalDateTime sendTime, String senderType, String prodReferAnswer, 
                                                          Integer score, String intentResult, Long teamId, String creator) {
        TrainConvWinchatLog chatLog = new TrainConvWinchatLog();
        chatLog.setConvDtlId(convDtlId);
        chatLog.setSessionId(sessionId);
        chatLog.setSender(sender);
        chatLog.setContent(content);
        chatLog.setSendTime(sendTime);
        chatLog.setSenderType(senderType);
        chatLog.setProdReferAnswer(prodReferAnswer);
        chatLog.setScore(score);
        chatLog.setIntentResult(intentResult);
        chatLog.setTeamId(teamId);
        chatLog.setCreator(creator);
        chatLog.setUpdater(creator);
        chatLog.setVersion(0L);
        
        try {
            int result = trainConvWinchatLogMapper.insert(chatLog);
            if (result > 0) {
                log.info("创建带商品参考答案和意图识别的聊天记录成功, ID: {}, convDtlId: {}, sessionId: {}, sender: {}, senderType: {}, prodAnswer: {}, score: {}, intentResult: {}, teamId: {}", 
                    chatLog.getId(), convDtlId, sessionId, sender, senderType, 
                    prodReferAnswer != null ? "有" : "无", score, 
                    intentResult != null ? "有" : "无", teamId);
                return chatLog;
            } else {
                log.error("创建带商品参考答案和意图识别的聊天记录失败, convDtlId: {}, sessionId: {}, sender: {}, senderType: {}, teamId: {}", 
                    convDtlId, sessionId, sender, senderType, teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建带商品参考答案和意图识别的聊天记录异常, convDtlId: {}, sessionId: {}, sender: {}, senderType: {}, teamId: {}", 
                convDtlId, sessionId, sender, senderType, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean batchCreateChatLog(List<TrainConvWinchatLog> chatLogs) {
        if (chatLogs == null || chatLogs.isEmpty()) {
            log.warn("批量创建聊天记录时，记录列表为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatLogMapper.batchInsert(chatLogs);
            boolean success = result > 0;
            if (success) {
                log.info("批量创建聊天记录成功, 数量: {}", chatLogs.size());
            } else {
                log.error("批量创建聊天记录失败, 数量: {}", chatLogs.size());
            }
            return success;
        } catch (Exception e) {
            log.error("批量创建聊天记录异常, 数量: {}", chatLogs.size(), e);
            throw e;
        }
    }

    @Override
    public TrainConvWinchatLog getById(Long id) {
        if (id == null) {
            log.warn("查询聊天记录时，ID为空");
            return null;
        }
        
        try {
            TrainConvWinchatLog result = trainConvWinchatLogMapper.selectByPrimaryKey(id);
            log.debug("查询聊天记录, ID: {}, 结果: {}", id, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("查询聊天记录异常, ID: {}", id, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateChatLog(TrainConvWinchatLog chatLog) {
        if (chatLog == null || chatLog.getId() == null) {
            log.warn("更新聊天记录时，记录或ID为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatLogMapper.updateByPrimaryKey(chatLog);
            boolean success = result > 0;
            if (success) {
                log.info("更新聊天记录成功, ID: {}", chatLog.getId());
            } else {
                log.warn("更新聊天记录失败, ID: {}", chatLog.getId());
            }
            return success;
        } catch (Exception e) {
            log.error("更新聊天记录异常, ID: {}", chatLog.getId(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        if (id == null) {
            log.warn("删除聊天记录时，ID为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatLogMapper.deleteByPrimaryKey(id);
            boolean success = result > 0;
            if (success) {
                log.info("删除聊天记录成功, ID: {}", id);
            } else {
                log.warn("删除聊天记录失败，记录不存在, ID: {}", id);
            }
            return success;
        } catch (Exception e) {
            log.error("删除聊天记录异常, ID: {}", id, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatLog> getByConvDtlId(Long convDtlId, Long teamId) {
        if (convDtlId == null || teamId == null) {
            log.warn("根据明细ID查询聊天记录时，参数为空: convDtlId={}, teamId={}", convDtlId, teamId);
            return List.of();
        }
        
        try {
            List<TrainConvWinchatLog> result = trainConvWinchatLogMapper.selectByConvDtlId(convDtlId, teamId);
            log.debug("根据明细ID查询聊天记录, convDtlId: {}, teamId: {}, 结果数量: {}", convDtlId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据明细ID查询聊天记录异常, convDtlId: {}, teamId: {}", convDtlId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatLog> getBySessionId(String sessionId, Long teamId) {
        if (!StringUtils.hasText(sessionId) || teamId == null) {
            log.warn("根据sessionId查询聊天记录时，参数为空: sessionId={}, teamId={}", sessionId, teamId);
            return List.of();
        }
        
        try {
            List<TrainConvWinchatLog> result = trainConvWinchatLogMapper.selectBySessionId(sessionId, teamId);
            log.debug("根据sessionId查询聊天记录, sessionId: {}, teamId: {}, 结果数量: {}", sessionId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据sessionId查询聊天记录异常, sessionId: {}, teamId: {}", sessionId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatLog> getByConditions(Long convDtlId, String sessionId, String senderType, Long teamId) {
        if (teamId == null) {
            log.warn("根据条件查询聊天记录时，teamId为空");
            return List.of();
        }
        
        try {
            List<TrainConvWinchatLog> result = trainConvWinchatLogMapper.selectByConditions(convDtlId, sessionId, senderType, teamId);
            log.debug("根据条件查询聊天记录, convDtlId: {}, sessionId: {}, senderType: {}, teamId: {}, 结果数量: {}", 
                convDtlId, sessionId, senderType, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据条件查询聊天记录异常, convDtlId: {}, sessionId: {}, senderType: {}, teamId: {}", 
                convDtlId, sessionId, senderType, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteByConvDtlId(Long convDtlId, Long teamId) {
        if (convDtlId == null || teamId == null) {
            log.warn("根据明细ID删除聊天记录时，参数为空: convDtlId={}, teamId={}", convDtlId, teamId);
            return false;
        }
        
        try {
            int result = trainConvWinchatLogMapper.deleteByConvDtlId(convDtlId, teamId);
            boolean success = result >= 0; // 删除0条记录也算成功
            log.info("根据明细ID删除聊天记录, convDtlId: {}, teamId: {}, 删除数量: {}", convDtlId, teamId, result);
            return success;
        } catch (Exception e) {
            log.error("根据明细ID删除聊天记录异常, convDtlId: {}, teamId: {}", convDtlId, teamId, e);
            throw e;
        }
    }
}