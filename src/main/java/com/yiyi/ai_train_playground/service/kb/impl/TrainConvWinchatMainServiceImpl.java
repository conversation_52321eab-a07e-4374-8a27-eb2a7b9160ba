package com.yiyi.ai_train_playground.service.kb.impl;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatMain;
import com.yiyi.ai_train_playground.mapper.kb.TrainConvWinchatMainMapper;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模拟聊天室窗口主表 Service实现类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Service
public class TrainConvWinchatMainServiceImpl implements TrainConvWinchatMainService {

    @Autowired
    private TrainConvWinchatMainMapper trainConvWinchatMainMapper;

    @Override
    @Transactional
    public TrainConvWinchatMain createWinchatMain(Long chatroomId, Long staffId, Long teamId, String creator) {
        TrainConvWinchatMain winchatMain = new TrainConvWinchatMain();
        winchatMain.setChatroomId(chatroomId);
        winchatMain.setStaffId(staffId);
        winchatMain.setTeamId(teamId);
        winchatMain.setCreator(creator);
        winchatMain.setUpdater(creator);
        winchatMain.setVersion(0L);
        
        try {
            int result = trainConvWinchatMainMapper.insert(winchatMain);
            if (result > 0) {
                log.info("创建聊天窗口主记录成功, ID: {}, chatroomId: {}, staffId: {}, teamId: {}", 
                    winchatMain.getId(), chatroomId, staffId, teamId);
                return winchatMain;
            } else {
                log.error("创建聊天窗口主记录失败, chatroomId: {}, staffId: {}, teamId: {}", chatroomId, staffId, teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建聊天窗口主记录异常, chatroomId: {}, staffId: {}, teamId: {}", chatroomId, staffId, teamId, e);
            throw e;
        }
    }

    @Override
    public TrainConvWinchatMain getById(Long id) {
        if (id == null) {
            log.warn("查询聊天窗口主记录时，ID为空");
            return null;
        }
        
        try {
            TrainConvWinchatMain result = trainConvWinchatMainMapper.selectByPrimaryKey(id);
            log.debug("查询聊天窗口主记录, ID: {}, 结果: {}", id, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("查询聊天窗口主记录异常, ID: {}", id, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateWinchatMain(TrainConvWinchatMain winchatMain) {
        if (winchatMain == null || winchatMain.getId() == null) {
            log.warn("更新聊天窗口主记录时，记录或ID为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatMainMapper.updateByPrimaryKey(winchatMain);
            boolean success = result > 0;
            if (success) {
                log.info("更新聊天窗口主记录成功, ID: {}", winchatMain.getId());
            } else {
                log.warn("更新聊天窗口主记录失败, ID: {}", winchatMain.getId());
            }
            return success;
        } catch (Exception e) {
            log.error("更新聊天窗口主记录异常, ID: {}", winchatMain.getId(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        if (id == null) {
            log.warn("删除聊天窗口主记录时，ID为空");
            return false;
        }
        
        try {
            int result = trainConvWinchatMainMapper.deleteByPrimaryKey(id);
            boolean success = result > 0;
            if (success) {
                log.info("删除聊天窗口主记录成功, ID: {}", id);
            } else {
                log.warn("删除聊天窗口主记录失败，记录不存在, ID: {}", id);
            }
            return success;
        } catch (Exception e) {
            log.error("删除聊天窗口主记录异常, ID: {}", id, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatMain> getByCharoomIdAndStaffId(Long chatroomId, Long staffId, Long teamId) {
        if (chatroomId == null || staffId == null || teamId == null) {
            log.warn("根据聊天室ID和员工ID查询时，参数为空: chatroomId={}, staffId={}, teamId={}", chatroomId, staffId, teamId);
            return List.of();
        }
        
        try {
            List<TrainConvWinchatMain> result = trainConvWinchatMainMapper.selectByChatroomIdAndStaffId(chatroomId, staffId, teamId);
            log.debug("根据聊天室ID和员工ID查询聊天窗口主记录, chatroomId: {}, staffId: {}, teamId: {}, 结果数量: {}", 
                chatroomId, staffId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据聊天室ID和员工ID查询聊天窗口主记录异常, chatroomId: {}, staffId: {}, teamId: {}", chatroomId, staffId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainConvWinchatMain> getByConditions(Long chatroomId, Long staffId, Long teamId) {
        if (teamId == null) {
            log.warn("根据条件查询时，teamId为空");
            return List.of();
        }
        
        try {
            List<TrainConvWinchatMain> result = trainConvWinchatMainMapper.selectByConditions(chatroomId, staffId, teamId);
            log.debug("根据条件查询聊天窗口主记录, chatroomId: {}, staffId: {}, teamId: {}, 结果数量: {}", 
                chatroomId, staffId, teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据条件查询聊天窗口主记录异常, chatroomId: {}, staffId: {}, teamId: {}", chatroomId, staffId, teamId, e);
            throw e;
        }
    }
}