package com.yiyi.ai_train_playground.service.shortphrase;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseCreateRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseDetailDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseQueryRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseUpdateRequest;
import com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase;

import java.util.List;

/**
 * 快捷短语服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
public interface ShortcutPhraseService {
    
    /**
     * 分页查询快捷短语列表
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<ShortcutPhraseListDTO> getPhraseList(ShortcutPhraseQueryRequest request, Long teamId);
    
    /**
     * 根据ID查询快捷短语详情
     * 
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 短语详情
     */
    ShortcutPhraseDetailDTO getPhraseDetail(Long id, Long teamId);
    
    /**
     * 根据ID查询快捷短语
     * 
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 短语信息
     */
    ShortcutPhrase getPhraseById(Long id, Long teamId);
    
    /**
     * 创建快捷短语
     * 
     * @param request 创建请求
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 短语ID
     */
    Long createPhrase(ShortcutPhraseCreateRequest request, Long teamId, String creator);
    
    /**
     * 更新快捷短语
     *
     * @param request 更新请求
     * @param teamId 团队ID
     * @param updater 更新者
     * @return 是否成功
     */
    boolean updatePhrase(ShortcutPhraseUpdateRequest request, Long teamId, String updater);
    
    /**
     * 删除快捷短语
     *
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deletePhrase(Long id, Long teamId);

    /**
     * 批量删除快捷短语
     *
     * @param ids 短语ID列表（逗号分隔）
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean batchDeletePhrases(String ids, Long teamId);
    
    /**
     * 增加使用次数
     *
     * @param id 短语ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean incrementUsageCount(Long id, Long teamId);
    
    /**
     * 根据分组ID查询短语列表
     *
     * @param spGroupId 分组ID
     * @param teamId 团队ID
     * @return 短语列表
     */
    List<ShortcutPhraseListDTO> getPhrasesByGroupId(Long spGroupId, Long teamId);
}
