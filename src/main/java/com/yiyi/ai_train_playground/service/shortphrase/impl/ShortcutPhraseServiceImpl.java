package com.yiyi.ai_train_playground.service.shortphrase.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseCreateRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseDetailDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseQueryRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseUpdateRequest;
import com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase;
import com.yiyi.ai_train_playground.mapper.shortphrase.ShortcutPhraseMapper;
import com.yiyi.ai_train_playground.service.shortphrase.ShortcutPhraseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 快捷短语服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@Service
public class ShortcutPhraseServiceImpl implements ShortcutPhraseService {
    
    @Autowired
    private ShortcutPhraseMapper shortcutPhraseMapper;
    
    @Override
    public PageResult<ShortcutPhraseListDTO> getPhraseList(ShortcutPhraseQueryRequest request, Long teamId) {
        log.info("查询快捷短语列表，teamId: {}, request: {}", teamId, request);
        
        // 计算偏移量
        int offset = (request.getPage() - 1) * request.getPageSize();
        
        // 查询列表数据
        List<ShortcutPhraseListDTO> list = shortcutPhraseMapper.selectPhraseList(request, teamId, offset, request.getPageSize());
        
        // 查询总数
        Long total = shortcutPhraseMapper.countPhrases(request, teamId);
        
        log.info("查询快捷短语列表完成，共 {} 条记录", total);
        return new PageResult<>(list, total, request.getPage(), request.getPageSize());
    }
    
    @Override
    public ShortcutPhraseDetailDTO getPhraseDetail(Long id, Long teamId) {
        log.info("查询快捷短语详情，id: {}, teamId: {}", id, teamId);
        
        ShortcutPhraseDetailDTO detail = shortcutPhraseMapper.selectDetailById(id, teamId);
        if (detail == null) {
            log.warn("快捷短语不存在，id: {}, teamId: {}", id, teamId);
            return null;
        }
        
        log.info("查询快捷短语详情完成，短语标题: {}", detail.getPhraseTitle());
        return detail;
    }
    
    @Override
    public ShortcutPhrase getPhraseById(Long id, Long teamId) {
        log.info("查询快捷短语，id: {}, teamId: {}", id, teamId);
        
        ShortcutPhrase phrase = shortcutPhraseMapper.selectById(id, teamId);
        if (phrase == null) {
            log.warn("快捷短语不存在，id: {}, teamId: {}", id, teamId);
        }
        
        return phrase;
    }
    
    @Override
    @Transactional
    public Long createPhrase(ShortcutPhraseCreateRequest request, Long teamId, String creator) {
        log.info("创建快捷短语，teamId: {}, creator: {}, phraseTitle: {}", teamId, creator, request.getPhraseTitle());
        
        // 创建短语实体
        ShortcutPhrase phrase = new ShortcutPhrase();
        BeanUtils.copyProperties(request, phrase);
        
        // 设置系统字段
        phrase.setTeamId(teamId);
        phrase.setCreator(creator);
        phrase.setUpdater(creator);
        phrase.setCreateTime(LocalDateTime.now());
        phrase.setUpdateTime(LocalDateTime.now());
        phrase.setVersion(0L);
        
        // 设置默认值
        if (phrase.getUsageCount() == null) {
            phrase.setUsageCount(0);
        }
        if (phrase.getIsActive() == null) {
            phrase.setIsActive(true);
        }
        if (phrase.getSortOrder() == null) {
            phrase.setSortOrder(0);
        }
        
        // 插入数据库
        int result = shortcutPhraseMapper.insert(phrase);
        if (result > 0) {
            log.info("创建快捷短语成功，phraseId: {}", phrase.getId());
            return phrase.getId();
        } else {
            log.error("创建快捷短语失败");
            throw new RuntimeException("创建快捷短语失败");
        }
    }

    @Override
    @Transactional
    public boolean updatePhrase(ShortcutPhraseUpdateRequest request, Long teamId, String updater) {
        log.info("更新快捷短语，id: {}, teamId: {}, updater: {}", request.getId(), teamId, updater);

        // 先查询短语是否存在
        ShortcutPhrase existingPhrase = shortcutPhraseMapper.selectById(request.getId(), teamId);
        if (existingPhrase == null) {
            log.warn("快捷短语不存在，id: {}, teamId: {}", request.getId(), teamId);
            return false;
        }

        // 创建更新实体
        ShortcutPhrase phrase = new ShortcutPhrase();
        BeanUtils.copyProperties(request, phrase);

        // 设置系统字段
        phrase.setTeamId(teamId);
        phrase.setUpdater(updater);
        phrase.setUpdateTime(LocalDateTime.now());
        phrase.setVersion(existingPhrase.getVersion());

        // 执行更新
        int result = shortcutPhraseMapper.update(phrase);
        if (result > 0) {
            log.info("更新快捷短语成功，id: {}", request.getId());
            return true;
        } else {
            log.warn("更新快捷短语失败，可能是版本冲突，id: {}", request.getId());
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deletePhrase(Long id, Long teamId) {
        log.info("删除快捷短语，id: {}, teamId: {}", id, teamId);

        int result = shortcutPhraseMapper.deleteById(id, teamId);
        if (result > 0) {
            log.info("删除快捷短语成功，id: {}", id);
            return true;
        } else {
            log.warn("删除快捷短语失败，短语不存在，id: {}", id);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchDeletePhrases(String ids, Long teamId) {
        log.info("批量删除快捷短语，ids: {}, teamId: {}", ids, teamId);

        if (!StringUtils.hasText(ids)) {
            log.warn("批量删除快捷短语失败，ids为空");
            return false;
        }

        // 解析ID列表
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        if (idList.isEmpty()) {
            log.warn("批量删除快捷短语失败，解析后的ID列表为空");
            return false;
        }

        int result = shortcutPhraseMapper.batchDeleteByIds(idList, teamId);
        log.info("批量删除快捷短语完成，删除了 {} 条记录", result);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean incrementUsageCount(Long id, Long teamId) {
        log.info("增加快捷短语使用次数，id: {}, teamId: {}", id, teamId);

        int result = shortcutPhraseMapper.incrementUsageCount(id, teamId);
        if (result > 0) {
            log.info("增加快捷短语使用次数成功，id: {}", id);
            return true;
        } else {
            log.warn("增加快捷短语使用次数失败，短语不存在，id: {}", id);
            return false;
        }
    }

    @Override
    public List<ShortcutPhraseListDTO> getPhrasesByGroupId(Long spGroupId, Long teamId) {
        log.info("根据分组ID查询快捷短语列表，spGroupId: {}, teamId: {}", spGroupId, teamId);

        List<ShortcutPhraseListDTO> list = shortcutPhraseMapper.selectByGroupId(spGroupId, teamId);

        log.info("根据分组ID查询快捷短语列表完成，共 {} 条记录", list.size());
        return list;
    }
}
