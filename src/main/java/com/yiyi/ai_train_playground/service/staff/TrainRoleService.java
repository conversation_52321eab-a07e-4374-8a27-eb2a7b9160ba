package com.yiyi.ai_train_playground.service.staff;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.entity.staff.TrainRole;

import java.util.List;

/**
 * 角色管理Service接口
 */
public interface TrainRoleService {

    /**
     * 创建角色
     */
    Long createRole(TrainRoleCreateRequest request);

    /**
     * 更新角色
     */
    void updateRole(TrainRoleUpdateRequest request);

    /**
     * 删除角色
     */
    void deleteRole(Long id);

    /**
     * 批量删除角色
     */
    void deleteRoleBatch(List<Long> ids);

    /**
     * 根据ID查询角色详情
     */
    TrainRoleDetailResponse getRoleDetail(Long id);

    /**
     * 分页查询角色列表
     */
    PageResult<TrainRoleDetailResponse> getRolePageList(TrainRoleQueryRequest request);

    /**
     * 检查角色名称是否存在
     */
    boolean checkRoleNameExists(String roleName, Long excludeId);

    /**
     * 检查角色编码是否存在
     */
    boolean checkRoleCodeExists(String roleCode, Long excludeId);

    /**
     * 根据角色编码查询角色
     */
    TrainRole getRoleByCode(String roleCode);

    /**
     * 查询团队所有角色
     */
    List<TrainRoleDetailResponse> getAllRoles();

    /**
     * 根据员工ID查询其拥有的角色
     */
    List<TrainRoleDetailResponse> getRolesByStaffId(Long staffId);
}