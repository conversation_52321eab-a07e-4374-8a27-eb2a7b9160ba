package com.yiyi.ai_train_playground.service.staff;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffCreateRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffDetailResponse;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffQueryRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffUpdateRequest;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;

import java.util.List;

/**
 * 员工服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
public interface TrainStaffService {
    
    /**
     * 创建员工
     *
     * @param request 创建请求
     * @return 影响行数
     */
    int createStaff(TrainStaffCreateRequest request);
    
    /**
     * 创建员工（实体类版本，兼容原有代码）
     *
     * @param staff 员工信息
     * @return 影响行数
     */
    int createStaff(TrainStaff staff);
    
    /**
     * 删除员工
     *
     * @param id 员工ID
     * @return 影响行数
     */
    int deleteStaff(Long id);
    
    /**
     * 批量删除员工
     *
     * @param ids 员工ID列表
     * @return 影响行数
     */
    int deleteStaffBatch(List<Long> ids);
    
    /**
     * 批量删除员工(通过拦截器添加team_id)
     *
     * @param ids 员工ID列表
     * @return 影响行数
     */
    int deleteStaffBatchWithIts(List<Long> ids);
    
    /**
     * 更新员工信息
     *
     * @param request 更新请求
     * @return 影响行数
     */
    int updateStaff(TrainStaffUpdateRequest request);
    
    /**
     * 根据ID查询员工详情
     *
     * @param id 员工ID
     * @return 员工详情
     */
    TrainStaffDetailResponse getStaffDetail(Long id);
    
    /**
     * 根据用户ID查询员工信息
     *
     * @param userId 用户ID
     * @return 员工信息
     */
    TrainStaff getStaffByUserId(Long userId);
    
    /**
     * 根据用户名查询员工信息
     *
     * @param username 用户名
     * @return 员工信息
     */
    TrainStaff getStaffByUsername(String username);
    
    /**
     * 分页查询员工列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    PageResult<TrainStaffDetailResponse> getStaffPageList(TrainStaffQueryRequest request);
    
    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkUsernameExists(String username, Long excludeId);
    
    /**
     * 重置密码
     *
     * @param id 员工ID
     * @param newPassword 新密码（明文）
     * @return 影响行数
     */
    int resetPassword(Long id, String newPassword);
    
    /**
     * 锁定/解锁账户
     *
     * @param id 员工ID
     * @param isLocked 是否锁定
     * @return 影响行数
     */
    int toggleAccountLock(Long id, Boolean isLocked);
    
    /**
     * 更新登录信息
     *
     * @param id 员工ID
     * @param failedAttempts 失败次数
     * @param lastLoginIp 最后登录IP
     * @return 影响行数
     */
    int updateLoginInfo(Long id, Integer failedAttempts, String lastLoginIp);
}