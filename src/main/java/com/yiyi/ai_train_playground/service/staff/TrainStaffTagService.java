package com.yiyi.ai_train_playground.service.staff;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.entity.staff.TrainStaffTag;

import java.util.List;

public interface TrainStaffTagService {

    /**
     * 分页查询员工标签
     */
    PageResult<StaffTagDTO> getStaffTagPage(StaffTagQueryRequest request);

    /**
     * 根据ID获取员工标签详情
     */
    StaffTagDTO getStaffTagById(Long id, Long teamId);

    /**
     * 创建员工标签
     */
    Long createStaffTag(StaffTagCreateRequest request, Long teamId, String creator);

    /**
     * 更新员工标签
     */
    void updateStaffTag(StaffTagUpdateRequest request, Long teamId, String updater);

    /**
     * 删除员工标签
     */
    void deleteStaffTag(Long id, Long teamId);

    /**
     * 批量删除员工标签
     */
    void batchDeleteStaffTags(List<Long> ids, Long teamId);

    /**
     * 检查标签名称是否重复
     */
    boolean checkNameExists(String name, Long teamId, Long excludeId);

    /**
     * 获取团队所有标签（不分页）
     */
    List<StaffTagDTO> getAllStaffTags(Long teamId);
}