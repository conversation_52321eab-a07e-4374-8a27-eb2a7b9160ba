package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.staff.StaffLoginRequest;
import com.yiyi.ai_train_playground.dto.staff.StaffLoginResponse;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper;
import com.yiyi.ai_train_playground.service.staff.StaffAuthService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 员工认证服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Service
public class StaffAuthServiceImpl implements StaffAuthService {

    private final TrainStaffMapper trainStaffMapper;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;

    public StaffAuthServiceImpl(TrainStaffMapper trainStaffMapper, JwtUtil jwtUtil, PasswordEncoder passwordEncoder) {
        this.trainStaffMapper = trainStaffMapper;
        this.jwtUtil = jwtUtil;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    @Transactional
    public StaffLoginResponse login(StaffLoginRequest request) {
        // 根据用户名查找员工
        TrainStaff staff = trainStaffMapper.selectByUsernameForLogin(request.getUsername());
        if (staff == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 检查员工状态（数据库中没有status字段，所以不检查状态）

        // 检查账号是否被锁定
        if (staff.getIsLocked() != null && staff.getIsLocked() && staff.getLockTime() != null) {
            // 如果锁定时间超过30分钟，解除锁定
            if (staff.getLockTime().plusMinutes(30).isBefore(LocalDateTime.now())) {
                trainStaffMapper.updateLoginStatus(staff.getId(), 0, false, null);
                staff.setFailedAttempts(0);
                staff.setIsLocked(false);
                staff.setLockTime(null);
            } else {
                throw new RuntimeException("账号已被锁定，请30分钟后再试");
            }
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), staff.getPasswordHash())) {
            // 更新失败次数
            int failedAttempts = (staff.getFailedAttempts() != null ? staff.getFailedAttempts() : 0) + 1;
            boolean shouldLock = failedAttempts >= 5;
            LocalDateTime lockTime = shouldLock ? LocalDateTime.now() : null;
            
            trainStaffMapper.updateLoginStatus(
                staff.getId(),
                failedAttempts,
                shouldLock,
                lockTime
            );

            throw new RuntimeException("用户名或密码错误");
        }

        // 登录成功，重置失败次数
        if (staff.getFailedAttempts() != null && staff.getFailedAttempts() > 0) {
            trainStaffMapper.updateLoginStatus(staff.getId(), 0, false, null);
        }

        // 更新最后登录时间
        trainStaffMapper.updateLastLoginTime(staff.getId(), LocalDateTime.now());

        // 生成token（使用员工ID作为userId，确保与现有JWT系统兼容）
        String token = jwtUtil.generateToken(staff.getId(), staff.getUsername(), staff.getTeamId(), request.getRememberMe());

        // 构建响应
        StaffLoginResponse response = new StaffLoginResponse();
        response.setStaffId(staff.getId());
        response.setUsername(staff.getUsername());
        response.setDisplayName(staff.getDisplayName());
        response.setTeamId(staff.getTeamId());
        response.setStatus(1); // 默认为正常状态，因为数据库中没有status字段
        response.setToken(token);

        return response;
    }
}