package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.entity.staff.TrainRole;
import com.yiyi.ai_train_playground.mapper.staff.TrainRoleMapper;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffRoleMapper;
import com.yiyi.ai_train_playground.service.staff.TrainRoleService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色管理Service实现类
 */
@Service
public class TrainRoleServiceImpl implements TrainRoleService {

    @Autowired
    private TrainRoleMapper trainRoleMapper;

    @Autowired
    private TrainStaffRoleMapper trainStaffRoleMapper;

    @Override
    @Transactional
    public Long createRole(TrainRoleCreateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        String currentUser = SecurityUtil.getCurrentUserId().toString();

        // 检查角色名称是否已存在
        if (checkRoleNameExists(request.getRoleName(), null)) {
            throw new RuntimeException("角色名称已存在");
        }

        // 检查角色编码是否已存在
        if (checkRoleCodeExists(request.getRoleCode(), null)) {
            throw new RuntimeException("角色编码已存在");
        }

        TrainRole role = new TrainRole();
        role.setRoleName(request.getRoleName());
        role.setRoleCode(request.getRoleCode());
        role.setDescription(request.getDescription());
        role.setTeamId(teamId);
        role.setCreator(currentUser);
        role.setUpdater(currentUser);

        trainRoleMapper.insert(role);
        return role.getId();
    }

    @Override
    @Transactional
    public void updateRole(TrainRoleUpdateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        String currentUser = SecurityUtil.getCurrentUserId().toString();

        TrainRole existingRole = trainRoleMapper.selectById(request.getId());
        if (existingRole == null || !existingRole.getTeamId().equals(teamId)) {
            throw new RuntimeException("角色不存在");
        }

        if (!existingRole.getVersion().equals(request.getVersion())) {
            throw new RuntimeException("数据已被其他用户修改，请刷新后重试");
        }

        // 检查角色名称是否已存在（排除当前角色）
        if (request.getRoleName() != null && 
            checkRoleNameExists(request.getRoleName(), request.getId())) {
            throw new RuntimeException("角色名称已存在");
        }

        TrainRole role = new TrainRole();
        role.setId(request.getId());
        role.setRoleName(request.getRoleName());
        role.setDescription(request.getDescription());
        role.setVersion(request.getVersion());
        role.setUpdater(currentUser);

        int updated = trainRoleMapper.updateById(role);
        if (updated == 0) {
            throw new RuntimeException("更新失败，数据可能已被其他用户修改");
        }
    }

    @Override
    @Transactional
    public void deleteRole(Long id) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        TrainRole role = trainRoleMapper.selectById(id);
        if (role == null || !role.getTeamId().equals(teamId)) {
            throw new RuntimeException("角色不存在");
        }

        // 删除角色相关的员工关系
        trainStaffRoleMapper.deleteByRoleId(id, teamId);

        // 删除角色
        trainRoleMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void deleteRoleBatch(List<Long> ids) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        for (Long id : ids) {
            // 删除角色相关的员工关系
            trainStaffRoleMapper.deleteByRoleId(id, teamId);
        }

        // 批量删除角色
        trainRoleMapper.deleteBatch(ids, teamId);
    }

    @Override
    public TrainRoleDetailResponse getRoleDetail(Long id) {
        Long teamId = SecurityUtil.getCurrentTeamId();

        TrainRole role = trainRoleMapper.selectById(id);
        if (role == null || !role.getTeamId().equals(teamId)) {
            throw new RuntimeException("角色不存在");
        }

        TrainRoleDetailResponse response = new TrainRoleDetailResponse();
        BeanUtils.copyProperties(role, response);
        return response;
    }

    @Override
    public PageResult<TrainRoleDetailResponse> getRolePageList(TrainRoleQueryRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        request.setTeamId(teamId);

        // 计算偏移量
        int offset = (request.getPage() - 1) * request.getPageSize();
        request.setOffset(offset);

        List<TrainRole> roleList = trainRoleMapper.selectPageList(request);
        long total = trainRoleMapper.selectCount(request);

        List<TrainRoleDetailResponse> responseList = roleList.stream()
            .map(role -> {
                TrainRoleDetailResponse response = new TrainRoleDetailResponse();
                BeanUtils.copyProperties(role, response);
                return response;
            })
            .collect(Collectors.toList());

        return new PageResult<>(responseList, total, request.getPage(), request.getPageSize());
    }

    @Override
    public boolean checkRoleNameExists(String roleName, Long excludeId) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        return trainRoleMapper.existsByRoleName(roleName, teamId, excludeId);
    }

    @Override
    public boolean checkRoleCodeExists(String roleCode, Long excludeId) {
        return trainRoleMapper.existsByRoleCode(roleCode, excludeId);
    }

    @Override
    public TrainRole getRoleByCode(String roleCode) {
        return trainRoleMapper.selectByRoleCode(roleCode);
    }

    @Override
    public List<TrainRoleDetailResponse> getAllRoles() {
        Long teamId = SecurityUtil.getCurrentTeamId();
        List<TrainRole> roleList = trainRoleMapper.selectByTeamId(teamId);

        return roleList.stream()
            .map(role -> {
                TrainRoleDetailResponse response = new TrainRoleDetailResponse();
                BeanUtils.copyProperties(role, response);
                return response;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<TrainRoleDetailResponse> getRolesByStaffId(Long staffId) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        List<TrainRole> roleList = trainRoleMapper.selectByStaffId(staffId, teamId);

        return roleList.stream()
            .map(role -> {
                TrainRoleDetailResponse response = new TrainRoleDetailResponse();
                BeanUtils.copyProperties(role, response);
                return response;
            })
            .collect(Collectors.toList());
    }
}