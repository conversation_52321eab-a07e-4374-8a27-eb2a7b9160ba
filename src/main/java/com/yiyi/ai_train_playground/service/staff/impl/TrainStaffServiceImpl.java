package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffCreateRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffDetailResponse;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffQueryRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffUpdateRequest;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.entity.staff.TrainStaffRole;
import com.yiyi.ai_train_playground.entity.staff.TrainStaffTagRelation;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffRoleMapper;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffTagRelationMapper;
import com.yiyi.ai_train_playground.service.staff.TrainStaffService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainStaffServiceImpl implements TrainStaffService {
    
    private final TrainStaffMapper trainStaffMapper;
    private final TrainStaffRoleMapper trainStaffRoleMapper;
    private final TrainStaffTagRelationMapper trainStaffTagRelationMapper;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    @Transactional
    public int createStaff(TrainStaffCreateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        Long currentUserId = SecurityUtil.getCurrentUserId();
        String currentUsername = currentUserId != null ? currentUserId.toString() : "system";
        
        log.info("创建员工，用户名: {}, 团队ID: {}, 操作人: {}", request.getUsername(), teamId, currentUsername);
        
        // 检查用户名是否重复
        if (checkUsernameExists(request.getUsername(), null)) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }
        
        // 转换为实体类
        TrainStaff staff = new TrainStaff();
        BeanUtils.copyProperties(request, staff);
        
        // 设置默认值
        staff.setUserId(SecurityUtil.getCurrentUserId()); // 设置当前用户ID
        staff.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        staff.setIsLocked(false);
        staff.setFailedAttempts(0);
        staff.setTeamId(teamId);
        staff.setCreator(currentUsername);
        staff.setUpdater(currentUsername);
        staff.setVersion(0L);
        
        int result = trainStaffMapper.insert(staff);
        log.info("员工创建结果，ID: {}, 影响行数: {}", staff.getId(), result);
        
        // 如果提供了角色ID，创建员工角色关联
        if (request.getRoleId() != null && result > 0) {
            createStaffRoleRelation(staff.getId(), request.getRoleId(), teamId, currentUsername);
        }
        
        // 如果提供了标签ID列表，创建员工标签关联
        if (request.getTagIds() != null && !request.getTagIds().isEmpty() && result > 0) {
            createStaffTagRelations(staff.getId(), request.getTagIds(), teamId, currentUsername);
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public int createStaff(TrainStaff staff) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        Long currentUserId = SecurityUtil.getCurrentUserId();
        String currentUsername = currentUserId != null ? currentUserId.toString() : "system";
        
        log.info("创建员工（实体类版本），用户名: {}, 团队ID: {}", staff.getUsername(), teamId);
        
        // 设置默认值
        if (staff.getTeamId() == null) {
            staff.setTeamId(teamId);
        }
        if (!StringUtils.hasText(staff.getCreator())) {
            staff.setCreator(currentUsername);
        }
        if (!StringUtils.hasText(staff.getUpdater())) {
            staff.setUpdater(currentUsername);
        }
        if (staff.getVersion() == null) {
            staff.setVersion(0L);
        }
        if (staff.getIsLocked() == null) {
            staff.setIsLocked(false);
        }
        if (staff.getFailedAttempts() == null) {
            staff.setFailedAttempts(0);
        }
        
        return trainStaffMapper.insert(staff);
    }
    
    @Override
    @Transactional
    public int deleteStaff(Long id) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        log.info("删除员工，ID: {}, 团队ID: {}", id, teamId);
        
        // 检查员工是否存在且属于当前团队
        TrainStaff staff = trainStaffMapper.selectById(id);
        if (staff == null || !teamId.equals(staff.getTeamId())) {
            throw new RuntimeException("员工不存在或无权限删除");
        }
        
        // 先删除员工角色关联
        trainStaffRoleMapper.deleteByStaffId(id, teamId);
        log.info("删除员工角色关联，员工ID: {}", id);
        
        // 删除员工标签关联
        trainStaffTagRelationMapper.deleteByStaffId(id);
        log.info("删除员工标签关联，员工ID: {}", id);
        
        return trainStaffMapper.deleteById(id);
    }
    
    @Override
    @Transactional
    public int deleteStaffBatch(List<Long> ids) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        log.info("批量删除员工，IDs: {}, 团队ID: {}", ids, teamId);
        
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        // 先删除所有员工的角色关联
        for (Long staffId : ids) {
            trainStaffRoleMapper.deleteByStaffId(staffId, teamId);
        }
        log.info("批量删除员工角色关联，员工IDs: {}", ids);
        
        // 删除所有员工的标签关联
        for (Long staffId : ids) {
            trainStaffTagRelationMapper.deleteByStaffId(staffId);
        }
        log.info("批量删除员工标签关联，员工IDs: {}", ids);
        
        return trainStaffMapper.deleteBatch(ids, teamId);
    }

    @Override
    @Transactional
    public int deleteStaffBatchWithIts(List<Long> ids) {
        log.info("批量删除员工(拦截器方式)，IDs: {}", ids);
        
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        // 使用拦截器自动添加team_id条件的删除方法
        return trainStaffMapper.deleteBatchWithIts(ids);
    }
    
    @Override
    @Transactional
    public int updateStaff(TrainStaffUpdateRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        Long currentUserId = SecurityUtil.getCurrentUserId();
        String currentUsername = currentUserId != null ? currentUserId.toString() : "system";
        
        log.info("更新员工信息，ID: {}, 团队ID: {}", request.getId(), teamId);
        
        // 检查员工是否存在
        TrainStaff existingStaff = trainStaffMapper.selectById(request.getId());
        if (existingStaff == null || !teamId.equals(existingStaff.getTeamId())) {
            throw new RuntimeException("员工不存在或无权限修改");
        }
        
        // 转换为实体类
        TrainStaff staff = new TrainStaff();
        BeanUtils.copyProperties(request, staff);
        staff.setTeamId(teamId);
        staff.setUpdater(currentUsername);
        
        int result = trainStaffMapper.updateById(staff);
        
        // 如果提供了角色ID，更新员工角色关联
        if (request.getRoleId() != null && result > 0) {
            updateStaffRoleRelation(request.getId(), request.getRoleId(), teamId, currentUsername);
        }
        
        // 如果提供了标签ID列表，更新员工标签关联
        if (request.getTagIds() != null && result > 0) {
            updateStaffTagRelations(request.getId(), request.getTagIds(), teamId, currentUsername);
        }
        
        return result;
    }
    
    @Override
    public TrainStaffDetailResponse getStaffDetail(Long id) {
        log.info("查询员工详情，ID: {}", id);
        
        TrainStaff staff = trainStaffMapper.selectById(id);
        if (staff == null) {
            return null;
        }
        
        // 团队隔离检查
        Long teamId = SecurityUtil.getCurrentTeamId();
        if (teamId != null && !teamId.equals(staff.getTeamId())) {
            log.warn("无权限查看该员工信息，员工团队ID: {}, 当前团队ID: {}", staff.getTeamId(), teamId);
            return null;
        }
        
        TrainStaffDetailResponse response = new TrainStaffDetailResponse();
        BeanUtils.copyProperties(staff, response);
        
        // 查询员工的角色信息
        List<TrainStaffRole> staffRoles = trainStaffRoleMapper.selectByStaffId(id, teamId);
        if (!staffRoles.isEmpty()) {
            // 如果有角色关联，取第一个角色ID（假设一个员工只有一个主要角色）
            response.setRoleId(staffRoles.get(0).getRoleId());
        }
        
        // 查询员工的标签信息
        List<TrainStaffTagRelation> staffTagRelations = trainStaffTagRelationMapper.selectByStaffId(id);
        if (!staffTagRelations.isEmpty()) {
            List<Long> tagIds = staffTagRelations.stream()
                .map(TrainStaffTagRelation::getTagId)
                .collect(Collectors.toList());
            response.setTagIds(tagIds);
        }
        
        return response;
    }
    
    @Override
    public TrainStaff getStaffByUserId(Long userId) {
        log.info("根据用户ID查询员工，userId: {}", userId);
        return trainStaffMapper.selectByUserId(userId);
    }
    
    @Override
    public TrainStaff getStaffByUsername(String username) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        log.info("根据用户名查询员工，username: {}, 团队ID: {}", username, teamId);
        return trainStaffMapper.selectByUsername(username, teamId);
    }
    
    @Override
    public PageResult<TrainStaffDetailResponse> getStaffPageList(TrainStaffQueryRequest request) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        log.info("分页查询员工列表，团队ID: {}, 页码: {}, 页大小: {}", teamId, request.getPage(), request.getPageSize());
        
        // 计算偏移量
        int offset = (request.getPage() - 1) * request.getPageSize();
        
        // 查询数据
        List<TrainStaff> staffList = trainStaffMapper.selectPageList(
            teamId, request.getUsername(), request.getDisplayName(), request.getEmail(),
            request.getStatus(), request.getIsLocked(), offset, request.getPageSize()
        );
        
        // 查询总数
        long total = trainStaffMapper.countPageList(
            teamId, request.getUsername(), request.getDisplayName(), request.getEmail(),
            request.getStatus(), request.getIsLocked()
        );
        
        // 转换为响应DTO
        List<TrainStaffDetailResponse> responseList = staffList.stream()
            .map(staff -> {
                TrainStaffDetailResponse response = new TrainStaffDetailResponse();
                BeanUtils.copyProperties(staff, response);
                
                // 查询员工的角色信息
                List<TrainStaffRole> staffRoles = trainStaffRoleMapper.selectByStaffId(staff.getId(), teamId);
                if (!staffRoles.isEmpty()) {
                    // 如果有角色关联，取第一个角色ID
                    response.setRoleId(staffRoles.get(0).getRoleId());
                }
                
                // 查询员工的标签信息
                List<TrainStaffTagRelation> staffTagRelations = trainStaffTagRelationMapper.selectByStaffId(staff.getId());
                if (!staffTagRelations.isEmpty()) {
                    List<Long> tagIds = staffTagRelations.stream()
                        .map(TrainStaffTagRelation::getTagId)
                        .collect(Collectors.toList());
                    response.setTagIds(tagIds);
                }
                
                return response;
            })
            .collect(Collectors.toList());
        
        return new PageResult<>(responseList, total, request.getPage(), request.getPageSize());
    }
    
    @Override
    public boolean checkUsernameExists(String username, Long excludeId) {
        Long teamId = SecurityUtil.getCurrentTeamId();
        log.info("检查用户名是否存在，username: {}, excludeId: {}, 团队ID: {}", username, excludeId, teamId);
        
        int count = trainStaffMapper.countByUsername(username, excludeId, teamId);
        return count > 0;
    }
    
    @Override
    @Transactional
    public int resetPassword(Long id, String newPassword) {
        log.info("重置员工密码，ID: {}", id);
        
        // 团队隔离检查
        Long teamId = SecurityUtil.getCurrentTeamId();
        TrainStaff staff = trainStaffMapper.selectById(id);
        if (staff == null || !teamId.equals(staff.getTeamId())) {
            throw new RuntimeException("员工不存在或无权限操作");
        }
        
        String hashedPassword = passwordEncoder.encode(newPassword);
        return trainStaffMapper.updatePassword(id, hashedPassword);
    }
    
    @Override
    @Transactional
    public int toggleAccountLock(Long id, Boolean isLocked) {
        log.info("切换账户锁定状态，ID: {}, isLocked: {}", id, isLocked);
        
        // 团队隔离检查
        Long teamId = SecurityUtil.getCurrentTeamId();
        TrainStaff staff = trainStaffMapper.selectById(id);
        if (staff == null || !teamId.equals(staff.getTeamId())) {
            throw new RuntimeException("员工不存在或无权限操作");
        }
        
        String lockTime = isLocked ? LocalDateTime.now().toString() : null;
        return trainStaffMapper.updateLoginInfo(id, null, isLocked, lockTime, null, null);
    }
    
    @Override
    @Transactional
    public int updateLoginInfo(Long id, Integer failedAttempts, String lastLoginIp) {
        log.info("更新登录信息，ID: {}, failedAttempts: {}, lastLoginIp: {}", id, failedAttempts, lastLoginIp);
        
        String lastLoginTime = LocalDateTime.now().toString();
        return trainStaffMapper.updateLoginInfo(id, failedAttempts, null, null, lastLoginTime, lastLoginIp);
    }
    
    /**
     * 创建员工角色关联
     *
     * @param staffId 员工ID
     * @param roleId 角色ID
     * @param teamId 团队ID
     * @param operator 操作人
     */
    private void createStaffRoleRelation(Long staffId, Long roleId, Long teamId, String operator) {
        log.info("创建员工角色关联，员工ID: {}, 角色ID: {}", staffId, roleId);
        
        TrainStaffRole staffRole = new TrainStaffRole();
        staffRole.setStaffId(staffId);
        staffRole.setRoleId(roleId);
        staffRole.setTeamId(teamId);
        staffRole.setCreator(operator);
        staffRole.setUpdater(operator);
        staffRole.setVersion(0L);
        
        trainStaffRoleMapper.insert(staffRole);
    }
    
    /**
     * 更新员工角色关联
     *
     * @param staffId 员工ID
     * @param roleId 角色ID
     * @param teamId 团队ID
     * @param operator 操作人
     */
    private void updateStaffRoleRelation(Long staffId, Long roleId, Long teamId, String operator) {
        log.info("更新员工角色关联，员工ID: {}, 角色ID: {}", staffId, roleId);
        
        // 先删除现有的角色关联
        trainStaffRoleMapper.deleteByStaffId(staffId, teamId);
        
        // 创建新的角色关联
        createStaffRoleRelation(staffId, roleId, teamId, operator);
    }
    
    /**
     * 创建员工标签关联
     *
     * @param staffId 员工ID
     * @param tagIds 标签ID列表
     * @param teamId 团队ID
     * @param operator 操作人
     */
    private void createStaffTagRelations(Long staffId, List<Long> tagIds, Long teamId, String operator) {
        log.info("创建员工标签关联，员工ID: {}, 标签IDs: {}", staffId, tagIds);
        
        if (tagIds == null || tagIds.isEmpty()) {
            return;
        }
        
        for (Long tagId : tagIds) {
            TrainStaffTagRelation relation = new TrainStaffTagRelation();
            relation.setStaffId(staffId);
            relation.setTagId(tagId);
            relation.setTeamId(teamId);
            relation.setCreator(operator);
            relation.setUpdater(operator);
            relation.setVersion(0L);
            
            trainStaffTagRelationMapper.insert(relation);
        }
    }
    
    /**
     * 更新员工标签关联
     *
     * @param staffId 员工ID
     * @param tagIds 标签ID列表
     * @param teamId 团队ID
     * @param operator 操作人
     */
    private void updateStaffTagRelations(Long staffId, List<Long> tagIds, Long teamId, String operator) {
        log.info("更新员工标签关联，员工ID: {}, 标签IDs: {}", staffId, tagIds);
        
        // 先删除现有的标签关联
        trainStaffTagRelationMapper.deleteByStaffId(staffId);
        
        // 创建新的标签关联
        if (tagIds != null && !tagIds.isEmpty()) {
            createStaffTagRelations(staffId, tagIds, teamId, operator);
        }
    }
}