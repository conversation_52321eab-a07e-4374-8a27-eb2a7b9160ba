package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.entity.staff.TrainStaffTag;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffTagMapper;
import com.yiyi.ai_train_playground.service.staff.TrainStaffTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainStaffTagServiceImpl implements TrainStaffTagService {

    private final TrainStaffTagMapper staffTagMapper;

    @Override
    public PageResult<StaffTagDTO> getStaffTagPage(StaffTagQueryRequest request) {
        // 设置默认值
        if (request.getPage() == null || request.getPage() < 1) {
            request.setPage(1);
        }
        if (request.getPageSize() == null || request.getPageSize() < 1) {
            request.setPageSize(10);
        }

        // 计算偏移量
        int offset = (request.getPage() - 1) * request.getPageSize();

        // 查询数据
        List<TrainStaffTag> staffTags = staffTagMapper.selectStaffTagPage(request, offset, request.getPageSize());
        Integer total = staffTagMapper.countStaffTag(request);

        // 转换为DTO
        List<StaffTagDTO> staffTagDTOs = staffTags.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return new PageResult<>(staffTagDTOs, total.longValue(), request.getPage(), request.getPageSize());
    }

    @Override
    public StaffTagDTO getStaffTagById(Long id, Long teamId) {
        if (id == null) {
            throw new IllegalArgumentException("员工标签ID不能为空");
        }

        TrainStaffTag staffTag = staffTagMapper.selectById(id);
        if (staffTag == null || !staffTag.getTeamId().equals(teamId)) {
            throw new RuntimeException("员工标签不存在或无权限访问");
        }

        return convertToDTO(staffTag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStaffTag(StaffTagCreateRequest request, Long teamId, String creator) {
        // 检查名称是否重复
        if (checkNameExists(request.getName(), teamId, null)) {
            throw new RuntimeException("标签名称已存在");
        }

        // 创建实体
        TrainStaffTag staffTag = new TrainStaffTag();
        staffTag.setName(request.getName());
        staffTag.setTeamId(teamId);
        staffTag.setCreator(creator);
        staffTag.setUpdater(creator);
        staffTag.setCreateTime(LocalDateTime.now());
        staffTag.setUpdateTime(LocalDateTime.now());
        staffTag.setVersion(0L);

        // 保存到数据库
        int result = staffTagMapper.insert(staffTag);
        if (result <= 0) {
            throw new RuntimeException("创建员工标签失败");
        }

        log.info("创建员工标签成功，ID: {}, 名称: {}, 团队ID: {}, 创建人: {}", 
                staffTag.getId(), staffTag.getName(), teamId, creator);

        return staffTag.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStaffTag(StaffTagUpdateRequest request, Long teamId, String updater) {
        // 先查询原记录
        TrainStaffTag existingStaffTag = staffTagMapper.selectById(request.getId());
        if (existingStaffTag == null || !existingStaffTag.getTeamId().equals(teamId)) {
            throw new RuntimeException("员工标签不存在或无权限访问");
        }

        // 检查版本号
        if (!existingStaffTag.getVersion().equals(request.getVersion())) {
            throw new RuntimeException("数据已被其他用户修改，请刷新后重试");
        }

        // 检查名称是否重复（排除自己）
        if (checkNameExists(request.getName(), teamId, request.getId())) {
            throw new RuntimeException("标签名称已存在");
        }

        // 更新实体
        TrainStaffTag staffTag = new TrainStaffTag();
        staffTag.setId(request.getId());
        staffTag.setName(request.getName());
        staffTag.setUpdater(updater);
        staffTag.setUpdateTime(LocalDateTime.now());
        staffTag.setVersion(request.getVersion() + 1);

        // 更新到数据库
        int result = staffTagMapper.updateById(staffTag);
        if (result <= 0) {
            throw new RuntimeException("更新员工标签失败");
        }

        log.info("更新员工标签成功，ID: {}, 名称: {}, 团队ID: {}, 更新人: {}", 
                request.getId(), request.getName(), teamId, updater);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStaffTag(Long id, Long teamId) {
        if (id == null) {
            throw new IllegalArgumentException("员工标签ID不能为空");
        }

        // 先查询确认存在且有权限
        TrainStaffTag staffTag = staffTagMapper.selectById(id);
        if (staffTag == null || !staffTag.getTeamId().equals(teamId)) {
            throw new RuntimeException("员工标签不存在或无权限访问");
        }

        // 删除
        int result = staffTagMapper.deleteById(id);
        if (result <= 0) {
            throw new RuntimeException("删除员工标签失败");
        }

        log.info("删除员工标签成功，ID: {}, 名称: {}, 团队ID: {}", id, staffTag.getName(), teamId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteStaffTags(List<Long> ids, Long teamId) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("删除的员工标签ID列表不能为空");
        }

        // 批量删除
        int result = staffTagMapper.batchDeleteByIds(ids, teamId);
        if (result <= 0) {
            throw new RuntimeException("批量删除员工标签失败");
        }

        log.info("批量删除员工标签成功，删除数量: {}, 团队ID: {}", result, teamId);
    }

    @Override
    public boolean checkNameExists(String name, Long teamId, Long excludeId) {
        Integer count = staffTagMapper.checkNameDuplicate(teamId, name, excludeId);
        return count != null && count > 0;
    }

    @Override
    public List<StaffTagDTO> getAllStaffTags(Long teamId) {
        List<TrainStaffTag> staffTags = staffTagMapper.selectList(teamId);
        return staffTags.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转换为DTO
     */
    private StaffTagDTO convertToDTO(TrainStaffTag staffTag) {
        if (staffTag == null) {
            return null;
        }

        StaffTagDTO dto = new StaffTagDTO();
        BeanUtils.copyProperties(staffTag, dto);
        return dto;
    }
}