package com.yiyi.ai_train_playground.service.system;

import com.yiyi.ai_train_playground.dto.system.*;
import com.yiyi.ai_train_playground.entity.system.SystemConfig;
import com.yiyi.ai_train_playground.dto.PageResult;

import java.util.List;

/**
 * 系统配置Service接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
public interface SystemConfigService {
    
    /**
     * 创建系统配置
     */
    Long createSystemConfig(SystemConfigCreateRequest request);
    
    /**
     * 更新系统配置
     */
    void updateSystemConfig(SystemConfigUpdateRequest request);
    
    /**
     * 删除系统配置
     */
    void deleteSystemConfig(Long id);
    
    /**
     * 批量删除系统配置
     */
    void deleteSystemConfigBatch(List<Long> ids);
    
    /**
     * 根据ID查询系统配置详情
     */
    SystemConfigResponse getSystemConfigDetail(Long id);
    
    /**
     * 分页查询系统配置列表
     */
    PageResult<SystemConfigResponse> getSystemConfigPageList(SystemConfigQueryRequest request);
    
    /**
     * 检查配置键是否存在
     */
    boolean checkConfigKeyExists(String namespace, String configKey, Long excludeId);
    
    /**
     * 根据命名空间和配置键获取配置值
     */
    String getConfigValue(String namespace, String configKey);
    
    /**
     * 根据命名空间和配置键获取配置值，如果不存在则返回默认值
     */
    String getConfigValue(String namespace, String configKey, String defaultValue);
    
    /**
     * 根据命名空间获取所有配置
     */
    List<SystemConfig> getConfigsByNamespace(String namespace);
    
    /**
     * 设置配置值（如果不存在则创建，存在则更新）
     */
    void setConfigValue(String namespace, String configKey, String configValue, String description);
}
