package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import java.util.List;

/**
 * 虚拟机器人服务接口
 * 用于模拟聊天记录中的买家消息，按客服回复分组并依次返回
 *
 * <AUTHOR> Assistant
 * @since 2025-08-19
 */
public interface FakeRobotService {

    /**
     * 生成虚拟机器人令牌并初始化聊天数据
     *
     * @param chatRecord 聊天记录字符串
     * @return 虚拟机器人令牌
     */
    String generateFakeRobotToken(String chatRecord);

    /**
     * 生成虚拟机器人令牌并初始化问答数据（重载方法）
     *
     * @param qsDtlList 问答简单数据列表
     * @param teamId
     * @param creator
     * @return 虚拟机器人令牌
     */
    String generateFakeRobotToken(List<QaSimpleDto> qsDtlList, Long teamId, String creator);

    /**
     * 获取下一个买家消息块
     *
     * @param fakeRobotToken 虚拟机器人令牌
     * @return JSON格式的响应，包含买家消息或结束提示
     */
    String getNextBuyerMessage(String fakeRobotToken);

    /**
     * 获取下一个买家消息对象（问答格式）
     *
     * @param fakeRobotToken 虚拟机器人令牌
     * @return QaSimplelDtoWithUUID对象，如果栈为空则返回null
     */
    QaSimplelDtoWithUUID getNextBuyerMessageAsQa(String fakeRobotToken);

    /**
     * 清除指定令牌的栈数据
     *
     * @param fakeRobotToken 虚拟机器人令牌
     */
    void clearSession(String fakeRobotToken);

    /**
     * 检查指定令牌是否还有消息
     *
     * @param fakeRobotToken 虚拟机器人令牌
     * @return 是否还有消息
     */
    boolean hasMoreMessages(String fakeRobotToken);
}
