package com.yiyi.ai_train_playground.service.task;

/**
 * 模板学习服务接口
 * 负责处理任务创建后的多线程模板学习功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-12
 */
public interface TemplateLearnService {
    
    /**
     * 启动模板学习（异步执行）
     * 在任务创建完成后调用，开始异步处理模板学习
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     */
    void startTemplateLearning(Long taskId, Long teamId);

    /**
     * 处理模板学习
     * 主要的学习处理逻辑，会被异步调用
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     */
    void processTemplateLearning(Long taskId, Long teamId);
}
