package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;

import java.util.List;

/**
 * 问答随机表 服务类
 * 这是一个整合中间表，特别好用
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
public interface TrainQaRdmService {

    /**
     * 根据UUID更新记录
     *
     * @param trainQaRdm 问答随机记录
     * @return 是否更新成功
     */
    boolean updateByUUID(TrainQaRdm trainQaRdm);

    /**
     * 根据UUID查询记录（不带团队ID过滤）
     *
     * @param uuid UUID
     * @return 问答随机记录
     */
    TrainQaRdm getByUUID(String uuid);

    /**
     * 根据UUID查询记录（带团队ID过滤）
     *
     * @param uuid UUID
     * @param teamId 团队ID
     * @return 问答随机记录
     */
    TrainQaRdm getByUUID(String uuid, Long teamId);

    /**
     * 分页查询问答随机记录
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param teamId 团队ID
     * @param quesNo 问题编号（可选）
     * @return 分页结果
     */
    List<TrainQaRdm> getPageList(int pageNum, int pageSize, Long teamId, String quesNo);

    /**
     * 查询总记录数
     *
     * @param teamId 团队ID
     * @param quesNo 问题编号（可选）
     * @return 总记录数
     */
    long countByCondition(Long teamId, String quesNo);

    /**
     * 根据团队ID查询所有记录
     *
     * @param teamId 团队ID
     * @return 问答随机记录列表
     */
    List<TrainQaRdm> getByTeamId(Long teamId);

    /**
     * 根据问题编号查询记录
     *
     * @param quesNo 问题编号
     * @param teamId 团队ID
     * @return 问答随机记录列表
     */
    List<TrainQaRdm> getByQuesNo(String quesNo, Long teamId);

    /**
     * 批量保存问答随机记录
     *
     * @param records 问答随机记录列表
     * @param teamId  团队ID
     * @param creator
     * @return 是否保存成功
     */
    boolean batchSave(List<TrainQaRdm> records, Long teamId, String creator);

    /**
     * 从QaSimplelDtoWithUUID列表创建TrainQaRdm记录
     *
     * @param dtoList QaSimplelDtoWithUUID列表
     * @param teamId 团队ID
     * @return TrainQaRdm记录列表
     */
    List<TrainQaRdm> createFromDtoList(List<QaSimplelDtoWithUUID> dtoList, Long teamId);

    /**
     * 保存QaSimplelDtoWithUUID列表到数据库
     *
     * @param dtoList QaSimplelDtoWithUUID列表
     * @param teamId  团队ID
     * @param creator
     * @return 是否保存成功
     */
    boolean saveDtoList(List<QaSimplelDtoWithUUID> dtoList, Long teamId, String creator);

    /**
     * 根据报告明细ID查询记录列表
     *
     * @param reportDtlId 报告明细ID
     * @param teamId      团队ID
     * @return 问答随机记录列表
     */
    List<TrainQaRdm> getByReportDtlId(Long reportDtlId, Long teamId);

    /**
     * 根据UUID删除记录
     *
     * @param uuid UUID
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteByUUID(String uuid, Long teamId);

    /**
     * 根据团队ID删除所有记录
     *
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteByTeamId(Long teamId);
}
