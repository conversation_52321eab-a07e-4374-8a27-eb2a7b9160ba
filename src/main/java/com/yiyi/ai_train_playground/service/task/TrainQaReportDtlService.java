package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl;

import java.util.List;

/**
 * 模拟聊天室报告明细表 服务类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
public interface TrainQaReportDtlService {

    /**
     * 创建记录
     *
     * @param record  记录对象
     * @param teamId  团队ID
     * @param creator 创建人
     * @return 是否创建成功
     */
    boolean create(TrainQaReportDtl record, Long teamId, String creator);

    /**
     * 根据ID查询记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 记录对象
     */
    TrainQaReportDtl getById(Long id, Long teamId);

    /**
     * 根据ID更新记录
     *
     * @param id      主键ID
     * @param record  记录对象
     * @param teamId  团队ID
     * @param updater 更新人
     * @return 是否更新成功
     */
    boolean updateById(Long id, TrainQaReportDtl record, Long teamId, String updater);

    /**
     * 根据ID删除记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id, Long teamId);

    /**
     * 根据sessionId查询记录
     *
     * @param sessionId 会话ID
     * @param teamId    团队ID
     * @return 记录对象
     */
    TrainQaReportDtl getBySessionId(String sessionId, Long teamId);

    /**
     * 根据taskId查询记录列表
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainQaReportDtl> getByTaskId(Long taskId, Long teamId);

    /**
     * 根据rpMainId查询记录列表
     *
     * @param rpMainId 聊天窗口主表ID
     * @param teamId   团队ID
     * @return 记录列表
     */
    List<TrainQaReportDtl> getByRpMainId(Long rpMainId, Long teamId);

    /**
     * 分页查询记录
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param teamId   团队ID
     * @param taskId   任务ID（可选）
     * @param qaMainId QA主表ID（可选）
     * @return 分页结果
     */
    List<TrainQaReportDtl> getPageList(int pageNum, int pageSize, Long teamId, Long taskId, Long qaMainId);

    /**
     * 查询总记录数
     *
     * @param teamId   团队ID
     * @param taskId   任务ID（可选）
     * @param qaMainId QA主表ID（可选）
     * @return 总记录数
     */
    long countByCondition(Long teamId, Long taskId, Long qaMainId);

    /**
     * 根据团队ID查询所有记录
     *
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainQaReportDtl> getByTeamId(Long teamId);

    /**
     * 批量创建记录
     *
     * @param records 记录列表
     * @param teamId  团队ID
     * @param creator 创建人
     * @return 是否创建成功
     */
    boolean batchCreate(List<TrainQaReportDtl> records, Long teamId, String creator);

    /**
     * 根据团队ID删除所有记录
     *
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteByTeamId(Long teamId);

    /**
     * 根据taskId删除记录
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteByTaskId(Long taskId, Long teamId);

    /**
     * 检查sessionId是否存在
     *
     * @param sessionId 会话ID
     * @param teamId    团队ID
     * @return 是否存在
     */
    boolean existsBySessionId(String sessionId, Long teamId);

    /**
     * 根据多个条件创建记录（便捷方法）
     *
     * @param rpMainId  聊天窗口主表ID
     * @param taskId    任务ID
     * @param sessionId 会话ID
     * @param qaMainId  QA主表ID
     * @param teamId    团队ID
     * @param creator   创建人
     * @return 是否创建成功
     */
    boolean createByConditions(Long rpMainId, Long taskId, String sessionId, Long qaMainId, Long teamId, String creator);
}
