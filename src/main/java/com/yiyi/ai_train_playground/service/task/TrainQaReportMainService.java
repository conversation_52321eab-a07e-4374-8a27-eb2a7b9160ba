package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportMain;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.ShowExamResultDTO;
import com.yiyi.ai_train_playground.dto.task.TrainQaReportMainQueryRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 问答报告主表服务接口
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
public interface TrainQaReportMainService {

    /**
     * 创建记录
     *
     * @param record 记录对象
     * @param teamId 团队ID
     * @return 创建的记录
     */
    TrainQaReportMain create(TrainQaReportMain record, Long teamId);

    /**
     * 根据ID查询记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 记录对象
     */
    TrainQaReportMain getById(Long id, Long teamId);

    /**
     * 根据ID更新记录
     *
     * @param record 记录对象
     * @param teamId 团队ID
     * @return 是否更新成功
     */
    boolean updateById(TrainQaReportMain record, Long teamId);

    /**
     * 根据ID删除记录
     *
     * @param id     主键ID
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id, Long teamId);

    /**
     * 分页查询记录
     *
     * @param request 查询请求参数
     * @param teamId  团队ID
     * @return 分页结果
     */
    PageResult<TrainQaReportMain> getPageList(TrainQaReportMainQueryRequest request, Long teamId);

    /**
     * 根据聊天室ID查询记录列表
     *
     * @param chatroomId 聊天室ID
     * @param teamId     团队ID
     * @return 记录列表
     */
    List<TrainQaReportMain> getByChatroomId(Long chatroomId, Long teamId);

    /**
     * 根据员工ID查询记录列表
     *
     * @param staffId 员工ID
     * @param teamId  团队ID
     * @return 记录列表
     */
    List<TrainQaReportMain> getByStaffId(Long staffId, Long teamId);

    /**
     * 根据团队ID查询所有记录
     *
     * @param teamId 团队ID
     * @return 记录列表
     */
    List<TrainQaReportMain> getByTeamId(Long teamId);

    /**
     * 批量创建记录
     *
     * @param records 记录列表
     * @param teamId  团队ID
     * @param creator 创建人
     * @return 是否创建成功
     */
    boolean batchCreate(List<TrainQaReportMain> records, Long teamId, String creator);

    /**
     * 根据团队ID删除所有记录
     *
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteByTeamId(Long teamId);

    /**
     * 根据条件统计记录数
     *
     * @param request 查询请求参数
     * @param teamId  团队ID
     * @return 记录数
     */
    long countByCondition(TrainQaReportMainQueryRequest request, Long teamId);

    /**
     * 展示考试结果
     * 根据报告主记录ID查询完整的考试结果，包括答题记录和计算总分
     *
     * @param qaReportMainId 报告主记录ID
     * @param teamId 团队ID
     * @param isNeedUpdate 是否需要更新分数，默认为true
     * @return 考试结果DTO
     */
    ShowExamResultDTO showExamResult(Long qaReportMainId, Long teamId, Boolean isNeedUpdate);

    /**
     * 根据聊天室ID分页查询接待详情
     *
     * @param chatroomId 聊天室ID
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<TrainQaReportMain> getReceptionDetailsByChatroomId(Long chatroomId, TrainQaReportMainQueryRequest request, Long teamId);

    /**
     * 更新考试分数
     *
     * @param id 主记录ID
     * @param examScore 考试分数
     * @param teamId 团队ID
     * @return 是否更新成功
     */
    boolean updateExamScore(Long id, BigDecimal examScore, Long teamId);

    /**
     * 导出Excel
     * 查询逻辑和getPageList一样，只是不要分页，限制10万条以内
     *
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return Excel字节数组
     */
    byte[] exportExcel(TrainQaReportMainQueryRequest request, Long teamId);
}
