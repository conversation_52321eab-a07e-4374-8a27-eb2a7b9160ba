package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.*;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;

import java.util.List;

/**
 * 接待聊天室服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
public interface TrainReceptionChatroomService {
    
    /**
     * 创建聊天室及关联任务
     *
     * @param request 创建请求
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 聊天室ID
     */
    Long createChatroomWithTasks(ChatroomCreateRequest request, Long teamId, String creator);
    
    /**
     * 根据ID查询聊天室详情
     *
     * @param id 聊天室ID
     * @param teamId 团队ID
     * @return 聊天室详情
     */
    ChatroomDetailDTO getChatroomDetail(Long id, Long teamId);
    
    /**
     * 分页查询聊天室列表
     *
     * @param queryRequest 查询请求
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<ChatroomListDTO> getChatroomList(ChatroomQueryRequest queryRequest, Long teamId);
    
    /**
     * 更新聊天室及关联任务
     *
     * @param request 更新请求
     * @param teamId 团队ID
     * @param updater 更新人
     * @return 是否成功
     */
    boolean updateChatroomWithTasks(ChatroomUpdateRequest request, Long teamId, String updater);
    
    /**
     * 删除聊天室
     *
     * @param id 聊天室ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deleteChatroom(Long id, Long teamId);
    
    /**
     * 批量删除聊天室
     *
     * @param ids ID列表，逗号分隔
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean batchDeleteChatrooms(String ids, Long teamId);
    
    /**
     * 查询员工的接待聊天任务列表（分页）
     *
     * @param queryRequest 查询条件
     * @param staffId 员工ID
     * @param teamId 团队ID
     * @return 员工的任务分页列表
     */
    PageResult<ChatroomListDTO> getMyTasks(ChatroomQueryRequest queryRequest, Long staffId, Long teamId);

    /**
     * 验证员工是否有权限访问指定聊天室
     *
     * @param receChatRoomId 接待聊天室ID
     * @param staffId 员工ID
     * @return 员工信息，如果无权限则返回null
     */
    TrainStaff validateStaffPermission(Long receChatRoomId, Long staffId);

    /**
     * 根据聊天室ID查询剧本任务列表
     *
     * @param receChatRoomId 接待聊天室ID
     * @return 剧本任务列表
     */
    List<ChatRoomTaskInfo> getChatRoomTaskList(Long receChatRoomId);

    /**
     * 根据聊天室ID查询问答任务列表（专用于问答场景）
     *
     * @param receChatRoomId 接待聊天室ID
     * @return 问答任务列表
     */
    List<ChatRoomTaskInfo> getCrTL4Qa(Long receChatRoomId);

    /**
     * 检查聊天室任务是否可用
     *
     * @param chatroomId 聊天室ID
     * @param teamId 团队ID
     * @return 不可用的任务列表，空表示可用
     */
    List<TaskAvailableCheckDTO> checkTasksAvailability(Long chatroomId, Long teamId);
}