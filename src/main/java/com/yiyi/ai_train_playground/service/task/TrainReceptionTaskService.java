package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskCreateRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskDetailDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskListDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskQueryRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskUpdateRequest;
import com.yiyi.ai_train_playground.entity.task.TrainReceptionTask;

/**
 * 接待任务服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
public interface TrainReceptionTaskService {
    
    /**
     * 分页查询接待任务列表
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<TrainReceptionTaskListDTO> getTaskList(TrainReceptionTaskQueryRequest request, Long teamId);
    
    /**
     * 根据ID查询接待任务详情
     * 
     * @param id 任务ID
     * @param teamId 团队ID
     * @return 任务详情
     */
    TrainReceptionTaskDetailDTO getTaskDetail(Long id, Long teamId);
    
    /**
     * 根据ID查询接待任务
     * 
     * @param id 任务ID
     * @param teamId 团队ID
     * @return 任务信息
     */
    TrainReceptionTask getTaskById(Long id, Long teamId);
    
    /**
     * 创建接待任务
     * 
     * @param request 创建请求
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 任务ID
     */
    Long createTask(TrainReceptionTaskCreateRequest request, Long teamId, String creator);
    
    /**
     * 更新接待任务
     *
     * @param request 更新请求
     * @param teamId 团队ID
     * @param updater 更新者
     * @return 是否成功
     */
    boolean updateTask(TrainReceptionTaskUpdateRequest request, Long teamId, String updater);
    
    /**
     * 更新任务的待学习数量
     *
     * @param taskId         任务ID
     * @param amtToBeLearned 待学习数量
     * @param teamId
     * @return 是否成功
     */
    boolean updateTask(Long taskId, Long amtToBeLearned, Long teamId);
    
    /**
     * 删除接待任务
     *
     * @param id 任务ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deleteTask(Long id, Long teamId);

    /**
     * 批量删除接待任务
     *
     * @param ids 任务ID列表（逗号分隔）
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean batchDeleteTasks(String ids, Long teamId);
}
