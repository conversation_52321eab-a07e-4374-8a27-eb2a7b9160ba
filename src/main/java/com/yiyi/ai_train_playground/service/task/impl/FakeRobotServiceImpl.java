package com.yiyi.ai_train_playground.service.task.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.consts.CONSTS;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.dto.task.BuyerBlock;
import com.yiyi.ai_train_playground.dto.task.ChatLine;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import com.yiyi.ai_train_playground.service.task.FakeRobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 虚拟机器人服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-19
 */
@Slf4j
@Service
public class FakeRobotServiceImpl implements FakeRobotService {
    
    private static final String STACK_KEY_PREFIX = "fake_robot_stack:";
    private static final String SESSION_KEY_PREFIX = "fake_robot_session:";
    private static final String INIT_FLAG_PREFIX = "fake_robot_init:";
    private static final long STACK_EXPIRE_HOURS = 24; // 栈数据24小时过期
    
    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;

    @Autowired
    private SuperBigModelInterface doubaoBigModelService;
    
    @Override
    public String generateFakeRobotToken(String chatRecord) {
        try {
            // 生成唯一的令牌
            String fakeRobotToken = generateUniqueToken(chatRecord);
            String stackKey = STACK_KEY_PREFIX + fakeRobotToken;
            String initFlagKey = INIT_FLAG_PREFIX + fakeRobotToken;

            // 解析聊天记录并初始化栈
            initializeStack(chatRecord, stackKey);

            // 设置初始化标志
            cacheManager.put(initFlagKey, "1", STACK_EXPIRE_HOURS, TimeUnit.HOURS);

            return fakeRobotToken;
        } catch (Exception e) {
            log.error("生成虚拟机器人令牌失败", e);
            throw new RuntimeException("生成令牌失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String generateFakeRobotToken(List<QaSimpleDto> qsDtlList, Long teamId, String creator) {
        // FakeRobotServiceImpl不支持问答列表方式，建议使用Qa2StackServiceImpl
        log.warn("FakeRobotServiceImpl不支持List<QaSimpleDto>参数，建议使用Qa2StackServiceImpl");
        throw new UnsupportedOperationException("FakeRobotServiceImpl不支持List<QaSimpleDto>参数，建议使用Qa2StackServiceImpl");
    }

    @Override
    public String getNextBuyerMessage(String fakeRobotToken) {
        try {
            String stackKey = STACK_KEY_PREFIX + fakeRobotToken;
            String initFlagKey = INIT_FLAG_PREFIX + fakeRobotToken;

            // 检查令牌是否有效
            if (!cacheManager.exists(initFlagKey)) {
                return createErrorMessage("无效的令牌或令牌已过期");
            }

            // 从栈中弹出一个买家消息块
            Object blockObj = cacheManager.leftPop(stackKey);

            if (blockObj == null) {
                // 栈为空，返回结束消息
                return createEndMessage();
            }

            // 反序列化买家消息块
            BuyerBlock block = objectMapper.convertValue(blockObj, BuyerBlock.class);

            // 格式化为JSON响应
            return formatBuyerBlockResponse(block);

        } catch (Exception e) {
            log.error("获取下一个买家消息失败", e);
            return createErrorMessage("处理聊天记录时发生错误: " + e.getMessage());
        }
    }
    
    @Override
    public void clearSession(String fakeRobotToken) {
        try {
            String stackKey = STACK_KEY_PREFIX + fakeRobotToken;
            String initFlagKey = INIT_FLAG_PREFIX + fakeRobotToken;
            cacheManager.delete(stackKey);
            cacheManager.delete(initFlagKey);
            log.info("已清除会话栈数据: fakeRobotToken={}", fakeRobotToken);
        } catch (Exception e) {
            log.error("清除会话栈数据失败: fakeRobotToken={}", fakeRobotToken, e);
        }
    }

    @Override
    public boolean hasMoreMessages(String fakeRobotToken) {
        try {
            String stackKey = STACK_KEY_PREFIX + fakeRobotToken;
            return cacheManager.getListSize(stackKey) > 0;
        } catch (Exception e) {
            log.error("检查会话消息状态失败: fakeRobotToken={}", fakeRobotToken, e);
            return false;
        }
    }
    
    /**
     * 生成唯一的虚拟机器人令牌
     * 使用UUID确保每次调用都生成不同的令牌，避免重复
     */
    private String generateUniqueToken(String chatRecord) {
        // 使用当前时间戳和随机UUID确保唯一性
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomUuid = UUID.randomUUID().toString();

        if (chatRecord == null) {
            return "null_token_" + timestamp + "_" + randomUuid;
        }

        // 结合时间戳和随机UUID生成唯一令牌
        return "token_" + timestamp + "_" + randomUuid;
    }
    
    /**
     * 初始化栈数据
     */
    private void initializeStack(String chatRecord, String stackKey) {
        try {
            // 解析聊天记录
            List<ChatLine> chatLines = parseChatRecord(chatRecord);

            if (chatLines.isEmpty()) {
                log.warn("聊天记录为空，无法初始化栈");
                return;
            }

            // 使用大模型识别客户和客服姓名，并重新标记聊天记录
            chatLines = identifyCustomerAndServicer(chatLines);

            // 提取买家消息块
            List<BuyerBlock> buyerBlocks = extractBuyerBlocks(chatLines);

            // 将买家消息块推入队列（按时间顺序推入，从右侧推入，从左侧弹出）
            for (int i = 0; i < buyerBlocks.size(); i++) {
                BuyerBlock block = buyerBlocks.get(i);
                cacheManager.rightPush(stackKey, block);
            }

            // 设置过期时间
            cacheManager.expire(stackKey, STACK_EXPIRE_HOURS, TimeUnit.HOURS);

            log.info("成功初始化栈数据: stackKey={}, blockCount={}", stackKey, buyerBlocks.size());

        } catch (Exception e) {
            log.error("初始化栈数据失败: stackKey={}", stackKey, e);
            throw new RuntimeException("初始化栈数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析聊天记录
     * 使用正则表达式准确识别发送者名称（时间前面的所有内容都视为发送者名称）
     */
    private List<ChatLine> parseChatRecord(String chatRecord) {
        List<ChatLine> chatLines = new ArrayList<>();

        if (chatRecord == null || chatRecord.trim().isEmpty()) {
            return chatLines;
        }

        String[] lines = chatRecord.split("\n");
        String buyerName = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 正则表达式：匹配时间前面的发送者名称
        // 格式：[发送者名称] [yyyy-MM-dd] [HH:mm:ss]
        Pattern messageHeaderPattern = Pattern.compile("^(.+?)\\s+(\\d{4}-\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2})$");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.isEmpty()) {
                continue;
            }

            // 使用正则表达式解析消息头
            Matcher matcher = messageHeaderPattern.matcher(line);
            if (matcher.matches()) {
                String sender = matcher.group(1).trim(); // 发送者名称（去除首尾空格）
                String dateStr = matcher.group(2);       // 日期部分
                String timeStr = matcher.group(3);       // 时间部分
                String fullTimeStr = dateStr + " " + timeStr; // 完整时间字符串

                try {
                    // 验证时间格式
                    LocalDateTime.parse(fullTimeStr, formatter);

                    // 第一个发送人是买家（这里先暂时保留，后续会被大模型识别覆盖）
                    if (buyerName == null) {
                        buyerName = sender;
                    }

                    // 收集所有连续的内容行（多行消息支持）
                    List<String> contentLines = new ArrayList<>();
                    int nextIndex = i + 1;

                    while (nextIndex < lines.length) {
                        String nextLine = lines[nextIndex].trim();
                        if (nextLine.isEmpty()) {
                            nextIndex++;
                            continue;
                        }

                        // 如果下一行是消息头格式，则停止收集内容
                        if (isMessageHeader(nextLine)) {
                            break;
                        }

                        // 否则作为内容行收集
                        contentLines.add(nextLine);
                        nextIndex++;
                    }

                    // 将多行内容合并为单个字符串
                    String content = String.join("\n", contentLines);

                    ChatLine chatLine = new ChatLine();
                    chatLine.setSender(sender);
                    chatLine.setSendTime(fullTimeStr);
                    chatLine.setContent(content);
                    chatLine.setBuyer(sender.equals(buyerName)); // 临时标记，后续会被大模型识别结果覆盖
                    chatLine.setLineNumber(i);

                    chatLines.add(chatLine);

                    // 跳过已处理的内容行
                    i = nextIndex - 1;

                } catch (Exception e) {
                    log.debug("解析聊天行失败，跳过: line={}, error={}", line, e.getMessage());
                }
            } else {
                log.debug("行格式不匹配消息头模式，跳过: {}", line);
            }
        }

        log.info("解析聊天记录完成: totalLines={}, buyerName={}", chatLines.size(), buyerName);
        return chatLines;
    }
    
    /**
     * 检查是否是消息头格式
     * 使用正则表达式检查格式：[发送者名称] [yyyy-MM-dd] [HH:mm:ss]
     */
    private boolean isMessageHeader(String line) {
        // 使用与parseChatRecord相同的正则表达式
        Pattern messageHeaderPattern = Pattern.compile("^(.+?)\\s+(\\d{4}-\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2})$");
        return messageHeaderPattern.matcher(line).matches();
    }

    /**
     * 提取买家消息块
     */
    private List<BuyerBlock> extractBuyerBlocks(List<ChatLine> chatLines) {
        List<BuyerBlock> blocks = new ArrayList<>();
        List<ChatLine> currentBlock = new ArrayList<>();
        String buyerName = null;

        for (ChatLine line : chatLines) {
            if (line.isBuyer()) {
                // 买家消息
                if (buyerName == null) {
                    buyerName = line.getSender();
                }
                currentBlock.add(line);
            } else {
                // 客服消息，结束当前块
                if (!currentBlock.isEmpty()) {
                    BuyerBlock block = new BuyerBlock();
                    block.setBuyerName(buyerName);
                    block.setMessages(new ArrayList<>(currentBlock));
                    block.setBlockIndex(blocks.size());
                    blocks.add(block);

                    currentBlock.clear();
                }
            }
        }

        // 处理最后一个块
        if (!currentBlock.isEmpty()) {
            BuyerBlock block = new BuyerBlock();
            block.setBuyerName(buyerName);
            block.setMessages(new ArrayList<>(currentBlock));
            block.setBlockIndex(blocks.size());
            blocks.add(block);
        }

        log.info("提取买家消息块完成: blockCount={}, buyerName={}", blocks.size(), buyerName);
        return blocks;
    }

    /**
     * 格式化买家消息块响应
     */
    private String formatBuyerBlockResponse(BuyerBlock block) {
        try {
            List<String> contents = new ArrayList<>();

            for (ChatLine message : block.getMessages()) {
                if (message.getContent() != null && !message.getContent().trim().isEmpty()) {
                    contents.add(message.getContent().trim());
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("result", contents);

            return objectMapper.writeValueAsString(response);

        } catch (Exception e) {
            log.error("格式化买家消息块响应失败", e);
            return createErrorMessage("格式化响应失败: " + e.getMessage());
        }
    }

    /**
     * 创建结束消息
     */
    private String createEndMessage() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("result", Arrays.asList("已无更多问题，会话结束"));
            return objectMapper.writeValueAsString(response);
        } catch (Exception e) {
            log.error("创建结束消息失败", e);
            return "{\"result\":[\"已无更多问题，会话结束\"]}";
        }
    }

    /**
     * 使用大模型识别客户和客服姓名，并重新标记聊天记录
     */
    private List<ChatLine> identifyCustomerAndServicer(List<ChatLine> chatLines) {
        try {
            // 1. 先从聊天记录中筛选出两个用户名
            Set<String> senderNames = new HashSet<>();
            for (ChatLine line : chatLines) {
                senderNames.add(line.getSender());
            }

            if (senderNames.size() < 2) {
                log.warn("聊天记录中发送人数量不足2个，无法进行客户客服识别，使用原有逻辑");
                return chatLines;
            }

            // 转换为数组并构建输入字符串
            String[] names = senderNames.toArray(new String[0]);
            StringBuilder inputSb = new StringBuilder();
            for (String name : names) {
                inputSb.append(name).append("/");
            }
            // 去掉最后一个/
            if (inputSb.length() > 0 && inputSb.charAt(inputSb.length() - 1) == '/') {
                inputSb.setLength(inputSb.length() - 1);
            }

            log.info("从聊天记录中提取到的用户名: inputString={}", inputSb.toString());

            // 2. 调用大模型识别
            // 2.1 获取系统提示词
            List<String> sysPmtOrig = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_GET_CUSTOMER_NAME_KEYWORD);
            if (sysPmtOrig.isEmpty()) {
                log.warn("未找到客户识别提示词，使用原有逻辑");
                return chatLines;
            }

            String sysPmt = sysPmtOrig.get(0);

            // 2.2 替换变量
            sysPmt = sysPmt.replace("{{input_string}}", inputSb.toString());

            log.info("调用大模型识别客户和客服，系统提示词: {}", sysPmt);

            // 2.3 调用大模型
            String result = doubaoBigModelService.ntnsOnce(sysPmt, "");

            log.info("大模型识别结果: {}", result);

            // 2.4 解析结果 - 支持新的JSON格式：{"customerName": "一位爱赶时髦的大叔"}
            String customerName;
            try {
                // 尝试解析新格式的JSON响应
                Map<String, Object> jsonResult = objectMapper.readValue(result, Map.class);
                customerName = (String) jsonResult.get("customerName");
                
                if (customerName == null || customerName.trim().isEmpty()) {
                    throw new Exception("customerName字段为空");
                }
                
                log.info("成功解析大模型返回的客户姓名: {}", customerName);
                
            } catch (Exception e) {
                log.warn("解析大模型JSON格式失败，使用降级策略: {}", e.getMessage());
                // 降级策略：取inputSb中第一个/前面的名称
                String inputString = inputSb.toString();
                int firstSlashIndex = inputString.indexOf("/");
                if (firstSlashIndex > 0) {
                    customerName = inputString.substring(0, firstSlashIndex);
                    log.info("使用降级策略获取客户姓名: {}", customerName);
                } else {
                    // 如果没有/，就取第一个名字
                    customerName = names[0];
                    log.info("使用第一个名字作为客户姓名: {}", customerName);
                }
            }

            // 2.5 重新标记聊天记录
            for (ChatLine line : chatLines) {
                boolean isBuyer = line.getSender().equals(customerName);
                line.setBuyer(isBuyer);
            }

            log.info("重新标记聊天记录完成，客户姓名: {}, 客户消息数: {}",
                    customerName, chatLines.stream().mapToInt(line -> line.isBuyer() ? 1 : 0).sum());

            return chatLines;

        } catch (Exception e) {
            log.error("大模型识别客户和客服失败，使用原有逻辑", e);
            return chatLines;
        }
    }

    @Override
    public QaSimplelDtoWithUUID getNextBuyerMessageAsQa(String fakeRobotToken) {
        // FakeRobotServiceImpl不支持问答格式返回，建议使用Qa2StackServiceImpl
        log.warn("FakeRobotServiceImpl不支持QA格式返回，建议使用Qa2StackServiceImpl");
        return null;
    }

    /**
     * 创建错误消息
     */
    private String createErrorMessage(String errorMsg) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("result", Arrays.asList("处理失败: " + errorMsg));
            return objectMapper.writeValueAsString(response);
        } catch (Exception e) {
            log.error("创建错误消息失败", e);
            return "{\"result\":[\"处理失败\"]}";
        }
    }
}
