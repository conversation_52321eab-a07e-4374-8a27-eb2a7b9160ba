package com.yiyi.ai_train_playground.service.task.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportDtlService;
import com.yiyi.ai_train_playground.service.task.FakeRobotService;
import com.yiyi.ai_train_playground.service.task.TrainQaRdmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 问答栈式机器人服务实现类
 * 基于问答知识库实现栈式消息返回
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-27
 */
@Slf4j
@Service
public class Qa2StackServiceImpl implements FakeRobotService {
    
    private static final String QA_STACK_KEY_PREFIX = "qa_robot_stack:";
    private static final String QA_INIT_FLAG_PREFIX = "qa_robot_init:";
    private static final long STACK_EXPIRE_HOURS = 24; // 栈数据24小时过期
    
    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private TrainQaImportDtlService trainQaImportDtlService;

    @Autowired
    private TrainQaRdmService trainQaRdmService;

    @Override
    public String generateFakeRobotToken(String chatRecord) {
        // 这个方法保留原有接口，但在Qa2StackServiceImpl中不使用
        log.warn("Qa2StackServiceImpl不支持通过chatRecord生成token，请使用List<QaSimpleDto>重载方法");
        throw new UnsupportedOperationException("Qa2StackServiceImpl不支持通过chatRecord生成token");
    }

    @Override
    public String generateFakeRobotToken(List<QaSimpleDto> qsDtlList, Long teamId, String creator) {
        try {
            log.info("开始生成问答栈式机器人令牌，问答数量：{}", qsDtlList.size());
            
            // 生成唯一的令牌
            String fakeRobotToken = generateUniqueToken();
            String stackKey = QA_STACK_KEY_PREFIX + fakeRobotToken;
            String initFlagKey = QA_INIT_FLAG_PREFIX + fakeRobotToken;

            // 对问答列表进行加工，为每行数据添加UUID和新字段
            List<QaSimplelDtoWithUUID> dtlWithUUIDList = new ArrayList<>();
            for (int i = 0; i < qsDtlList.size(); i++) {
                QaSimpleDto qaDto = qsDtlList.get(i);
                String uuid = UUID.randomUUID().toString();
                String quesNo = (i + 1) + "/" + qsDtlList.size(); // 生成问题编号：1/50, 2/50, ...

                QaSimplelDtoWithUUID dtoWithUUID = new QaSimplelDtoWithUUID(
                    uuid,
                    qaDto.getQuestion(),
                    qaDto.getAnswer(),
                    null, // actualQuestion - 初始为空
                    null, // actualAnswer - 初始为空
                    null, // resolve - 初始为空
                    quesNo // quesNo - 问题编号
                );
                dtlWithUUIDList.add(dtoWithUUID);
            }

            // 保存到数据库
            try {
//                Long teamId = SecurityUtil.getCurrentTeamId();
                boolean saveResult = trainQaRdmService.saveDtoList(dtlWithUUIDList, teamId,creator);
                if (saveResult) {
                    log.info("问答数据保存到数据库成功，teamId={}, 保存记录数={}", teamId, dtlWithUUIDList.size());
                } else {
                    log.warn("问答数据保存到数据库失败，teamId={}", teamId);
                }
            } catch (Exception e) {
                log.error("保存问答数据到数据库异常", e);
                // 不影响主流程，继续执行
            }

            // 将问答数据入栈（从右侧插入，左侧弹出，实现先进后出）
            for (QaSimplelDtoWithUUID dto : dtlWithUUIDList) {
                cacheManager.rightPush(stackKey, dto);
            }
            
            // 设置栈的过期时间
            cacheManager.expire(stackKey, STACK_EXPIRE_HOURS, TimeUnit.HOURS);

            // 设置初始化标志
            cacheManager.put(initFlagKey, "1", STACK_EXPIRE_HOURS, TimeUnit.HOURS);

            log.info("问答栈式机器人令牌生成成功，token：{}，入栈数量：{}", fakeRobotToken, dtlWithUUIDList.size());
            return fakeRobotToken;
            
        } catch (Exception e) {
            log.error("生成问答栈式机器人令牌失败", e);
            throw new RuntimeException("生成令牌失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getNextBuyerMessage(String fakeRobotToken) {
        try {
            // 调用新的方法获取QA对象
            QaSimplelDtoWithUUID qaDto = getNextBuyerMessageAsQa(fakeRobotToken);
            
            if (qaDto == null) {
                // 栈为空，返回结束消息
                log.info("问答栈为空，会话结束：{}", fakeRobotToken);
                return "所有问答已处理完毕";
            }

            // 直接返回原格式，不进行JSON格式化
            String result = "问题：" + qaDto.getQuestion() + "\n答案：" + qaDto.getAnswer() + "\nUUID：" + qaDto.getUuid();
            
            log.info("成功获取下一个问答消息，token：{}，UUID：{}", fakeRobotToken, qaDto.getUuid());
            return result;

        } catch (Exception e) {
            log.error("获取下一个问答消息失败，token：{}", fakeRobotToken, e);
            return "处理问答时发生错误: " + e.getMessage();
        }
    }

    @Override
    public QaSimplelDtoWithUUID getNextBuyerMessageAsQa(String fakeRobotToken) {
        try {
            String stackKey = QA_STACK_KEY_PREFIX + fakeRobotToken;
            String initFlagKey = QA_INIT_FLAG_PREFIX + fakeRobotToken;

            // 检查令牌是否有效
            if (!cacheManager.exists(initFlagKey)) {
                log.warn("无效的令牌或令牌已过期：{}", fakeRobotToken);
                return null;
            }

            // 从栈中弹出一个问答对象
            Object qaObj = cacheManager.leftPop(stackKey);

            if (qaObj == null) {
                // 栈为空，返回null
                log.info("问答栈为空，会话结束：{}", fakeRobotToken);
                return null;
            }

            // 反序列化问答对象
            QaSimplelDtoWithUUID qaDto = objectMapper.convertValue(qaObj, QaSimplelDtoWithUUID.class);
            
            log.info("成功获取下一个问答对象，token：{}，UUID：{}", fakeRobotToken, qaDto.getUuid());
            return qaDto;

        } catch (Exception e) {
            log.error("获取下一个问答对象失败，token：{}", fakeRobotToken, e);
            return null;
        }
    }

    @Override
    public void clearSession(String fakeRobotToken) {
        try {
            String stackKey = QA_STACK_KEY_PREFIX + fakeRobotToken;
            String initFlagKey = QA_INIT_FLAG_PREFIX + fakeRobotToken;
            cacheManager.delete(stackKey);
            cacheManager.delete(initFlagKey);
            log.info("已清除问答会话栈数据：{}", fakeRobotToken);
        } catch (Exception e) {
            log.error("清除问答会话栈数据失败：{}", fakeRobotToken, e);
        }
    }

    @Override
    public boolean hasMoreMessages(String fakeRobotToken) {
        try {
            String stackKey = QA_STACK_KEY_PREFIX + fakeRobotToken;
            boolean hasMore = cacheManager.getListSize(stackKey) > 0;
            log.debug("检查问答会话消息状态，token：{}，还有消息：{}", fakeRobotToken, hasMore);
            return hasMore;
        } catch (Exception e) {
            log.error("检查问答会话消息状态失败：{}", fakeRobotToken, e);
            return false;
        }
    }

    /**
     * 生成唯一的问答栈式机器人令牌
     */
    private String generateUniqueToken() {
        // 使用当前时间戳和随机UUID确保唯一性
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomUuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "qa_token_" + timestamp + "_" + randomUuid;
    }
}