package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.consts.CONSTS;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.task.TaskLearningDTO;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.mapper.converkb.TrainTaskConvKbDtlMapper;
import com.yiyi.ai_train_playground.mapper.task.TrainReceptionTaskMapper;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.impl.DoubaoBigModelServiceImpl;
import com.yiyi.ai_train_playground.service.impl.TrainScriptServiceImpl;
import com.yiyi.ai_train_playground.service.task.TemplateLearnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 模板学习服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-12
 */
@Slf4j
@Service
public class TemplateLearnServiceImpl implements TemplateLearnService {
    
    private static final int BATCH_SIZE = 20; // 每批处理20条记录
    private static final String SYSTEM_USER = "system"; // 系统用户
    
    @Autowired
    private TrainTaskConvKbDtlMapper trainTaskConvKbDtlMapper;
    
    @Autowired
    private TrainReceptionTaskMapper trainReceptionTaskMapper;
    
    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;
    
    @Autowired
    private DoubaoBigModelServiceImpl doubaoBigModelServiceImpl;
    
    @Autowired
    @Qualifier("templateLearningExecutor")
    private Executor templateLearningExecutor;

    @Autowired
    private TrainScriptServiceImpl trainScriptService;
    
    @Override
    @Async("templateLearningExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void startTemplateLearning(Long taskId, Long teamId) {
        log.info("开始异步模板学习，taskId: {}, teamId: {}", taskId, teamId);

        try {
            processTemplateLearning(taskId, teamId);
            log.info("模板学习完成，taskId: {}", taskId);
        } catch (Exception e) {
            log.error("模板学习失败，taskId: {}", taskId, e);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processTemplateLearning(Long taskId, Long teamId) {
        log.info("开始处理模板学习，taskId: {}, teamId: {}", taskId, teamId);

        try {
            // 第一阶段：总 - 更新主表状态为LEARNING
//            updateTaskStatusToLearning(taskId, teamId);

            // 第二阶段：查询该taskId下所有UN_LEARN状态的明细
            List<TaskLearningDTO> unlearnedTasks = getUnlearnedTasksByTaskId(taskId);

            if (unlearnedTasks == null || unlearnedTasks.isEmpty()) {
                log.info("任务 {} 没有未学习的明细，直接更新状态为已学习", taskId);
                updateTaskStatusToLearned(taskId, teamId);
                return;
            }

            log.info("任务 {} 查询到 {} 条未学习的明细，开始处理", taskId, unlearnedTasks.size());

            // 第三阶段：分 - 多线程处理明细
            processTaskDetails(unlearnedTasks);

            // 第四阶段：总 - 更新主表状态为LEARNED
            updateTaskStatusToLearned(taskId, teamId);

            log.info("任务 {} 模板学习处理完成，总共处理 {} 条记录", taskId, unlearnedTasks.size());

        } catch (Exception e) {
            log.error("任务 {} 模板学习处理失败", taskId, e);
            throw e; // 重新抛出异常以便事务回滚
        }
    }

    /**
     * 查询指定任务下未学习的明细
     *
     * @param taskId 任务ID
     * @return 未学习的任务明细列表
     */
    private List<TaskLearningDTO> getUnlearnedTasksByTaskId(Long taskId) {
        try {
            List<TaskLearningDTO> unlearnedTasks = trainTaskConvKbDtlMapper.selectUnlearnedTaskDetailsByTaskId(taskId);
            log.debug("查询任务 {} 的未学习明细，共 {} 条", taskId, unlearnedTasks != null ? unlearnedTasks.size() : 0);
            return unlearnedTasks;
        } catch (Exception e) {
            log.error("查询任务 {} 的未学习明细失败", taskId, e);
            throw e;
        }
    }

    /**
     * 多线程处理任务明细（明细处理并行，主表更新串行）
     *
     * @param unlearnedTasks 未学习的任务明细列表
     */
    private void processTaskDetails(List<TaskLearningDTO> unlearnedTasks) {
        log.info("开始多线程处理 {} 条明细", unlearnedTasks.size());

        // 第一步：并行处理所有明细的复杂业务逻辑
        CompletableFuture<Boolean>[] futures = unlearnedTasks.stream()
            .map(task -> CompletableFuture.supplyAsync(() -> {
                try {
                    return processSingleLearningWithoutTaskUpdate(task);
                } catch (Exception e) {
                    log.error("处理明细失败，id: {}, taskId: {}", task.getId(), task.getTaskId(), e);
                    return false;
                }
            }, templateLearningExecutor))
            .toArray(CompletableFuture[]::new);

        // 等待所有明细处理完成
        try {
            CompletableFuture.allOf(futures).join();
            log.info("所有明细业务逻辑处理完成");

            // 第二步：串行更新主表计数（避免锁冲突）
            int successCount = 0;
            for (int i = 0; i < futures.length; i++) {
                if (futures[i].get()) {
                    successCount++;
                }
            }

            // 一次性更新主表的已学习数量
            if (successCount > 0) {
                updateTaskAmtHasLearnedBatch(unlearnedTasks.get(0).getTaskId(), successCount);
                log.info("批量更新主表成功，taskId: {}, 成功处理: {} 条",
                    unlearnedTasks.get(0).getTaskId(), successCount);
            }

        } catch (Exception e) {
            log.error("明细处理过程中发生异常", e);
            throw new RuntimeException("明细处理失败", e);
        }
    }



    /**
     * 更新任务状态为已学习
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     */
    private void updateTaskStatusToLearned(Long taskId, Long teamId) {
        try {
            int updateResult = trainReceptionTaskMapper.updateLearningStatus(
                taskId,
                LearnStatus.LEARNED.getCode(),
                SYSTEM_USER,
                teamId
            );

            if (updateResult > 0) {
                log.info("更新任务状态为已学习成功，taskId: {}", taskId);
            } else {
                log.warn("更新任务状态为已学习失败，taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("更新任务状态为已学习失败，taskId: {}", taskId, e);
            throw e;
        }
    }

    /**
     * 批量更新任务的已学习数量
     *
     * @param taskId 任务ID
     * @param incrementCount 增加的数量
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskAmtHasLearnedBatch(Long taskId, int incrementCount) {
        try {
            // 使用批量更新，避免并发锁冲突
            int updateResult = trainReceptionTaskMapper.updateAmtHasLearnedBatchWithPessimisticLock(
                taskId,
                incrementCount,
                SYSTEM_USER
            );

            if (updateResult > 0) {
                log.debug("批量更新任务已学习数量成功，taskId: {}, incrementCount: {}", taskId, incrementCount);
            } else {
                log.warn("批量更新任务已学习数量失败，taskId: {}, incrementCount: {}", taskId, incrementCount);
            }

        } catch (Exception e) {
            log.error("批量更新任务已学习数量失败，taskId: {}, incrementCount: {}", taskId, incrementCount, e);
            throw e;
        }
    }

    /**
     * 处理单条学习记录（独立事务，不更新任务计数）
     *
     * @param taskLearning 学习任务数据
     * @return 是否处理成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean processSingleLearningWithoutTaskUpdate(TaskLearningDTO taskLearning) {
        try {
            log.debug("开始处理单条学习记录，id: {}, taskId: {}, scriptId: {}",
                taskLearning.getId(), taskLearning.getTaskId(), taskLearning.getScriptId());

            // 1. 获取f1st_raw_chatlog
            String f1stRawChatlog = taskLearning.getF1stRawChatlog();
            if (f1stRawChatlog == null || f1stRawChatlog.trim().isEmpty()) {
                log.warn("f1st_raw_chatlog为空，跳过处理，id: {}", taskLearning.getId());
                return false;
            }

            // 2. 调用get2ndAggFromRawChatlog方法，现在可以使用scriptId
            String f2ndAggSysPrompt = get2ndAggFromRawChatlog(f1stRawChatlog ,taskLearning);
            if (f2ndAggSysPrompt == null) {
                log.warn("生成系统提示词失败，跳过处理，id: {}", taskLearning.getId());
                return false;
            }

            // 3. 调用大模型
            String t3rdRewriteSysPmt = doubaoBigModelServiceImpl.ntnsOnce(f2ndAggSysPrompt, "");
//            String t3rdRewriteSysPmt = "test_3rd";
            if (t3rdRewriteSysPmt == null || t3rdRewriteSysPmt.trim().isEmpty()) {
                log.warn("大模型返回结果为空，跳过处理，id: {}", taskLearning.getId());
                return false;
            }

            // 4. 更新train_task_conv_kb_dtl表（使用悲观锁）
            int updateResult = trainTaskConvKbDtlMapper.updateLearningResultWithPessimisticLock(
                taskLearning.getId(),
                f2ndAggSysPrompt,
                t3rdRewriteSysPmt,
                LearnStatus.LEARNED.getCode(),
                SYSTEM_USER
            );

            if (updateResult > 0) {
                log.debug("更新学习结果成功，id: {}", taskLearning.getId());
                return true;
            } else {
                log.warn("更新学习结果失败，id: {}", taskLearning.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("处理单条学习记录失败，id: {}", taskLearning.getId(), e);
            return false;
        }
    }

    /**
     * 处理单条学习记录（独立事务）
     *
     * @param taskLearning 学习任务数据
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processSingleLearning(TaskLearningDTO taskLearning) {
        try {
            log.debug("开始处理单条学习记录，id: {}, taskId: {}, scriptId: {}", 
                taskLearning.getId(), taskLearning.getTaskId(), taskLearning.getScriptId());
            
            // 1. 获取f1st_raw_chatlog
            String f1stRawChatlog = taskLearning.getF1stRawChatlog();
            if (f1stRawChatlog == null || f1stRawChatlog.trim().isEmpty()) {
                log.warn("f1st_raw_chatlog为空，跳过处理，id: {}", taskLearning.getId());
                return;
            }
            
            // 2. 调用get2ndAggFromRawChatlog方法，现在可以使用scriptId
            String f2ndAggSysPrompt = get2ndAggFromRawChatlog(f1stRawChatlog ,taskLearning);
            if (f2ndAggSysPrompt == null) {
                log.warn("生成系统提示词失败，跳过处理，id: {}", taskLearning.getId());
                return;
            }
            
            // 3. 调用大模型
//            String t3rdRewriteSysPmt = doubaoBigModelServiceImpl.ntnsOnce(f2ndAggSysPrompt, "");
            String t3rdRewriteSysPmt = "test_3rd";
            if (t3rdRewriteSysPmt == null || t3rdRewriteSysPmt.trim().isEmpty()) {
                log.warn("大模型返回结果为空，跳过处理，id: {}", taskLearning.getId());
                return;
            }
            
            // 4. 更新train_task_conv_kb_dtl表（使用悲观锁）
            int updateResult = trainTaskConvKbDtlMapper.updateLearningResultWithPessimisticLock(
                taskLearning.getId(),
                f2ndAggSysPrompt,
                t3rdRewriteSysPmt,
                LearnStatus.LEARNED.getCode(),
                SYSTEM_USER
            );
            
            if (updateResult > 0) {
                log.debug("更新学习结果成功，id: {}", taskLearning.getId());

                // 5. 更新train_reception_task的amt_has_learned（使用悲观锁，在同一事务中执行）
                updateTaskAmtHasLearned(taskLearning.getTaskId());

            } else {
                log.warn("更新学习结果失败，id: {}", taskLearning.getId());
            }
            
        } catch (Exception e) {
            log.error("处理单条学习记录失败，id: {}", taskLearning.getId(), e);
        }
    }
    
    /**
     * 根据原始聊天记录和剧本ID生成第二个聚合系统提示词
     * 参考SimuChatWS4ConvController.get2ndAggFromTask方法
     *
     * @param f1stRawChatlog  原始聊天记录
     * @param taskLearningDTO
     * @return 生成的系统提示词
     */
    private String get2ndAggFromRawChatlog(String f1stRawChatlog, TaskLearningDTO taskLearningDTO) {
        try {
            log.debug("开始生成系统提示词，scriptId: {}, chatlog长度: {}", taskLearningDTO.getScriptId(),
                f1stRawChatlog != null ? f1stRawChatlog.length() : 0);
            
            // 1. 从数据库获取提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_GENE_2ST_CHAT_TPL_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到提示词模板，keyword: {}", CONSTS.DEFAULT_GENE_2ST_CHAT_TPL_KEYWORD);
                return null;
            }

            String template = prompts.get(0); // 取第一个提示词作为模板
            log.debug("获取到提示词模板：{}", template);

            // 2. 进行变量替换
            // 2.1 替换chat_log
            String result = template.replace("{{chat_log}}", f1stRawChatlog != null ? f1stRawChatlog : "");
            
            // 2.2 TODO: 根据scriptId获取剧本信息，替换其他变量
            ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(taskLearningDTO.getScriptId(), taskLearningDTO.getTeamId());
            // 这里可以根据scriptId查询train_script表获取相关信息
            // 暂时设为空，后续可以扩展
            result = result.replace("{{targetProKnowledge}}", scriptDetail.getProductList().get(0).getProdKb());
            result = result.replace("{{targetRole}}", scriptDetail.getBuyerRequirement());
            result = result.replace("{{targetProductTitle}}", scriptDetail.getProductList().get(0).getExternalProductName());
            
            log.debug("变量替换后的提示词长度：{}", result.length());
            return result;

        } catch (Exception e) {
            log.error("构建系统提示词失败，scriptId: {}", taskLearningDTO.getScriptId(), e);
            return null;
        }
    }



    /**
     * 更新任务的已学习数量（使用悲观锁）
     * 在当前事务中执行，不新开事务
     *
     * @param taskId 任务ID
     */
    private void updateTaskAmtHasLearned(Long taskId) {
        try {
            log.debug("开始更新任务已学习数量，taskId: {}", taskId);

            // 使用悲观锁更新
            int updateResult = trainReceptionTaskMapper.updateAmtHasLearnedWithPessimisticLock(
                taskId,
                SYSTEM_USER
            );

            if (updateResult > 0) {
                log.debug("更新任务已学习数量成功，taskId: {}, 影响行数: {}", taskId, updateResult);

                // 查询更新后的任务信息以进行状态检查
                var taskInfo = trainReceptionTaskMapper.selectLearningInfo(taskId);
                if (taskInfo != null) {
                    log.debug("查询任务信息成功，taskId: {}, amtHasLearned: {}, amtToBeLearned: {}",
                        taskId, taskInfo.getAmtHasLearned(), taskInfo.getAmtToBeLearned());

                    // 检查是否所有明细都已学习完成
                    checkAndUpdateTaskLearningStatus(taskId, taskInfo.getAmtHasLearned(),
                        taskInfo.getAmtToBeLearned(), taskInfo.getTeamId());
                } else {
                    log.warn("查询任务信息失败，taskId: {}", taskId);
                }

            } else {
                log.warn("更新任务已学习数量失败，taskId: {}, 影响行数: {}", taskId, updateResult);
            }

        } catch (Exception e) {
            log.error("更新任务已学习数量失败，taskId: {}", taskId, e);
            throw e; // 重新抛出异常以便事务回滚
        }
    }

    /**
     * 更新任务状态为学习中
     * 在开始处理模板学习前调用
     *
     * @param taskId 任务ID
     * @param teamId 团队ID
     */
    private void updateTaskStatusToLearning(Long taskId, Long teamId) {
        try {
            int updateResult = trainReceptionTaskMapper.updateLearningStatus(
                taskId,
                LearnStatus.LEARNING.getCode(),
                SYSTEM_USER,
                teamId
            );

            if (updateResult > 0) {
                log.info("任务状态更新为学习中，taskId: {}", taskId);
            } else {
                log.warn("更新任务状态为学习中失败，taskId: {}", taskId);
            }
        } catch (Exception e) {
            log.error("更新任务状态为学习中时发生异常，taskId: {}", taskId, e);
        }
    }

    /**
     * 检查并更新任务学习状态
     * 当所有明细都学习完成后，更新任务状态为已学习
     *
     * @param taskId 任务ID
     * @param amtHasLearned 已学习数量
     * @param amtToBeLearned 待学习总数
     * @param teamId 团队ID
     */
    private void checkAndUpdateTaskLearningStatus(Long taskId, Long amtHasLearned, Long amtToBeLearned, Long teamId) {
        log.info("开始更新任务状态开始,bygermmy...");
        if (amtToBeLearned != null && amtHasLearned.equals(amtToBeLearned)) {
            // 所有明细都已学习完成，更新任务状态
            int updateResult = trainReceptionTaskMapper.updateLearningStatus(
                taskId,
                LearnStatus.LEARNED.getCode(),
                SYSTEM_USER,
                teamId
            );

            if (updateResult > 0) {
                log.info("任务学习完成，更新状态为已学习，taskId: {}", taskId);
            } else {
                log.warn("更新任务学习状态失败，taskId: {}", taskId);
            }
        }
        log.info("开始更新任务状态结束,bygermmy...");
    }
}
