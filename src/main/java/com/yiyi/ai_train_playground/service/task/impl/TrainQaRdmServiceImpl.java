package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;
import com.yiyi.ai_train_playground.mapper.task.TrainQaRdmMapper;
import com.yiyi.ai_train_playground.service.task.TrainQaRdmService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 问答随机表 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@Service
public class TrainQaRdmServiceImpl implements TrainQaRdmService {

    @Autowired
    private TrainQaRdmMapper trainQaRdmMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByUUID(TrainQaRdm trainQaRdm) {
        try {
            log.info("开始根据UUID更新问答随机记录：uuid={}", trainQaRdm.getUuid());
            
            // 设置更新人
            trainQaRdm.setUpdater(SecurityUtil.getCurrentUsername());
            
            int result = trainQaRdmMapper.updateByUUID(trainQaRdm);
            
            if (result > 0) {
                log.info("根据UUID更新问答随机记录成功：uuid={}", trainQaRdm.getUuid());
                return true;
            } else {
                log.warn("根据UUID更新问答随机记录失败，可能记录不存在或版本冲突：uuid={}", trainQaRdm.getUuid());
                return false;
            }
        } catch (Exception e) {
            log.error("根据UUID更新问答随机记录异常：uuid={}", trainQaRdm.getUuid(), e);
            throw e;
        }
    }

    @Override
    public TrainQaRdm getByUUID(String uuid) {
        try {
            log.info("开始根据UUID查询问答随机记录（不带团队ID过滤）：uuid={}", uuid);
            
            TrainQaRdm result = trainQaRdmMapper.selectByUUID(uuid);
            
            if (result != null) {
                log.info("根据UUID查询问答随机记录成功：uuid={}", uuid);
            } else {
                log.info("根据UUID未找到问答随机记录：uuid={}", uuid);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据UUID查询问答随机记录异常：uuid={}", uuid, e);
            throw e;
        }
    }

    @Override
    public TrainQaRdm getByUUID(String uuid, Long teamId) {
        try {
            log.info("开始根据UUID查询问答随机记录：uuid={}, teamId={}", uuid, teamId);
            
            TrainQaRdm result = trainQaRdmMapper.selectByUUIDWithTeam(uuid, teamId);
            
            if (result != null) {
                log.info("根据UUID查询问答随机记录成功：uuid={}", uuid);
            } else {
                log.info("根据UUID未找到问答随机记录：uuid={}", uuid);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据UUID查询问答随机记录异常：uuid={}, teamId={}", uuid, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaRdm> getPageList(int pageNum, int pageSize, Long teamId, String quesNo) {
        try {
            log.info("开始分页查询问答随机记录：teamId={}, quesNo={}, pageNum={}, pageSize={}",
                    teamId, quesNo, pageNum, pageSize);

            int offset = (pageNum - 1) * pageSize;
            List<TrainQaRdm> result = trainQaRdmMapper.selectPageList(teamId, quesNo, offset, pageSize);

            log.info("分页查询问答随机记录完成：当前页记录数={}", result.size());

            return result;
        } catch (Exception e) {
            log.error("分页查询问答随机记录异常：teamId={}, quesNo={}", teamId, quesNo, e);
            throw e;
        }
    }

    @Override
    public long countByCondition(Long teamId, String quesNo) {
        try {
            log.info("开始查询问答随机记录总数：teamId={}, quesNo={}", teamId, quesNo);

            long result = trainQaRdmMapper.countByCondition(teamId, quesNo);

            log.info("查询问答随机记录总数完成：总记录数={}", result);

            return result;
        } catch (Exception e) {
            log.error("查询问答随机记录总数异常：teamId={}, quesNo={}", teamId, quesNo, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaRdm> getByTeamId(Long teamId) {
        try {
            log.info("开始根据团队ID查询问答随机记录：teamId={}", teamId);
            
            List<TrainQaRdm> result = trainQaRdmMapper.selectByTeamId(teamId);
            
            log.info("根据团队ID查询问答随机记录完成：teamId={}, 记录数={}", teamId, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("根据团队ID查询问答随机记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaRdm> getByQuesNo(String quesNo, Long teamId) {
        try {
            log.info("开始根据问题编号查询问答随机记录：quesNo={}, teamId={}", quesNo, teamId);
            
            List<TrainQaRdm> result = trainQaRdmMapper.selectByQuesNo(quesNo, teamId);
            
            log.info("根据问题编号查询问答随机记录完成：quesNo={}, 记录数={}", quesNo, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("根据问题编号查询问答随机记录异常：quesNo={}, teamId={}", quesNo, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<TrainQaRdm> records, Long teamId, String creator) {
        try {
            log.info("开始批量保存问答随机记录：teamId={}, 记录数={}", teamId, records.size());
            
            if (records.isEmpty()) {
                log.warn("批量保存问答随机记录失败：记录列表为空");
                return false;
            }
            
            // 设置团队ID和创建人信息
            String currentUser = creator;
            LocalDateTime now = LocalDateTime.now();
            
            for (TrainQaRdm record : records) {
                record.setTeamId(teamId);
                record.setCreator(currentUser);
                record.setUpdater(currentUser);
                record.setCreateTime(now);
                record.setUpdateTime(now);
                record.setVersion(0L);
            }
            
            int result = trainQaRdmMapper.batchInsert(records);
            
            if (result > 0) {
                log.info("批量保存问答随机记录成功：teamId={}, 保存记录数={}", teamId, result);
                return true;
            } else {
                log.warn("批量保存问答随机记录失败：teamId={}", teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("批量保存问答随机记录异常：teamId={}, 记录数={}", teamId, records.size(), e);
            throw e;
        }
    }

    @Override
    public List<TrainQaRdm> createFromDtoList(List<QaSimplelDtoWithUUID> dtoList, Long teamId) {
        try {
            log.info("开始从DTO列表创建TrainQaRdm记录：teamId={}, DTO数量={}", teamId, dtoList.size());

            List<TrainQaRdm> records = new ArrayList<>();
            String currentUser = SecurityUtil.getCurrentUsername();
            LocalDateTime now = LocalDateTime.now();

            for (QaSimplelDtoWithUUID dto : dtoList) {
                TrainQaRdm record = new TrainQaRdm();
                record.setUuid(dto.getUuid());
                record.setQuestion(dto.getQuestion());
                record.setAnswer(dto.getAnswer());
                record.setActualQuestion(dto.getActualQuestion());
                record.setActualAnswer(dto.getActualAnswer());
                record.setResolve(dto.getResolve());
                record.setQuesNo(dto.getQuesNo());
                record.setSendTime(now); // 设置发送时间为当前时间
                record.setTeamId(teamId);
                record.setCreator(currentUser);
                record.setUpdater(currentUser);
                record.setCreateTime(now);
                record.setUpdateTime(now);
                record.setVersion(0L);

                records.add(record);
            }

            log.info("从DTO列表创建TrainQaRdm记录完成：teamId={}, 创建记录数={}", teamId, records.size());
            return records;
        } catch (Exception e) {
            log.error("从DTO列表创建TrainQaRdm记录异常：teamId={}, DTO数量={}", teamId, dtoList.size(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDtoList(List<QaSimplelDtoWithUUID> dtoList, Long teamId, String creator) {
        try {
            log.info("开始保存DTO列表到数据库：teamId={}, DTO数量={}", teamId, dtoList.size());

            if (dtoList.isEmpty()) {
                log.warn("保存DTO列表失败：DTO列表为空");
                return false;
            }

            // 转换为TrainQaRdm记录
            List<TrainQaRdm> records = createFromDtoList(dtoList, teamId);

            // 批量保存
            boolean result = batchSave(records, teamId, creator);

            if (result) {
                log.info("保存DTO列表到数据库成功：teamId={}, 保存记录数={}", teamId, records.size());
            } else {
                log.warn("保存DTO列表到数据库失败：teamId={}", teamId);
            }

            return result;
        } catch (Exception e) {
            log.error("保存DTO列表到数据库异常：teamId={}, DTO数量={}", teamId, dtoList.size(), e);
            throw e;
        }
    }

    @Override
    public List<TrainQaRdm> getByReportDtlId(Long reportDtlId, Long teamId) {
        try {
            log.info("开始根据报告明细ID查询问答随机记录：reportDtlId={}, teamId={}", reportDtlId, teamId);

            List<TrainQaRdm> result = trainQaRdmMapper.selectByReportDtlId(reportDtlId, teamId);

            log.info("根据报告明细ID查询问答随机记录完成：reportDtlId={}, 查询到记录数={}", reportDtlId, result.size());

            return result;
        } catch (Exception e) {
            log.error("根据报告明细ID查询问答随机记录异常：reportDtlId={}, teamId={}", reportDtlId, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByUUID(String uuid, Long teamId) {
        try {
            log.info("开始根据UUID删除问答随机记录：uuid={}, teamId={}", uuid, teamId);

            int result = trainQaRdmMapper.deleteByUUID(uuid, teamId);

            if (result > 0) {
                log.info("根据UUID删除问答随机记录成功：uuid={}", uuid);
                return true;
            } else {
                log.warn("根据UUID删除问答随机记录失败，可能记录不存在：uuid={}", uuid);
                return false;
            }
        } catch (Exception e) {
            log.error("根据UUID删除问答随机记录异常：uuid={}, teamId={}", uuid, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByTeamId(Long teamId) {
        try {
            log.info("开始根据团队ID删除所有问答随机记录：teamId={}", teamId);

            int result = trainQaRdmMapper.deleteByTeamId(teamId);

            if (result > 0) {
                log.info("根据团队ID删除所有问答随机记录成功：teamId={}", teamId);
                return true;
            } else {
                log.warn("根据团队ID删除所有问答随机记录失败：teamId={}", teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("根据团队ID删除所有问答随机记录异常：teamId={}", teamId, e);
            throw e;
        }
    }
}
