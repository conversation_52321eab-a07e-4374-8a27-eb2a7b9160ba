package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl;
import com.yiyi.ai_train_playground.mapper.task.TrainQaReportDtlMapper;
import com.yiyi.ai_train_playground.service.task.TrainQaReportDtlService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模拟聊天室报告明细表 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@Service
public class TrainQaReportDtlServiceImpl implements TrainQaReportDtlService {

    @Autowired
    private TrainQaReportDtlMapper trainQaReportDtlMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(TrainQaReportDtl record, Long teamId, String creator) {
        try {
            log.info("开始创建模拟聊天室报告明细记录：teamId={}", teamId);
            
            // 设置基础信息
            record.setTeamId(teamId);
            record.setCreator(creator);
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            record.setVersion(0L);
            
            int result = trainQaReportDtlMapper.insert(record);
            
            if (result > 0) {
                log.info("创建模拟聊天室报告明细记录成功：id={}", record.getId());
                return true;
            } else {
                log.warn("创建模拟聊天室报告明细记录失败：teamId={}", teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("创建模拟聊天室报告明细记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    public TrainQaReportDtl getById(Long id, Long teamId) {
        try {
            log.info("开始根据ID查询模拟聊天室报告明细记录：id={}, teamId={}", id, teamId);
            
            TrainQaReportDtl result = trainQaReportDtlMapper.selectById(id, teamId);
            
            if (result != null) {
                log.info("根据ID查询模拟聊天室报告明细记录成功：id={}", id);
            } else {
                log.info("根据ID未找到模拟聊天室报告明细记录：id={}", id);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据ID查询模拟聊天室报告明细记录异常：id={}, teamId={}", id, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(Long id, TrainQaReportDtl record, Long teamId, String updater) {
        try {
            log.info("开始根据ID更新模拟聊天室报告明细记录：id={}, teamId={}", id, teamId);
            
            // 设置更新信息
            record.setId(id);
            record.setTeamId(teamId);
            record.setUpdater(updater);
            record.setUpdateTime(LocalDateTime.now());
            
            int result = trainQaReportDtlMapper.updateById(record);
            
            if (result > 0) {
                log.info("根据ID更新模拟聊天室报告明细记录成功：id={}", id);
                return true;
            } else {
                log.warn("根据ID更新模拟聊天室报告明细记录失败，可能记录不存在或版本冲突：id={}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("根据ID更新模拟聊天室报告明细记录异常：id={}, teamId={}", id, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id, Long teamId) {
        try {
            log.info("开始根据ID删除模拟聊天室报告明细记录：id={}, teamId={}", id, teamId);
            
            int result = trainQaReportDtlMapper.deleteById(id, teamId);
            
            if (result > 0) {
                log.info("根据ID删除模拟聊天室报告明细记录成功：id={}", id);
                return true;
            } else {
                log.warn("根据ID删除模拟聊天室报告明细记录失败，可能记录不存在：id={}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("根据ID删除模拟聊天室报告明细记录异常：id={}, teamId={}", id, teamId, e);
            throw e;
        }
    }

    @Override
    public TrainQaReportDtl getBySessionId(String sessionId, Long teamId) {
        try {
            log.info("开始根据sessionId查询模拟聊天室报告明细记录：sessionId={}, teamId={}", sessionId, teamId);
            
            TrainQaReportDtl result = trainQaReportDtlMapper.selectBySessionId(sessionId, teamId);
            
            if (result != null) {
                log.info("根据sessionId查询模拟聊天室报告明细记录成功：sessionId={}", sessionId);
            } else {
                log.info("根据sessionId未找到模拟聊天室报告明细记录：sessionId={}", sessionId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据sessionId查询模拟聊天室报告明细记录异常：sessionId={}, teamId={}", sessionId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportDtl> getByTaskId(Long taskId, Long teamId) {
        try {
            log.info("开始根据taskId查询模拟聊天室报告明细记录列表：taskId={}, teamId={}", taskId, teamId);
            
            List<TrainQaReportDtl> result = trainQaReportDtlMapper.selectByTaskId(taskId, teamId);
            
            log.info("根据taskId查询模拟聊天室报告明细记录列表成功：taskId={}, 记录数={}", taskId, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("根据taskId查询模拟聊天室报告明细记录列表异常：taskId={}, teamId={}", taskId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportDtl> getByRpMainId(Long rpMainId, Long teamId) {
        try {
            log.info("开始根据rpMainId查询模拟聊天室报告明细记录列表：rpMainId={}, teamId={}", rpMainId, teamId);
            
            List<TrainQaReportDtl> result = trainQaReportDtlMapper.selectByRpMainId(rpMainId, teamId);
            
            log.info("根据rpMainId查询模拟聊天室报告明细记录列表成功：rpMainId={}, 记录数={}", rpMainId, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("根据rpMainId查询模拟聊天室报告明细记录列表异常：rpMainId={}, teamId={}", rpMainId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportDtl> getPageList(int pageNum, int pageSize, Long teamId, Long taskId, Long qaMainId) {
        try {
            log.info("开始分页查询模拟聊天室报告明细记录：pageNum={}, pageSize={}, teamId={}, taskId={}, qaMainId={}", 
                    pageNum, pageSize, teamId, taskId, qaMainId);
            
            int offset = (pageNum - 1) * pageSize;
            List<TrainQaReportDtl> result = trainQaReportDtlMapper.selectPageList(teamId, taskId, qaMainId, offset, pageSize);
            
            log.info("分页查询模拟聊天室报告明细记录成功：pageNum={}, pageSize={}, 记录数={}", pageNum, pageSize, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("分页查询模拟聊天室报告明细记录异常：pageNum={}, pageSize={}, teamId={}", pageNum, pageSize, teamId, e);
            throw e;
        }
    }

    @Override
    public long countByCondition(Long teamId, Long taskId, Long qaMainId) {
        try {
            log.info("开始查询模拟聊天室报告明细记录总数：teamId={}, taskId={}, qaMainId={}", teamId, taskId, qaMainId);
            
            long result = trainQaReportDtlMapper.countByCondition(teamId, taskId, qaMainId);
            
            log.info("查询模拟聊天室报告明细记录总数成功：teamId={}, 总数={}", teamId, result);
            
            return result;
        } catch (Exception e) {
            log.error("查询模拟聊天室报告明细记录总数异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportDtl> getByTeamId(Long teamId) {
        try {
            log.info("开始根据团队ID查询模拟聊天室报告明细记录列表：teamId={}", teamId);
            
            List<TrainQaReportDtl> result = trainQaReportDtlMapper.selectByTeamId(teamId);
            
            log.info("根据团队ID查询模拟聊天室报告明细记录列表成功：teamId={}, 记录数={}", teamId, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("根据团队ID查询模拟聊天室报告明细记录列表异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreate(List<TrainQaReportDtl> records, Long teamId, String creator) {
        try {
            log.info("开始批量创建模拟聊天室报告明细记录：teamId={}, 记录数={}", teamId, records.size());

            if (records == null || records.isEmpty()) {
                log.warn("批量创建模拟聊天室报告明细记录失败：记录列表为空");
                return false;
            }

            // 设置基础信息
            for (TrainQaReportDtl record : records) {
                record.setTeamId(teamId);
                record.setCreator(creator);
                record.setCreateTime(LocalDateTime.now());
                record.setUpdateTime(LocalDateTime.now());
                record.setVersion(0L);
            }

            int result = trainQaReportDtlMapper.batchInsert(records);

            if (result > 0) {
                log.info("批量创建模拟聊天室报告明细记录成功：teamId={}, 插入记录数={}", teamId, result);
                return true;
            } else {
                log.warn("批量创建模拟聊天室报告明细记录失败：teamId={}", teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("批量创建模拟聊天室报告明细记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByTeamId(Long teamId) {
        try {
            log.info("开始根据团队ID删除模拟聊天室报告明细记录：teamId={}", teamId);

            int result = trainQaReportDtlMapper.deleteByTeamId(teamId);

            log.info("根据团队ID删除模拟聊天室报告明细记录成功：teamId={}, 删除记录数={}", teamId, result);
            return true;
        } catch (Exception e) {
            log.error("根据团队ID删除模拟聊天室报告明细记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByTaskId(Long taskId, Long teamId) {
        try {
            log.info("开始根据taskId删除模拟聊天室报告明细记录：taskId={}, teamId={}", taskId, teamId);

            int result = trainQaReportDtlMapper.deleteByTaskId(taskId, teamId);

            if (result > 0) {
                log.info("根据taskId删除模拟聊天室报告明细记录成功：taskId={}, 删除记录数={}", taskId, result);
                return true;
            } else {
                log.warn("根据taskId删除模拟聊天室报告明细记录失败，可能记录不存在：taskId={}", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("根据taskId删除模拟聊天室报告明细记录异常：taskId={}, teamId={}", taskId, teamId, e);
            throw e;
        }
    }

    @Override
    public boolean existsBySessionId(String sessionId, Long teamId) {
        try {
            log.info("开始检查sessionId是否存在：sessionId={}, teamId={}", sessionId, teamId);

            TrainQaReportDtl result = trainQaReportDtlMapper.selectBySessionId(sessionId, teamId);
            boolean exists = result != null;

            log.info("检查sessionId是否存在完成：sessionId={}, 存在={}", sessionId, exists);

            return exists;
        } catch (Exception e) {
            log.error("检查sessionId是否存在异常：sessionId={}, teamId={}", sessionId, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createByConditions(Long rpMainId, Long taskId, String sessionId, Long qaMainId, Long teamId, String creator) {
        try {
            log.info("开始根据条件创建模拟聊天室报告明细记录：rpMainId={}, taskId={}, sessionId={}, qaMainId={}, teamId={}",
                    rpMainId, taskId, sessionId, qaMainId, teamId);

            TrainQaReportDtl record = new TrainQaReportDtl();
            record.setRpMainId(rpMainId);
            record.setTaskId(taskId);
            record.setSessionId(sessionId);
            record.setQaMainId(qaMainId);

            return create(record, teamId, creator);
        } catch (Exception e) {
            log.error("根据条件创建模拟聊天室报告明细记录异常：rpMainId={}, taskId={}, sessionId={}, qaMainId={}, teamId={}",
                    rpMainId, taskId, sessionId, qaMainId, teamId, e);
            throw e;
        }
    }
}
