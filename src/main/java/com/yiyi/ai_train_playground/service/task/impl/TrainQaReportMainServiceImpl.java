package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportMain;
import com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl;
import com.yiyi.ai_train_playground.mapper.task.TrainQaReportMainMapper;
import com.yiyi.ai_train_playground.mapper.task.TrainQaRdmMapper;
import com.yiyi.ai_train_playground.mapper.task.TrainQaReportDtlMapper;
import com.yiyi.ai_train_playground.service.task.TrainQaReportMainService;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.ShowExamResultDTO;
import com.yiyi.ai_train_playground.dto.task.ExamAnswerRecordDTO;
import com.yiyi.ai_train_playground.dto.task.QaResolveResultDTO;
import com.yiyi.ai_train_playground.dto.task.TrainQaReportMainQueryRequest;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import com.yiyi.ai_train_playground.util.QaReslUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 问答报告主表服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@Service
public class TrainQaReportMainServiceImpl implements TrainQaReportMainService {

    @Autowired
    private TrainQaReportMainMapper trainQaReportMainMapper;

    @Autowired
    private TrainQaRdmMapper trainQaRdmMapper;
    
    @Autowired
    private TrainQaReportDtlMapper trainQaReportDtlMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TrainQaReportMain create(TrainQaReportMain record, Long teamId) {
        try {
            log.info("开始创建问答报告主表记录：teamId={}", teamId);

            // 设置基础字段
            record.setTeamId(teamId);
//            String creator = SecurityUtil.getCurrentUsername();
//            record.setCreator(creator != null ? creator : "system");
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            record.setVersion(0L);

            int result = trainQaReportMainMapper.insert(record);

            if (result > 0) {
                log.info("创建问答报告主表记录成功：id={}", record.getId());
                return record;
            } else {
                log.warn("创建问答报告主表记录失败：teamId={}", teamId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建问答报告主表记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    public TrainQaReportMain getById(Long id, Long teamId) {
        try {
            log.info("开始根据ID查询问答报告主表记录：id={}, teamId={}", id, teamId);

            TrainQaReportMain result = trainQaReportMainMapper.selectById(id, teamId);

            if (result != null) {
                log.info("根据ID查询问答报告主表记录成功：id={}", id);
            } else {
                log.info("根据ID未找到问答报告主表记录：id={}", id);
            }

            return result;
        } catch (Exception e) {
            log.error("根据ID查询问答报告主表记录异常：id={}, teamId={}", id, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(TrainQaReportMain record, Long teamId) {
        try {
            log.info("开始根据ID更新问答报告主表记录：id={}, teamId={}", record.getId(), teamId);

            // 设置更新字段
            record.setTeamId(teamId);
            String updater = SecurityUtil.getCurrentUsername();
            record.setUpdater(updater != null ? updater : "system");

            int result = trainQaReportMainMapper.updateById(record);

            if (result > 0) {
                log.info("根据ID更新问答报告主表记录成功：id={}", record.getId());
                return true;
            } else {
                log.warn("根据ID更新问答报告主表记录失败：id={}", record.getId());
                return false;
            }
        } catch (Exception e) {
            log.error("根据ID更新问答报告主表记录异常：id={}, teamId={}", record.getId(), teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id, Long teamId) {
        try {
            log.info("开始根据ID删除问答报告主表记录（级联删除）：id={}, teamId={}", id, teamId);
            
            // 首先查询该主表记录下的所有明细表记录ID
            List<TrainQaReportDtl> dtlList = trainQaReportDtlMapper.selectByRpMainId(id, teamId);
            
            // 1. 删除train_qa_rdm表中的相关记录（最底层）
            int rdmDeleteCount = 0;
            for (TrainQaReportDtl dtl : dtlList) {
                int count = trainQaRdmMapper.deleteByReportDtlId(dtl.getId(), teamId);
                rdmDeleteCount += count;
                log.info("删除train_qa_rdm记录：reportDtlId={}, 删除数量={}", dtl.getId(), count);
            }
            
            // 2. 删除train_qa_report_dtl表中的相关记录（中间层）
            int dtlDeleteCount = trainQaReportDtlMapper.deleteByRpMainId(id, teamId);
            log.info("删除train_qa_report_dtl记录：rpMainId={}, 删除数量={}", id, dtlDeleteCount);
            
            // 3. 删除train_qa_report_main表中的主记录（顶层）
            int mainDeleteCount = trainQaReportMainMapper.deleteById(id, teamId);
            log.info("删除train_qa_report_main记录：id={}, 删除数量={}", id, mainDeleteCount);
            
            if (mainDeleteCount > 0) {
                log.info("根据ID删除问答报告主表记录成功：id={}, 级联删除rdm记录{}条, dtl记录{}条", 
                        id, rdmDeleteCount, dtlDeleteCount);
                return true;
            } else {
                log.warn("根据ID删除问答报告主表记录失败，可能记录不存在：id={}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("根据ID删除问答报告主表记录异常：id={}, teamId={}", id, teamId, e);
            throw e;
        }
    }

    @Override
    public PageResult<TrainQaReportMain> getPageList(TrainQaReportMainQueryRequest request, Long teamId) {
        try {
            log.info("开始分页查询问答报告主表记录：request={}, teamId={}", request, teamId);

            int offset = (request.getPage() - 1) * request.getPageSize();

            List<TrainQaReportMain> records = trainQaReportMainMapper.selectPageList(
                    teamId, 
                    request.getChatroomId(),
                    request.getStaffId(),
                    request.getExamUserRealName(),
                    request.getExamUserNo(),
                    request.getCreateTimeStart(),
                    request.getCreateTimeEnd(),
                    request.getMinScore(),
                    offset, 
                    request.getPageSize()
            );
            long total = trainQaReportMainMapper.countByCondition(
                    teamId,
                    request.getChatroomId(),
                    request.getStaffId(),
                    request.getExamUserRealName(),
                    request.getExamUserNo(),
                    request.getCreateTimeStart(),
                    request.getCreateTimeEnd(),
                    request.getMinScore()
            );

            log.info("分页查询问答报告主表记录完成：当前页记录数={}, 总记录数={}", records.size(), total);

            return new PageResult<>(records, total, request.getPage(), request.getPageSize());
        } catch (Exception e) {
            log.error("分页查询问答报告主表记录异常：request={}, teamId={}", request, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportMain> getByChatroomId(Long chatroomId, Long teamId) {
        try {
            log.info("开始根据聊天室ID查询问答报告主表记录：chatroomId={}, teamId={}", chatroomId, teamId);

            List<TrainQaReportMain> result = trainQaReportMainMapper.selectByChatroomId(chatroomId, teamId);

            log.info("根据聊天室ID查询问答报告主表记录完成：chatroomId={}, 记录数={}", chatroomId, result.size());

            return result;
        } catch (Exception e) {
            log.error("根据聊天室ID查询问答报告主表记录异常：chatroomId={}, teamId={}", chatroomId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportMain> getByStaffId(Long staffId, Long teamId) {
        try {
            log.info("开始根据员工ID查询问答报告主表记录：staffId={}, teamId={}", staffId, teamId);

            List<TrainQaReportMain> result = trainQaReportMainMapper.selectByStaffId(staffId, teamId);

            log.info("根据员工ID查询问答报告主表记录完成：staffId={}, 记录数={}", staffId, result.size());

            return result;
        } catch (Exception e) {
            log.error("根据员工ID查询问答报告主表记录异常：staffId={}, teamId={}", staffId, teamId, e);
            throw e;
        }
    }

    @Override
    public List<TrainQaReportMain> getByTeamId(Long teamId) {
        try {
            log.info("开始根据团队ID查询问答报告主表记录：teamId={}", teamId);

            List<TrainQaReportMain> result = trainQaReportMainMapper.selectByTeamId(teamId);

            log.info("根据团队ID查询问答报告主表记录完成：teamId={}, 记录数={}", teamId, result.size());

            return result;
        } catch (Exception e) {
            log.error("根据团队ID查询问答报告主表记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreate(List<TrainQaReportMain> records, Long teamId, String creator) {
        try {
            log.info("开始批量创建问答报告主表记录：teamId={}, 记录数={}", teamId, records.size());

            if (records.isEmpty()) {
                log.warn("批量创建问答报告主表记录失败：记录列表为空");
                return false;
            }

            // 设置基础字段
            LocalDateTime now = LocalDateTime.now();
            for (TrainQaReportMain record : records) {
                record.setTeamId(teamId);
                record.setCreator(creator);
                record.setUpdater(creator);
                record.setCreateTime(now);
                record.setUpdateTime(now);
                record.setVersion(0L);
            }

            int result = trainQaReportMainMapper.batchInsert(records);

            if (result > 0) {
                log.info("批量创建问答报告主表记录成功：teamId={}, 创建记录数={}", teamId, result);
                return true;
            } else {
                log.warn("批量创建问答报告主表记录失败：teamId={}", teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("批量创建问答报告主表记录异常：teamId={}, 记录数={}", teamId, records.size(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByTeamId(Long teamId) {
        try {
            log.info("开始根据团队ID删除所有问答报告主表记录：teamId={}", teamId);

            int result = trainQaReportMainMapper.deleteByTeamId(teamId);

            if (result > 0) {
                log.info("根据团队ID删除所有问答报告主表记录成功：teamId={}, 删除记录数={}", teamId, result);
                return true;
            } else {
                log.warn("根据团队ID删除所有问答报告主表记录失败：teamId={}", teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("根据团队ID删除所有问答报告主表记录异常：teamId={}", teamId, e);
            throw e;
        }
    }

    @Override
    public long countByCondition(TrainQaReportMainQueryRequest request, Long teamId) {
        try {
            log.info("开始查询问答报告主表记录总数：request={}, teamId={}", request, teamId);

            long result = trainQaReportMainMapper.countByCondition(
                    teamId,
                    request.getChatroomId(),
                    request.getStaffId(),
                    request.getExamUserRealName(),
                    request.getExamUserNo(),
                    request.getCreateTimeStart(),
                    request.getCreateTimeEnd(),
                    request.getMinScore()
            );

            log.info("查询问答报告主表记录总数成功：teamId={}, 总数={}", teamId, result);

            return result;
        } catch (Exception e) {
            log.error("查询问答报告主表记录总数异常：request={}, teamId={}", request, teamId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShowExamResultDTO showExamResult(Long qaReportMainId, Long teamId, Boolean isNeedUpdate) {
        try {
            log.info("开始展示考试结果：qaReportMainId={}, teamId={}, isNeedUpdate={}", qaReportMainId, teamId, isNeedUpdate);

            // 1. 查询报告主记录
            TrainQaReportMain reportMain = trainQaReportMainMapper.selectById(qaReportMainId, teamId);
            if (reportMain == null) {
                log.warn("未找到报告主记录：qaReportMainId={}, teamId={}", qaReportMainId, teamId);
                return null;
            }

            // 2. 查询所有答题记录
            List<ExamAnswerRecordDTO> qaRdmList = trainQaRdmMapper.selectExamAnswerRecordsByMainId(qaReportMainId, teamId);
            if (qaRdmList == null || qaRdmList.isEmpty()) {
                log.warn("未找到答题记录：qaReportMainId={}, teamId={}", qaReportMainId, teamId);
                return null;
            }

            // 声明examScore变量
            BigDecimal examScore;

            // 根据isNeedUpdate参数决定是否计算和更新分数
            if (isNeedUpdate != null && isNeedUpdate) {
                // 3. 计算总分
                BigDecimal totalScore = BigDecimal.ZERO;
                int validQuestionCount = 0;

                for (ExamAnswerRecordDTO record : qaRdmList) {
                    if (record.getResolve() != null && !record.getResolve().trim().isEmpty()) {
                        try {
                            // 使用QaReslUtil解析resolve字段提取score
                            QaResolveResultDTO resolveResult = QaReslUtil.parseResolve(record.getResolve());
                            if (resolveResult.getScore() != null) {
                                totalScore = totalScore.add(new BigDecimal(resolveResult.getScore()));
                                record.setScore(resolveResult.getScore()); // 设置到记录中便于返回
                                validQuestionCount++;
                                log.debug("提取题目得分：uuid={}, score={}", record.getUuid(), resolveResult.getScore());
                            }
                        } catch (Exception e) {
                            log.warn("解析resolve失败，跳过该题：uuid={}, resolve={}", record.getUuid(), record.getResolve(), e);
                        }
                    }
                }

                // 4. 计算平均分（保留2位小数）
                examScore = BigDecimal.ZERO;
                if (validQuestionCount > 0) {
                    examScore = totalScore.divide(new BigDecimal(validQuestionCount), 2, RoundingMode.HALF_UP);
                    log.info("计算考试总分：totalScore={}, validQuestionCount={}, examScore={}", totalScore, validQuestionCount, examScore);
                }

                // 5. 更新报告主记录的考试分数
                updateExamScore(qaReportMainId, examScore, teamId);
            } else {
                // 1.1 如果isNeedUpdate为false，则从reportMain获取examScore
                examScore = reportMain.getExamScore();
                if (examScore == null) {
                    examScore = BigDecimal.ZERO;
                }
                log.info("使用已有考试分数：qaReportMainId={}, examScore={}", qaReportMainId, examScore);
                
                // 仍需要解析每题的得分用于展示
                for (ExamAnswerRecordDTO record : qaRdmList) {
                    if (record.getResolve() != null && !record.getResolve().trim().isEmpty()) {
                        try {
                            QaResolveResultDTO resolveResult = QaReslUtil.parseResolve(record.getResolve());
                            if (resolveResult.getScore() != null) {
                                record.setScore(resolveResult.getScore());
                            }
                        } catch (Exception e) {
                            log.warn("解析resolve失败，跳过该题：uuid={}, resolve={}", record.getUuid(), record.getResolve(), e);
                        }
                    }
                }
            }

            // 6. 构造返回结果
            ShowExamResultDTO result = new ShowExamResultDTO();
            result.setQaReportMainId(qaReportMainId);
            result.setExamUserRealName(reportMain.getExamUserRealName());
            result.setExamUserNo(reportMain.getExamUserNo());
            result.setExamScore(examScore);
            result.setTotalQuestions(qaRdmList.size());
            result.setQaRdmList(qaRdmList);

            log.info("展示考试结果成功：qaReportMainId={}, examScore={}, totalQuestions={}, isNeedUpdate={}", 
                    qaReportMainId, examScore, qaRdmList.size(), isNeedUpdate);

            return result;
        } catch (Exception e) {
            log.error("展示考试结果异常：qaReportMainId={}, teamId={}, isNeedUpdate={}", qaReportMainId, teamId, isNeedUpdate, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamScore(Long id, BigDecimal examScore, Long teamId) {
        try {
            log.info("开始更新考试分数：id={}, examScore={}, teamId={}", id, examScore, teamId);

            int result = trainQaReportMainMapper.updateExamScoreById(id, examScore, teamId);

            if (result > 0) {
                log.info("更新考试分数成功：id={}, examScore={}", id, examScore);
                return true;
            } else {
                log.warn("更新考试分数失败：id={}, examScore={}, teamId={}", id, examScore, teamId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新考试分数异常：id={}, examScore={}, teamId={}", id, examScore, teamId, e);
            throw e;
        }
    }

    @Override
    public PageResult<TrainQaReportMain> getReceptionDetailsByChatroomId(Long chatroomId, TrainQaReportMainQueryRequest request, Long teamId) {
        try {
            log.info("根据聊天室ID分页查询接待详情：chatroomId={}, teamId={}, page={}, pageSize={}",
                    chatroomId, teamId, request.getPage(), request.getPageSize());

            // 计算偏移量
            int offset = (request.getPage() - 1) * request.getPageSize();

            // 查询总数
            long total = trainQaReportMainMapper.countByCondition(
                    teamId,
                    chatroomId,
                    request.getStaffId(),
                    request.getExamUserRealName(),
                    request.getExamUserNo(),
                    request.getCreateTimeStart(),
                    request.getCreateTimeEnd(),
                    request.getMinScore()
            );

            // 查询列表数据
            List<TrainQaReportMain> records = trainQaReportMainMapper.selectPageList(
                    teamId, 
                    chatroomId, 
                    request.getStaffId(), 
                    request.getExamUserRealName(),
                    request.getExamUserNo(),
                    request.getCreateTimeStart(),
                    request.getCreateTimeEnd(),
                    request.getMinScore(),
                    offset, 
                    request.getPageSize()
            );

            log.info("查询接待详情成功：chatroomId={}, 总数={}, 当前页数量={}", chatroomId, total, records.size());

            return new PageResult<>(records, total, request.getPage(), request.getPageSize());

        } catch (Exception e) {
            log.error("根据聊天室ID分页查询接待详情失败：chatroomId={}, teamId={}", chatroomId, teamId, e);
            throw e;
        }
    }

    @Override
    public byte[] exportExcel(TrainQaReportMainQueryRequest request, Long teamId) {
        try {
            log.info("开始导出问答报告Excel：request={}, teamId={}", request, teamId);

            // 查询数据，复用查询逻辑，限制10万条
            List<TrainQaReportMain> records = trainQaReportMainMapper.selectListForExport(
                    teamId, 
                    request.getChatroomId(),
                    request.getStaffId(),
                    request.getExamUserRealName(),
                    request.getExamUserNo(),
                    request.getCreateTimeStart(),
                    request.getCreateTimeEnd(),
                    request.getMinScore()
            );

            log.info("查询到{}条记录，开始生成Excel", records.size());

            // 创建工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                Sheet sheet = workbook.createSheet("问答报告");

                // 创建标题样式
                CellStyle headerStyle = workbook.createCellStyle();
                Font headerFont = workbook.createFont();
                headerFont.setBold(true);
                headerFont.setFontHeightInPoints((short) 12);
                headerStyle.setFont(headerFont);
                headerStyle.setAlignment(HorizontalAlignment.CENTER);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                // 创建数据样式
                CellStyle dataStyle = workbook.createCellStyle();
                dataStyle.setAlignment(HorizontalAlignment.LEFT);
                dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                // 创建标题行
                Row headerRow = sheet.createRow(0);
                String[] headers = {"ID", "聊天室ID", "员工ID", "考试用户姓名", "考试用户编号", "考试分数", "团队ID", "创建时间", "更新时间", "创建人", "更新人", "版本号"};
                
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(headerStyle);
                }

                // 填充数据
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                for (int i = 0; i < records.size(); i++) {
                    TrainQaReportMain record = records.get(i);
                    Row row = sheet.createRow(i + 1);

                    // ID
                    Cell cell0 = row.createCell(0);
                    cell0.setCellValue(record.getId());
                    cell0.setCellStyle(dataStyle);

                    // 聊天室ID
                    Cell cell1 = row.createCell(1);
                    if (record.getChatroomId() != null) {
                        cell1.setCellValue(record.getChatroomId());
                    }
                    cell1.setCellStyle(dataStyle);

                    // 员工ID
                    Cell cell2 = row.createCell(2);
                    if (record.getStaffId() != null) {
                        cell2.setCellValue(record.getStaffId());
                    }
                    cell2.setCellStyle(dataStyle);

                    // 考试用户姓名
                    Cell cell3 = row.createCell(3);
                    if (record.getExamUserRealName() != null) {
                        cell3.setCellValue(record.getExamUserRealName());
                    }
                    cell3.setCellStyle(dataStyle);

                    // 考试用户编号
                    Cell cell4 = row.createCell(4);
                    if (record.getExamUserNo() != null) {
                        cell4.setCellValue(record.getExamUserNo());
                    }
                    cell4.setCellStyle(dataStyle);

                    // 考试分数
                    Cell cell5 = row.createCell(5);
                    if (record.getExamScore() != null) {
                        cell5.setCellValue(record.getExamScore().doubleValue());
                    }
                    cell5.setCellStyle(dataStyle);

                    // 团队ID
                    Cell cell6 = row.createCell(6);
                    cell6.setCellValue(record.getTeamId());
                    cell6.setCellStyle(dataStyle);

                    // 创建时间
                    Cell cell7 = row.createCell(7);
                    if (record.getCreateTime() != null) {
                        cell7.setCellValue(record.getCreateTime().format(formatter));
                    }
                    cell7.setCellStyle(dataStyle);

                    // 更新时间
                    Cell cell8 = row.createCell(8);
                    if (record.getUpdateTime() != null) {
                        cell8.setCellValue(record.getUpdateTime().format(formatter));
                    }
                    cell8.setCellStyle(dataStyle);

                    // 创建人
                    Cell cell9 = row.createCell(9);
                    if (record.getCreator() != null) {
                        cell9.setCellValue(record.getCreator());
                    }
                    cell9.setCellStyle(dataStyle);

                    // 更新人
                    Cell cell10 = row.createCell(10);
                    if (record.getUpdater() != null) {
                        cell10.setCellValue(record.getUpdater());
                    }
                    cell10.setCellStyle(dataStyle);

                    // 版本号
                    Cell cell11 = row.createCell(11);
                    if (record.getVersion() != null) {
                        cell11.setCellValue(record.getVersion());
                    }
                    cell11.setCellStyle(dataStyle);
                }

                // 自动调整列宽
                for (int i = 0; i < headers.length; i++) {
                    sheet.autoSizeColumn(i);
                }

                // 输出到字节数组
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                workbook.write(outputStream);
                
                log.info("Excel导出成功，共{}条记录", records.size());
                return outputStream.toByteArray();

            }
        } catch (IOException e) {
            log.error("导出Excel时IO异常：request={}, teamId={}", request, teamId, e);
            throw new RuntimeException("导出Excel失败：IO错误", e);
        } catch (Exception e) {
            log.error("导出Excel异常：request={}, teamId={}", request, teamId, e);
            throw e;
        }
    }
}
