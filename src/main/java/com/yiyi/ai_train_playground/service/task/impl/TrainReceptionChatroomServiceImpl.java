package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.*;
import com.yiyi.ai_train_playground.entity.task.TrainChatroomStaff;
import com.yiyi.ai_train_playground.entity.task.TrainChatroomTask;
import com.yiyi.ai_train_playground.entity.task.TrainReceptionChatroom;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.mapper.task.TrainChatroomStaffMapper;
import com.yiyi.ai_train_playground.mapper.task.TrainChatroomTaskMapper;
import com.yiyi.ai_train_playground.mapper.task.TrainReceptionChatroomMapper;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper;
import com.yiyi.ai_train_playground.service.task.TrainReceptionChatroomService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 接待聊天室服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@Slf4j
@Service
public class TrainReceptionChatroomServiceImpl implements TrainReceptionChatroomService {
    
    @Autowired
    private TrainReceptionChatroomMapper chatroomMapper;
    
    @Autowired
    private TrainChatroomTaskMapper chatroomTaskMapper;
    
    @Autowired
    private TrainChatroomStaffMapper chatroomStaffMapper;

    @Autowired
    private TrainStaffMapper trainStaffMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createChatroomWithTasks(ChatroomCreateRequest request, Long teamId, String creator) {
        log.info("创建聊天室及关联任务：teamId={}, creator={}", teamId, creator);
        
        // 1. 创建聊天室主记录
        TrainReceptionChatroom chatroom = new TrainReceptionChatroom();
        chatroom.setRoomName(request.getRoomName());
        chatroom.setReceptionSkin(request.getReceptionSkin());
        chatroom.setSceneMode(request.getSceneMode());
        chatroom.setQuickPhrasesId(request.getQuickPhrasesId());
        chatroom.setReceptionDuration(request.getReceptionDuration());
        chatroom.setTimerDisplay(request.getTimerDisplay());
        chatroom.setEntryFreqMin(request.getEntryFreqMin());
        chatroom.setEntryFreqMax(request.getEntryFreqMax());
        chatroom.setTeamId(teamId);
        chatroom.setCreator(creator);
        chatroom.setUpdater(creator);
        
        int insertResult = chatroomMapper.insert(chatroom);
        if (insertResult <= 0) {
            throw new RuntimeException("创建聊天室失败");
        }
        
        Long chatroomId = chatroom.getId();
        log.info("聊天室创建成功，ID={}", chatroomId);
        
        // 2. 创建关联任务记录
        if (!CollectionUtils.isEmpty(request.getTaskList())) {
            List<TrainChatroomTask> chatroomTasks = new ArrayList<>();
            for (ChatroomCreateRequest.ChatroomTaskCreateDTO taskDTO : request.getTaskList()) {
                TrainChatroomTask chatroomTask = new TrainChatroomTask();
                chatroomTask.setChatroomId(chatroomId);
                chatroomTask.setTaskId(taskDTO.getTaskId());
                chatroomTask.setTrainingRecycleCnt(taskDTO.getTrainingRecycleCnt());
                chatroomTask.setTeamId(teamId);
                chatroomTask.setCreator(creator);
                chatroomTask.setUpdater(creator);
                chatroomTasks.add(chatroomTask);
            }
            
            int batchInsertResult = chatroomTaskMapper.batchInsert(chatroomTasks);
            log.info("批量创建聊天室任务关联成功，数量={}", batchInsertResult);
        }
        
        // 3. 创建员工关联记录
        if (!CollectionUtils.isEmpty(request.getStaffList())) {
            List<TrainChatroomStaff> chatroomStaffs = new ArrayList<>();
            for (Long staffId : request.getStaffList()) {
                TrainChatroomStaff chatroomStaff = new TrainChatroomStaff(chatroomId, staffId, teamId, creator);
                chatroomStaffs.add(chatroomStaff);
            }
            
            int batchInsertStaffResult = chatroomStaffMapper.batchInsert(chatroomStaffs);
            log.info("批量创建聊天室员工关联成功，数量={}", batchInsertStaffResult);
        }
        
        return chatroomId;
    }
    
    @Override
    public ChatroomDetailDTO getChatroomDetail(Long id, Long teamId) {
        log.info("查询聊天室详情：id={}, teamId={}", id, teamId);
        
        // 查询聊天室基本信息
        ChatroomDetailDTO detail = chatroomMapper.selectDetailById(id, teamId);
        if (detail == null) {
            throw new RuntimeException("聊天室不存在或无权限访问");
        }
        
        // 查询关联任务列表
        List<TrainChatroomTask> chatroomTasks = chatroomTaskMapper.selectByChatroomId(id, teamId);
        if (!CollectionUtils.isEmpty(chatroomTasks)) {
            List<ChatroomDetailDTO.ChatroomTaskDetailDTO> taskList = new ArrayList<>();
            for (TrainChatroomTask chatroomTask : chatroomTasks) {
                ChatroomDetailDTO.ChatroomTaskDetailDTO taskDetailDTO = new ChatroomDetailDTO.ChatroomTaskDetailDTO();
                taskDetailDTO.setId(chatroomTask.getId());
                taskDetailDTO.setTaskId(chatroomTask.getTaskId());
                // TODO: 这里需要根据taskId查询任务名称，暂时设置为空
                taskDetailDTO.setTaskName("任务-" + chatroomTask.getTaskId());
                taskDetailDTO.setTrainingRecycleCnt(chatroomTask.getTrainingRecycleCnt());
                taskList.add(taskDetailDTO);
            }
            detail.setTaskList(taskList);
        }
        
        // 查询关联员工ID列表
        List<Long> staffIds = chatroomStaffMapper.selectStaffIdsByReceChatroomId(id, teamId);
        detail.setStaffList(staffIds != null ? staffIds : new ArrayList<>());
        
        return detail;
    }
    
    @Override
    public PageResult<ChatroomListDTO> getChatroomList(ChatroomQueryRequest queryRequest, Long teamId) {
        log.info("分页查询聊天室列表：queryRequest={}, teamId={}", queryRequest, teamId);
        
        // 获取当前用户creator
        String creator = SecurityUtil.getCurrentUsername();
        
        // 计算分页偏移量
        int offset = (queryRequest.getPage() - 1) * queryRequest.getPageSize();
        queryRequest.setOffset(offset);

        // 清理空字符串参数为null
        if(queryRequest.getReceptionSkin() != null && queryRequest.getReceptionSkin().equals("")){
            queryRequest.setReceptionSkin(null);
        }
        if(queryRequest.getRoomName() != null && queryRequest.getRoomName().equals("")){
            queryRequest.setRoomName(null);
        }

        // 查询总数
        Long total = chatroomMapper.selectPageCount(queryRequest, teamId, creator);
        
        // 查询列表数据
        List<ChatroomListDTO> records = chatroomMapper.selectPageList(queryRequest, teamId, creator);
        if (records == null) {
            records = new ArrayList<>();
        }

        // 构建分页结果
        PageResult<ChatroomListDTO> pageResult = new PageResult<>();
        pageResult.setRecords(records);
        pageResult.setTotal(total);

        log.info("查询聊天室列表成功：total={}, records.size={}", total, records.size());

        return pageResult;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateChatroomWithTasks(ChatroomUpdateRequest request, Long teamId, String updater) {
        log.info("更新聊天室及关联任务：id={}, teamId={}, updater={}", request.getId(), teamId, updater);
        
        // 1. 更新聊天室主记录
        TrainReceptionChatroom chatroom = new TrainReceptionChatroom();
        chatroom.setId(request.getId());
        chatroom.setRoomName(request.getRoomName());
        chatroom.setReceptionSkin(request.getReceptionSkin());
        chatroom.setSceneMode(request.getSceneMode());
        chatroom.setQuickPhrasesId(request.getQuickPhrasesId());
        chatroom.setReceptionDuration(request.getReceptionDuration());
        chatroom.setTimerDisplay(request.getTimerDisplay());
        chatroom.setEntryFreqMin(request.getEntryFreqMin());
        chatroom.setEntryFreqMax(request.getEntryFreqMax());
        chatroom.setTeamId(teamId);
        chatroom.setUpdater(updater);
        chatroom.setVersion(request.getVersion());
        
        int updateResult = chatroomMapper.updateById(chatroom);
        if (updateResult <= 0) {
            throw new RuntimeException("聊天室更新失败，可能是版本冲突或无权限");
        }
        
        // 2. 更新关联任务记录（先删除再新增的策略）
        if (request.getTaskList() != null) {
            // 删除原有关联
            chatroomTaskMapper.deleteByChatroomId(request.getId(), teamId);
            
            // 重新创建关联
            if (!CollectionUtils.isEmpty(request.getTaskList())) {
                List<TrainChatroomTask> chatroomTasks = new ArrayList<>();
                for (ChatroomUpdateRequest.ChatroomTaskUpdateDTO taskDTO : request.getTaskList()) {
                    TrainChatroomTask chatroomTask = new TrainChatroomTask();
                    chatroomTask.setChatroomId(request.getId());
                    chatroomTask.setTaskId(taskDTO.getTaskId());
                    chatroomTask.setTrainingRecycleCnt(taskDTO.getTrainingRecycleCnt());
                    chatroomTask.setTeamId(teamId);
                    chatroomTask.setCreator(updater);
                    chatroomTask.setUpdater(updater);
                    chatroomTasks.add(chatroomTask);
                }
                
                chatroomTaskMapper.batchInsert(chatroomTasks);
                log.info("重新创建聊天室任务关联成功，数量={}", chatroomTasks.size());
            }
        }
        
        // 3. 更新员工关联记录（先删除再新增的策略）
        if (request.getStaffList() != null) {
            // 删除原有员工关联
            chatroomStaffMapper.deleteByReceChatroomId(request.getId(), teamId);
            
            // 重新创建员工关联
            if (!CollectionUtils.isEmpty(request.getStaffList())) {
                List<TrainChatroomStaff> chatroomStaffs = new ArrayList<>();
                for (Long staffId : request.getStaffList()) {
                    TrainChatroomStaff chatroomStaff = new TrainChatroomStaff(request.getId(), staffId, teamId, updater);
                    chatroomStaffs.add(chatroomStaff);
                }
                
                chatroomStaffMapper.batchInsert(chatroomStaffs);
                log.info("重新创建聊天室员工关联成功，数量={}", chatroomStaffs.size());
            }
        }
        
        log.info("聊天室更新成功：id={}", request.getId());
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteChatroom(Long id, Long teamId) {
        log.info("删除聊天室：id={}, teamId={}", id, teamId);
        
        // 1. 删除关联任务记录
        chatroomTaskMapper.deleteByChatroomId(id, teamId);
        
        // 2. 删除员工关联记录
        chatroomStaffMapper.deleteByReceChatroomId(id, teamId);
        
        // 3. 删除聊天室主记录
        int deleteResult = chatroomMapper.deleteById(id, teamId);
        if (deleteResult <= 0) {
            throw new RuntimeException("聊天室删除失败或无权限");
        }
        
        log.info("聊天室删除成功：id={}", id);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteChatrooms(String ids, Long teamId) {
        log.info("批量删除聊天室：ids={}, teamId={}", ids, teamId);
        
        if (ids == null || ids.trim().isEmpty()) {
            throw new RuntimeException("删除ID列表不能为空");
        }
        
        // 解析ID列表
        String[] idArray = ids.split(",");
        List<Long> idList = new ArrayList<>();
        for (String idStr : idArray) {
            try {
                Long id = Long.parseLong(idStr.trim());
                idList.add(id);
                // 删除每个聊天室的关联任务
                chatroomTaskMapper.deleteByChatroomId(id, teamId);
                // 删除每个聊天室的员工关联
                chatroomStaffMapper.deleteByReceChatroomId(id, teamId);
            } catch (NumberFormatException e) {
                log.warn("无效的聊天室ID：{}", idStr);
            }
        }

        // 批量删除聊天室主记录
        int deleteResult = chatroomMapper.batchDeleteByIds(idList, teamId);
        
        log.info("批量删除聊天室成功：删除数量={}", deleteResult);
        return deleteResult > 0;
    }
    
    @Override
    public PageResult<ChatroomListDTO> getMyTasks(ChatroomQueryRequest queryRequest, Long staffId, Long teamId) {
        log.info("查询员工的接待聊天任务：staffId={}, teamId={}, queryRequest={}", staffId, teamId, queryRequest);
        
        try {
            // 设置分页参数
            if (queryRequest.getPage() == null) {
                queryRequest.setPage(1);
            }
            if (queryRequest.getPageSize() == null) {
                queryRequest.setPageSize(10);
            }
            
            // 计算偏移量
            int offset = (queryRequest.getPage() - 1) * queryRequest.getPageSize();
            queryRequest.setOffset(offset);
            
            // 查询总数
            Long total = chatroomMapper.selectMyTasksPageCount(queryRequest, staffId, teamId);
            
            // 查询列表数据
            List<ChatroomListDTO> records = chatroomMapper.selectMyTasksPageList(queryRequest, staffId, teamId);
            
            log.info("查询员工接待聊天任务成功：staffId={}, 总数={}, 当前页数量={}", staffId, total, records.size());
            
            return new PageResult<>(records, total, queryRequest.getPage(), queryRequest.getPageSize());
            
        } catch (Exception e) {
            log.error("查询员工接待聊天任务失败：staffId={}, teamId={}", staffId, teamId, e);
            throw new RuntimeException("查询员工接待聊天任务失败：" + e.getMessage());
        }
    }

    @Override
    public TrainStaff validateStaffPermission(Long receChatRoomId, Long staffId) {
        log.info("验证员工权限：receChatRoomId={}, staffId={}", receChatRoomId, staffId);

        try {
            // 查询员工是否有权限访问指定聊天室
            TrainStaff staff = chatroomMapper.selectStaffByReceChatroomId(receChatRoomId, staffId);

            if (staff != null) {
                log.info("员工权限验证成功：staffId={}, displayName={}", staffId, staff.getDisplayName());
            } else {
                log.warn("员工无权限访问聊天室：receChatRoomId={}, staffId={}", receChatRoomId, staffId);
            }

            return staff;

        } catch (Exception e) {
            log.error("验证员工权限失败：receChatRoomId={}, staffId={}", receChatRoomId, staffId, e);
            throw new RuntimeException("验证员工权限失败：" + e.getMessage());
        }
    }

    @Override
    public List<ChatRoomTaskInfo> getChatRoomTaskList(Long receChatRoomId) {
        log.info("查询聊天室任务列表：receChatRoomId={}", receChatRoomId);

        try {
            List<ChatRoomTaskInfo> taskList = chatroomMapper.selectChatRoomTaskList(receChatRoomId);

            log.info("查询聊天室任务列表成功：receChatRoomId={}, 任务数量={}", receChatRoomId, taskList.size());

            return taskList;

        } catch (Exception e) {
            log.error("查询聊天室任务列表失败：receChatRoomId={}", receChatRoomId, e);
            throw new RuntimeException("查询聊天室任务列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<ChatRoomTaskInfo> getCrTL4Qa(Long receChatRoomId) {
        log.info("查询问答聊天室任务列表：receChatRoomId={}", receChatRoomId);

        try {
            List<ChatRoomTaskInfo> taskList = chatroomMapper.selectCrTL4Qa(receChatRoomId);

            log.info("查询问答聊天室任务列表成功：receChatRoomId={}, 任务数量={}", receChatRoomId, taskList.size());

            return taskList;

        } catch (Exception e) {
            log.error("查询问答聊天室任务列表失败：receChatRoomId={}", receChatRoomId, e);
            throw new RuntimeException("查询问答聊天室任务列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<TaskAvailableCheckDTO> checkTasksAvailability(Long chatroomId, Long teamId) {
        log.info("检查聊天室任务可用性：chatroomId={}, teamId={}", chatroomId, teamId);

        try {
            List<TaskAvailableCheckDTO> learningTasks = chatroomMapper.checkTasksLearningStatus(chatroomId, teamId);
            
            log.info("聊天室任务可用性检查完成：chatroomId={}, 正在学习的任务数={}", 
                    chatroomId, learningTasks != null ? learningTasks.size() : 0);
            
            return learningTasks != null ? learningTasks : new ArrayList<>();

        } catch (Exception e) {
            log.error("检查聊天室任务可用性失败：chatroomId={}, teamId={}", chatroomId, teamId, e);
            throw new RuntimeException("检查聊天室任务可用性失败：" + e.getMessage());
        }
    }
}