package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskCreateRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskDetailDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskListDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskQueryRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskUpdateRequest;
import com.yiyi.ai_train_playground.entity.task.TrainReceptionTask;
import com.yiyi.ai_train_playground.mapper.task.TrainReceptionTaskMapper;
import com.yiyi.ai_train_playground.service.task.TrainReceptionTaskService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 接待任务服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@Service
public class TrainReceptionTaskServiceImpl implements TrainReceptionTaskService {
    
    @Autowired
    private TrainReceptionTaskMapper receptionTaskMapper;
    
    @Override
    public PageResult<TrainReceptionTaskListDTO> getTaskList(TrainReceptionTaskQueryRequest request, Long teamId) {
        log.info("查询接待任务列表，teamId: {}, request: {}", teamId, request);

        // 参数验证和修正
        if (request.getPage() == null || request.getPage() < 1) {
            request.setPage(1);
        }
        if (request.getPageSize() == null || request.getPageSize() < 1) {
            request.setPageSize(10);
        }

        String creator = SecurityUtil.getCurrentUsername();

        // 计算偏移量
        int offset = (request.getPage() - 1) * request.getPageSize();

        // 查询列表数据
        List<TrainReceptionTaskListDTO> list = receptionTaskMapper.selectTaskList(request, teamId, creator, offset, request.getPageSize());

        // 查询总数
        Long total = receptionTaskMapper.countTasks(request, teamId, creator);

        log.info("查询接待任务列表完成，共 {} 条记录", total);
        return new PageResult<>(list, total, request.getPage(), request.getPageSize());
    }
    
    @Override
    public TrainReceptionTaskDetailDTO getTaskDetail(Long id, Long teamId) {
        log.info("查询接待任务详情，id: {}, teamId: {}", id, teamId);
        
        TrainReceptionTaskDetailDTO detail = receptionTaskMapper.selectDetailById(id, teamId);
        if (detail == null) {
            log.warn("接待任务不存在，id: {}, teamId: {}", id, teamId);
            return null;
        }
        
        log.info("查询接待任务详情完成，任务名称: {}", detail.getTaskName());
        return detail;
    }
    
    @Override
    public TrainReceptionTask getTaskById(Long id, Long teamId) {
        log.info("查询接待任务，id: {}, teamId: {}", id, teamId);
        
        TrainReceptionTask task = receptionTaskMapper.selectById(id, teamId);
        if (task == null) {
            log.warn("接待任务不存在，id: {}, teamId: {}", id, teamId);
        }
        
        return task;
    }
    
    @Override
    @Transactional
    public Long createTask(TrainReceptionTaskCreateRequest request, Long teamId, String creator) {
        log.info("创建接待任务，teamId: {}, creator: {}, taskName: {}", teamId, creator, request.getTaskName());
        
        // 创建任务实体
        TrainReceptionTask task = new TrainReceptionTask();
        BeanUtils.copyProperties(request, task);
        
        // 设置系统字段
        task.setTeamId(teamId);
        task.setCreator(creator);
        task.setUpdater(creator);
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        task.setVersion(0L);
        
        // 设置默认值
        if (task.getReceptionDuration() == null) {
            task.setReceptionDuration(30);
        }
        if (task.getQuestionIntervalType() == null) {
            task.setQuestionIntervalType(0);
        }
        if (task.getQuestionIntervalSeconds() == null) {
            task.setQuestionIntervalSeconds(3);
        }
        if (task.getTrainingLimitEnabled() == null) {
            task.setTrainingLimitEnabled(false);
        }
        if (task.getTaskPurposeTag() == null) {
            task.setTaskPurposeTag(0);
        }
        if (task.getJudgeType() == null) {
            task.setJudgeType(0);
        }
        
        // 插入数据库
        int result = receptionTaskMapper.insert(task);
        if (result > 0) {
            log.info("创建接待任务成功，taskId: {}", task.getId());
            return task.getId();
        } else {
            log.error("创建接待任务失败");
            throw new RuntimeException("创建接待任务失败");
        }
    }

    @Override
    @Transactional
    public boolean updateTask(TrainReceptionTaskUpdateRequest request, Long teamId, String updater) {
        log.info("更新接待任务，id: {}, teamId: {}, updater: {}", request.getId(), teamId, updater);

        // 先查询任务是否存在
        TrainReceptionTask existingTask = receptionTaskMapper.selectById(request.getId(), teamId);
        if (existingTask == null) {
            log.warn("接待任务不存在，id: {}, teamId: {}", request.getId(), teamId);
            return false;
        }

        // 创建更新实体
        TrainReceptionTask task = new TrainReceptionTask();
        BeanUtils.copyProperties(request, task);

        // 设置系统字段
        task.setTeamId(teamId);
        task.setUpdater(updater);
        task.setUpdateTime(LocalDateTime.now());
        task.setVersion(existingTask.getVersion());

        // 执行更新
        int result = receptionTaskMapper.update(task);
        if (result > 0) {
            log.info("更新接待任务成功，id: {}", request.getId());
            return true;
        } else {
            log.warn("更新接待任务失败，可能是版本冲突，id: {}", request.getId());
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateTask(Long taskId, Long amtToBeLearned, Long teamId) {
        log.info("更新任务待学习数量，taskId: {}, amtToBeLearned: {}", taskId, amtToBeLearned);
        
        // 1.1 先用 TrainReceptionTask existingTask = receptionTaskMapper.selectById(request.getId(), teamId);
        // 注意：这里需要传入teamId，但方法签名中没有teamId，我们需要从查询中获取
        // 为了简化，我们先查询任务获取teamId
      /*  TrainReceptionTask existingTask = receptionTaskMapper.selectLearningInfo(taskId);
        if (existingTask == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return false;
        }*/
        
        // 重新查询完整信息，使用taskId和teamId
        TrainReceptionTask existingTask = receptionTaskMapper.selectById(taskId, teamId);
        if (existingTask == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return false;
        }
        
        // 1.2 再执行existingTask.setAmtToBeLearned(amtToBeLearned)
        existingTask.setAmtToBeLearned(amtToBeLearned);
        existingTask.setUpdateTime(LocalDateTime.now());
        
        // 1.3 // 执行更新 int result = receptionTaskMapper.update(task);
        int result = receptionTaskMapper.update(existingTask);
        
        if (result > 0) {
            log.info("更新任务待学习数量成功，taskId: {}", taskId);
            return true;
        } else {
            log.warn("更新任务待学习数量失败，可能是版本冲突，taskId: {}", taskId);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteTask(Long id, Long teamId) {
        log.info("删除接待任务，id: {}, teamId: {}", id, teamId);

        int result = receptionTaskMapper.deleteById(id, teamId);
        if (result > 0) {
            log.info("删除接待任务成功，id: {}", id);
            return true;
        } else {
            log.warn("删除接待任务失败，任务不存在，id: {}", id);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteTasks(String ids, Long teamId) {
        log.info("批量删除接待任务，ids: {}, teamId: {}", ids, teamId);

        if (!StringUtils.hasText(ids)) {
            log.warn("批量删除接待任务失败，ids为空");
            return false;
        }

        // 解析ID列表
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        if (idList.isEmpty()) {
            log.warn("批量删除接待任务失败，解析后的ID列表为空");
            return false;
        }

        int result = receptionTaskMapper.batchDeleteByIds(idList, teamId);
        log.info("批量删除接待任务完成，删除了 {} 条记录", result);
        return result > 0;
    }
}
