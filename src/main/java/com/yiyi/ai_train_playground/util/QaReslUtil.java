package com.yiyi.ai_train_playground.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.task.QaResolveResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * QA解析工具类
 * 专门解析train_qa_rdm表的resolve字段
 */
@Slf4j
@Component
public class QaReslUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 解析resolve字段，提取完整的QA解析结果
     * 兼容两种格式：
     * 1. 嵌套格式：{"resp":{"msgId":"...","results":["..."]}}
     * 2. 直接格式：{"分析":"...","得分":0,"扣分点":[],"正确答案":"...","下一题":"..."}
     *
     * @param lastRobotResp JSON字符串
     * @return QaResolveResultDTO对象，包含analysis、score、deductionPoints、correctAnswer、nextQuestion
     */
    public static QaResolveResultDTO parseResolve(String lastRobotResp) {
        try {
            log.info("开始解析resolve字段：{}", lastRobotResp);

            // 1. 解析外层JSON
            Map<String, Object> outerJson = objectMapper.readValue(lastRobotResp, Map.class);

            // 2. 检查是否为直接格式（包含中文字段）
            if (outerJson.containsKey("分析") || outerJson.containsKey("得分")) {
                log.info("检测到直接JSON格式，直接解析中文字段");
                return parseDirectFormat(outerJson);
            }

            // 3. 处理嵌套格式
            Object respObj = outerJson.get("resp");
            if (!(respObj instanceof Map)) {
                log.warn("resp字段不是Map类型，返回空对象");
                return new QaResolveResultDTO();
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> respMap = (Map<String, Object>) respObj;

            // 4. 获取results数组
            Object resultsObj = respMap.get("results");
            if (!(resultsObj instanceof List)) {
                log.warn("results字段不是List类型，返回空对象");
                return new QaResolveResultDTO();
            }

            @SuppressWarnings("unchecked")
            List<String> resultsList = (List<String>) resultsObj;

            // 5. 拼接所有results元素
            StringBuilder allResults = new StringBuilder();
            for (String result : resultsList) {
                allResults.append(result);
            }

            String combinedResults = allResults.toString();
            log.info("拼接后的results内容：{}", combinedResults);

            // 6. 解析拼接后的JSON字符串
            Map<String, Object> innerJson = objectMapper.readValue(combinedResults, Map.class);
            return parseDirectFormat(innerJson);

        } catch (Exception e) {
            log.warn("解析resolve字段失败，返回空对象：{}", e.getMessage());
            return new QaResolveResultDTO();
        }
    }

    /**
     * 解析直接格式的JSON（包含中文字段）
     *
     * @param jsonMap JSON Map对象
     * @return QaResolveResultDTO对象
     */
    private static QaResolveResultDTO parseDirectFormat(Map<String, Object> jsonMap) {
        QaResolveResultDTO result = new QaResolveResultDTO();

        // 映射中文字段到DTO
        Object analysisObj = jsonMap.get("分析");
        if (analysisObj != null) {
            result.setAnalysis(analysisObj.toString());
        }

        Object scoreObj = jsonMap.get("得分");
        if (scoreObj != null) {
            if (scoreObj instanceof Number) {
                result.setScore(((Number) scoreObj).intValue());
            } else {
                try {
                    result.setScore(Integer.parseInt(scoreObj.toString()));
                } catch (NumberFormatException e) {
                    log.warn("无法解析得分字段: {}", scoreObj);
                    result.setScore(0);
                }
            }
        }

        Object deductionPointsObj = jsonMap.get("扣分点");
        if (deductionPointsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> deductionPoints = (List<String>) deductionPointsObj;
            result.setDeductionPoints(deductionPoints);
        }

        Object correctAnswerObj = jsonMap.get("正确答案");
        if (correctAnswerObj != null) {
            result.setCorrectAnswer(correctAnswerObj.toString());
        }

        Object nextQuestionObj = jsonMap.get("下一题");
        if (nextQuestionObj != null) {
            result.setNextQuestion(nextQuestionObj.toString());
        }

        log.info("成功解析resolve字段，提取字段数量：analysis={}, score={}, deductionPoints={}, correctAnswer={}, nextQuestion={}",
                result.getAnalysis() != null ? "有" : "无",
                result.getScore() != null ? result.getScore() : "无",
                result.getDeductionPoints() != null ? result.getDeductionPoints().size() : "无",
                result.getCorrectAnswer() != null ? "有" : "无",
                result.getNextQuestion() != null ? "有" : "无");

        return result;
    }

    /**
     * 从resolve字段中提取下一题问题（兼容原有方法）
     * 
     * @param lastRobotResponse JSON字符串
     * @return 下一题问题，如果解析失败则返回原字符串
     */
    public static String extractNextQuestion(String lastRobotResponse) {
        try {
            QaResolveResultDTO result = parseResolve(lastRobotResponse);
            if (result.getNextQuestion() != null && !result.getNextQuestion().isEmpty()) {
                return result.getNextQuestion();
            }
        } catch (Exception e) {
            log.warn("提取下一题问题失败：{}", e.getMessage());
        }
        
        // 如果解析失败或没有找到下一题，返回原字符串
        return lastRobotResponse;
    }
}