package com.yiyi.ai_train_playground.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 反射工具类
 * 提供反射相关的通用方法
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Slf4j
public class ReflectUtil {
    
    /**
     * 基础类型集合
     */
    private static final Set<Class<?>> PRIMITIVE_TYPES = new HashSet<>();
    
    static {
        PRIMITIVE_TYPES.add(String.class);
        PRIMITIVE_TYPES.add(Integer.class);
        PRIMITIVE_TYPES.add(Long.class);
        PRIMITIVE_TYPES.add(Double.class);
        PRIMITIVE_TYPES.add(Float.class);
        PRIMITIVE_TYPES.add(Boolean.class);
        PRIMITIVE_TYPES.add(Character.class);
        PRIMITIVE_TYPES.add(Byte.class);
        PRIMITIVE_TYPES.add(Short.class);
        PRIMITIVE_TYPES.add(BigDecimal.class);
        PRIMITIVE_TYPES.add(Date.class);
        PRIMITIVE_TYPES.add(LocalDate.class);
        PRIMITIVE_TYPES.add(LocalDateTime.class);
        
        // 原始类型
        PRIMITIVE_TYPES.add(int.class);
        PRIMITIVE_TYPES.add(long.class);
        PRIMITIVE_TYPES.add(double.class);
        PRIMITIVE_TYPES.add(float.class);
        PRIMITIVE_TYPES.add(boolean.class);
        PRIMITIVE_TYPES.add(char.class);
        PRIMITIVE_TYPES.add(byte.class);
        PRIMITIVE_TYPES.add(short.class);
    }
    
    /**
     * 设置字段值
     */
    public static void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            throw new RuntimeException("设置字段值失败: " + fieldName, e);
        }
    }
    
    /**
     * 获取字段值
     */
    @SuppressWarnings("unchecked")
    public static <T> T getFieldValue(Object obj, String fieldName, Class<T> targetType) {
        try {
            Field field = findField(obj.getClass(), fieldName);
            if (field == null) {
                log.debug("字段不存在: {}.{}", obj.getClass().getSimpleName(), fieldName);
                return null;
            }
            
            field.setAccessible(true);
            Object value = field.get(obj);
            return castValue(value, targetType);
        } catch (Exception e) {
            log.warn("获取字段值失败: {}.{}", obj.getClass().getSimpleName(), fieldName, e);
            return null;
        }
    }
    
    /**
     * 查找字段（支持继承）
     */
    public static Field findField(Class<?> clazz, String fieldName) {
        Class<?> searchClass = clazz;
        while (searchClass != null && searchClass != Object.class) {
            try {
                return searchClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                searchClass = searchClass.getSuperclass();
            }
        }
        return null;
    }
    
    /**
     * 类型转换
     */
    @SuppressWarnings("unchecked")
    public static <T> T castValue(Object value, Class<T> targetType) {
        if (value == null) {
            return null;
        }
        
        if (targetType.isInstance(value)) {
            return (T) value;
        }
        
        try {
            if (targetType == Long.class || targetType == long.class) {
                if (value instanceof Number) {
                    return (T) Long.valueOf(((Number) value).longValue());
                } else if (value instanceof String) {
                    return (T) Long.valueOf((String) value);
                }
            } else if (targetType == Integer.class || targetType == int.class) {
                if (value instanceof Number) {
                    return (T) Integer.valueOf(((Number) value).intValue());
                } else if (value instanceof String) {
                    return (T) Integer.valueOf((String) value);
                }
            } else if (targetType == String.class) {
                return (T) value.toString();
            }
        } catch (Exception e) {
            log.warn("类型转换失败: {} -> {}", value.getClass().getSimpleName(), targetType.getSimpleName(), e);
        }
        
        return null;
    }
    
    /**
     * 判断是否为基础类型
     */
    public static boolean isPrimitiveType(Class<?> clazz) {
        return clazz.isPrimitive() || PRIMITIVE_TYPES.contains(clazz);
    }
}