package com.yiyi.ai_train_playground.util;

import java.util.ArrayList;
import java.util.List;

/**
 * 剧本分组ID解析工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
public class ScriptGroupIdParser {
    
    /**
     * 解析scriptGroupIds字符串为Long列表
     * 用于MyBatis XML中的foreach循环
     * 
     * @param scriptGroupIds 逗号分隔的分组ID字符串
     * @return 分组ID列表
     */
    public static List<Long> parseIds(String scriptGroupIds) {
        List<Long> groupIds = new ArrayList<>();
        
        if (scriptGroupIds == null || scriptGroupIds.trim().isEmpty()) {
            return groupIds;
        }

        String[] idArray = scriptGroupIds.split(",");
        
        for (String idStr : idArray) {
            try {
                Long id = Long.parseLong(idStr.trim());
                // 过滤掉-1和1，因为它们有特殊处理逻辑
                if (id != -1L && id != 1L) {
                    groupIds.add(id);
                }
            } catch (NumberFormatException e) {
                // 忽略无效的ID格式
            }
        }
        
        return groupIds;
    }
    
    /**
     * 检查是否包含指定的分组ID
     * 
     * @param scriptGroupIds 逗号分隔的分组ID字符串
     * @param targetId 目标ID
     * @return 是否包含
     */
    public static boolean containsId(String scriptGroupIds, Long targetId) {
        if (scriptGroupIds == null || scriptGroupIds.trim().isEmpty() || targetId == null) {
            return false;
        }
        
        String[] idArray = scriptGroupIds.split(",");
        String targetStr = targetId.toString();
        
        for (String idStr : idArray) {
            if (targetStr.equals(idStr.trim())) {
                return true;
            }
        }
        
        return false;
    }
}
