package com.yiyi.ai_train_playground.util;

import com.yiyi.ai_train_playground.interceptor.tenant.TenantCondition;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.schema.Table;

/**
 * SQL处理工具类
 * 基于JSQLParser实现SQL的解析和条件注入
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@Slf4j
public class SqlUtil {
    
    /**
     * SQL解析超时时间（毫秒）
     */
    private static final int PARSE_TIMEOUT = 3000; // 3秒超时
    
    /**
     * 为SQL添加租户条件
     * 
     * @param originalSql 原始SQL
     * @param condition 租户条件
     * @return 修改后的SQL
     */
    public static String addTenantCondition(String originalSql, TenantCondition condition) {
        if (condition == null || condition.isEmpty()) {
            return originalSql;
        }
        
        // 快速检查：如果SQL过长或过复杂，跳过处理
        if (originalSql.length() > 5000 || isComplexQuery(originalSql)) {
            log.warn("SQL过于复杂，跳过租户拦截处理: 长度={}", originalSql.length());
            return originalSql;
        }
        
        try {
            // 使用超时控制的解析
            Statement statement = parseWithTimeout(originalSql);
            
            if (statement instanceof Select) {
                processSelectStatement((Select) statement, condition);
            } else if (statement instanceof Update) {
                processUpdateStatement((Update) statement, condition);
            } else if (statement instanceof Delete) {
                processDeleteStatement((Delete) statement, condition);
            } else if (statement instanceof Insert) {
                // INSERT语句通常不需要添加WHERE条件
                return originalSql;
            } else {
                log.debug("不支持的SQL类型，跳过处理: {}", statement.getClass().getSimpleName());
                return originalSql;
            }
            
            String modifiedSql = statement.toString();
            log.debug("SQL修改完成: {} -> {}", originalSql, modifiedSql);
            return modifiedSql;
            
        } catch (JSQLParserException e) {
            if (e.getMessage() != null && e.getMessage().contains("Time out")) {
                log.warn("SQL解析超时，跳过租户拦截: {}", originalSql);
            } else {
                log.warn("SQL解析失败，返回原始SQL: {}", originalSql, e);
            }
            return originalSql;
        } catch (Exception e) {
            log.error("SQL处理异常，返回原始SQL: {}", originalSql, e);
            return originalSql;
        }
    }
    
    /**
     * 带超时控制的SQL解析
     */
    private static Statement parseWithTimeout(String sql) throws JSQLParserException {
        // 简单的超时控制实现
        long startTime = System.currentTimeMillis();
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            long elapsed = System.currentTimeMillis() - startTime;
            if (elapsed > PARSE_TIMEOUT) {
                log.warn("SQL解析耗时过长: {}ms, SQL长度: {}", elapsed, sql.length());
            }
            return statement;
        } catch (Exception e) {
            long elapsed = System.currentTimeMillis() - startTime;
            if (elapsed > PARSE_TIMEOUT) {
                throw new JSQLParserException("Time out occurred during SQL parsing after " + elapsed + "ms");
            }
            throw e;
        }
    }
    
    /**
     * 检查是否为复杂查询
     */
    private static boolean isComplexQuery(String sql) {
        if (sql == null) return false;
        
        String upperSql = sql.toUpperCase();
        
        // 检查复杂查询特征
        int complexity = 0;
        
        // 多表JOIN
        if (upperSql.contains(" JOIN ")) complexity += 2;
        if (upperSql.contains(" LEFT JOIN ")) complexity += 2;
        if (upperSql.contains(" RIGHT JOIN ")) complexity += 2;
        if (upperSql.contains(" INNER JOIN ")) complexity += 2;
        
        // 子查询
        if (countOccurrences(upperSql, " SELECT ") > 1) complexity += 3;
        
        // UNION操作
        if (upperSql.contains(" UNION ")) complexity += 3;
        
        // 复杂函数
        if (upperSql.contains(" CASE ")) complexity += 1;
        if (upperSql.contains(" WITH ")) complexity += 2; // CTE
        
        // 大量WHERE条件
        if (countOccurrences(upperSql, " AND ") > 10) complexity += 2;
        if (countOccurrences(upperSql, " OR ") > 5) complexity += 2;
        
        return complexity > 8; // 复杂度阈值
    }
    
    /**
     * 计算子字符串出现次数
     */
    private static int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
    
    /**
     * 处理SELECT语句
     */
    private static void processSelectStatement(Select select, TenantCondition condition) {
        if (select.getSelectBody() instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
            Expression whereExpression = plainSelect.getWhere();
            String mainTableAlias = getMainTableAlias(plainSelect);
            Expression tenantExpression = buildTenantExpression(condition, mainTableAlias);
            
            if (whereExpression == null) {
                plainSelect.setWhere(tenantExpression);
            } else {
                plainSelect.setWhere(new AndExpression(whereExpression, tenantExpression));
            }
        }
    }
    
    /**
     * 处理UPDATE语句
     */
    private static void processUpdateStatement(Update update, TenantCondition condition) {
        Expression whereExpression = update.getWhere();
        String mainTableAlias = getUpdateTableAlias(update);
        Expression tenantExpression = buildTenantExpression(condition, mainTableAlias);
        
        if (whereExpression == null) {
            update.setWhere(tenantExpression);
        } else {
            update.setWhere(new AndExpression(whereExpression, tenantExpression));
        }
    }
    
    /**
     * 处理DELETE语句
     */
    private static void processDeleteStatement(Delete delete, TenantCondition condition) {
        Expression whereExpression = delete.getWhere();
        String mainTableAlias = getDeleteTableAlias(delete);
        Expression tenantExpression = buildTenantExpression(condition, mainTableAlias);
        
        if (whereExpression == null) {
            delete.setWhere(tenantExpression);
        } else {
            delete.setWhere(new AndExpression(whereExpression, tenantExpression));
        }
    }
    
    /**
     * 构建租户过滤表达式
     * 
     * @param condition 租户条件
     * @param tableAlias 主表别名，如果为null则不使用别名
     */
    private static Expression buildTenantExpression(TenantCondition condition, String tableAlias) {
        Expression expression = null;
        
        // 添加team_id条件
        if (condition.hasTeamId()) {
            EqualsTo teamIdEquals = new EqualsTo();
            String teamIdColumn = (tableAlias != null) ? tableAlias + ".team_id" : "team_id";
            teamIdEquals.setLeftExpression(new Column(teamIdColumn));
            teamIdEquals.setRightExpression(new LongValue(condition.getTeamId()));
            expression = teamIdEquals;
        }
        
        // 添加creator条件
        if (condition.hasCreator()) {
            EqualsTo creatorEquals = new EqualsTo();
            String creatorColumn = (tableAlias != null) ? tableAlias + ".creator" : "creator";
            creatorEquals.setLeftExpression(new Column(creatorColumn));
            creatorEquals.setRightExpression(new StringValue(condition.getCreator()));

            if (expression == null) {
                expression = creatorEquals;
            } else {
                expression = new AndExpression(expression, creatorEquals);
            }
        }
        
        return expression;
    }
    
    /**
     * 获取SELECT语句的主表别名
     * 从FROM子句中获取第一个表的别名，如果没有别名则返回null
     */
    private static String getMainTableAlias(PlainSelect plainSelect) {
        try {
            FromItem fromItem = plainSelect.getFromItem();
            if (fromItem instanceof Table) {
                Table table = (Table) fromItem;
                return table.getAlias() != null ? table.getAlias().getName() : null;
            }
        } catch (Exception e) {
            log.debug("获取主表别名失败", e);
        }
        return null;
    }
    
    /**
     * 获取UPDATE语句的表别名
     */
    private static String getUpdateTableAlias(Update update) {
        try {
            Table table = update.getTable();
            if (table != null) {
                return table.getAlias() != null ? table.getAlias().getName() : null;
            }
        } catch (Exception e) {
            log.debug("获取UPDATE表别名失败", e);
        }
        return null;
    }
    
    /**
     * 获取DELETE语句的表别名
     */
    private static String getDeleteTableAlias(Delete delete) {
        try {
            Table table = delete.getTable();
            if (table != null) {
                return table.getAlias() != null ? table.getAlias().getName() : null;
            }
        } catch (Exception e) {
            log.debug("获取DELETE表别名失败", e);
        }
        return null;
    }
    
    /**
     * 判断SQL是否需要处理
     * 简单的启发式规则，避免处理不必要的SQL
     */
    public static boolean shouldProcess(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        String upperSql = sql.toUpperCase().trim();
        
        // 只处理SELECT、UPDATE、DELETE语句
        return upperSql.startsWith("SELECT") || 
               upperSql.startsWith("UPDATE") || 
               upperSql.startsWith("DELETE");
    }
}