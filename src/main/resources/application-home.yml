server:
  port: 8081
  tomcat:
    threads:
      max: 200 # 统一设置最大线程

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:www.yiyiailocal.com}:${DB_PORT:3306}/${DB_NAME:yiyi_ai_db}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&allowPublicKeyRetrieval=true&useSSL=false
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 100
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
#  mvc:
#    servlet:
#      path: /api
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:www.yiyiailocal.com} # Redis服务器地址
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:123456} # Redis密码
      database: ${REDIS_DATABASE:0} # 指定数据库
      ssl:
        enabled: false # 开发环境不启用SSL
      # Lettuce连接池配置（关键优化点）
      lettuce:
        pool:
          max-active: 150 # 最大连接数，增加以支持更多并发
          max-idle: 50   # 最大空闲连接
          min-idle: 10    # 最小空闲连接
          max-wait: 3000 # 获取连接最大等待时间(ms)
          time-between-eviction-runs: 30000 # 空闲连接检查周期(ms)
  # 定时任务配置
  task:
    scheduling:
      enabled: ${SPRING_TASK_SCHEDULING_ENABLED:false} # 定时任务总开关，默认启用
      pool:
        size: 5 # 定时任务线程池大小
      thread-name-prefix: "scheduler-" # 线程名前缀

mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.yiyi.ai_train_playground.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    root: INFO
    com.yiyi.ai_train_playground: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log

jwt:
  secret: ${JWT_SECRET:gJj0XQdx9yhNY3sQpOPcHLYV7rWmb8A4KtIZ2EMi6onFk1qlaDSeuTvRfzG5wU}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时 (24 * 60 * 60 * 1000)
  remember-me-expiration: ${JWT_REMEMBER_ME_EXPIRATION:2592000000} # 30天 (30 * 24 * 60 * 60 * 1000)



# AI Guide项目配置
ai-guide:
  base-url: ${AI_GUIDE_BASE_URL:http://www.yiyiailocal.com:7081}
  callback-page-url: ${AI_GUIDE_CALLBACK_PAGE_URL:http://training.yiyiai.com:5173/pg-callback}

# 豆包大模型配置
ARK_API_KEY: df48ab3c-dc0d-46c3-bae4-5e83b1258297

# 豆包模型配置
my:
  doubao:
    think:
      model:
        name: doubao-1.5-thinking-pro-250415
    normal:
      model:
        name: doubao-1-5-pro-32k-250115
      endpoint:
        name: ep-20250629195408-gtv9c
    db16-normal:
      model:
        name: doubao-seed-1-6-250615
    url: https://ark.cn-beijing.volces.com/api/v3/
    embed:
      model-name: doubao-embedding-text-240715
    image:
      model:
        name: doubao-1-5-vision-pro-32k-250115
    estimateToken: 500
    connection-pool:
      max-idle: 50               # 最大空闲连接数（优化：减少资源占用）
      max-requests: 200          # 最大并发请求数（优化：提升并发能力）
      max-requests-per-host: 100 # 每个主机的最大请求数（优化：配合max-requests调整）
      keep-alive-duration: 3     # 连接保持时间（分钟）（优化：缩短保活时间）
    temperature: 0.3             # 模型温度参数，控制输出的随机性
    top-p: 0.9                   # top-p采样参数，控制输出的多样性

# 阿里云OSS配置
oss:
  endpoint: ${OSS_ENDPOINT:oss-cn-shanghai.aliyuncs.com}
  access-id: ${OSS_ACCESS_ID:LTAIPXL4NBBjtwG6}
  access-key: ${OSS_ACCESS_KEY:smybdcjqJAL4oL8qkL9qRESoYQfhv0}
  bucket-name: ${OSS_BUCKET_NAME:ai-playground}
  temp-file-expiration: ${OSS_TEMP_FILE_EXPIRATION:24}  # 临时文件过期时间（小时）

# Qdrant向量数据库配置
qdrant:
  host: ${QDRANT_HOST:www.yiyiailocal.com}
  http-port: ${QDRANT_HTTP_PORT:6333}
  grpc-port: ${QDRANT_GRPC_PORT:6334}
  username: ${QDRANT_USERNAME:qdrant}
  password: ${QDRANT_PASSWORD:qdrant}
  use-tls: ${QDRANT_USE_TLS:false}
  api-key: ${QDRANT_API_KEY:} # 如果需要认证，请设置API Key
  search:
    limit: ${QDRANT_SEARCH_LIMIT:10}       # 默认搜索数量限制
    min-score: ${QDRANT_SEARCH_MIN_SCORE:0.65}  # 最小相似度得分阈值

# 商品处理配置
product:
  processing:
    chunk-size: 100      # 文本分块大小
    overlap-size: 20     # 文本重叠大小
    vector-dimension: 2560 # 向量维度
    collection-name: train_prod_collection # 向量集合名称
    jd-collection-name: train_prod_jd_collection # 京东商品向量集合名称

# 京东配置
jd:
  server-url: ${JD_SERVER_URL:https://api.jd.com/routerjson}  # 京东API服务器地址
  expected-state: ${JD_EXPECTED_STATE:YyJdPlayground2025}  # 期望的state参数值
  app-key: ${JD_APP_KEY:58EC57CE1D6C6B997519BA25E73A7228}  # 京东应用Key
  app-secret: ${JD_APP_SECRET:dbff743f33fd4a46bfa3399cf189e252}  # 京东应用Secret
  access-token: ${JD_ACCESS_TOKEN:89fd9dcc03d34c6d997fc66e019700bcy2mw}  # 京东访问令牌
  accessToken: ${JD_ACCESS_TOKEN:89fd9dcc03d34c6d997fc66e019700bcy2mw}  # 兼容性配置
  token-url: ${JD_TOKEN_URL:https://open-oauth.jd.com/oauth2/access_token}  # 京东令牌获取URL
  refresh-token-url: ${JD_REFRESH_TOKEN_URL:https://open-oauth.jd.com/oauth2/refresh_token}  # 京东令牌刷新URL
  redirect-base-url: ${JD_REDIRECT_BASE_URL:http://www.yiyiailocal.com:5173/home}  # 重定向基础URL
  sync:
    page-size: ${JD_SYNC_PAGE_SIZE:50}  # 京东商品同步分页大小，每页获取的商品数量
    is-mock-switch: ${JD_SYNC_IS_MOCK_SWITCH:true}  # 是否使用本地模拟数据开关，默认false使用远程API
  # JSON文件路径配置
  json:
    file:
      path: C:/projects/ai_playground/bot_product.json  # 京东商品JSON文件路径
  sku:
    json:
      file:
        path: C:/projects/ai_playground/bot_product_sku.json  # 京东SKU JSON文件路径
  # Token刷新定时任务配置
  token-refresh:
    interval-minutes: ${JD_TOKEN_REFRESH_INTERVAL_MINUTES:60}  # Token刷新间隔时间（分钟），默认60分钟
    lock-expire-minutes: ${JD_TOKEN_REFRESH_LOCK_EXPIRE_MINUTES:50}  # 分布式锁过期时间（分钟），默认50分钟
  # 图片向量处理配置
  img-vector:
    thread-pool-size: ${JD_IMG_VECTOR_THREAD_POOL_SIZE:50}  # 图片处理核心线程池大小，优化为50个线程
    max-thread-pool-size: ${JD_IMG_VECTOR_MAX_THREAD_POOL_SIZE:100}  # 图片处理最大线程池大小，优化为100个线程
    batch-size: ${JD_IMG_VECTOR_BATCH_SIZE:50}  # 每批处理的图片数量，优化为50张
    timeout-seconds: ${JD_IMG_VECTOR_TIMEOUT_SECONDS:600}  # 单个图片处理超时时间（秒），增加到10分钟
    scheduler-interval: ${JD_IMG_VECTOR_SCHEDULER_INTERVAL:300000}  # 定时任务执行间隔（毫秒），默认5分钟
    enabled: ${JD_IMG_VECTOR_ENABLED:false}  # 图片向量处理定时任务开关，默认启用

# 知识库模板学习配置
kb:
  tpl-learning:
    enabled: ${KB_TPL_LEARNING_ENABLED:false}  # 知识库模板学习开关，默认启用
    interval-minutes: ${KB_TPL_LEARNING_INTERVAL_MINUTES:10}  # 定时任务执行间隔（分钟），默认10分钟
  llm-output-size: ${KB_LLM_OUTPUT_SIZE:3}  # LLM输出大小，默认3
