-- 为train_task_conv_kb_dtl表添加f2nd_agg_sys_prompt字段
-- 执行时间：2025-08-16

-- 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'train_task_conv_kb_dtl'
    AND COLUMN_NAME = 'f2nd_agg_sys_prompt'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE train_task_conv_kb_dtl ADD COLUMN f2nd_agg_sys_prompt longtext COMMENT ''第二步聚合系统提示词'' AFTER f1st_raw_chatlog',
    'SELECT ''Column f2nd_agg_sys_prompt already exists'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
