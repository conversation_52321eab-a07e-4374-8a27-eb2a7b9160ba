-- 创建接待任务表
CREATE TABLE `train_reception_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_name` varchar(255) NOT NULL COMMENT '接待任务名称',
  `task_description` text COMMENT '任务描述',
  `task_mode` int NOT NULL DEFAULT '0' COMMENT '接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法',
  `task_type` int NOT NULL DEFAULT '0' COMMENT '接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务',
  `script_content` text COMMENT '实家剧本内容',
  `max_rounds` int DEFAULT '0' COMMENT '最多回合数',
  `assigned_staff` varchar(255) COMMENT '指定客服',
  `quick_phrases` varchar(255) COMMENT '快捷短语',
  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
  `training_limit_enabled` tinyint(1) DEFAULT '0' COMMENT '训练次数限制：0-关，1-开',
  `reception_skin` int DEFAULT '0' COMMENT '接待皮肤：0-干牛，1-咩咩，2-抖音',
  `tool_training_enabled` tinyint(1) DEFAULT '0' COMMENT '工具训练：0-关闭，1-开启',
  `assistant_tool_enabled` tinyint(1) DEFAULT '0' COMMENT '辅助工具：0-关闭，1-开启',
  `scene_mode` int DEFAULT '0' COMMENT '场景模式：0-萌新友好，1-压力考核，2-自定义',
  `timer_display` tinyint(1) DEFAULT '0' COMMENT '读秒：0-不显示，1-显示',
  `auto_close_enabled` tinyint(1) DEFAULT '0' COMMENT '会话自动关闭：0-关，1-开',
  `patience_mode_enabled` tinyint(1) DEFAULT '0' COMMENT '耐心度模式：0-关，1-开',
  `patience_rounds` int DEFAULT '10' COMMENT '耐心度轮数',
  `question_interval_type` int DEFAULT '0' COMMENT '顾客提问时间间隔：0-随机，1-固定',
  `question_interval_seconds` int DEFAULT '3' COMMENT '提问间隔秒数',
  `quality_standard_1` varchar(255) COMMENT '质检标准1',
  `quality_standard_2` varchar(255) COMMENT '质检标准2',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_task_mode` (`task_mode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待任务表';
