-- 京东商品表
CREATE TABLE `train_jd_products` (
    -- 固定字段（FF）
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '京东商品表主键ID',
    `team_id` bigint NOT NULL DEFAULT 0 COMMENT '团队ID,0代表系统'
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
    `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
    `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
    
    -- 业务字段（BF）- 商品基本信息
    `brand_id` bigint DEFAULT NULL COMMENT '品牌ID',
    `brand_name` varchar(255) DEFAULT NULL COMMENT '品牌名称',
    `category_id` bigint DEFAULT NULL COMMENT '一级分类ID',
    `category_sec_id` bigint DEFAULT NULL COMMENT '二级分类ID',
    `col_type` int DEFAULT NULL COMMENT '颜色类型',
    `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价格',
    `created` datetime DEFAULT NULL COMMENT '京东商品创建时间',
    `height` decimal(8,2) DEFAULT NULL COMMENT '商品高度(cm)',
    `jd_price` decimal(10,2) DEFAULT NULL COMMENT '京东价格',
    `length` decimal(8,2) DEFAULT NULL COMMENT '商品长度(cm)',
    `logo` varchar(500) DEFAULT NULL COMMENT '商品logo图片URL',
    `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价格',
    `modified` datetime DEFAULT NULL COMMENT '京东商品修改时间',
    `offline_time` datetime DEFAULT NULL COMMENT '下线时间',
    `shop_id` bigint DEFAULT NULL COMMENT '京东平台店铺ID',
    `spu_id` bigint DEFAULT NULL COMMENT 'SPU ID',
    `stock_num` int DEFAULT NULL COMMENT '库存数量',
    `template_id` bigint DEFAULT NULL COMMENT '模板ID',
    `title` varchar(500) DEFAULT NULL COMMENT '商品标题',
    `ware_id` bigint DEFAULT NULL COMMENT '商品ID',
    `ware_status` tinyint DEFAULT NULL COMMENT '商品状态：1-上架，0-下架',
    `weight` decimal(8,3) DEFAULT NULL COMMENT '商品重量(kg)',
    `width` decimal(8,2) DEFAULT NULL COMMENT '商品宽度(cm)',
    `wrap` varchar(255) DEFAULT NULL COMMENT '包装信息',
    `ware_location` int DEFAULT NULL COMMENT '商品所在仓库位置',
    `introduction` text DEFAULT NULL COMMENT '商品介绍',
    `mobile_desc` text DEFAULT NULL COMMENT '手机端描述',
    `fit_case_html_app` text DEFAULT NULL COMMENT 'APP端适配HTML',
    `features` text DEFAULT NULL COMMENT '商品功能特性JSON数据',
    `multi_cate_props` text DEFAULT NULL COMMENT '商品多类目属性JSON数据',
    
    PRIMARY KEY (`id`),
    INDEX `idx_team_id` (`team_id`),
    INDEX `idx_shop_id` (`shop_id`),
    INDEX `idx_ware_id` (`ware_id`),
    INDEX `idx_brand_id` (`brand_id`),
    INDEX `idx_category_id` (`category_id`),
    INDEX `idx_ware_status` (`ware_status`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='京东商品表'; 