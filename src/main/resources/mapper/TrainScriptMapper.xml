<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainScriptMapper">

    <!-- 分页查询剧本列表 -->
    <select id="selectScriptList" resultType="com.yiyi.ai_train_playground.dto.ScriptListDTO">
        SELECT
            ts.id,
            ts.name,
            ts.buyer_requirement as buyerRequirement,
            ts.intent_id as intentId,
            ti.name as intentName,
            ts.generation_type as generationTypeCode,
            CASE
                WHEN ts.generation_type = 0 THEN '商品知识训练'
                WHEN ts.generation_type = 1 THEN '实战能力进阶'
                WHEN ts.generation_type = 2 THEN '自定义内容'
                ELSE '未知类型'
            END as generationType,
            COALESCE(ts.group_id, 1) as groupId,
            ts.creator,
            ts.create_time as createTime,
            ts.update_time as updateTime
        FROM train_script ts
        LEFT JOIN train_intent ti ON ts.intent_id = ti.id
        WHERE ts.team_id = #{teamId}
        <if test="request.name != null and request.name != ''">
            AND ts.name LIKE CONCAT('%', #{request.name}, '%')
        </if>
        <if test="request.creator != null and request.creator != ''">
            AND ts.creator LIKE CONCAT('%', #{request.creator}, '%')
        </if>
        <if test="request.generationTypeCode != null">
            AND ts.generation_type = #{request.generationTypeCode}
        </if>
        <if test="request.groupId != null">
            AND ts.group_id = #{request.groupId}
        </if>
        <if test="request.createTimeStart != null and request.createTimeStart != ''">
            AND DATE(ts.create_time) &gt;= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null and request.createTimeEnd != ''">
            AND DATE(ts.create_time) &lt;= #{request.createTimeEnd}
        </if>
        <if test="request.updateTimeStart != null and request.updateTimeStart != ''">
            AND DATE(ts.update_time) &gt;= #{request.updateTimeStart}
        </if>
        <if test="request.updateTimeEnd != null and request.updateTimeEnd != ''">
            AND DATE(ts.update_time) &lt;= #{request.updateTimeEnd}
        </if>
        ORDER BY ts.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询剧本总数 -->
    <select id="countScripts" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM train_script ts
        WHERE ts.team_id = #{teamId}
        <if test="request.name != null and request.name != ''">
            AND ts.name LIKE CONCAT('%', #{request.name}, '%')
        </if>
        <if test="request.creator != null and request.creator != ''">
            AND ts.creator LIKE CONCAT('%', #{request.creator}, '%')
        </if>
        <if test="request.generationTypeCode != null">
            AND ts.generation_type = #{request.generationTypeCode}
        </if>
        <if test="request.groupId != null">
            AND ts.group_id = #{request.groupId}
        </if>
        <if test="request.createTimeStart != null and request.createTimeStart != ''">
            AND DATE(ts.create_time) &gt;= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null and request.createTimeEnd != ''">
            AND DATE(ts.create_time) &lt;= #{request.createTimeEnd}
        </if>
        <if test="request.updateTimeStart != null and request.updateTimeStart != ''">
            AND DATE(ts.update_time) &gt;= #{request.updateTimeStart}
        </if>
        <if test="request.updateTimeEnd != null and request.updateTimeEnd != ''">
            AND DATE(ts.update_time) &lt;= #{request.updateTimeEnd}
        </if>
    </select>

    <!-- 根据分组ID分页查询剧本列表 -->
    <select id="selectScriptListByGroup" resultType="com.yiyi.ai_train_playground.dto.ScriptListDTO">
        SELECT
            ts.id,
            ts.name,
            ts.buyer_requirement as buyerRequirement,
            ts.intent_id as intentId,
            ti.name as intentName,
            ts.generation_type as generationTypeCode,
            CASE
                WHEN ts.generation_type = 0 THEN '商品知识训练'
                WHEN ts.generation_type = 1 THEN '实战能力进阶'
                WHEN ts.generation_type = 2 THEN '自定义内容'
                ELSE '未知类型'
            END as generationType,
            COALESCE(ts.group_id, 1) as groupId,
            ts.creator,
            ts.create_time as createTime,
            ts.update_time as updateTime
        FROM train_script ts
        LEFT JOIN train_intent ti ON ts.intent_id = ti.id
        WHERE ts.team_id = #{teamId}
        <if test="request.scriptGroupIds != null and request.scriptGroupIds != ''">
            <choose>
                <when test='request.scriptGroupIds == "-1"'>
                    <!-- 查询所有分组，不添加group_id条件 -->
                </when>
                <when test='request.scriptGroupIds == "1"'>
                    <!-- 只查询group_id为NULL的记录 -->
                    AND ts.group_id IS NULL
                </when>
                <when test='@com.yiyi.ai_train_playground.util.ScriptGroupIdParser@containsId(request.scriptGroupIds, 1L) and request.scriptGroupIds.indexOf(",") >= 0'>
                    <!-- 包含默认分组和其他分组 -->
                    AND (ts.group_id IS NULL
                    <foreach collection="@com.yiyi.ai_train_playground.util.ScriptGroupIdParser@parseIds(request.scriptGroupIds)"
                             item="groupId" separator="">
                        OR ts.group_id = #{groupId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    <!-- 查询指定的group_id列表 -->
                    AND ts.group_id IN
                    <foreach collection="@com.yiyi.ai_train_playground.util.ScriptGroupIdParser@parseIds(request.scriptGroupIds)"
                             item="groupId" open="(" close=")" separator=",">
                        #{groupId}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        ORDER BY ts.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据分组ID查询剧本总数 -->
    <select id="countScriptsByGroup" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM train_script ts
        WHERE ts.team_id = #{teamId}
        <if test="request.scriptGroupIds != null and request.scriptGroupIds != ''">
            <choose>
                <when test='request.scriptGroupIds == "-1"'>
                    <!-- 查询所有分组，不添加group_id条件 -->
                </when>
                <when test='request.scriptGroupIds == "1"'>
                    <!-- 只查询group_id为NULL的记录 -->
                    AND ts.group_id IS NULL
                </when>
                <when test='@com.yiyi.ai_train_playground.util.ScriptGroupIdParser@containsId(request.scriptGroupIds, 1L) and request.scriptGroupIds.indexOf(",") >= 0'>
                    <!-- 包含默认分组和其他分组 -->
                    AND (ts.group_id IS NULL
                    <foreach collection="@com.yiyi.ai_train_playground.util.ScriptGroupIdParser@parseIds(request.scriptGroupIds)"
                             item="groupId" separator="">
                        OR ts.group_id = #{groupId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    <!-- 查询指定的group_id列表 -->
                    AND ts.group_id IN
                    <foreach collection="@com.yiyi.ai_train_playground.util.ScriptGroupIdParser@parseIds(request.scriptGroupIds)"
                             item="groupId" open="(" close=")" separator=",">
                        #{groupId}
                    </foreach>
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- 根据ID查询剧本 -->
    <select id="selectById" resultType="com.yiyi.ai_train_playground.entity.TrainScript">
        SELECT * FROM train_script 
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 插入剧本 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.TrainScript" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_script (
            team_id, name, generation_type, group_id, intent_id, evaluation_id,
            buyer_requirement, order_priority, order_remark, retry_buyer_requirement_counts,
            retry_flow_node_counts, simulation_tool, prod_type, create_time,
            update_time, creator, updater, version, is_official
        ) VALUES (
            #{teamId}, #{name}, #{generationType}, #{groupId}, #{intentId}, #{evaluationId},
            #{buyerRequirement}, #{orderPriority}, #{orderRemark}, #{retryBuyerRequirementCounts},
            #{retryFlowNodeCounts}, #{simulationTool}, #{prodType}, NOW(),
            NOW(), #{creator}, #{updater}, #{version}, #{isOfficial}
        )
    </insert>

    <!-- 更新剧本 -->
    <update id="update" parameterType="com.yiyi.ai_train_playground.entity.TrainScript">
        UPDATE train_script SET
            name = #{name},
            generation_type = #{generationType},
            group_id = #{groupId},
            intent_id = #{intentId},
            evaluation_id = #{evaluationId},
            buyer_requirement = #{buyerRequirement},
            order_priority = #{orderPriority},
            order_remark = #{orderRemark},
            retry_buyer_requirement_counts = #{retryBuyerRequirementCounts},
            retry_flow_node_counts = #{retryFlowNodeCounts},
            simulation_tool = #{simulationTool},
            prod_type = #{prodType},
            update_time = NOW(),
            updater = #{updater},
            version = #{version},
            is_official = #{isOfficial}
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 删除剧本 -->
    <delete id="deleteById">
        DELETE FROM train_script
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 批量删除剧本 -->
    <delete id="batchDeleteByIds">
        DELETE FROM train_script
        WHERE team_id = #{teamId}
        <if test="ids != null and ids.size() > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <!-- 根据ID查询剧本详情 -->
    <select id="getScriptDetailById" resultMap="ScriptDetailResultMap">
        SELECT
            ts.id,
            ts.name,
            ts.generation_type as generationTypeCode,
            CASE
                WHEN ts.generation_type = 0 THEN '商品知识训练'
                WHEN ts.generation_type = 1 THEN '实战能力进阶'
                WHEN ts.generation_type = 2 THEN '自定义内容'
                ELSE '未知类型'
            END as generationType,
            COALESCE(ts.group_id, 1) as groupId,
            ts.intent_id as intentId,
            ti.name as intentName,
            ti.parent_id as parentIntentId,
            tip.name as parentIntentName,
            ts.evaluation_id as evaluationPlanId,
            tep.name as evaluationPlanName,
            teg.group_title as evaluationPlanGroupName,
            ts.buyer_requirement as buyerRequirement,
            ts.order_priority as orderPriority,
            ts.order_remark as orderRemark,
            ts.retry_flow_node_counts as retryFlowNodeCounts,
            ts.retry_buyer_requirement_counts as retryBuyerRequirementCounts,
            ts.simulation_tool as simulationTool,
            ts.prod_type as prodType,
            ts.create_time as createTime,
            ts.update_time as updateTime,
            ts.version
        FROM train_script ts
        LEFT JOIN train_intent ti ON ts.intent_id = ti.id
        LEFT JOIN train_intent tip ON ti.parent_id = tip.id
        LEFT JOIN train_evaluation_plan tep ON ts.evaluation_id = tep.id
        LEFT JOIN train_evaluation_group teg ON tep.group_id = teg.id
        WHERE ts.id = #{id} AND ts.team_id = #{teamId}
    </select>

    <!-- 查询剧本的商品类型 -->
    <select id="getScriptProdType" resultType="java.lang.Integer">
        SELECT prod_type
        FROM train_script
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 剧本详情结果映射 -->
    <resultMap id="ScriptDetailResultMap" type="com.yiyi.ai_train_playground.dto.ScriptDetailDTO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="generationTypeCode" column="generationTypeCode"/>
        <result property="generationType" column="generationType"/>
        <result property="groupId" column="groupId"/>
        <result property="intentId" column="intentId"/>
        <result property="intentName" column="intentName"/>
        <result property="parentIntentId" column="parentIntentId"/>
        <result property="parentIntentName" column="parentIntentName"/>
        <result property="evaluationPlanId" column="evaluationPlanId"/>
        <result property="evaluationPlanName" column="evaluationPlanName"/>
        <result property="evaluationPlanGroupName" column="evaluationPlanGroupName"/>
        <result property="buyerRequirement" column="buyerRequirement"/>
        <result property="orderPriority" column="orderPriority"/>
        <result property="orderRemark" column="orderRemark"/>
        <result property="retryFlowNodeCounts" column="retryFlowNodeCounts"/>
        <result property="retryBuyerRequirementCounts" column="retryBuyerRequirementCounts"/>
        <result property="simulationTool" column="simulationTool"/>
        <result property="prodType" column="prodType"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="version" column="version"/>
        <!-- 商品列表通过collection查询 -->
        <collection property="productList" ofType="com.yiyi.ai_train_playground.dto.ProductListDTO"
                   select="selectProductsByScriptId" column="id"/>
        <!-- 关联图片通过collection查询 -->
        <collection property="relateImgs" ofType="com.yiyi.ai_train_playground.dto.RelatedImageDTO"
                   select="selectRelatedImagesByScriptId" column="id"/>
        <!-- 流程节点通过collection查询 -->
        <collection property="flowNodes" ofType="com.yiyi.ai_train_playground.dto.FlowNodeDTO"
                   select="selectFlowNodesByScriptId" column="id"/>
    </resultMap>

    <!-- 查询剧本关联的商品列表 -->
    <select id="selectProductsByScriptId" resultType="com.yiyi.ai_train_playground.dto.ProductListDTO">
        SELECT
            tsp.id,
            tp.external_product_id as externalProductId,
            tp.external_product_name as externalProductName,
            tp.external_product_link as externalProductLink,
            tp.external_product_image as externalProductImage,
            tp.external_product_detail_cleaned as prodKb
        FROM train_script_products tsp
--         JOIN train_product tp ON tsp.train_product_id = tp.id
        JOIN train_product tp ON tsp.train_product_id = tp.external_product_id
        WHERE tsp.script_id = #{id}
        ORDER BY tsp.id
    </select>

    <!-- 查询剧本关联的图片列表 -->
    <select id="selectRelatedImagesByScriptId" resultType="com.yiyi.ai_train_playground.dto.RelatedImageDTO">
        SELECT
            tri.media_type as mediaType,
            tri.upload_type as uploadType,
            tri.recognized_text as recognizedText,
            tri.url
        FROM train_related_image tri
        WHERE tri.script_id = #{id}
        ORDER BY tri.id
    </select>

    <!-- 查询剧本关联的流程节点列表 -->
    <select id="selectFlowNodesByScriptId" resultType="com.yiyi.ai_train_playground.dto.FlowNodeDTO">
        SELECT
            tfn.node_name as nodeName,
            tfn.node_buyer_requirement as nodeBuyerRequirement
        FROM train_flow_node tfn
        WHERE tfn.script_id = #{id}
        ORDER BY tfn.id
    </select>

    <!-- 查询JD商品类型的剧本详情 -->
    <select id="getScriptDetailWithJdById" resultMap="ScriptDetailWithJdResultMap">
        SELECT
            ts.id,
            ts.name,
            ts.generation_type as generationTypeCode,
            CASE
                WHEN ts.generation_type = 0 THEN '商品知识训练'
                WHEN ts.generation_type = 1 THEN '实战能力进阶'
                WHEN ts.generation_type = 2 THEN '自定义内容'
                ELSE '未知类型'
            END as generationType,
            COALESCE(ts.group_id, 1) as groupId,
            ts.intent_id as intentId,
            ti.name as intentName,
            ti.parent_id as parentIntentId,
            tip.name as parentIntentName,
            ts.evaluation_id as evaluationPlanId,
            tep.name as evaluationPlanName,
            teg.group_title as evaluationPlanGroupName,
            ts.buyer_requirement as buyerRequirement,
            ts.order_priority as orderPriority,
            ts.order_remark as orderRemark,
            ts.retry_flow_node_counts as retryFlowNodeCounts,
            ts.retry_buyer_requirement_counts as retryBuyerRequirementCounts,
            ts.simulation_tool as simulationTool,
            ts.prod_type as prodType,
            ts.create_time as createTime,
            ts.update_time as updateTime,
            ts.version
        FROM train_script ts
        LEFT JOIN train_intent ti ON ts.intent_id = ti.id
        LEFT JOIN train_intent tip ON ti.parent_id = tip.id
        LEFT JOIN train_evaluation_plan tep ON ts.evaluation_id = tep.id
        LEFT JOIN train_evaluation_group teg ON tep.group_id = teg.id
        WHERE ts.id = #{id} AND ts.team_id = #{teamId}
    </select>

    <!-- JD商品剧本详情结果映射 -->
    <resultMap id="ScriptDetailWithJdResultMap" type="com.yiyi.ai_train_playground.dto.ScriptDetailDTO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="generationTypeCode" column="generationTypeCode"/>
        <result property="generationType" column="generationType"/>
        <result property="groupId" column="groupId"/>
        <result property="intentId" column="intentId"/>
        <result property="intentName" column="intentName"/>
        <result property="parentIntentId" column="parentIntentId"/>
        <result property="parentIntentName" column="parentIntentName"/>
        <result property="evaluationPlanId" column="evaluationPlanId"/>
        <result property="evaluationPlanName" column="evaluationPlanName"/>
        <result property="evaluationPlanGroupName" column="evaluationPlanGroupName"/>
        <result property="buyerRequirement" column="buyerRequirement"/>
        <result property="orderPriority" column="orderPriority"/>
        <result property="orderRemark" column="orderRemark"/>
        <result property="retryFlowNodeCounts" column="retryFlowNodeCounts"/>
        <result property="retryBuyerRequirementCounts" column="retryBuyerRequirementCounts"/>
        <result property="simulationTool" column="simulationTool"/>
        <result property="prodType" column="prodType"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="version" column="version"/>
        <!-- JD商品列表通过collection查询 -->
        <collection property="productList" ofType="com.yiyi.ai_train_playground.dto.ProductListDTO"
                   select="selectJdProductsByScriptId" column="id"/>
        <!-- 关联图片通过collection查询 -->
        <collection property="relateImgs" ofType="com.yiyi.ai_train_playground.dto.RelatedImageDTO"
                   select="selectRelatedImagesByScriptId" column="id"/>
        <!-- 流程节点通过collection查询 -->
        <collection property="flowNodes" ofType="com.yiyi.ai_train_playground.dto.FlowNodeDTO"
                   select="selectFlowNodesByScriptId" column="id"/>
    </resultMap>

    <!-- 查询剧本关联的JD商品列表 -->
    <select id="selectJdProductsByScriptId" resultType="com.yiyi.ai_train_playground.dto.ProductListDTO">
        SELECT
            t_script.id,
            CAST(t_script.tr_jd_sku_id AS CHAR) as externalProductId,
            COALESCE(t_product.title, CONCAT('JD商品-', t_script.tr_jd_sku_id)) as externalProductName,
--             COALESCE(t_product.logo, CONCAT('https://img11.360buyimg.com/devfe/', t_script.tr_jd_sku_id)) as externalProductLink,
            CONCAT('https://img11.360buyimg.com/devfe/', t_product.logo) as externalProductLink,
            CONCAT('https://img11.360buyimg.com/devfe/', t_product.logo) as externalProductImage,
            t_product.jd_prod_dtl as prodKb
        FROM train_script_jd_products t_script
--         LEFT JOIN train_jd_sku t_sku ON t_sku.sku_id = t_script.tr_jd_sku_id
--         LEFT JOIN train_jd_products t_product ON t_sku.ware_id = t_product.ware_id
        LEFT JOIN train_jd_products t_product ON t_script.tr_jd_sku_id = t_product.ware_id
        WHERE t_script.script_id = #{id}
        ORDER BY t_script.id
    </select>

</mapper>
