<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplDetailMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tpl_id" jdbcType="BIGINT" property="tplId" />
        <result column="content" jdbcType="LONGVARCHAR" property="content" />
        <result column="tpl_type" jdbcType="VARCHAR" property="tplType" />
        <result column="pre_id" jdbcType="BIGINT" property="preId" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="version" jdbcType="BIGINT" property="version" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, tpl_id, content, tpl_type, pre_id,
        team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_detail
        WHERE id = #{id}
    </select>

    <!-- 根据模板ID查询明细列表 -->
    <select id="selectByTplId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_detail
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
        ORDER BY id ASC
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplDetail"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_kb_tpl_detail 
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tplId != null">tpl_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="tplType != null and tplType != ''">tpl_type,</if>
            <if test="preId != null">pre_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="updater != null and updater != ''">updater,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="tplId != null">#{tplId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="tplType != null and tplType != ''">#{tplType},</if>
            <if test="preId != null">#{preId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO train_kb_tpl_detail (
            tpl_id, content, tpl_type, pre_id,
            team_id, creator, updater
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.tplId}, #{record.content}, #{record.tplType}, #{record.preId},
                #{record.teamId}, #{record.creator}, #{record.updater}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplDetail">
        UPDATE train_kb_tpl_detail
        <set>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="tplType != null and tplType != ''">tpl_type = #{tplType},</if>
            <if test="preId != null">pre_id = #{preId},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            version = version + 1
        </set>
        WHERE id = #{id}
          AND team_id = #{teamId}
          AND version = #{version}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM train_kb_tpl_detail
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 根据模板ID删除所有明细 -->
    <delete id="deleteByTplId">
        DELETE FROM train_kb_tpl_detail
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
    </delete>

    <!-- 统计模板的明细数量 -->
    <select id="countByTplId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM train_kb_tpl_detail
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
    </select>

    <!-- 结果映射用于详情分页响应 -->
    <resultMap id="DetailItemResultMap" type="com.yiyi.ai_train_playground.dto.converkb.KbTplDetailResponse$KbTplDetailItem">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="content" jdbcType="LONGVARCHAR" property="content" />
        <result column="tpl_type" jdbcType="VARCHAR" property="tplTypeCode" />
        <result column="tpl_type_value" jdbcType="VARCHAR" property="tplTypeValue" />
    </resultMap>

    <!-- 根据模板ID分页查询明细列表（带枚举描述） -->
    <select id="selectPageByTplId" resultMap="DetailItemResultMap">
        SELECT
            id,
            content,
            tpl_type,
            CASE tpl_type
                WHEN 'pre_sales' THEN '售前'
                WHEN 'saling' THEN '销售中'
                WHEN 'after_sale' THEN '售后'
                WHEN 'other' THEN '其他'
                ELSE '未知类型'
            END as tpl_type_value
        FROM train_kb_tpl_detail
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
        <if test="request.tplType != null and request.tplType != ''">
            AND tpl_type = #{request.tplType}
        </if>
        ORDER BY id ASC
        LIMIT #{request.pageSize} OFFSET #{offset}
    </select>

    <!-- 查询明细总数 -->
    <select id="selectPageCountByTplId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_kb_tpl_detail
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
        <if test="request.tplType != null and request.tplType != ''">
            AND tpl_type = #{request.tplType}
        </if>
    </select>

    <!-- 根据任务ID随机获取知识库明细内容 -->
    <select id="selectRandomContentByTaskId" resultType="java.lang.String">
        SELECT train_kb_tpl_detail.content
        FROM train_reception_task
        JOIN train_kb_tpl_main ON train_reception_task.conv_kb_id = train_kb_tpl_main.id
        JOIN train_kb_tpl_detail ON train_kb_tpl_main.id = train_kb_tpl_detail.tpl_id
        WHERE train_reception_task.id = #{taskId}
          AND train_reception_task.team_id = #{teamId}
          AND train_kb_tpl_main.team_id = #{teamId}
          AND train_kb_tpl_detail.team_id = #{teamId}
        ORDER BY RAND()
        LIMIT 1
    </select>

</mapper>