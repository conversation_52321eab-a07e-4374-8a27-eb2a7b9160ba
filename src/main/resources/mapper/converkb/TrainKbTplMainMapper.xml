<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplMainMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="tokens" jdbcType="BIGINT" property="tokens" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="file_type" jdbcType="VARCHAR" property="fileType" />
        <result column="learn_status" jdbcType="VARCHAR" property="learnStatus" />
        <result column="token_in" jdbcType="BIGINT" property="tokenIn" />
        <result column="token_out" jdbcType="BIGINT" property="tokenOut" />
        <result column="description" jdbcType="VARCHAR" property="description" />
    </resultMap>

    <!-- 列表结果映射 -->
    <resultMap id="ListResultMap" type="com.yiyi.ai_train_playground.dto.converkb.KbTplListResponse">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="tokens" jdbcType="BIGINT" property="tokens" />
        <result column="detail_count" jdbcType="INTEGER" property="detailCount" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="file_type" jdbcType="VARCHAR" property="fileType" />
        <result column="learn_status" jdbcType="VARCHAR" property="learnStatusCode" />
        <result column="learn_status_value" jdbcType="VARCHAR" property="learnStatusValue" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, name, tokens, team_id, create_time, update_time, creator, updater, version, file_type, learn_status, token_in, token_out, description
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_main
        WHERE id = #{id}
    </select>

    <!-- 根据ID和团队ID查询 -->
    <select id="selectByIdAndTeamId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_main
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_kb_tpl_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="tokens != null">
                tokens,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="updater != null and updater != ''">
                updater,
            </if>
            <if test="fileType != null and fileType != ''">
                file_type,
            </if>
            <if test="learnStatus != null and learnStatus != ''">
                learn_status,
            </if>
            <if test="tokenIn != null">
                token_in,
            </if>
            <if test="tokenOut != null">
                token_out,
            </if>
            <if test="description != null and description != ''">
                description,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="tokens != null">
                #{tokens},
            </if>
            <if test="teamId != null">
                #{teamId},
            </if>
            <if test="creator != null and creator != ''">
                #{creator},
            </if>
            <if test="updater != null and updater != ''">
                #{updater},
            </if>
            <if test="fileType != null and fileType != ''">
                #{fileType},
            </if>
            <if test="learnStatus != null and learnStatus != ''">
                #{learnStatus},
            </if>
            <if test="tokenIn != null">
                #{tokenIn},
            </if>
            <if test="tokenOut != null">
                #{tokenOut},
            </if>
            <if test="description != null and description != ''">
                #{description},
            </if>
        </trim>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain">
        UPDATE train_kb_tpl_main
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="tokens != null">
                tokens = #{tokens},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="fileType != null and fileType != ''">
                file_type = #{fileType},
            </if>
            <if test="learnStatus != null and learnStatus != ''">
                learn_status = #{learnStatus},
            </if>
            <if test="tokenIn != null">
                token_in = #{tokenIn},
            </if>
            <if test="tokenOut != null">
                token_out = #{tokenOut},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            version = version + 1,
            update_time = NOW()
        </set>
        WHERE id = #{id}
          AND team_id = #{teamId}
          AND version = #{version}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM train_kb_tpl_main
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 分页查询列表 -->
    <select id="selectPageList" resultMap="ListResultMap">
        SELECT
            m.id,
            m.name,
            m.description,
            m.tokens,
            m.create_time,
            m.update_time,
            m.file_type,
            m.learn_status,
            CASE m.learn_status
                WHEN 'file_uploading' THEN '文件上传中'
                WHEN 'un_learn' THEN '未学习'
                WHEN 'learning' THEN '学习中'
                WHEN 'learned' THEN '已学习'
                ELSE '未知状态'
            END as learn_status_value,
            COALESCE(d.detail_count, 0) as detail_count
        FROM train_kb_tpl_main m
        LEFT JOIN (
            SELECT tpl_id, COUNT(*) as detail_count
            FROM train_kb_tpl_detail
            WHERE team_id = #{teamId}
            GROUP BY tpl_id
        ) d ON m.id = d.tpl_id
        WHERE m.team_id = #{teamId}
        <if test="request.name != null and request.name != ''">
            AND m.name LIKE CONCAT('%', #{request.name}, '%')
        </if>
        <if test="request.tplType != null and request.tplType != ''">
            AND EXISTS (
                SELECT 1 FROM train_kb_tpl_detail td 
                WHERE td.tpl_id = m.id 
                AND td.team_id = #{teamId}
                AND td.tpl_type = #{request.tplType}
            )
        </if>
        ORDER BY m.create_time DESC
        LIMIT #{request.pageSize} OFFSET #{offset}
    </select>

    <!-- 查询总数 -->
    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_kb_tpl_main m
        WHERE m.team_id = #{teamId}
        <if test="request.name != null and request.name != ''">
            AND m.name LIKE CONCAT('%', #{request.name}, '%')
        </if>
        <if test="request.tplType != null and request.tplType != ''">
            AND EXISTS (
                SELECT 1 FROM train_kb_tpl_detail td 
                WHERE td.tpl_id = m.id 
                AND td.team_id = #{teamId}
                AND td.tpl_type = #{request.tplType}
            )
        </if>
    </select>

    <!-- 检查名称是否存在 -->
    <select id="checkNameExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM train_kb_tpl_main
        WHERE name = #{name}
        AND team_id = #{teamId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 更新token数量 -->
    <update id="updateTokens">
        UPDATE train_kb_tpl_main
        SET tokens = #{tokens}
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 更新学习状态 -->
    <update id="updateLearnStatus">
        UPDATE train_kb_tpl_main
        SET learn_status = #{learnStatus}, update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

</mapper>