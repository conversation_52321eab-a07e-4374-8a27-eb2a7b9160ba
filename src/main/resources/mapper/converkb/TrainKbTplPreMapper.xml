<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplPreMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tpl_id" jdbcType="BIGINT" property="tplId" />
        <result column="content" jdbcType="LONGVARCHAR" property="content" />
        <result column="index" jdbcType="INTEGER" property="index" />
        <result column="learn_status" jdbcType="VARCHAR" property="learnStatus" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="version" jdbcType="BIGINT" property="version" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, tpl_id, content, `index`, learn_status, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_pre
        WHERE id = #{id}
    </select>

    <!-- 根据ID和团队ID查询 -->
    <select id="selectByIdAndTeamId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_pre
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 根据模板ID查询所有预处理记录 -->
    <select id="selectByTplId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_pre
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
        ORDER BY `index` ASC
    </select>

    <!-- 根据模板ID和学习状态查询 -->
    <select id="selectByTplIdAndLearnStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_pre
        WHERE tpl_id = #{tplId} AND learn_status = #{learnStatus} AND team_id = #{teamId}
        ORDER BY `index` ASC
    </select>

    <!-- 根据学习状态查询 -->
    <select id="selectByLearnStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_pre
        WHERE learn_status = #{learnStatus} AND team_id = #{teamId}
        ORDER BY tpl_id ASC, `index` ASC
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_kb_tpl_pre (
            tpl_id, content, `index`, learn_status, team_id, creator, updater
        ) VALUES (
            #{tplId}, #{content}, #{index}, #{learnStatus}, #{teamId}, #{creator}, #{updater}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_kb_tpl_pre (
            tpl_id, content, `index`, learn_status, team_id, creator, updater
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.tplId}, #{record.content}, #{record.index}, #{record.learnStatus}, 
             #{record.teamId}, #{record.creator}, #{record.updater})
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre">
        UPDATE train_kb_tpl_pre
        <set>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="index != null">`index` = #{index},</if>
            <if test="learnStatus != null and learnStatus != ''">learn_status = #{learnStatus},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            version = version + 1
        </set>
        WHERE id = #{id}
          AND team_id = #{teamId}
          AND version = #{version}
    </update>

    <!-- 批量更新学习状态 -->
    <update id="updateLearnStatusByTplId">
        UPDATE train_kb_tpl_pre
        SET learn_status = #{newLearnStatus},
            updater = #{updater},
            version = version + 1
        WHERE tpl_id = #{tplId} 
          AND learn_status = #{oldLearnStatus}
          AND team_id = #{teamId}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM train_kb_tpl_pre
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 根据模板ID删除所有预处理记录 -->
    <delete id="deleteByTplId">
        DELETE FROM train_kb_tpl_pre
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
    </delete>

    <!-- 根据模板ID和学习状态删除 -->
    <delete id="deleteByTplIdAndLearnStatus">
        DELETE FROM train_kb_tpl_pre
        WHERE tpl_id = #{tplId} AND learn_status = #{learnStatus} AND team_id = #{teamId}
    </delete>

    <!-- 统计模板的预处理记录数量 -->
    <select id="countByTplId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_kb_tpl_pre
        WHERE tpl_id = #{tplId} AND team_id = #{teamId}
    </select>

    <!-- 统计模板指定学习状态的记录数量 -->
    <select id="countByTplIdAndLearnStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_kb_tpl_pre
        WHERE tpl_id = #{tplId} AND learn_status = #{learnStatus} AND team_id = #{teamId}
    </select>

    <!-- 分页查询指定学习状态的记录 -->
    <select id="selectByLearnStatusWithLimit" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_kb_tpl_pre
        WHERE learn_status = #{learnStatus}
        ORDER BY tpl_id ASC, `index` ASC
        LIMIT #{limit}
    </select>

</mapper>
