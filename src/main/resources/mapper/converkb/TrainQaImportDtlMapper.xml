<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportDtlMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="qa_main_id" property="qaMainId" jdbcType="BIGINT"/>
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="answer" property="answer" jdbcType="LONGVARCHAR"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- QA简单信息结果映射 -->
    <resultMap id="QaSimpleResultMap" type="com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto">
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="answer" property="answer" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <!-- 插入问答详情记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl">
        INSERT INTO train_qa_import_dtl (
            qa_main_id,
            question,
            answer,
            team_id,
            creator,
            updater
        ) VALUES (
            #{qaMainId, jdbcType=BIGINT},
            #{question, jdbcType=VARCHAR},
            #{answer, jdbcType=LONGVARCHAR},
            #{teamId, jdbcType=BIGINT},
            #{creator, jdbcType=VARCHAR},
            #{updater, jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据ID删除详情记录 -->
    <delete id="deleteById">
        DELETE FROM train_qa_import_dtl
        WHERE id = #{id, jdbcType=BIGINT}
    </delete>

    <!-- 根据ID批量删除详情记录 -->
    <delete id="deleteByIds">
        DELETE FROM train_qa_import_dtl
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id, jdbcType=BIGINT}
        </foreach>
    </delete>

    <!-- 根据主表ID删除详情记录 -->
    <delete id="deleteByQaMainId">
        DELETE FROM train_qa_import_dtl
        WHERE qa_main_id = #{qaMainId, jdbcType=BIGINT}
    </delete>

    <!-- 更新详情记录 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl">
        UPDATE train_qa_import_dtl
        <set>
            <if test="question != null and question != ''">
                question = #{question, jdbcType=VARCHAR},
            </if>
            <if test="answer != null and answer != ''">
                answer = #{answer, jdbcType=LONGVARCHAR},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater, jdbcType=VARCHAR},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id, jdbcType=BIGINT}
          AND team_id = #{teamId, jdbcType=BIGINT}
    </update>

    <!-- 根据ID查询详情记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT id, qa_main_id, question, answer, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_dtl
        WHERE id = #{id, jdbcType=BIGINT}
    </select>

    <!-- 根据主表ID查询详情记录列表 -->
    <select id="selectByQaMainId" resultMap="BaseResultMap">
        SELECT id, qa_main_id, question, answer, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_dtl
        WHERE qa_main_id = #{qaMainId, jdbcType=BIGINT}
        <if test="question != null and question != ''">
            AND question LIKE CONCAT('%', #{question, jdbcType=VARCHAR}, '%')
        </if>
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据团队ID查询详情记录列表 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT id, qa_main_id, question, answer, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_dtl
        WHERE team_id = #{teamId, jdbcType=BIGINT}
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据主表ID统计详情记录数量 -->
    <select id="countByQaMainId" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_dtl
        WHERE qa_main_id = #{qaMainId, jdbcType=BIGINT}
        <if test="question != null and question != ''">
            AND question LIKE CONCAT('%', #{question, jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <!-- 根据团队ID统计详情记录数量 -->
    <select id="countByTeamId" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_dtl
        WHERE team_id = #{teamId, jdbcType=BIGINT}
    </select>

    <!-- 检查问题是否已存在 -->
    <select id="existsByQuestion" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_dtl
        WHERE question = #{question, jdbcType=VARCHAR}
          AND team_id = #{teamId, jdbcType=BIGINT}
    </select>

    <!-- 根据问题模糊查询详情记录 -->
    <select id="selectByQuestionLike" resultMap="BaseResultMap">
        SELECT id, qa_main_id, question, answer, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_dtl
        WHERE team_id = #{teamId, jdbcType=BIGINT}
          AND question LIKE CONCAT('%', #{question, jdbcType=VARCHAR}, '%')
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据问题模糊查询统计数量 -->
    <select id="countByQuestionLike" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_dtl
        WHERE team_id = #{teamId, jdbcType=BIGINT}
          AND question LIKE CONCAT('%', #{question, jdbcType=VARCHAR}, '%')
    </select>

    <!-- 根据主表ID查询所有明细（只返回question和answer字段，不分页） -->
    <select id="selectAllByMainId" resultMap="QaSimpleResultMap">
        SELECT question, answer
        FROM train_qa_import_dtl
        WHERE qa_main_id = #{qaMainId, jdbcType=BIGINT}
        ORDER BY create_time ASC
    </select>

    <!-- 根据主表ID随机获取指定数量的明细（只返回question和answer字段） -->
    <select id="selectRandomByMainId" resultMap="QaSimpleResultMap">
        <if test="count != null and count > 0">
            SELECT question, answer
            FROM train_qa_import_dtl
            WHERE qa_main_id = #{qaMainId, jdbcType=BIGINT}
            ORDER BY RAND()
            LIMIT #{count, jdbcType=INTEGER}
        </if>
        <if test="count == null or count &lt;= 0">
            SELECT question, answer
            FROM train_qa_import_dtl
            WHERE 1 = 0
        </if>
    </select>

</mapper>
