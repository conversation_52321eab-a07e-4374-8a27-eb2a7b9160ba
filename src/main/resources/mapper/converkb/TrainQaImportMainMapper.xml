<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportMainMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
        <result column="qa_im_name" property="qaImName" jdbcType="VARCHAR"/>
        <result column="qa_im_desc" property="qaImDesc" jdbcType="VARCHAR"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 插入主表记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_qa_import_main (
            batch_no,
            qa_im_name,
            qa_im_desc,
            team_id,
            creator,
            updater
        ) VALUES (
            #{batchNo, jdbcType=VARCHAR},
            #{qaImName, jdbcType=VARCHAR},
            #{qaImDesc, jdbcType=VARCHAR},
            #{teamId, jdbcType=BIGINT},
            #{creator, jdbcType=VARCHAR},
            #{updater, jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据ID删除主表记录 -->
    <delete id="deleteById">
        DELETE FROM train_qa_import_main
        WHERE id = #{id, jdbcType=BIGINT}
    </delete>

    <!-- 根据ID批量删除主表记录 -->
    <delete id="deleteByIds">
        DELETE FROM train_qa_import_main
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id, jdbcType=BIGINT}
        </foreach>
    </delete>

    <!-- 更新主表记录 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain">
        UPDATE train_qa_import_main
        <set>
            <if test="batchNo != null and batchNo != ''">
                batch_no = #{batchNo, jdbcType=VARCHAR},
            </if>
            <if test="qaImName != null and qaImName != ''">
                qa_im_name = #{qaImName, jdbcType=VARCHAR},
            </if>
            <if test="qaImDesc != null and qaImDesc != ''">
                qa_im_desc = #{qaImDesc, jdbcType=VARCHAR},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater, jdbcType=VARCHAR},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id, jdbcType=BIGINT}
          AND team_id = #{teamId, jdbcType=BIGINT}
    </update>

    <!-- 根据ID查询主表记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT id, batch_no, qa_im_name, qa_im_desc, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_main
        WHERE id = #{id, jdbcType=BIGINT}
    </select>

    <!-- 根据团队ID查询主表记录列表 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT id, batch_no, qa_im_name, qa_im_desc, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_main
        WHERE team_id = #{teamId, jdbcType=BIGINT}
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据团队ID和创建人查询主表记录列表 -->
    <select id="selectByTeamIdAndCreator" resultMap="BaseResultMap">
        SELECT id, batch_no, qa_im_name, qa_im_desc, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_main
        WHERE team_id = #{teamId, jdbcType=BIGINT}
        <if test="creator != null and creator != ''">
            AND creator = #{creator, jdbcType=VARCHAR}
        </if>
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据团队ID统计主表记录数量 -->
    <select id="countByTeamId" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_main
        WHERE team_id = #{teamId, jdbcType=BIGINT}
    </select>

    <!-- 根据团队ID和创建人统计主表记录数量 -->
    <select id="countByTeamIdAndCreator" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_main
        WHERE team_id = #{teamId, jdbcType=BIGINT}
        <if test="creator != null and creator != ''">
            AND creator = #{creator, jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 根据条件查询主表记录列表 -->
    <select id="selectByConditions" resultMap="BaseResultMap">
        SELECT id, batch_no, qa_im_name, qa_im_desc, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_main
        WHERE team_id = #{teamId, jdbcType=BIGINT}
        <if test="creator != null and creator != ''">
            AND creator = #{creator, jdbcType=VARCHAR}
        </if>
        <if test="qaImName != null and qaImName != ''">
            AND qa_im_name LIKE CONCAT('%', #{qaImName, jdbcType=VARCHAR}, '%')
        </if>
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据条件统计主表记录数量 -->
    <select id="countByConditions" resultType="int">
        SELECT COUNT(1)
        FROM train_qa_import_main
        WHERE team_id = #{teamId, jdbcType=BIGINT}
        <if test="creator != null and creator != ''">
            AND creator = #{creator, jdbcType=VARCHAR}
        </if>
        <if test="qaImName != null and qaImName != ''">
            AND qa_im_name LIKE CONCAT('%', #{qaImName, jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <!-- 更新知识库名称和描述 -->
    <update id="updateMain">
        UPDATE train_qa_import_main
        <set>
            <if test="qaImName != null and qaImName != ''">
                qa_im_name = #{qaImName, jdbcType=VARCHAR},
            </if>
            <if test="qaImDesc != null and qaImDesc != ''">
                qa_im_desc = #{qaImDesc, jdbcType=VARCHAR},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater, jdbcType=VARCHAR},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id, jdbcType=BIGINT}
    </update>

    <!-- 根据批次号查询主表记录 -->
    <select id="selectByBatchNo" resultMap="BaseResultMap">
        SELECT id, batch_no, qa_im_name, qa_im_desc, team_id,
               create_time, update_time, creator, updater, version
        FROM train_qa_import_main
        WHERE batch_no = #{batchNo, jdbcType=VARCHAR}
          AND team_id = #{teamId, jdbcType=BIGINT}
    </select>

</mapper>
