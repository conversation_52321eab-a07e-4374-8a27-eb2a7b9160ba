<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yiyi.ai_train_playground.mapper.converkb.TrainTaskConvKbDtlMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="kb_dtl_id" jdbcType="BIGINT" property="kbDtlId" />
        <result column="learning_status" jdbcType="VARCHAR" property="learningStatus" />
        <result column="f1st_raw_chatlog" jdbcType="LONGVARCHAR" property="f1stRawChatlog" />
        <result column="f2nd_agg_sys_prompt" jdbcType="LONGVARCHAR" property="f2ndAggSysPrompt" />
        <result column="final_chat_log" jdbcType="LONGVARCHAR" property="finalChatLog" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="version" jdbcType="BIGINT" property="version" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, task_id, kb_dtl_id, learning_status, f1st_raw_chatlog, f2nd_agg_sys_prompt, final_chat_log,
        team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_task_conv_kb_dtl
        WHERE id = #{id}
    </select>

    <!-- 根据任务ID查询明细列表 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_task_conv_kb_dtl
        WHERE task_id = #{taskId} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID随机查询一个明细 -->
    <select id="selectRandomByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_task_conv_kb_dtl
        WHERE task_id = #{taskId} AND team_id = #{teamId}
        ORDER BY RAND()
        LIMIT 1
    </select>

    <!-- 根据知识库明细ID查询 -->
    <select id="selectByKbDtlId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_task_conv_kb_dtl
        WHERE kb_dtl_id = #{kbDtlId} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据学习状态查询 -->
    <select id="selectByLearningStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM train_task_conv_kb_dtl
        WHERE learning_status = #{learningStatus} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_task_conv_kb_dtl (
            task_id, kb_dtl_id, learning_status, f1st_raw_chatlog, f2nd_agg_sys_prompt, final_chat_log,
            team_id, creator, updater
        ) VALUES (
            #{taskId}, #{kbDtlId}, #{learningStatus}, #{f1stRawChatlog}, #{f2ndAggSysPrompt}, #{finalChatLog},
            #{teamId}, #{creator}, #{updater}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_task_conv_kb_dtl (
            task_id, kb_dtl_id, learning_status, f1st_raw_chatlog, final_chat_log,
            team_id, creator, updater
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.taskId}, #{record.kbDtlId}, #{record.learningStatus}, #{record.f1stRawChatlog}, #{record.finalChatLog},
                #{record.teamId}, #{record.creator}, #{record.updater}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl">
        UPDATE train_task_conv_kb_dtl
        <set>
            update_time = NOW(),
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="kbDtlId != null">
                kb_dtl_id = #{kbDtlId},
            </if>
            <if test="learningStatus != null and learningStatus != ''">
                learning_status = #{learningStatus},
            </if>
            <if test="f1stRawChatlog != null">
                f1st_raw_chatlog = #{f1stRawChatlog},
            </if>
            <if test="f2ndAggSysPrompt != null">
                f2nd_agg_sys_prompt = #{f2ndAggSysPrompt},
            </if>
            <if test="finalChatLog != null">
                final_chat_log = #{finalChatLog},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 更新学习状态 -->
    <update id="updateLearningStatus">
        UPDATE train_task_conv_kb_dtl
        SET learning_status = #{learningStatus},
            updater = #{updater},
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 更新初始聊天记录 -->
    <update id="updateF1stRawChatlog">
        UPDATE train_task_conv_kb_dtl
        SET f1st_raw_chatlog = #{f1stRawChatlog},
            updater = #{updater},
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 更新最终聊天记录 -->
    <update id="updateFinalChatLog">
        UPDATE train_task_conv_kb_dtl
        SET final_chat_log = #{finalChatLog},
            updater = #{updater},
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM train_task_conv_kb_dtl
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 根据任务ID删除所有明细 -->
    <delete id="deleteByTaskId">
        DELETE FROM train_task_conv_kb_dtl
        WHERE task_id = #{taskId} AND team_id = #{teamId}
    </delete>

    <!-- 统计任务的明细数量 -->
    <select id="countByTaskId" resultType="int">
        SELECT COUNT(*)
        FROM train_task_conv_kb_dtl
        WHERE task_id = #{taskId} AND team_id = #{teamId}
    </select>

    <!-- 统计指定学习状态的数量 -->
    <select id="countByTaskIdAndStatus" resultType="int">
        SELECT COUNT(*)
        FROM train_task_conv_kb_dtl
        WHERE task_id = #{taskId}
        AND learning_status = #{learningStatus}
        AND team_id = #{teamId}
    </select>

    <!-- TaskLearningDTO结果映射 -->
    <resultMap id="TaskLearningResultMap" type="com.yiyi.ai_train_playground.dto.task.TaskLearningDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="kb_dtl_id" jdbcType="BIGINT" property="kbDtlId" />
        <result column="script_id" jdbcType="BIGINT" property="scriptId" />
        <result column="learning_status" jdbcType="VARCHAR" property="learningStatus" />
        <result column="f1st_raw_chatlog" jdbcType="LONGVARCHAR" property="f1stRawChatlog" />
        <result column="final_chat_log" jdbcType="LONGVARCHAR" property="finalChatLog" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="version" jdbcType="BIGINT" property="version" />
    </resultMap>

    <!-- 查询未学习的任务明细（分页） -->
    <select id="selectUnlearnedTaskDetails" resultMap="TaskLearningResultMap">
        SELECT 
            train_task_conv_kb_dtl.*,
            train_reception_task.id as taskId,
            train_script.id as script_id
        FROM train_reception_task
        JOIN train_task_conv_kb_dtl ON train_reception_task.id = train_task_conv_kb_dtl.task_id
        JOIN train_script ON train_reception_task.script_id = train_script.id
        WHERE train_task_conv_kb_dtl.learning_status = 'un_learn'
        ORDER BY train_task_conv_kb_dtl.create_time ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询指定任务下未学习的明细 -->
    <select id="selectUnlearnedTaskDetailsByTaskId" resultMap="TaskLearningResultMap">
        SELECT
            train_task_conv_kb_dtl.*,
            train_reception_task.id as taskId,
            train_script.id as script_id
        FROM train_reception_task
        JOIN train_task_conv_kb_dtl ON train_reception_task.id = train_task_conv_kb_dtl.task_id
        JOIN train_script ON train_reception_task.script_id = train_script.id
        WHERE train_task_conv_kb_dtl.learning_status = 'un_learn'
          AND train_reception_task.id = #{taskId}
        ORDER BY train_task_conv_kb_dtl.create_time ASC
    </select>

    <!-- 更新学习结果（使用乐观锁） -->
    <update id="updateLearningResult">
        UPDATE train_task_conv_kb_dtl
        SET f2nd_agg_sys_prompt = #{f2ndAggSysPrompt},
            final_chat_log = #{finalChatLog},
            learning_status = #{learningStatus},
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        WHERE id = #{id} AND version = #{version}
    </update>

    <!-- 更新学习结果（使用悲观锁） -->
    <update id="updateLearningResultWithPessimisticLock">
        UPDATE train_task_conv_kb_dtl
        SET f2nd_agg_sys_prompt = #{f2ndAggSysPrompt},
            final_chat_log = #{finalChatLog},
            learning_status = #{learningStatus},
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        WHERE id = (
            SELECT id FROM (
                SELECT id FROM train_task_conv_kb_dtl
                WHERE id = #{id}
                FOR UPDATE
            ) AS locked_record
        )
    </update>

</mapper>