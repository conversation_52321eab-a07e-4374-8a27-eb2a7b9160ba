<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.jd.TrainJdAccessTokenMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.jd.TrainJdAccessToken">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="access_token" property="accessToken"/>
        <result column="expires_time" property="expiresTime"/>
        <result column="refresh_token" property="refreshToken"/>
        <result column="scope" property="scope"/>
        <result column="xid" property="xid"/>
        <result column="shop_id" property="shopId"/>
        <result column="is_authorize" property="isAuthorize"/>
        <result column="is_sync_complete" property="isSyncComplete"/>
    </resultMap>

</mapper> 