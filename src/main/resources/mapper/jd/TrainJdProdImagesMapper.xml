<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.jd.TrainJdProdImagesMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="jd_prod_id" property="jdProdId"/>
        <result column="img_url" property="imgUrl"/>
        <result column="img_reco_text" property="imgRecoText"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, team_id, jd_prod_id, img_url, img_reco_text, sync_status,
        create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入商品图片信息 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_jd_prod_images
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId != null">team_id,</if>
            <if test="jdProdId != null">jd_prod_id,</if>
            <if test="imgUrl != null and imgUrl != ''">img_url,</if>
            <if test="imgRecoText != null and imgRecoText != ''">img_reco_text,</if>
            <if test="syncStatus != null">sync_status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="updater != null and updater != ''">updater,</if>
            create_time, update_time, version
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="teamId != null">#{teamId},</if>
            <if test="jdProdId != null">#{jdProdId},</if>
            <if test="imgUrl != null and imgUrl != ''">#{imgUrl},</if>
            <if test="imgRecoText != null and imgRecoText != ''">#{imgRecoText},</if>
            <if test="syncStatus != null">#{syncStatus},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            NOW(), NOW(), 0
        </trim>
    </insert>

    <!-- 批量插入商品图片信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_jd_prod_images (
            team_id, jd_prod_id, img_url, img_reco_text, sync_status,
            creator, updater, create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.teamId}, #{item.jdProdId}, #{item.imgUrl}, #{item.imgRecoText}, #{item.syncStatus},
            #{item.creator}, #{item.updater}, NOW(), NOW(), 0
        )
        </foreach>
    </insert>

    <!-- 根据ID更新商品图片信息 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages">
        UPDATE train_jd_prod_images
        <set>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="jdProdId != null">jd_prod_id = #{jdProdId},</if>
            <if test="imgUrl != null and imgUrl != ''">img_url = #{imgUrl},</if>
            <if test="imgRecoText != null and imgRecoText != ''">img_reco_text = #{imgRecoText},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            update_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除商品图片信息 -->
    <delete id="deleteById">
        DELETE FROM train_jd_prod_images WHERE id = #{id}
    </delete>

    <!-- 根据teamId和jdProdId查询商品图片列表 -->
    <select id="findByTeamIdAndJdProdId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_jd_prod_images
        WHERE team_id = #{teamId} AND jd_prod_id = #{jdProdId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询商品图片信息 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_jd_prod_images
        WHERE id = #{id}
    </select>

    <!-- 根据teamId查询商品图片列表 -->
    <select id="findByTeamId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_jd_prod_images
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据同步状态查询商品图片列表 -->
    <select id="findByTeamIdAndSyncStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_jd_prod_images
        WHERE team_id = #{teamId} AND sync_status = #{syncStatus}
        ORDER BY create_time DESC
    </select>

    <!-- 根据teamId和jdProdId删除商品图片信息 -->
    <delete id="deleteByTeamIdAndJdProdId">
        DELETE FROM train_jd_prod_images 
        WHERE team_id = #{teamId} AND jd_prod_id = #{jdProdId}
    </delete>

    <!-- 统计团队商品图片数量 -->
    <select id="countByTeamId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM train_jd_prod_images
        WHERE team_id = #{teamId}
    </select>

    <!-- 统计指定商品的图片数量 -->
    <select id="countByTeamIdAndJdProdId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM train_jd_prod_images
        WHERE team_id = #{teamId} AND jd_prod_id = #{jdProdId}
    </select>

    <!-- 根据teamId、jdProdId和imgUrl查询商品图片信息 -->
    <select id="findByTeamIdAndJdProdIdAndImgUrl" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_jd_prod_images
        WHERE team_id = #{teamId} AND jd_prod_id = #{jdProdId} AND img_url = #{imgUrl}
        LIMIT 1
    </select>

    <!-- 根据team_id、sync_status分页查询商品图片列表 -->
    <select id="findByTeamIdAndSyncStatusWithPagination" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_jd_prod_images
        <where>
            <if test="teamId != null">
                AND team_id = #{teamId}
            </if>
            <if test="syncStatus != null">
                AND sync_status = #{syncStatus}
            </if>
            <if test="jdProdId != null">
                AND jd_prod_id = #{jdProdId}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

</mapper>
