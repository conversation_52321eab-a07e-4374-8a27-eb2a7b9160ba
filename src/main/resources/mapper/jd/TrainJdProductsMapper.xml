<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.jd.TrainJdProductsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.jd.TrainJdProducts">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="brand_id" property="brandId"/>
        <result column="brand_name" property="brandName"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_sec_id" property="categorySecId"/>
        <result column="col_type" property="colType"/>
        <result column="cost_price" property="costPrice"/>
        <result column="created" property="created"/>
        <result column="height" property="height"/>
        <result column="jd_price" property="jdPrice"/>
        <result column="length" property="length"/>
        <result column="logo" property="logo"/>
        <result column="market_price" property="marketPrice"/>
        <result column="modified" property="modified"/>
        <result column="offline_time" property="offlineTime"/>
        <result column="online_time" property="onlineTime"/>
        <result column="shop_id" property="shopId"/>
        <result column="spu_id" property="spuId"/>
        <result column="stock_num" property="stockNum"/>
        <result column="template_id" property="templateId"/>
        <result column="title" property="title"/>
        <result column="ware_id" property="wareId"/>
        <result column="ware_status" property="wareStatus"/>
        <result column="weight" property="weight"/>
        <result column="width" property="width"/>
        <result column="wrap" property="wrap"/>
        <result column="ware_location" property="wareLocation"/>
        <result column="introduction" property="introduction"/>
        <result column="mobile_desc" property="mobileDesc"/>
        <result column="fit_case_html_app" property="fitCaseHtmlApp"/>
        <result column="features" property="features"/>
        <result column="multi_cate_props" property="multiCateProps"/>
        <result column="jd_prod_dtl" property="jdProdDtl"/>
        <result column="jd_prod_img_list" property="jdProdImgList"/>
        <result column="desc_signature" property="descSignature"/>
        <result column="sync_status" property="syncStatus"/>
    </resultMap>

    <!-- 根据wareId查询商品信息 -->
    <select id="findByWareIdAndTeamId" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE ware_id = #{wareId} AND team_id = #{teamId}
    </select>

    <!-- 插入商品信息 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdProducts" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_jd_products (
            team_id, brand_id, brand_name, category_id, category_sec_id, col_type, cost_price,
            created, height, jd_price, length, logo, market_price, modified, offline_time, online_time,
            shop_id, spu_id, stock_num, template_id, title, ware_id, ware_status, weight,
            width, wrap, ware_location, introduction, mobile_desc, fit_case_html_app,
            features, multi_cate_props, jd_prod_dtl, jd_prod_img_list, desc_signature, sync_status,
            creator, updater, create_time, update_time, version
        ) VALUES (
            #{teamId}, #{brandId}, #{brandName}, #{categoryId}, #{categorySecId}, #{colType}, #{costPrice},
            #{created}, #{height}, #{jdPrice}, #{length}, #{logo}, #{marketPrice}, #{modified}, #{offlineTime}, #{onlineTime},
            #{shopId}, #{spuId}, #{stockNum}, #{templateId}, #{title}, #{wareId}, #{wareStatus}, #{weight},
            #{width}, #{wrap}, #{wareLocation}, #{introduction}, #{mobileDesc}, #{fitCaseHtmlApp},
            #{features}, #{multiCateProps}, #{jdProdDtl}, #{jdProdImgList}, #{descSignature}, #{syncStatus},
            #{creator}, #{updater}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 根据wareId和teamId更新商品信息 -->
    <update id="updateByWareIdAndTeamId" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdProducts">
        UPDATE train_jd_products SET
            brand_id = #{brandId}, brand_name = #{brandName}, category_id = #{categoryId},
            category_sec_id = #{categorySecId}, col_type = #{colType}, cost_price = #{costPrice},
            created = #{created}, height = #{height}, jd_price = #{jdPrice}, length = #{length},
            logo = #{logo}, market_price = #{marketPrice}, modified = #{modified},
            offline_time = #{offlineTime}, online_time = #{onlineTime}, shop_id = #{shopId}, spu_id = #{spuId},
            stock_num = #{stockNum}, template_id = #{templateId}, title = #{title},
            ware_status = #{wareStatus}, weight = #{weight}, width = #{width}, wrap = #{wrap},
            ware_location = #{wareLocation}, introduction = #{introduction}, mobile_desc = #{mobileDesc},
            fit_case_html_app = #{fitCaseHtmlApp}, features = #{features}, multi_cate_props = #{multiCateProps},
            jd_prod_dtl = #{jdProdDtl}, jd_prod_img_list = #{jdProdImgList},
            desc_signature = #{descSignature}, sync_status = #{syncStatus},
            updater = #{updater}, update_time = NOW(),
            version = version + 1
        WHERE ware_id = #{wareId} AND team_id = #{teamId}
    </update>

    <!-- 批量插入商品信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_jd_products (
            team_id, brand_id, brand_name, category_id, category_sec_id, col_type, cost_price,
            created, height, jd_price, length, logo, market_price, modified, offline_time, online_time,
            shop_id, spu_id, stock_num, template_id, title, ware_id, ware_status, weight,
            width, wrap, ware_location, introduction, mobile_desc, fit_case_html_app,
            features, multi_cate_props, jd_prod_dtl, jd_prod_img_list, desc_signature, sync_status,
            creator, updater, create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.teamId}, #{item.brandId}, #{item.brandName}, #{item.categoryId}, #{item.categorySecId},
            #{item.colType}, #{item.costPrice}, #{item.created}, #{item.height}, #{item.jdPrice},
            #{item.length}, #{item.logo}, #{item.marketPrice}, #{item.modified}, #{item.offlineTime}, #{item.onlineTime},
            #{item.shopId}, #{item.spuId}, #{item.stockNum}, #{item.templateId}, #{item.title},
            #{item.wareId}, #{item.wareStatus}, #{item.weight}, #{item.width}, #{item.wrap},
            #{item.wareLocation}, #{item.introduction}, #{item.mobileDesc}, #{item.fitCaseHtmlApp},
            #{item.features}, #{item.multiCateProps}, #{item.jdProdDtl}, #{item.jdProdImgList},
            #{item.descSignature}, #{item.syncStatus}, #{item.creator}, #{item.updater}, NOW(), NOW(), 0
        )
        </foreach>
    </insert>

    <!-- 根据团队ID查询商品列表 -->
    <select id="findByTeamId" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据wareId和teamId删除商品信息 -->
    <delete id="deleteByWareIdAndTeamId">
        DELETE FROM train_jd_products
        WHERE ware_id = #{wareId} AND team_id = #{teamId}
    </delete>

    <!-- 统计团队商品数量 -->
    <select id="countByTeamId" resultType="long">
        SELECT COUNT(*) FROM train_jd_products
        WHERE team_id = #{teamId}
    </select>

    <!-- 根据wareId列表批量查询商品 -->
    <select id="findByWareIds" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE team_id = #{teamId}
        <if test="wareIds != null and wareIds.size() > 0">
            AND ware_id IN
            <foreach collection="wareIds" item="wareId" open="(" separator="," close=")">
                #{wareId}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询团队商品 -->
    <select id="findByTeamIdWithPage" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据商品标题模糊查询 -->
    <select id="findByTeamIdAndTitleLike" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE team_id = #{teamId}
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据品牌查询商品 -->
    <select id="findByTeamIdAndBrandName" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE team_id = #{teamId}
        <if test="brandName != null and brandName != ''">
            AND brand_name = #{brandName}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据价格区间查询商品 -->
    <select id="findByTeamIdAndPriceRange" resultMap="BaseResultMap">
        SELECT * FROM train_jd_products
        WHERE team_id = #{teamId}
        <if test="minPrice != null">
            AND jd_price >= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND jd_price &lt;= #{maxPrice}
        </if>
        ORDER BY jd_price ASC
    </select>

    <!-- 分页查询京东商品列表（以train_jd_products为主表） -->
    <select id="findJdProductListWithPagination" resultType="com.yiyi.ai_train_playground.vo.JdProductListVO">
        SELECT DISTINCT
            tjp.id,
            tjp.brand_id AS brandId,
            tjp.ware_id AS wareId,
            tjp.brand_name AS brandName,
            tjp.shop_id AS shopId,
            tjp.logo,
            tjp.title,
            tjp.ware_status AS status,
            tjp.online_time AS onlineTime,
            tjp.offline_time AS offlineTime
        FROM train_jd_products tjp
        LEFT JOIN train_jd_sku tjs ON tjp.ware_id = tjs.ware_id
        WHERE tjp.team_id = #{teamId}
        <if test="searchKeyword != null and searchKeyword != ''">
            AND (
                tjp.title LIKE CONCAT('%', #{searchKeyword}, '%')
                OR tjp.brand_name LIKE CONCAT('%', #{searchKeyword}, '%')
                OR tjs.sku_id = #{searchKeyword}
            )
        </if>
        ORDER BY tjp.id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计京东商品总数（以train_jd_products为主表） -->
    <select id="countJdProductList" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT tjp.id)
        FROM train_jd_products tjp
        LEFT JOIN train_jd_sku tjs ON tjp.ware_id = tjs.ware_id
        WHERE tjp.team_id = #{teamId}
        <if test="searchKeyword != null and searchKeyword != ''">
            AND (
                tjp.title LIKE CONCAT('%', #{searchKeyword}, '%')
                OR tjp.brand_name LIKE CONCAT('%', #{searchKeyword}, '%')
                OR tjs.sku_id = #{searchKeyword}
            )
        </if>
    </select>

    <!-- 根据wareId查询商品详情 -->
    <select id="findByTeamIdAndWareId" resultType="com.yiyi.ai_train_playground.entity.jd.TrainJdProducts">
        SELECT
            id,
            ware_id,
            jd_prod_dtl,
            jd_prod_img_list
        FROM train_jd_products
        WHERE team_id = #{teamId} AND ware_id = #{wareId}
    </select>

    <!-- 根据team_id、sync_status分页查询商品列表 -->
    <select id="findByTeamIdAndSyncStatusWithPagination" resultType="com.yiyi.ai_train_playground.entity.jd.TrainJdProducts">
        SELECT
            id, team_id, brand_id, brand_name, category_id, category_sec_id, col_type, cost_price,
            created, height, jd_price, length, logo, market_price, modified, offline_time, online_time,
            shop_id, spu_id, stock_num, template_id, title, ware_id, ware_status, weight,
            width, wrap, ware_location, introduction, mobile_desc, fit_case_html_app,
            features, multi_cate_props, jd_prod_dtl, jd_prod_img_list, desc_signature, sync_status,
            creator, updater, create_time, update_time, version
        FROM train_jd_products
        WHERE team_id = #{teamId}
        <if test="syncStatus != null">
            AND sync_status = #{syncStatus}
        </if>
        <if test="jdProdId != null">
            AND id = #{jdProdId}
        </if>
        ORDER BY id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 根据ID动态更新商品信息 -->
    <update id="updateByIdSelective" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdProducts">
        UPDATE train_jd_products
        <set>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="categorySecId != null">category_sec_id = #{categorySecId},</if>
            <if test="colType != null">col_type = #{colType},</if>
            <if test="costPrice != null">cost_price = #{costPrice},</if>
            <if test="created != null">created = #{created},</if>
            <if test="height != null">height = #{height},</if>
            <if test="jdPrice != null">jd_price = #{jdPrice},</if>
            <if test="length != null">length = #{length},</if>
            <if test="logo != null">logo = #{logo},</if>
            <if test="marketPrice != null">market_price = #{marketPrice},</if>
            <if test="modified != null">modified = #{modified},</if>
            <if test="offlineTime != null">offline_time = #{offlineTime},</if>
            <if test="onlineTime != null">online_time = #{onlineTime},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="spuId != null">spu_id = #{spuId},</if>
            <if test="stockNum != null">stock_num = #{stockNum},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="wareId != null">ware_id = #{wareId},</if>
            <if test="wareStatus != null">ware_status = #{wareStatus},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="width != null">width = #{width},</if>
            <if test="wrap != null">wrap = #{wrap},</if>
            <if test="wareLocation != null">ware_location = #{wareLocation},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="mobileDesc != null">mobile_desc = #{mobileDesc},</if>
            <if test="fitCaseHtmlApp != null">fit_case_html_app = #{fitCaseHtmlApp},</if>
            <if test="features != null">features = #{features},</if>
            <if test="multiCateProps != null">multi_cate_props = #{multiCateProps},</if>
            <if test="jdProdDtl != null">jd_prod_dtl = #{jdProdDtl},</if>
            <if test="jdProdImgList != null">jd_prod_img_list = #{jdProdImgList},</if>
            <if test="descSignature != null">desc_signature = #{descSignature},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="version != null">version = #{version}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据teamId和wareId查询商品图片详情（join查询） -->
    <select id="findProductImageDetailsByTeamIdAndWareId" resultType="com.yiyi.ai_train_playground.vo.JdProductImageDetailVO">
        SELECT
            tji.id,
            tji.team_id AS teamId,
            tji.jd_prod_id AS jdProdId,
            tjp.ware_id AS wareId,
            tji.img_url AS imgUrl,
            tji.img_reco_text AS imgRecoText,
            tji.sync_status AS syncStatus,
            tjp.jd_prod_dtl AS jdProdDtl,
            tji.create_time AS createTime,
            tji.update_time AS updateTime,
            tji.creator,
            tji.updater,
            tji.version
        FROM train_jd_products tjp
        INNER JOIN train_jd_prod_images tji ON tjp.ware_id = tji.jd_prod_id
        WHERE tjp.team_id = #{teamId} AND tjp.ware_id = #{wareId}
        ORDER BY tji.create_time ASC
    </select>

</mapper>