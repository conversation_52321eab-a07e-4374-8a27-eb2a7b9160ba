<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.jd.TrainJdProductsMcpMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.jd.TrainJdProductsMcp">
        <id column="id" property="id"/>
        <result column="tjp_id" property="tjpId"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="attrId" property="attrId"/>
        <result column="attrValues" property="attrValues"/>
        <result column="expands" property="expands"/>
        <result column="units" property="units"/>
    </resultMap>

    <!-- 批量插入商品多种类属性信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_jd_products_mcp (
            tjp_id, team_id, attrId, attrValues, expands, units,
            creator, updater, create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.tjpId}, #{item.teamId}, #{item.attrId}, #{item.attrValues}, #{item.expands}, #{item.units},
            #{item.creator}, #{item.updater}, NOW(), NOW(), 0
        )
        </foreach>
    </insert>

</mapper> 