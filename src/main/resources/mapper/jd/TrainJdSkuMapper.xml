<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.jd.TrainJdSkuMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.jd.TrainJdSku">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="bar_code" property="barCode"/>
        <result column="category_id" property="categoryId"/>
        <result column="created" property="created"/>
        <result column="enable" property="enable"/>
        <result column="jd_price" property="jdPrice"/>
        <result column="logo" property="logo"/>
        <result column="modified" property="modified"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_name" property="skuName"/>
        <result column="status" property="status"/>
        <result column="stock_num" property="stockNum"/>
        <result column="ware_id" property="wareId"/>
        <result column="ware_title" property="wareTitle"/>
    </resultMap>

    <!-- 根据skuId查询SKU信息 -->
    <select id="findBySkuId" parameterType="long" resultMap="BaseResultMap">
        SELECT *
        FROM train_jd_sku
        WHERE sku_id = #{skuId}
        LIMIT 1
    </select>

    <!-- 插入SKU信息 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdSku"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_jd_sku (
            team_id, bar_code, category_id, created, enable, jd_price, logo, modified,
            sku_id, sku_name, status, stock_num, ware_id, ware_title,
            creator, updater, create_time, update_time, version
        ) VALUES (
            #{teamId}, #{barCode}, #{categoryId}, #{created}, #{enable}, #{jdPrice}, 
            #{logo}, #{modified}, #{skuId}, #{skuName}, #{status}, #{stockNum}, 
            #{wareId}, #{wareTitle}, #{creator}, #{updater}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 根据skuId更新SKU信息 -->
    <update id="updateBySkuId" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainJdSku">
        UPDATE train_jd_sku SET
            bar_code = #{barCode},
            category_id = #{categoryId},
            created = #{created},
            enable = #{enable},
            jd_price = #{jdPrice},
            logo = #{logo},
            modified = #{modified},
            sku_name = #{skuName},
            status = #{status},
            stock_num = #{stockNum},
            ware_id = #{wareId},
            ware_title = #{wareTitle},
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        WHERE sku_id = #{skuId}
    </update>

    <!-- 检查SKU的modified时间是否比本地新 -->
    <select id="isRemoteNewer" resultType="boolean">
        SELECT CASE 
            WHEN COUNT(*) = 0 THEN TRUE
            WHEN #{remoteModified} > MAX(modified) THEN TRUE
            ELSE FALSE
        END
        FROM train_jd_sku
        WHERE sku_id = #{skuId}
    </select>

</mapper> 