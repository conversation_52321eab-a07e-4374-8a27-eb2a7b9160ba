<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.jd.TrainScriptJdProductsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="script_id" property="scriptId" jdbcType="BIGINT"/>
        <result column="tr_jd_sku_id" property="trJdSkuId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, team_id, script_id, tr_jd_sku_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入剧本京东商品关联记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_script_jd_products (
            team_id, script_id, tr_jd_sku_id, creator, updater, create_time, update_time, version
        ) VALUES (
            #{teamId}, #{scriptId}, #{trJdSkuId}, #{creator}, #{updater}, NOW(), NOW(), #{version}
        )
    </insert>

    <!-- 批量插入剧本京东商品关联记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_script_jd_products (
            team_id, script_id, tr_jd_sku_id, creator, updater, create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.teamId}, #{item.scriptId}, #{item.trJdSkuId}, #{item.creator}, #{item.updater}, NOW(), NOW(), #{item.version})
        </foreach>
    </insert>

    <!-- 根据剧本ID删除剧本京东商品关联 -->
    <delete id="deleteByScriptIdAndTeamId">
        DELETE FROM train_script_jd_products 
        WHERE script_id = #{scriptId} AND team_id = #{teamId}
    </delete>

    <!-- 根据剧本ID查询剧本京东商品关联 -->
    <select id="selectByScriptIdAndTeamId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM train_script_jd_products
        WHERE script_id = #{scriptId} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据剧本ID和团队ID查询关联的京东SKU ID列表 -->
    <select id="selectJdSkuIdsByScriptIdAndTeamId" resultType="java.lang.Long">
        SELECT tr_jd_sku_id
        FROM train_script_jd_products
        WHERE script_id = #{scriptId} AND team_id = #{teamId}
        AND tr_jd_sku_id IS NOT NULL
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询剧本京东商品关联 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM train_script_jd_products
        WHERE id = #{id}
    </select>

    <!-- 根据ID更新剧本京东商品关联 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts">
        UPDATE train_script_jd_products
        <set>
            <if test="teamId != null">
                team_id = #{teamId},
            </if>
            <if test="scriptId != null">
                script_id = #{scriptId},
            </if>
            <if test="trJdSkuId != null">
                tr_jd_sku_id = #{trJdSkuId},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除剧本京东商品关联 -->
    <delete id="deleteById">
        DELETE FROM train_script_jd_products 
        WHERE id = #{id}
    </delete>

    <!-- 根据京东SKU ID查询关联的剧本列表 -->
    <select id="selectByJdSkuIdAndTeamId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM train_script_jd_products
        WHERE tr_jd_sku_id = #{trJdSkuId} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 批量删除剧本京东商品关联 -->
    <delete id="batchDeleteByIds">
        DELETE FROM train_script_jd_products 
        WHERE team_id = #{teamId}
        <if test="ids != null and ids.size() > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

</mapper>
