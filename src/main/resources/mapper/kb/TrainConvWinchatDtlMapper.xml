<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.kb.TrainConvWinchatDtlMapper">

  <!-- 结果映射 -->
  <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatDtl">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="conv_winchat_main_id" jdbcType="BIGINT" property="convWinchatMainId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="f1st_raw_chatlog" jdbcType="LONGVARCHAR" property="f1stRawChatlog" />
    <result column="s2nd_agg_sys_prompt" jdbcType="LONGVARCHAR" property="s2ndAggSysPrompt" />
    <result column="t3rd_rewrite" jdbcType="LONGVARCHAR" property="t3rdRewrite" />
    <result column="f4th_final_sys_prompt" jdbcType="LONGVARCHAR" property="f4thFinalSysPrompt" />
    <result column="conv_kb_id" jdbcType="BIGINT" property="convKbId" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <!-- 插入记录 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO train_conv_winchat_dtl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="convWinchatMainId != null">
        conv_winchat_main_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="sessionId != null and sessionId != ''">
        session_id,
      </if>
      <if test="f1stRawChatlog != null and f1stRawChatlog != ''">
        f1st_raw_chatlog,
      </if>
      <if test="s2ndAggSysPrompt != null and s2ndAggSysPrompt != ''">
        s2nd_agg_sys_prompt,
      </if>
      <if test="t3rdRewrite != null and t3rdRewrite != ''">
        t3rd_rewrite,
      </if>
      <if test="f4thFinalSysPrompt != null and f4thFinalSysPrompt != ''">
        f4th_final_sys_prompt,
      </if>
      <if test="convKbId != null">
        conv_kb_id,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="convWinchatMainId != null">
        #{convWinchatMainId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null and sessionId != ''">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="f1stRawChatlog != null and f1stRawChatlog != ''">
        #{f1stRawChatlog,jdbcType=LONGVARCHAR},
      </if>
      <if test="s2ndAggSysPrompt != null and s2ndAggSysPrompt != ''">
        #{s2ndAggSysPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="t3rdRewrite != null and t3rdRewrite != ''">
        #{t3rdRewrite,jdbcType=LONGVARCHAR},
      </if>
      <if test="f4thFinalSysPrompt != null and f4thFinalSysPrompt != ''">
        #{f4thFinalSysPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="convKbId != null">
        #{convKbId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <!-- 更新记录 -->
  <update id="updateByPrimaryKey">
    UPDATE train_conv_winchat_dtl
    <set>
      update_time = NOW(),
      <if test="convWinchatMainId != null">
        conv_winchat_main_id = #{convWinchatMainId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null and sessionId != ''">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="f1stRawChatlog != null and f1stRawChatlog != ''">
        f1st_raw_chatlog = #{f1stRawChatlog,jdbcType=LONGVARCHAR},
      </if>
      <if test="s2ndAggSysPrompt != null and s2ndAggSysPrompt != ''">
        s2nd_agg_sys_prompt = #{s2ndAggSysPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="t3rdRewrite != null and t3rdRewrite != ''">
        t3rd_rewrite = #{t3rdRewrite,jdbcType=LONGVARCHAR},
      </if>
      <if test="f4thFinalSysPrompt != null and f4thFinalSysPrompt != ''">
        f4th_final_sys_prompt = #{f4thFinalSysPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="convKbId != null">
        conv_kb_id = #{convKbId,jdbcType=BIGINT},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT} AND team_id = #{teamId,jdbcType=BIGINT}
  </update>

  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    SELECT id, conv_winchat_main_id, task_id, session_id, f1st_raw_chatlog, s2nd_agg_sys_prompt, 
           t3rd_rewrite, f4th_final_sys_prompt, conv_kb_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_dtl
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <!-- 根据主键删除 -->
  <delete id="deleteByPrimaryKey">
    DELETE FROM train_conv_winchat_dtl
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>

  <!-- 根据sessionId查询 -->
  <select id="selectBySessionId" resultMap="BaseResultMap">
    SELECT id, conv_winchat_main_id, task_id, session_id, f1st_raw_chatlog, s2nd_agg_sys_prompt, 
           t3rd_rewrite, f4th_final_sys_prompt, conv_kb_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_dtl
    WHERE session_id = #{sessionId,jdbcType=VARCHAR}
  </select>

  <!-- 根据聊天窗口主表ID查询 -->
  <select id="selectByConvWinchatMainId" resultMap="BaseResultMap">
    SELECT id, conv_winchat_main_id, task_id, session_id, f1st_raw_chatlog, s2nd_agg_sys_prompt, 
           t3rd_rewrite, f4th_final_sys_prompt, conv_kb_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_dtl
    WHERE conv_winchat_main_id = #{convWinchatMainId,jdbcType=BIGINT}
    AND team_id = #{teamId,jdbcType=BIGINT}
    ORDER BY create_time DESC
  </select>

  <!-- 根据任务ID查询 -->
  <select id="selectByTaskId" resultMap="BaseResultMap">
    SELECT id, conv_winchat_main_id, task_id, session_id, f1st_raw_chatlog, s2nd_agg_sys_prompt, 
           t3rd_rewrite, f4th_final_sys_prompt, conv_kb_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_dtl
    WHERE task_id = #{taskId,jdbcType=BIGINT}
    AND team_id = #{teamId,jdbcType=BIGINT}
    ORDER BY create_time DESC
  </select>

  <!-- 根据条件查询 -->
  <select id="selectByConditions" resultMap="BaseResultMap">
    SELECT id, conv_winchat_main_id, task_id, session_id, f1st_raw_chatlog, s2nd_agg_sys_prompt, 
           t3rd_rewrite, f4th_final_sys_prompt, conv_kb_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_dtl
    WHERE team_id = #{teamId,jdbcType=BIGINT}
    <if test="convWinchatMainId != null">
      AND conv_winchat_main_id = #{convWinchatMainId,jdbcType=BIGINT}
    </if>
    <if test="taskId != null">
      AND task_id = #{taskId,jdbcType=BIGINT}
    </if>
    ORDER BY create_time DESC
  </select>

</mapper>