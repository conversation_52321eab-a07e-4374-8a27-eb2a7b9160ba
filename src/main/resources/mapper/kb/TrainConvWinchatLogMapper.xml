<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.kb.TrainConvWinchatLogMapper">

  <!-- 结果映射 -->
  <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="conv_dtl_id" jdbcType="BIGINT" property="convDtlId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="sender" jdbcType="VARCHAR" property="sender" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="sender_type" jdbcType="VARCHAR" property="senderType" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="prod_refer_answer" jdbcType="LONGVARCHAR" property="prodReferAnswer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="intent_result" jdbcType="LONGVARCHAR" property="intentResult" />
  </resultMap>

  <!-- 插入记录 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO train_conv_winchat_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="convDtlId != null">
        conv_dtl_id,
      </if>
      <if test="sessionId != null and sessionId != ''">
        session_id,
      </if>
      <if test="sender != null and sender != ''">
        sender,
      </if>
      <if test="content != null and content != ''">
        content,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="senderType != null and senderType != ''">
        sender_type,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="prodReferAnswer != null and prodReferAnswer != ''">
        prod_refer_answer,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="intentResult != null and intentResult != ''">
        intent_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="convDtlId != null">
        #{convDtlId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null and sessionId != ''">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sender != null and sender != ''">
        #{sender,jdbcType=VARCHAR},
      </if>
      <if test="content != null and content != ''">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="senderType != null and senderType != ''">
        #{senderType,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="prodReferAnswer != null and prodReferAnswer != ''">
        #{prodReferAnswer,jdbcType=LONGVARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=INTEGER},
      </if>
      <if test="intentResult != null and intentResult != ''">
        #{intentResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <!-- 批量插入记录 -->
  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO train_conv_winchat_log (conv_dtl_id, session_id, sender, content, send_time, sender_type, team_id, creator, updater, version, prod_refer_answer, score, intent_result)
    VALUES
    <foreach collection="records" item="record" separator=",">
      (#{record.convDtlId,jdbcType=BIGINT}, #{record.sessionId,jdbcType=VARCHAR}, #{record.sender,jdbcType=VARCHAR}, 
       #{record.content,jdbcType=LONGVARCHAR}, #{record.sendTime,jdbcType=TIMESTAMP}, #{record.senderType,jdbcType=VARCHAR}, 
       #{record.teamId,jdbcType=BIGINT}, #{record.creator,jdbcType=VARCHAR}, #{record.updater,jdbcType=VARCHAR}, 
       #{record.version,jdbcType=BIGINT}, #{record.prodReferAnswer,jdbcType=LONGVARCHAR}, #{record.score,jdbcType=INTEGER}, 
       #{record.intentResult,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <!-- 更新记录 -->
  <update id="updateByPrimaryKey">
    UPDATE train_conv_winchat_log
    <set>
      update_time = NOW(),
      <if test="convDtlId != null">
        conv_dtl_id = #{convDtlId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null and sessionId != ''">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sender != null and sender != ''">
        sender = #{sender,jdbcType=VARCHAR},
      </if>
      <if test="content != null and content != ''">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="senderType != null and senderType != ''">
        sender_type = #{senderType,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="prodReferAnswer != null and prodReferAnswer != ''">
        prod_refer_answer = #{prodReferAnswer,jdbcType=LONGVARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="intentResult != null and intentResult != ''">
        intent_result = #{intentResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT} AND team_id = #{teamId,jdbcType=BIGINT}
  </update>

  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    SELECT id, conv_dtl_id, session_id, sender, content, send_time, sender_type, team_id, 
           create_time, update_time, creator, updater, version, prod_refer_answer, score, intent_result
    FROM train_conv_winchat_log
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <!-- 根据主键删除 -->
  <delete id="deleteByPrimaryKey">
    DELETE FROM train_conv_winchat_log
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>

  <!-- 根据明细ID查询聊天记录列表 -->
  <select id="selectByConvDtlId" resultMap="BaseResultMap">
    SELECT id, conv_dtl_id, session_id, sender, content, send_time, sender_type, team_id, 
           create_time, update_time, creator, updater, version, prod_refer_answer, score, intent_result
    FROM train_conv_winchat_log
    WHERE conv_dtl_id = #{convDtlId,jdbcType=BIGINT}
    AND team_id = #{teamId,jdbcType=BIGINT}
    ORDER BY send_time ASC
  </select>

  <!-- 根据sessionId查询聊天记录列表 -->
  <select id="selectBySessionId" resultMap="BaseResultMap">
    SELECT id, conv_dtl_id, session_id, sender, content, send_time, sender_type, team_id, 
           create_time, update_time, creator, updater, version, prod_refer_answer, score, intent_result
    FROM train_conv_winchat_log
    WHERE session_id = #{sessionId,jdbcType=VARCHAR}
    AND team_id = #{teamId,jdbcType=BIGINT}
    ORDER BY send_time ASC
  </select>

  <!-- 根据条件查询 -->
  <select id="selectByConditions" resultMap="BaseResultMap">
    SELECT id, conv_dtl_id, session_id, sender, content, send_time, sender_type, team_id, 
           create_time, update_time, creator, updater, version, prod_refer_answer, score, intent_result
    FROM train_conv_winchat_log
    WHERE team_id = #{teamId,jdbcType=BIGINT}
    <if test="convDtlId != null">
      AND conv_dtl_id = #{convDtlId,jdbcType=BIGINT}
    </if>
    <if test="sessionId != null and sessionId != ''">
      AND session_id = #{sessionId,jdbcType=VARCHAR}
    </if>
    <if test="senderType != null and senderType != ''">
      AND sender_type = #{senderType,jdbcType=VARCHAR}
    </if>
    ORDER BY send_time ASC
  </select>

  <!-- 根据明细ID删除聊天记录 -->
  <delete id="deleteByConvDtlId">
    DELETE FROM train_conv_winchat_log
    WHERE conv_dtl_id = #{convDtlId,jdbcType=BIGINT}
    AND team_id = #{teamId,jdbcType=BIGINT}
  </delete>

</mapper>