<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.kb.TrainConvWinchatMainMapper">

  <!-- 结果映射 -->
  <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatMain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="chatroom_id" jdbcType="BIGINT" property="chatroomId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <!-- 插入记录 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO train_conv_winchat_main
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="chatroomId != null">
        chatroom_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="chatroomId != null">
        #{chatroomId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <!-- 更新记录 -->
  <update id="updateByPrimaryKey">
    UPDATE train_conv_winchat_main
    <set>
      update_time = NOW(),
      <if test="chatroomId != null">
        chatroom_id = #{chatroomId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT} AND team_id = #{teamId,jdbcType=BIGINT}
  </update>

  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    SELECT id, chatroom_id, staff_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_main
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <!-- 根据主键删除 -->
  <delete id="deleteByPrimaryKey">
    DELETE FROM train_conv_winchat_main
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>

  <!-- 根据聊天室ID和员工ID查询 -->
  <select id="selectByChatroomIdAndStaffId" resultMap="BaseResultMap">
    SELECT id, chatroom_id, staff_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_main
    WHERE chatroom_id = #{chatroomId,jdbcType=BIGINT}
    AND staff_id = #{staffId,jdbcType=BIGINT}
    AND team_id = #{teamId,jdbcType=BIGINT}
    ORDER BY create_time DESC
  </select>

  <!-- 根据条件查询 -->
  <select id="selectByConditions" resultMap="BaseResultMap">
    SELECT id, chatroom_id, staff_id, team_id, create_time, update_time, creator, updater, version
    FROM train_conv_winchat_main
    WHERE team_id = #{teamId,jdbcType=BIGINT}
    <if test="chatroomId != null">
      AND chatroom_id = #{chatroomId,jdbcType=BIGINT}
    </if>
    <if test="staffId != null">
      AND staff_id = #{staffId,jdbcType=BIGINT}
    </if>
    ORDER BY create_time DESC
  </select>

</mapper>