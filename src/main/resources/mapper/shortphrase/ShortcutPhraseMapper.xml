<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.shortphrase.ShortcutPhraseMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sp_group_id" property="spGroupId" jdbcType="BIGINT"/>
        <result column="phrase_title" property="phraseTitle" jdbcType="VARCHAR"/>
        <result column="phrase_content" property="phraseContent" jdbcType="LONGVARCHAR"/>
        <result column="phrase_type" property="phraseType" jdbcType="INTEGER"/>
        <result column="usage_count" property="usageCount" jdbcType="INTEGER"/>
        <result column="is_active" property="isActive" jdbcType="BOOLEAN"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, sp_group_id, phrase_title, phrase_content, phrase_type, usage_count, is_active,
        sort_order, tags, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 查询条件 -->
    <sql id="Where_Clause">
        <where>
            tsp.team_id = #{teamId}
            <if test="request.spGroupId != null">
                AND tsp.sp_group_id = #{request.spGroupId}
            </if>
            <if test="request.phraseTitle != null and request.phraseTitle != ''">
                AND tsp.phrase_title LIKE CONCAT('%', #{request.phraseTitle}, '%')
            </if>
            <if test="request.phraseContent != null and request.phraseContent != ''">
                AND tsp.phrase_content LIKE CONCAT('%', #{request.phraseContent}, '%')
            </if>
            <if test="request.phraseType != null">
                AND tsp.phrase_type = #{request.phraseType}
            </if>
            <if test="request.isActive != null">
                AND tsp.is_active = #{request.isActive}
            </if>
            <if test="request.tags != null and request.tags != ''">
                AND tsp.tags LIKE CONCAT('%', #{request.tags}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询短语列表 -->
    <select id="selectPhraseList" resultType="com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO">
        SELECT 
            tsp.id,
            tsp.sp_group_id AS spGroupId,
            tspg.group_title AS groupTitle,
            tsp.phrase_title AS phraseTitle,
            tsp.phrase_content AS phraseContent,
            tsp.phrase_type AS phraseType,
            CASE tsp.phrase_type 
                WHEN 0 THEN '文本'
                WHEN 1 THEN '图片'
                WHEN 2 THEN '链接'
                ELSE '未知'
            END AS phraseTypeName,
            tsp.usage_count AS usageCount,
            tsp.is_active AS isActive,
            tsp.sort_order AS sortOrder,
            tsp.tags,
            tsp.create_time AS createTime,
            tsp.update_time AS updateTime,
            tsp.creator,
            tsp.updater
        FROM train_shortcut_phrases tsp
        LEFT JOIN train_shortcut_phrases_group tspg ON tsp.sp_group_id = tspg.id
        <include refid="Where_Clause"/>
        ORDER BY tsp.sort_order ASC, tsp.update_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询短语总数 -->
    <select id="countPhrases" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_shortcut_phrases tsp
        LEFT JOIN train_shortcut_phrases_group tspg ON tsp.sp_group_id = tspg.id
        <include refid="Where_Clause"/>
    </select>

    <!-- 根据ID查询短语 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_shortcut_phrases
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 根据ID查询短语详情 -->
    <select id="selectDetailById" resultType="com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseDetailDTO">
        SELECT 
            tsp.id,
            tsp.sp_group_id AS spGroupId,
            tspg.group_title AS groupTitle,
            tsp.phrase_title AS phraseTitle,
            tsp.phrase_content AS phraseContent,
            tsp.phrase_type AS phraseType,
            CASE tsp.phrase_type 
                WHEN 0 THEN '文本'
                WHEN 1 THEN '图片'
                WHEN 2 THEN '链接'
                ELSE '未知'
            END AS phraseTypeName,
            tsp.usage_count AS usageCount,
            tsp.is_active AS isActive,
            tsp.sort_order AS sortOrder,
            tsp.tags,
            tsp.team_id AS teamId,
            tsp.create_time AS createTime,
            tsp.update_time AS updateTime,
            tsp.creator,
            tsp.updater,
            tsp.version
        FROM train_shortcut_phrases tsp
        LEFT JOIN train_shortcut_phrases_group tspg ON tsp.sp_group_id = tspg.id
        WHERE tsp.id = #{id} AND tsp.team_id = #{teamId}
    </select>

    <!-- 插入短语 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_shortcut_phrases (
            sp_group_id, phrase_title, phrase_content, phrase_type, usage_count, is_active,
            sort_order, tags, team_id, creator, updater, create_time, update_time, version
        ) VALUES (
            #{spGroupId}, #{phraseTitle}, #{phraseContent}, #{phraseType}, #{usageCount}, #{isActive},
            #{sortOrder}, #{tags}, #{teamId}, #{creator}, #{updater}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新短语 -->
    <update id="update" parameterType="com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase">
        UPDATE train_shortcut_phrases
        <set>
            <if test="spGroupId != null">sp_group_id = #{spGroupId},</if>
            <if test="phraseTitle != null">phrase_title = #{phraseTitle},</if>
            <if test="phraseContent != null">phrase_content = #{phraseContent},</if>
            <if test="phraseType != null">phrase_type = #{phraseType},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="tags != null">tags = #{tags},</if>
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} AND team_id = #{teamId} AND version = #{version}
    </update>

    <!-- 删除短语 -->
    <delete id="deleteById">
        DELETE FROM train_shortcut_phrases
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 批量删除短语 -->
    <delete id="batchDeleteByIds">
        DELETE FROM train_shortcut_phrases
        WHERE team_id = #{teamId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 增加使用次数 -->
    <update id="incrementUsageCount">
        UPDATE train_shortcut_phrases
        SET usage_count = usage_count + 1,
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 根据分组ID查询短语列表 -->
    <select id="selectByGroupId" resultType="com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO">
        SELECT
            tsp.id,
            tsp.sp_group_id AS spGroupId,
            tspg.group_title AS groupTitle,
            tsp.phrase_title AS phraseTitle,
            tsp.phrase_content AS phraseContent,
            tsp.phrase_type AS phraseType,
            CASE tsp.phrase_type
                WHEN 0 THEN '文本'
                WHEN 1 THEN '图片'
                WHEN 2 THEN '链接'
                ELSE '未知'
            END AS phraseTypeName,
            tsp.usage_count AS usageCount,
            tsp.is_active AS isActive,
            tsp.sort_order AS sortOrder,
            tsp.tags,
            tsp.create_time AS createTime,
            tsp.update_time AS updateTime,
            tsp.creator,
            tsp.updater
        FROM train_shortcut_phrases tsp
        LEFT JOIN train_shortcut_phrases_group tspg ON tsp.sp_group_id = tspg.id
        WHERE tsp.sp_group_id = #{spGroupId} AND tsp.team_id = #{teamId} AND tsp.is_active = 1
        ORDER BY tsp.sort_order ASC, tsp.update_time DESC
    </select>

</mapper>
