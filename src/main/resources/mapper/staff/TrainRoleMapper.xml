<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.staff.TrainRoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.staff.TrainRole">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, role_name, role_code, description, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询角色 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_role
        WHERE id = #{id}
    </select>

    <!-- 插入角色 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainRole" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_role (
            role_name, role_code, description, team_id, creator, updater
        ) VALUES (
            #{roleName}, #{roleCode}, #{description}, #{teamId}, #{creator}, #{updater}
        )
    </insert>

    <!-- 根据ID更新角色 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainRole">
        UPDATE train_role
        <set>
            <if test="roleName != null and roleName != ''">
                role_name = #{roleName},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            updater = #{updater},
            version = version + 1
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <!-- 根据ID删除角色 -->
    <delete id="deleteById">
        DELETE FROM train_role WHERE id = #{id}
    </delete>

    <!-- 分页查询角色列表 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_role
        <where>
            team_id = #{request.teamId}
            <if test="request.roleName != null and request.roleName != ''">
                AND role_name LIKE CONCAT('%', #{request.roleName}, '%')
            </if>
            <if test="request.roleCode != null and request.roleCode != ''">
                AND role_code LIKE CONCAT('%', #{request.roleCode}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{request.offset}, #{request.pageSize}
    </select>

    <!-- 查询角色总数 -->
    <select id="selectCount" resultType="long">
        SELECT COUNT(*)
        FROM train_role
        <where>
            team_id = #{request.teamId}
            <if test="request.roleName != null and request.roleName != ''">
                AND role_name LIKE CONCAT('%', #{request.roleName}, '%')
            </if>
            <if test="request.roleCode != null and request.roleCode != ''">
                AND role_code LIKE CONCAT('%', #{request.roleCode}, '%')
            </if>
        </where>
    </select>

    <!-- 根据角色编码查询角色 -->
    <select id="selectByRoleCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_role
        WHERE role_code = #{roleCode}
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="existsByRoleName" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM train_role
        WHERE role_name = #{roleName} AND team_id = #{teamId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查角色编码是否存在 -->
    <select id="existsByRoleCode" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM train_role
        WHERE role_code = #{roleCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量删除角色 -->
    <delete id="deleteBatch">
        DELETE FROM train_role
        WHERE team_id = #{teamId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据团队ID查询角色列表 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_role
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据员工ID查询其拥有的角色 -->
    <select id="selectByStaffId" resultMap="BaseResultMap">
        SELECT
        r.id, r.role_name, r.role_code, r.description, r.team_id, 
        r.create_time, r.update_time, r.creator, r.updater, r.version
        FROM train_role r
        INNER JOIN train_staff_role sr ON r.id = sr.role_id
        WHERE sr.staff_id = #{staffId} AND r.team_id = #{teamId}
        ORDER BY r.create_time DESC
    </select>

</mapper>