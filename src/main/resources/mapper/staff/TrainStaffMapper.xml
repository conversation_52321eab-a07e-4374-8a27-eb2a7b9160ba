<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.staff.TrainStaff">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password_hash" property="passwordHash"/>
        <result column="display_name" property="displayName"/>
        <result column="is_locked" property="isLocked"/>
        <result column="failed_attempts" property="failedAttempts"/>
        <result column="lock_time" property="lockTime"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 通用列 -->
    <sql id="Base_Column_List">
        id, user_id, username, password_hash, display_name,
        is_locked, failed_attempts, lock_time, last_login_time,
        team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入员工 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaff" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_staff (
            user_id, username, password_hash, display_name,
            is_locked, failed_attempts, team_id, creator, updater
        ) VALUES (
            #{userId}, #{username}, #{passwordHash}, #{displayName},
            #{isLocked}, #{failedAttempts}, #{teamId}, #{creator}, #{updater}
        )
    </insert>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM train_staff WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteBatch">
        DELETE FROM train_staff 
        WHERE team_id = #{teamId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量删除(通过拦截器添加team_id) -->
    <delete id="deleteBatchWithIts">
        DELETE FROM train_staff 
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaff">
        UPDATE train_staff
        <set>
            <if test="displayName != null">display_name = #{displayName},</if>
            <if test="isLocked != null">is_locked = #{isLocked},</if>
            <if test="failedAttempts != null">failed_attempts = #{failedAttempts},</if>
            <if test="lockTime != null">lock_time = #{lockTime},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            update_time = CURRENT_TIMESTAMP,
            version = version + 1
        </set>
        WHERE id = #{id} AND team_id = #{teamId} AND version = #{version}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_staff
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询 -->
    <select id="selectByUserId" parameterType="long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_staff
        WHERE user_id = #{userId}
    </select>

    <!-- 根据用户名查询 - 带团队ID限制 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_staff
        WHERE username = #{username} AND team_id = #{teamId}
    </select>

    <!-- 根据用户名查询 - 用于登录（不限制团队） -->
    <select id="selectByUsernameForLogin" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_staff
        WHERE username = #{username}
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="countByUsername" resultType="int">
        SELECT COUNT(1)
        FROM train_staff
        WHERE username = #{username}
        AND team_id = #{teamId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 分页查询员工列表 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_staff
        WHERE team_id = #{teamId}
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="displayName != null and displayName != ''">
            AND display_name LIKE CONCAT('%', #{displayName}, '%')
        </if>
        <if test="isLocked != null">
            AND is_locked = #{isLocked}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计员工总数 -->
    <select id="countPageList" resultType="long">
        SELECT COUNT(1)
        FROM train_staff
        WHERE team_id = #{teamId}
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="displayName != null and displayName != ''">
            AND display_name LIKE CONCAT('%', #{displayName}, '%')
        </if>
        <if test="isLocked != null">
            AND is_locked = #{isLocked}
        </if>
    </select>

    <!-- 更新登录相关信息 -->
    <update id="updateLoginInfo">
        UPDATE train_staff
        <set>
            <if test="failedAttempts != null">failed_attempts = #{failedAttempts},</if>
            <if test="isLocked != null">is_locked = #{isLocked},</if>
            <if test="lockTime != null">lock_time = #{lockTime},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新登录状态 -->
    <update id="updateLoginStatus">
        UPDATE train_staff
        SET failed_attempts = #{failedAttempts},
            is_locked = #{isLocked},
            lock_time = #{lockTime},
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 更新最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE train_staff
        SET last_login_time = #{lastLoginTime},
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 更新密码 -->
    <update id="updatePassword">
        UPDATE train_staff
        SET password_hash = #{passwordHash},
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

</mapper>