<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.staff.TrainStaffRoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.staff.TrainStaffRole">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="staff_id" property="staffId" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, staff_id, role_id, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询员工角色关系 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_role
        WHERE id = #{id}
    </select>

    <!-- 插入员工角色关系 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaffRole" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_staff_role (
            staff_id, role_id, team_id, creator, updater
        ) VALUES (
            #{staffId}, #{roleId}, #{teamId}, #{creator}, #{updater}
        )
    </insert>

    <!-- 根据ID更新员工角色关系 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaffRole">
        UPDATE train_staff_role
        <set>
            staff_id = #{staffId},
            role_id = #{roleId},
            updater = #{updater},
            version = version + 1
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <!-- 根据ID删除员工角色关系 -->
    <delete id="deleteById">
        DELETE FROM train_staff_role WHERE id = #{id}
    </delete>

    <!-- 根据员工ID和角色ID查询关系 -->
    <select id="selectByStaffIdAndRoleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_role
        WHERE staff_id = #{staffId} AND role_id = #{roleId}
    </select>

    <!-- 根据员工ID删除所有角色关系 -->
    <delete id="deleteByStaffId">
        DELETE FROM train_staff_role 
        WHERE staff_id = #{staffId} AND team_id = #{teamId}
    </delete>

    <!-- 根据角色ID删除所有员工关系 -->
    <delete id="deleteByRoleId">
        DELETE FROM train_staff_role 
        WHERE role_id = #{roleId} AND team_id = #{teamId}
    </delete>

    <!-- 批量插入员工角色关系 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO train_staff_role (
            staff_id, role_id, team_id, creator, updater
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.staffId}, #{item.roleId}, #{item.teamId}, #{item.creator}, #{item.updater})
        </foreach>
    </insert>

    <!-- 根据员工ID查询其角色关系列表 -->
    <select id="selectByStaffId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_role
        WHERE staff_id = #{staffId} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据角色ID查询其员工关系列表 -->
    <select id="selectByRoleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_role
        WHERE role_id = #{roleId} AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 检查员工角色关系是否存在 -->
    <select id="existsByStaffIdAndRoleId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM train_staff_role
        WHERE staff_id = #{staffId} AND role_id = #{roleId}
    </select>

    <!-- 批量删除员工角色关系 -->
    <delete id="deleteBatchByStaffIdAndRoleIds">
        DELETE FROM train_staff_role
        WHERE staff_id = #{staffId} AND team_id = #{teamId}
        AND role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

</mapper>