<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.staff.TrainStaffTagMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.staff.TrainStaffTag">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询员工标签 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag
        WHERE id = #{id}
    </select>

    <!-- 插入员工标签 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaffTag"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_staff_tag
        (name, team_id, create_time, update_time, creator, updater, version)
        VALUES
        (#{name}, #{teamId}, #{createTime}, #{updateTime}, #{creator}, #{updater}, #{version})
    </insert>

    <!-- 根据ID更新员工标签 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaffTag">
        UPDATE train_staff_tag
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="version != null">version = #{version},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除员工标签 -->
    <delete id="deleteById">
        DELETE FROM train_staff_tag WHERE id = #{id}
    </delete>

    <!-- 分页查询员工标签 -->
    <select id="selectStaffTagPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag
        <where>
            <if test="request.teamId != null">
                AND team_id = #{request.teamId}
            </if>
            <if test="request.name != null and request.name != ''">
                AND name LIKE CONCAT('%', #{request.name}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询员工标签总数 -->
    <select id="countStaffTag" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM train_staff_tag
        <where>
            <if test="request.teamId != null">
                AND team_id = #{request.teamId}
            </if>
            <if test="request.name != null and request.name != ''">
                AND name LIKE CONCAT('%', #{request.name}, '%')
            </if>
        </where>
    </select>

    <!-- 检查标签名称在团队内是否重复 -->
    <select id="checkNameDuplicate" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM train_staff_tag
        WHERE team_id = #{teamId}
        AND name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量删除员工标签 -->
    <delete id="batchDeleteByIds">
        DELETE FROM train_staff_tag
        WHERE team_id = #{teamId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据条件查询员工标签列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据员工ID查询其拥有的标签 -->
    <select id="selectByStaffId" resultMap="BaseResultMap">
        SELECT
        t.id, t.name, t.team_id, t.create_time, t.update_time, t.creator, t.updater, t.version
        FROM train_staff_tag t
        INNER JOIN train_staff_tag_relation r ON t.id = r.tag_id
        WHERE r.staff_id = #{staffId}
        ORDER BY t.create_time DESC
    </select>

</mapper>