<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.staff.TrainStaffTagRelationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.staff.TrainStaffTagRelation">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="staff_id" property="staffId" jdbcType="BIGINT"/>
        <result column="tag_id" property="tagId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, staff_id, tag_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 根据ID查询员工标签关系 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag_relation
        WHERE id = #{id}
    </select>

    <!-- 插入员工标签关系 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaffTagRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_staff_tag_relation (
            staff_id, tag_id, creator, updater
        ) VALUES (
            #{staffId}, #{tagId}, #{creator}, #{updater}
        )
    </insert>

    <!-- 根据ID更新员工标签关系 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.staff.TrainStaffTagRelation">
        UPDATE train_staff_tag_relation
        <set>
            staff_id = #{staffId},
            tag_id = #{tagId},
            updater = #{updater},
            version = version + 1
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <!-- 根据ID删除员工标签关系 -->
    <delete id="deleteById">
        DELETE FROM train_staff_tag_relation WHERE id = #{id}
    </delete>

    <!-- 根据员工ID和标签ID查询关系 -->
    <select id="selectByStaffIdAndTagId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag_relation
        WHERE staff_id = #{staffId} AND tag_id = #{tagId}
    </select>

    <!-- 根据员工ID删除所有标签关系 -->
    <delete id="deleteByStaffId">
        DELETE FROM train_staff_tag_relation 
        WHERE staff_id = #{staffId}
    </delete>

    <!-- 根据标签ID删除所有员工关系 -->
    <delete id="deleteByTagId">
        DELETE FROM train_staff_tag_relation 
        WHERE tag_id = #{tagId}
    </delete>

    <!-- 批量插入员工标签关系 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO train_staff_tag_relation (
            staff_id, tag_id, creator, updater
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.staffId}, #{item.tagId}, #{item.creator}, #{item.updater})
        </foreach>
    </insert>

    <!-- 根据员工ID查询其标签关系列表 -->
    <select id="selectByStaffId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag_relation
        WHERE staff_id = #{staffId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据标签ID查询其员工关系列表 -->
    <select id="selectByTagId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_staff_tag_relation
        WHERE tag_id = #{tagId}
        ORDER BY create_time DESC
    </select>

    <!-- 检查员工标签关系是否存在 -->
    <select id="existsByStaffIdAndTagId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM train_staff_tag_relation
        WHERE staff_id = #{staffId} AND tag_id = #{tagId}
    </select>

    <!-- 批量删除员工标签关系 -->
    <delete id="deleteBatchByStaffIdAndTagIds">
        DELETE FROM train_staff_tag_relation
        WHERE staff_id = #{staffId}
        AND tag_id IN
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>

</mapper>