<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.system.SystemConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.system.SystemConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="namespace" property="namespace" jdbcType="VARCHAR"/>
        <result column="config_key" property="configKey" jdbcType="VARCHAR"/>
        <result column="config_value" property="configValue" jdbcType="LONGVARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, team_id, namespace, config_key, config_value, description, version, creator, updater, create_time, update_time
    </sql>

    <!-- 插入系统配置 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.system.SystemConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO system_config (
            team_id, namespace, config_key, config_value, description, version, creator, updater, create_time, update_time
        ) VALUES (
            #{teamId}, #{namespace}, #{configKey}, #{configValue}, #{description}, #{version}, #{creator}, #{updater}, NOW(), NOW()
        )
    </insert>

    <!-- 根据ID更新系统配置 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.system.SystemConfig">
        UPDATE system_config SET
            namespace = #{namespace},
            config_key = #{configKey},
            config_value = #{configValue},
            description = #{description},
            version = version + 1,
            updater = #{updater},
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId} AND version = #{version}
    </update>

    <!-- 根据ID删除系统配置 -->
    <delete id="deleteById">
        DELETE FROM system_config WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 批量删除系统配置 -->
    <delete id="deleteBatchByIds">
        DELETE FROM system_config 
        WHERE team_id = #{teamId} AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据ID查询系统配置 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 分页查询系统配置列表 -->
    <select id="selectPageList" resultType="com.yiyi.ai_train_playground.dto.system.SystemConfigResponse">
        SELECT 
            id, team_id as teamId, namespace, config_key as configKey, config_value as configValue, 
            description, version, creator, updater, create_time as createTime, update_time as updateTime
        FROM system_config
        WHERE team_id = #{teamId}
        <if test="namespace != null and namespace != ''">
            AND namespace = #{namespace}
        </if>
        <if test="configKey != null and configKey != ''">
            AND config_key LIKE CONCAT('%', #{configKey}, '%')
        </if>
        <if test="description != null and description != ''">
            AND description LIKE CONCAT('%', #{description}, '%')
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 查询系统配置总数 -->
    <select id="countPageList" resultType="long">
        SELECT COUNT(*)
        FROM system_config
        WHERE team_id = #{teamId}
        <if test="namespace != null and namespace != ''">
            AND namespace = #{namespace}
        </if>
        <if test="configKey != null and configKey != ''">
            AND config_key LIKE CONCAT('%', #{configKey}, '%')
        </if>
        <if test="description != null and description != ''">
            AND description LIKE CONCAT('%', #{description}, '%')
        </if>
    </select>

    <!-- 检查配置键是否存在 -->
    <select id="existsByConfigKey" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM system_config
        WHERE team_id = #{teamId} AND namespace = #{namespace} AND config_key = #{configKey}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据命名空间和配置键查询配置值 -->
    <select id="selectConfigValue" resultType="string">
        SELECT config_value
        FROM system_config
        WHERE team_id = #{teamId} AND namespace = #{namespace} AND config_key = #{configKey}
        LIMIT 1
    </select>

    <!-- 根据命名空间查询所有配置 -->
    <select id="selectByNamespace" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE team_id = #{teamId} AND namespace = #{namespace}
        ORDER BY config_key
    </select>

</mapper>
