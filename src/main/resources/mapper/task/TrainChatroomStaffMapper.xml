<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainChatroomStaffMapper">

    <!-- 聊天室员工关联表的结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainChatroomStaff">
        <id column="id" property="id"/>
        <result column="rece_chatroom_id" property="receChatroomId"/>
        <result column="staff_id" property="staffId"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 插入聊天室员工关联记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.task.TrainChatroomStaff" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_chatroom_staff (
            rece_chatroom_id,
            staff_id,
            team_id,
            creator,
            updater
        ) VALUES (
            #{receChatroomId},
            #{staffId},
            #{teamId},
            #{creator},
            #{updater}
        )
    </insert>

    <!-- 批量插入聊天室员工关联记录 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO train_chatroom_staff (
            rece_chatroom_id,
            staff_id,
            team_id,
            creator,
            updater
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.receChatroomId},
                #{item.staffId},
                #{item.teamId},
                #{item.creator},
                #{item.updater}
            )
        </foreach>
    </insert>

    <!-- 根据聊天室ID删除关联记录 -->
    <delete id="deleteByReceChatroomId">
        DELETE FROM train_chatroom_staff
        WHERE rece_chatroom_id = #{receChatroomId}
        AND team_id = #{teamId}
    </delete>

    <!-- 根据聊天室ID批量删除关联记录 -->
    <delete id="deleteByReceChatroomIds">
        DELETE FROM train_chatroom_staff
        WHERE team_id = #{teamId}
        <if test="receChatroomIds != null and receChatroomIds.size() > 0">
            AND rece_chatroom_id IN
            <foreach collection="receChatroomIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <!-- 根据聊天室ID查询关联的员工ID列表 -->
    <select id="selectStaffIdsByReceChatroomId" resultType="java.lang.Long">
        SELECT staff_id
        FROM train_chatroom_staff
        WHERE rece_chatroom_id = #{receChatroomId}
        AND team_id = #{teamId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据员工ID查询关联的聊天室ID列表 -->
    <select id="selectReceChatroomIdsByStaffId" resultType="java.lang.Long">
        SELECT rece_chatroom_id
        FROM train_chatroom_staff
        WHERE staff_id = #{staffId}
        AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 检查聊天室员工关联是否存在 -->
    <select id="existsByChatroomAndStaff" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM train_chatroom_staff
        WHERE rece_chatroom_id = #{receChatroomId}
        AND staff_id = #{staffId}
        AND team_id = #{teamId}
    </select>

    <!-- 统计聊天室关联的员工数量 -->
    <select id="countStaffByReceChatroomId" resultType="int">
        SELECT COUNT(1)
        FROM train_chatroom_staff
        WHERE rece_chatroom_id = #{receChatroomId}
        AND team_id = #{teamId}
    </select>

    <!-- 根据聊天室ID查询完整的关联记录 -->
    <select id="selectByReceChatroomId" resultMap="BaseResultMap">
        SELECT
            id,
            rece_chatroom_id,
            staff_id,
            team_id,
            create_time,
            update_time,
            creator,
            updater,
            version
        FROM train_chatroom_staff
        WHERE rece_chatroom_id = #{receChatroomId}
        AND team_id = #{teamId}
        ORDER BY create_time ASC
    </select>

</mapper>