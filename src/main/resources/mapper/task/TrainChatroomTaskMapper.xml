<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainChatroomTaskMapper">

    <!-- 聊天室任务关联基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainChatroomTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="chatroom_id" property="chatroomId" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="training_recycle_cnt" property="trainingRecycleCnt" jdbcType="INTEGER"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, chatroom_id, task_id, training_recycle_cnt, team_id, 
        create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入聊天室任务关联记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.task.TrainChatroomTask" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_chatroom_task (
            chatroom_id, task_id, training_recycle_cnt, team_id, creator, updater
        ) VALUES (
            #{chatroomId,jdbcType=BIGINT}, 
            #{taskId,jdbcType=BIGINT}, 
            #{trainingRecycleCnt,jdbcType=INTEGER}, 
            #{teamId,jdbcType=BIGINT}, 
            #{creator,jdbcType=VARCHAR}, 
            #{updater,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入聊天室任务关联记录 -->
    <insert id="batchInsert">
        INSERT INTO train_chatroom_task (
            chatroom_id, task_id, training_recycle_cnt, team_id, creator, updater
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.chatroomId,jdbcType=BIGINT}, 
                #{item.taskId,jdbcType=BIGINT}, 
                #{item.trainingRecycleCnt,jdbcType=INTEGER}, 
                #{item.teamId,jdbcType=BIGINT}, 
                #{item.creator,jdbcType=VARCHAR}, 
                #{item.updater,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 根据聊天室ID查询任务列表 -->
    <select id="selectByChatroomId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_chatroom_task
        WHERE chatroom_id = #{chatroomId,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
        ORDER BY create_time ASC
    </select>

    <!-- 根据ID更新聊天室任务关联 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.task.TrainChatroomTask">
        UPDATE train_chatroom_task
        <set>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="trainingRecycleCnt != null">
                training_recycle_cnt = #{trainingRecycleCnt,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            version = version + 1
        </set>
        WHERE id = #{id,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
          AND version = #{version,jdbcType=BIGINT}
    </update>

    <!-- 根据聊天室ID删除所有任务关联 -->
    <delete id="deleteByChatroomId">
        DELETE FROM train_chatroom_task
        WHERE chatroom_id = #{chatroomId,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
    </delete>

    <!-- 根据ID删除任务关联 -->
    <delete id="deleteById">
        DELETE FROM train_chatroom_task
        WHERE id = #{id,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
    </delete>

</mapper>