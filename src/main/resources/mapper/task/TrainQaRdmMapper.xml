<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainQaRdmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainQaRdm">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="question" property="question" jdbcType="LONGVARCHAR"/>
        <result column="answer" property="answer" jdbcType="LONGVARCHAR"/>
        <result column="actual_question" property="actualQuestion" jdbcType="LONGVARCHAR"/>
        <result column="actual_answer" property="actualAnswer" jdbcType="LONGVARCHAR"/>
        <result column="resolve" property="resolve" jdbcType="LONGVARCHAR"/>
        <result column="ques_no" property="quesNo" jdbcType="VARCHAR"/>
        <result column="report_dtl_id" property="reportDtlId" jdbcType="BIGINT"/>
        <result column="send_time" property="sendTime" jdbcType="TIMESTAMP"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uuid, question, answer, actual_question, actual_answer, resolve, ques_no,
        report_dtl_id, send_time, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 考试答题记录DTO映射结果 -->
    <resultMap id="ExamAnswerRecordResultMap" type="com.yiyi.ai_train_playground.dto.task.ExamAnswerRecordDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="ques_no" property="quesNo" jdbcType="VARCHAR"/>
        <result column="question" property="question" jdbcType="LONGVARCHAR"/>
        <result column="answer" property="answer" jdbcType="LONGVARCHAR"/>
        <result column="actual_question" property="actualQuestion" jdbcType="LONGVARCHAR"/>
        <result column="actual_answer" property="actualAnswer" jdbcType="LONGVARCHAR"/>
        <result column="resolve" property="resolve" jdbcType="LONGVARCHAR"/>
        <result column="send_time" property="sendTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据UUID更新记录 -->
    <update id="updateByUUID">
        UPDATE train_qa_rdm
        <set>
            <if test="record.question != null">
                question = #{record.question},
            </if>
            <if test="record.answer != null">
                answer = #{record.answer},
            </if>
            <if test="record.actualQuestion != null">
                actual_question = #{record.actualQuestion},
            </if>
            <if test="record.actualAnswer != null">
                actual_answer = #{record.actualAnswer},
            </if>
            <if test="record.resolve != null">
                resolve = #{record.resolve},
            </if>
            <if test="record.quesNo != null">
                ques_no = #{record.quesNo},
            </if>
            <if test="record.reportDtlId != null">
                report_dtl_id = #{record.reportDtlId},
            </if>
            <if test="record.sendTime != null">
                send_time = #{record.sendTime},
            </if>
            <if test="record.updater != null">
                updater = #{record.updater},
            </if>
            update_time = CURRENT_TIMESTAMP,
            version = version + 1
        </set>
        WHERE uuid = #{record.uuid}
        <if test="record.version != null">
            AND version = #{record.version}
        </if>
    </update>

    <!-- 根据UUID查询记录（不带团队ID过滤） -->
    <select id="selectByUUID" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_rdm
        WHERE uuid = #{uuid}
    </select>

    <!-- 根据UUID查询记录（带团队ID过滤） -->
    <select id="selectByUUIDWithTeam" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_rdm
        WHERE uuid = #{uuid}
          AND team_id = #{teamId}
    </select>

    <!-- 分页查询问答随机记录 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_rdm
        WHERE 1=1
        <if test="quesNo != null and quesNo != ''">
            AND ques_no LIKE CONCAT('%', #{quesNo}, '%')
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询总记录数 -->
    <select id="countByCondition" resultType="long">
        SELECT COUNT(*)
        FROM train_qa_rdm
        WHERE 1=1
        <if test="quesNo != null and quesNo != ''">
            AND ques_no LIKE CONCAT('%', #{quesNo}, '%')
        </if>
    </select>

    <!-- 根据团队ID查询所有记录 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_rdm
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据问题编号查询记录 -->
    <select id="selectByQuesNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_rdm
        WHERE ques_no = #{quesNo}
        ORDER BY create_time DESC
    </select>

    <!-- 根据报告明细ID查询记录 -->
    <select id="selectByReportDtlId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_rdm
        WHERE report_dtl_id = #{reportDtlId}
        AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 插入单条记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_qa_rdm (
            uuid, question, answer, actual_question, actual_answer, resolve, ques_no,
            report_dtl_id, send_time, team_id, creator, updater, create_time, update_time, version
        ) VALUES (
            #{uuid},
            #{question},
            #{answer},
            #{actualQuestion},
            #{actualAnswer},
            #{resolve},
            #{quesNo},
            #{reportDtlId},
            #{sendTime},
            #{teamId},
            #{creator},
            #{updater},
            #{createTime},
            #{updateTime},
            #{version}
        )
    </insert>

    <!-- 批量插入问答随机记录 -->
    <insert id="batchInsert">
        INSERT INTO train_qa_rdm (
            uuid, question, answer, actual_question, actual_answer, resolve, ques_no,
            report_dtl_id, send_time, team_id, creator, updater
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.uuid},
                #{record.question},
                #{record.answer},
                #{record.actualQuestion},
                #{record.actualAnswer},
                #{record.resolve},
                #{record.quesNo},
                #{record.reportDtlId},
                #{record.sendTime},
                #{record.teamId},
                COALESCE(#{record.creator}, 'system'),
                COALESCE(#{record.updater}, 'system')
            )
        </foreach>
    </insert>

    <!-- 根据UUID删除记录 -->
    <delete id="deleteByUUID">
        DELETE FROM train_qa_rdm
        WHERE uuid = #{uuid}
        AND team_id = #{teamId}
    </delete>

    <!-- 根据团队ID删除所有记录 -->
    <delete id="deleteByTeamId">
        DELETE FROM train_qa_rdm
        WHERE team_id = #{teamId}
    </delete>
    
    <!-- 根据报告明细ID删除记录 -->
    <delete id="deleteByReportDtlId">
        DELETE FROM train_qa_rdm
        WHERE report_dtl_id = #{reportDtlId}
        AND team_id = #{teamId}
    </delete>

    <!-- 根据报告主记录ID查询考试答题记录 -->
    <select id="selectExamAnswerRecordsByMainId" resultMap="ExamAnswerRecordResultMap">
        SELECT 
            tqr.id,
            tqr.uuid,
            tqr.ques_no,
            tqr.question,
            tqr.answer,
            tqr.actual_question,
            tqr.actual_answer,
            tqr.resolve,
            tqr.send_time
        FROM train_qa_report_main trm 
        LEFT JOIN train_qa_report_dtl trd ON trm.id = trd.rp_main_id
        LEFT JOIN train_qa_rdm tqr ON trd.id = tqr.report_dtl_id
        WHERE trm.id = #{qaReportMainId}
        ORDER BY 
            CAST(SUBSTRING_INDEX(tqr.ques_no, '/', 1) AS UNSIGNED) ASC,
            CAST(SUBSTRING_INDEX(tqr.ques_no, '/', -1) AS UNSIGNED) ASC
    </select>

</mapper>
