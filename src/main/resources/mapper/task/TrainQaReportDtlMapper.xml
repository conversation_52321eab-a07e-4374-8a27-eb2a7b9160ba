<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainQaReportDtlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl">
        <id column="id" property="id"/>
        <result column="rp_main_id" property="rpMainId"/>
        <result column="task_id" property="taskId"/>
        <result column="session_id" property="sessionId"/>
        <result column="qa_main_id" property="qaMainId"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rp_main_id, task_id, session_id, qa_main_id, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="record.id">
        INSERT INTO train_qa_report_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="record.rpMainId != null">
                rp_main_id,
            </if>
            <if test="record.taskId != null">
                task_id,
            </if>
            <if test="record.sessionId != null">
                session_id,
            </if>
            <if test="record.qaMainId != null">
                qa_main_id,
            </if>
            <if test="record.teamId != null">
                team_id,
            </if>
            <if test="record.creator != null">
                creator,
            </if>
            create_time,
            update_time,
            version
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="record.rpMainId != null">
                #{record.rpMainId},
            </if>
            <if test="record.taskId != null">
                #{record.taskId},
            </if>
            <if test="record.sessionId != null">
                #{record.sessionId},
            </if>
            <if test="record.qaMainId != null">
                #{record.qaMainId},
            </if>
            <if test="record.teamId != null">
                #{record.teamId},
            </if>
            <if test="record.creator != null">
                #{record.creator},
            </if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            0
        </trim>
    </insert>

    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_dtl
        WHERE id = #{id}
        AND team_id = #{teamId}
    </select>

    <!-- 根据ID更新记录 -->
    <update id="updateById">
        UPDATE train_qa_report_dtl
        <set>
            <if test="record.rpMainId != null">
                rp_main_id = #{record.rpMainId},
            </if>
            <if test="record.taskId != null">
                task_id = #{record.taskId},
            </if>
            <if test="record.sessionId != null">
                session_id = #{record.sessionId},
            </if>
            <if test="record.qaMainId != null">
                qa_main_id = #{record.qaMainId},
            </if>
            <if test="record.updater != null">
                updater = #{record.updater},
            </if>
            update_time = CURRENT_TIMESTAMP,
            version = version + 1
        </set>
        WHERE id = #{record.id}
        AND team_id = #{record.teamId}
        <if test="record.version != null">
            AND version = #{record.version}
        </if>
    </update>

    <!-- 根据ID删除记录 -->
    <delete id="deleteById">
        DELETE FROM train_qa_report_dtl
        WHERE id = #{id}
        AND team_id = #{teamId}
    </delete>

    <!-- 根据sessionId查询记录 -->
    <select id="selectBySessionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_dtl
        WHERE session_id = #{sessionId}
        AND team_id = #{teamId}
    </select>

    <!-- 根据taskId查询记录列表 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_dtl
        WHERE task_id = #{taskId}
        AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据rpMainId查询记录列表 -->
    <select id="selectByRpMainId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_dtl
        WHERE rp_main_id = #{rpMainId}
        AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询记录 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_dtl
        WHERE team_id = #{teamId}
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        <if test="qaMainId != null">
            AND qa_main_id = #{qaMainId}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询总记录数 -->
    <select id="countByCondition" resultType="long">
        SELECT COUNT(*)
        FROM train_qa_report_dtl
        WHERE team_id = #{teamId}
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        <if test="qaMainId != null">
            AND qa_main_id = #{qaMainId}
        </if>
    </select>

    <!-- 根据团队ID查询所有记录 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_dtl
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入记录 -->
    <insert id="batchInsert">
        INSERT INTO train_qa_report_dtl
        (rp_main_id, task_id, session_id, qa_main_id, team_id, creator, create_time, update_time, version)
        VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.rpMainId}, #{record.taskId}, #{record.sessionId}, #{record.qaMainId}, 
             #{record.teamId}, #{record.creator}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
        </foreach>
    </insert>

    <!-- 根据团队ID删除所有记录 -->
    <delete id="deleteByTeamId">
        DELETE FROM train_qa_report_dtl
        WHERE team_id = #{teamId}
    </delete>

    <!-- 根据taskId删除记录 -->
    <delete id="deleteByTaskId">
        DELETE FROM train_qa_report_dtl
        WHERE task_id = #{taskId}
        AND team_id = #{teamId}
    </delete>
    
    <!-- 根据rpMainId删除记录 -->
    <delete id="deleteByRpMainId">
        DELETE FROM train_qa_report_dtl
        WHERE rp_main_id = #{rpMainId}
        AND team_id = #{teamId}
    </delete>

</mapper>
