<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainQaReportMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainQaReportMain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="chatroom_id" property="chatroomId" jdbcType="BIGINT"/>
        <result column="staff_id" property="staffId" jdbcType="BIGINT"/>
        <result column="exam_user_real_name" property="examUserRealName" jdbcType="VARCHAR"/>
        <result column="exam_user_no" property="examUserNo" jdbcType="VARCHAR"/>
        <result column="exam_score" property="examScore" jdbcType="DECIMAL"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, chatroom_id, staff_id, exam_user_real_name, exam_user_no, exam_score,
        team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_qa_report_main (
            chatroom_id,
            staff_id,
            exam_user_real_name,
            exam_user_no,
            exam_score,
            team_id,
            creator,
            create_time,
            update_time,
            version
        ) VALUES (
            #{chatroomId},
            #{staffId},
            #{examUserRealName},
            #{examUserNo},
            #{examScore},
            #{teamId},
            #{creator},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            0
        )
    </insert>

    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_main
        WHERE id = #{id}
    </select>

    <!-- 根据ID更新记录 -->
    <update id="updateById">
        UPDATE train_qa_report_main
        <set>
            <if test="chatroomId != null">
                chatroom_id = #{chatroomId},
            </if>
            <if test="staffId != null">
                staff_id = #{staffId},
            </if>
            <if test="examUserRealName != null">
                exam_user_real_name = #{examUserRealName},
            </if>
            <if test="examUserNo != null">
                exam_user_no = #{examUserNo},
            </if>
            <if test="examScore != null">
                exam_score = #{examScore},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            update_time = CURRENT_TIMESTAMP,
            version = version + 1
        </set>
        WHERE id = #{id}
        AND team_id = #{teamId}
        <if test="version != null">
            AND version = #{version}
        </if>
    </update>

    <!-- 根据ID删除记录 -->
    <delete id="deleteById">
        DELETE FROM train_qa_report_main
        WHERE id = #{id}
        AND team_id = #{teamId}
    </delete>

    <!-- 分页查询记录 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_main
        WHERE team_id = #{teamId}
        <if test="chatroomId != null">
            AND chatroom_id = #{chatroomId}
        </if>
        <if test="staffId != null">
            AND staff_id = #{staffId}
        </if>
        <if test="examUserRealName != null and examUserRealName != ''">
            AND exam_user_real_name LIKE CONCAT('%', #{examUserRealName}, '%')
        </if>
        <if test="examUserNo != null and examUserNo != ''">
            AND exam_user_no LIKE CONCAT('%', #{examUserNo}, '%')
        </if>
        <if test="createTimeStart != null">
            AND create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND create_time &lt;= #{createTimeEnd}
        </if>
        <if test="minScore != null">
            AND exam_score &gt;= #{minScore}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询总记录数 -->
    <select id="countByCondition" resultType="long">
        SELECT COUNT(*)
        FROM train_qa_report_main
        WHERE team_id = #{teamId}
        <if test="chatroomId != null">
            AND chatroom_id = #{chatroomId}
        </if>
        <if test="staffId != null">
            AND staff_id = #{staffId}
        </if>
        <if test="examUserRealName != null and examUserRealName != ''">
            AND exam_user_real_name LIKE CONCAT('%', #{examUserRealName}, '%')
        </if>
        <if test="examUserNo != null and examUserNo != ''">
            AND exam_user_no LIKE CONCAT('%', #{examUserNo}, '%')
        </if>
        <if test="createTimeStart != null">
            AND create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND create_time &lt;= #{createTimeEnd}
        </if>
        <if test="minScore != null">
            AND exam_score &gt;= #{minScore}
        </if>
    </select>

    <!-- 查询记录列表用于导出Excel（限制10万条） -->
    <select id="selectListForExport" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_main
        WHERE team_id = #{teamId}
        <if test="chatroomId != null">
            AND chatroom_id = #{chatroomId}
        </if>
        <if test="staffId != null">
            AND staff_id = #{staffId}
        </if>
        <if test="examUserRealName != null and examUserRealName != ''">
            AND exam_user_real_name LIKE CONCAT('%', #{examUserRealName}, '%')
        </if>
        <if test="examUserNo != null and examUserNo != ''">
            AND exam_user_no LIKE CONCAT('%', #{examUserNo}, '%')
        </if>
        <if test="createTimeStart != null">
            AND create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND create_time &lt;= #{createTimeEnd}
        </if>
        <if test="minScore != null">
            AND exam_score &gt;= #{minScore}
        </if>
        ORDER BY create_time DESC
        LIMIT 100000
    </select>

    <!-- 根据聊天室ID查询记录 -->
    <select id="selectByChatroomId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_main
        WHERE chatroom_id = #{chatroomId}
        AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据员工ID查询记录 -->
    <select id="selectByStaffId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_main
        WHERE staff_id = #{staffId}
        AND team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据团队ID查询所有记录 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM train_qa_report_main
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入记录 -->
    <insert id="batchInsert">
        INSERT INTO train_qa_report_main (
            chatroom_id, staff_id, exam_user_real_name, exam_user_no, exam_score,
            team_id, creator, updater
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.chatroomId},
                #{record.staffId},
                #{record.examUserRealName},
                #{record.examUserNo},
                #{record.examScore},
                #{record.teamId},
                COALESCE(#{record.creator}, 'system'),
                COALESCE(#{record.updater}, 'system')
            )
        </foreach>
    </insert>

    <!-- 根据团队ID删除所有记录 -->
    <delete id="deleteByTeamId">
        DELETE FROM train_qa_report_main
        WHERE team_id = #{teamId}
    </delete>

    <!-- 根据ID更新考试分数 -->
    <update id="updateExamScoreById">
        UPDATE train_qa_report_main
        SET exam_score = #{examScore},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
