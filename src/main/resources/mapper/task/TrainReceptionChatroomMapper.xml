<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainReceptionChatroomMapper">

    <!-- 聊天室基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainReceptionChatroom">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="room_name" property="roomName" jdbcType="VARCHAR"/>
        <result column="reception_skin" property="receptionSkin" jdbcType="VARCHAR"/>
        <result column="scene_mode" property="sceneMode" jdbcType="INTEGER"/>
        <result column="quick_phrases_id" property="quickPhrasesId" jdbcType="BIGINT"/>
        <result column="reception_duration" property="receptionDuration" jdbcType="INTEGER"/>
        <result column="timer_display" property="timerDisplay" jdbcType="TINYINT"/>
        <result column="entry_freq_min" property="entryFreqMin" jdbcType="INTEGER"/>
        <result column="entry_freq_max" property="entryFreqMax" jdbcType="INTEGER"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 聊天室详情结果映射 -->
    <resultMap id="DetailResultMap" type="com.yiyi.ai_train_playground.dto.task.ChatroomDetailDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="room_name" property="roomName" jdbcType="VARCHAR"/>
        <result column="reception_skin" property="receptionSkin" jdbcType="VARCHAR"/>
        <result column="reception_skin_name" property="receptionSkinName" jdbcType="VARCHAR"/>
        <result column="scene_mode" property="sceneMode" jdbcType="INTEGER"/>
        <result column="scene_mode_name" property="sceneModeName" jdbcType="VARCHAR"/>
        <result column="quick_phrases_id" property="quickPhrasesId" jdbcType="BIGINT"/>
        <result column="quick_phrases_name" property="quickPhrasesName" jdbcType="VARCHAR"/>
        <result column="reception_duration" property="receptionDuration" jdbcType="INTEGER"/>
        <result column="timer_display" property="timerDisplay" jdbcType="TINYINT"/>
        <result column="entry_freq_min" property="entryFreqMin" jdbcType="INTEGER"/>
        <result column="entry_freq_max" property="entryFreqMax" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 聊天室列表结果映射 -->
    <resultMap id="ListResultMap" type="com.yiyi.ai_train_playground.dto.task.ChatroomListDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="room_name" property="roomName" jdbcType="VARCHAR"/>
        <result column="reception_skin" property="receptionSkin" jdbcType="VARCHAR"/>
        <result column="reception_skin_name" property="receptionSkinName" jdbcType="VARCHAR"/>
        <result column="scene_mode" property="sceneMode" jdbcType="INTEGER"/>
        <result column="scene_mode_name" property="sceneModeName" jdbcType="VARCHAR"/>
        <result column="quick_phrases_id" property="quickPhrasesId" jdbcType="BIGINT"/>
        <result column="quick_phrases_name" property="quickPhrasesName" jdbcType="VARCHAR"/>
        <result column="reception_duration" property="receptionDuration" jdbcType="INTEGER"/>
        <result column="timer_display" property="timerDisplay" jdbcType="TINYINT"/>
        <result column="entry_freq_min" property="entryFreqMin" jdbcType="INTEGER"/>
        <result column="entry_freq_max" property="entryFreqMax" jdbcType="INTEGER"/>
        <result column="task_count" property="taskCount" jdbcType="INTEGER"/>
        <result column="staff_count" property="staffCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, room_name, reception_skin, scene_mode, quick_phrases_id, reception_duration,
        timer_display, entry_freq_min, entry_freq_max, team_id, create_time, update_time, creator, updater, version
    </sql>

    <!-- 插入聊天室记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.task.TrainReceptionChatroom" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_reception_chatroom (
            room_name, reception_skin, scene_mode, quick_phrases_id, reception_duration,
            timer_display, entry_freq_min, entry_freq_max, team_id, creator, updater
        ) VALUES (
            #{roomName,jdbcType=VARCHAR},
            #{receptionSkin,jdbcType=VARCHAR},
            #{sceneMode,jdbcType=INTEGER},
            #{quickPhrasesId,jdbcType=BIGINT},
            #{receptionDuration,jdbcType=INTEGER},
            #{timerDisplay,jdbcType=TINYINT},
            #{entryFreqMin,jdbcType=INTEGER},
            #{entryFreqMax,jdbcType=INTEGER},
            #{teamId,jdbcType=BIGINT},
            #{creator,jdbcType=VARCHAR},
            #{updater,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据ID查询聊天室 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_reception_chatroom
        WHERE id = #{id,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
    </select>

    <!-- 查询聊天室详情（包含关联信息） -->
    <select id="selectDetailById" resultMap="DetailResultMap">
        SELECT 
            trc.id,
            trc.room_name,
            trc.reception_skin,
            CASE trc.reception_skin
                WHEN 'qianniu_skin' THEN '千牛'
                WHEN 'dongdong_skin' THEN '咚咚'
                WHEN 'douyin_skin' THEN '抖音'
                WHEN 'default_skin' THEN '默认'
                WHEN '0' THEN '千牛'
                WHEN '1' THEN '咚咚'
                WHEN '2' THEN '抖音'
                WHEN '3' THEN '默认'
                ELSE '未知'
            END as reception_skin_name,
            trc.scene_mode,
            CASE trc.scene_mode 
                WHEN 0 THEN '萌新友好'
                WHEN 1 THEN '压力考核'
                WHEN 2 THEN '自定义'
                ELSE '未知'
            END as scene_mode_name,
            trc.quick_phrases_id,
            tsp.phrase_title as quick_phrases_name,
            trc.reception_duration,
            trc.timer_display,
            trc.entry_freq_min,
            trc.entry_freq_max,
            trc.create_time,
            trc.update_time,
            trc.creator,
            trc.version
        FROM train_reception_chatroom trc
        LEFT JOIN train_shortcut_phrases tsp ON trc.quick_phrases_id = tsp.id AND tsp.team_id = #{teamId,jdbcType=BIGINT}
        WHERE trc.id = #{id,jdbcType=BIGINT} 
          AND trc.team_id = #{teamId,jdbcType=BIGINT}
    </select>

    <!-- 分页查询聊天室列表 -->
    <select id="selectPageList" resultMap="ListResultMap">
        SELECT 
            trc.id,
            trc.room_name,
            trc.reception_skin,
            CASE trc.reception_skin
                WHEN 'qianniu_skin' THEN '千牛'
                WHEN 'dongdong_skin' THEN '咚咚'
                WHEN 'douyin_skin' THEN '抖音'
                WHEN 'default_skin' THEN '默认'
                WHEN '0' THEN '千牛'
                WHEN '1' THEN '咚咚'
                WHEN '2' THEN '抖音'
                WHEN '3' THEN '默认'
                ELSE '未知'
            END as reception_skin_name,
            trc.scene_mode,
            CASE trc.scene_mode 
                WHEN 0 THEN '萌新友好'
                WHEN 1 THEN '压力考核'
                WHEN 2 THEN '自定义'
                ELSE '未知'
            END as scene_mode_name,
            trc.quick_phrases_id,
            tsp.phrase_title as quick_phrases_name,
            trc.reception_duration,
            trc.timer_display,
            trc.entry_freq_min,
            trc.entry_freq_max,
            COALESCE(task_stats.task_count, 0) as task_count,
            COALESCE(staff_stats.staff_count, 0) as staff_count,
            trc.create_time,
            trc.update_time,
            trc.creator
        FROM train_reception_chatroom trc
        LEFT JOIN train_shortcut_phrases tsp ON trc.quick_phrases_id = tsp.id AND tsp.team_id = #{teamId,jdbcType=BIGINT}
        LEFT JOIN (
            SELECT chatroom_id, COUNT(*) as task_count
            FROM train_chatroom_task
            WHERE team_id = #{teamId,jdbcType=BIGINT}
            GROUP BY chatroom_id
        ) task_stats ON trc.id = task_stats.chatroom_id
        LEFT JOIN (
            SELECT rece_chatroom_id, COUNT(*) as staff_count
            FROM train_chatroom_staff
            WHERE team_id = #{teamId,jdbcType=BIGINT}
            GROUP BY rece_chatroom_id
        ) staff_stats ON trc.id = staff_stats.rece_chatroom_id
        WHERE trc.team_id = #{teamId,jdbcType=BIGINT}
        <include refid="queryConditions"/>
        ORDER BY trc.create_time DESC
        <if test="request.page != null and request.pageSize != null">
            LIMIT #{request.pageSize} OFFSET #{request.offset}
        </if>
    </select>

    <!-- 查询聊天室列表总数 -->
    <select id="selectPageCount" resultType="long">
        SELECT COUNT(1)
        FROM train_reception_chatroom trc
        WHERE trc.team_id = #{teamId,jdbcType=BIGINT}
        <include refid="queryConditions"/>
    </select>

    <!-- 查询条件 -->
    <sql id="queryConditions">
        <if test="request.receptionSkin != null">
            AND trc.reception_skin = #{request.receptionSkin,jdbcType=INTEGER}
        </if>
        <if test="request.sceneMode != null">
            AND trc.scene_mode = #{request.sceneMode,jdbcType=INTEGER}
        </if>
        <if test="request.roomName != null and request.roomName != ''">
            AND trc.room_name LIKE CONCAT('%', #{request.roomName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="creator != null and creator != ''">
            AND trc.creator = #{creator,jdbcType=VARCHAR}
        </if>
        <if test="request.quickPhrasesId != null">
            AND trc.quick_phrases_id = #{request.quickPhrasesId,jdbcType=BIGINT}
        </if>
        <if test="request.createTimeStart != null and request.createTimeStart != ''">
            AND DATE(trc.create_time) &gt;= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null and request.createTimeEnd != ''">
            AND DATE(trc.create_time) &lt;= #{request.createTimeEnd}
        </if>
    </sql>

    <!-- 更新聊天室 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.task.TrainReceptionChatroom">
        UPDATE train_reception_chatroom
        <set>
            <if test="roomName != null">
                room_name = #{roomName,jdbcType=VARCHAR},
            </if>
            <if test="receptionSkin != null">
                reception_skin = #{receptionSkin,jdbcType=VARCHAR},
            </if>
            <if test="sceneMode != null">
                scene_mode = #{sceneMode,jdbcType=INTEGER},
            </if>
            <if test="quickPhrasesId != null">
                quick_phrases_id = #{quickPhrasesId,jdbcType=BIGINT},
            </if>
            <if test="receptionDuration != null">
                reception_duration = #{receptionDuration,jdbcType=INTEGER},
            </if>
            <if test="timerDisplay != null">
                timer_display = #{timerDisplay,jdbcType=TINYINT},
            </if>
            <if test="entryFreqMin != null">
                entry_freq_min = #{entryFreqMin,jdbcType=INTEGER},
            </if>
            <if test="entryFreqMax != null">
                entry_freq_max = #{entryFreqMax,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            version = version + 1
        </set>
        WHERE id = #{id,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
          AND version = #{version,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除聊天室 -->
    <delete id="deleteById">
        DELETE FROM train_reception_chatroom
        WHERE id = #{id,jdbcType=BIGINT} 
          AND team_id = #{teamId,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除聊天室 -->
    <delete id="batchDeleteByIds">
        DELETE FROM train_reception_chatroom
        WHERE team_id = #{teamId,jdbcType=BIGINT}
          AND id IN 
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <!-- 分页查询员工的接待聊天任务列表 -->
    <select id="selectMyTasksPageList" resultMap="ListResultMap">
        SELECT 
            trc.id,
            trc.room_name,
            trc.reception_skin,
            CASE trc.reception_skin
                WHEN 'qianniu_skin' THEN '千牛'
                WHEN 'dongdong_skin' THEN '咚咚'
                WHEN 'douyin_skin' THEN '抖音'
                WHEN 'default_skin' THEN '默认'
                WHEN '0' THEN '千牛'
                WHEN '1' THEN '咚咚'
                WHEN '2' THEN '抖音'
                WHEN '3' THEN '默认'
                ELSE '未知'
            END as reception_skin_name,
            trc.scene_mode,
            CASE trc.scene_mode
                WHEN 0 THEN '萌新友好'
                WHEN 1 THEN '压力考核'
                WHEN 2 THEN '自定义'
                ELSE '未知'
            END as scene_mode_name,
            trc.quick_phrases_id,
            'train_shortcut_phrases' as quick_phrases_name,
            trc.reception_duration,
            trc.timer_display,
            trc.entry_freq_min,
            trc.entry_freq_max,
            trc.create_time,
            trc.update_time,
            trc.creator,
            trc.version,
            (SELECT COUNT(*) FROM train_chatroom_staff tcs2 WHERE tcs2.rece_chatroom_id = trc.id AND tcs2.team_id = #{teamId}) as staff_count
        FROM train_reception_chatroom trc
        INNER JOIN train_chatroom_staff tcs ON trc.id = tcs.rece_chatroom_id AND tcs.staff_id = #{staffId}
        WHERE trc.team_id = #{teamId}
          AND tcs.team_id = #{teamId}
        <if test="request.receptionSkin != null">
            AND trc.reception_skin = #{request.receptionSkin,jdbcType=INTEGER}
        </if>
        <if test="request.sceneMode != null">
            AND trc.scene_mode = #{request.sceneMode,jdbcType=INTEGER}
        </if>
        <if test="request.roomName != null and request.roomName != ''">
            AND trc.room_name LIKE CONCAT('%', #{request.roomName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="request.quickPhrasesId != null">
            AND trc.quick_phrases_id = #{request.quickPhrasesId,jdbcType=BIGINT}
        </if>
        ORDER BY trc.create_time DESC
        <if test="request.page != null and request.pageSize != null">
            LIMIT #{request.pageSize} OFFSET #{request.offset}
        </if>
    </select>

    <!-- 查询员工的接待聊天任务总数 -->
    <select id="selectMyTasksPageCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_reception_chatroom trc
        INNER JOIN train_chatroom_staff tcs ON trc.id = tcs.rece_chatroom_id AND tcs.staff_id = #{staffId}
        WHERE trc.team_id = #{teamId}
          AND tcs.team_id = #{teamId}
        <if test="request.receptionSkin != null">
            AND trc.reception_skin = #{request.receptionSkin,jdbcType=INTEGER}
        </if>
        <if test="request.sceneMode != null">
            AND trc.scene_mode = #{request.sceneMode,jdbcType=INTEGER}
        </if>
        <if test="request.roomName != null and request.roomName != ''">
            AND trc.room_name LIKE CONCAT('%', #{request.roomName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="request.quickPhrasesId != null">
            AND trc.quick_phrases_id = #{request.quickPhrasesId,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 员工信息结果映射 -->
    <resultMap id="StaffResultMap" type="com.yiyi.ai_train_playground.entity.staff.TrainStaff">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password_hash" property="passwordHash" jdbcType="VARCHAR"/>
        <result column="display_name" property="displayName" jdbcType="VARCHAR"/>
        <result column="is_locked" property="isLocked" jdbcType="TINYINT"/>
        <result column="failed_attempts" property="failedAttempts" jdbcType="INTEGER"/>
        <result column="lock_time" property="lockTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 聊天室任务信息结果映射 -->
    <resultMap id="ChatRoomTaskInfoResultMap" type="com.yiyi.ai_train_playground.dto.task.ChatRoomTaskInfo">
        <result column="script_id" property="scriptId" jdbcType="BIGINT"/>
        <result column="script_name" property="scriptName" jdbcType="VARCHAR"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="task_name" property="taskName" jdbcType="VARCHAR"/>
        <result column="training_recycle_cnt" property="trainingRecycleCnt" jdbcType="INTEGER"/>
        <result column="buyer_requirement" property="buyerRequirement" jdbcType="VARCHAR"/>
        <result column="generation_type_code" property="generationTypeCode" jdbcType="INTEGER"/>
        <result column="intent_id" property="intentId" jdbcType="BIGINT"/>
        <result column="intent_name" property="intentName" jdbcType="VARCHAR"/>
        <result column="parent_intent_name" property="parentIntentName" jdbcType="VARCHAR"/>
        <result column="srv_send_cd" property="srvSendCd" jdbcType="BIGINT"/>
        <result column="qa_main_id" property="qaMainId" jdbcType="BIGINT"/>
        <result column="task_purpose_tag" property="taskPurposeTag" jdbcType="INTEGER"/>
        <result column="judge_type" property="judgeType" jdbcType="INTEGER"/>
        <result column="is_show_resolve" property="isShowResolve" jdbcType="TINYINT"/>
        <result column="is_show_correct" property="isShowCorrect" jdbcType="TINYINT"/>
        <result column="freq_aues_cnt" property="freqAuesCnt" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 根据聊天室ID和员工ID查询员工信息（验证权限） -->
    <select id="selectStaffByReceChatroomId" resultMap="StaffResultMap">
        SELECT train_staff.*
        FROM train_reception_chatroom
        JOIN train_chatroom_staff ON train_reception_chatroom.id = train_chatroom_staff.rece_chatroom_id
        JOIN train_staff ON train_chatroom_staff.staff_id = train_staff.id
        WHERE train_reception_chatroom.id = #{receChatRoomId}
          AND train_staff.id = #{staffId}
    </select>

    <!-- 根据聊天室ID查询剧本任务列表 -->
    <select id="selectChatRoomTaskList" resultMap="ChatRoomTaskInfoResultMap">
        SELECT
            ts.id as script_id,
            ts.name as script_name,
            trt.id as task_id,
            trt.task_name as task_name,
            tct.training_recycle_cnt,
            ts.buyer_requirement,
            ts.generation_type as generation_type_code,
            ts.intent_id,
            ti.name as intent_name,
            tip.name as parent_intent_name,
            trt.srv_send_cd,
            trt.qa_main_id,
            trt.task_purpose_tag,
            trt.judge_type,
            trt.is_show_resolve,
            trt.is_show_correct,
            trt.freq_aues_cnt
        FROM train_script ts
        LEFT JOIN train_reception_task trt ON trt.script_id = ts.id
        LEFT JOIN train_chatroom_task tct ON trt.id = tct.task_id
        LEFT JOIN train_reception_chatroom trc ON trc.id = tct.chatroom_id
        LEFT JOIN train_intent ti ON ts.intent_id = ti.id
        LEFT JOIN train_intent tip ON ti.parent_id = tip.id
        WHERE trc.id = #{receChatRoomId}
    </select>

    <!-- 根据聊天室ID查询问答任务列表（专用于问答场景） -->
    <select id="selectCrTL4Qa" resultMap="ChatRoomTaskInfoResultMap">
        SELECT
            trt.id as task_id,
            trt.task_name as task_name,
            tct.training_recycle_cnt,
            trt.srv_send_cd,
            trt.qa_main_id,
            trt.task_purpose_tag,
            trt.judge_type,
            trt.is_show_resolve,
            trt.is_show_correct,
            trt.freq_aues_cnt
        FROM train_reception_chatroom trc
        LEFT JOIN train_chatroom_task tct ON trc.id = tct.chatroom_id
        LEFT JOIN train_reception_task trt ON tct.task_id = trt.id
        WHERE trc.id = #{receChatRoomId}
    </select>

    <!-- 任务可用性检查结果映射 -->
    <resultMap id="TaskAvailableCheckResultMap" type="com.yiyi.ai_train_playground.dto.task.TaskAvailableCheckDTO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_name" property="taskName" jdbcType="VARCHAR"/>
        <result column="amt_has_learned" property="amtHasLearned" jdbcType="BIGINT"/>
        <result column="learning_status" property="learningStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 检查聊天室中的任务是否仍在学习中 -->
    <select id="checkTasksLearningStatus" resultMap="TaskAvailableCheckResultMap">
        SELECT 
            trt.id,
            trt.task_name,
            trt.amt_has_learned,
            trt.learning_status
        FROM train_chatroom_task tct 
        JOIN train_reception_task trt ON tct.task_id = trt.id
        WHERE tct.chatroom_id = #{chatroomId,jdbcType=BIGINT}
          AND tct.team_id = #{teamId,jdbcType=BIGINT}
          AND trt.team_id = #{teamId,jdbcType=BIGINT}
          AND (trt.learning_status = 'un_learn' 
               OR (trt.learning_status = 'learning' AND trt.amt_has_learned = 0))
    </select>

</mapper>