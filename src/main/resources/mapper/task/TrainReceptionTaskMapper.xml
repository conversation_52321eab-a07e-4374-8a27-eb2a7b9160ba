<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.task.TrainReceptionTaskMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.task.TrainReceptionTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_mode" property="taskMode" jdbcType="INTEGER"/>
        <result column="task_type" property="taskType" jdbcType="INTEGER"/>
        <result column="task_name" property="taskName" jdbcType="VARCHAR"/>
        <result column="task_description" property="taskDescription" jdbcType="LONGVARCHAR"/>
        <result column="script_id" property="scriptId" jdbcType="BIGINT"/>
        <result column="reception_duration" property="receptionDuration" jdbcType="INTEGER"/>
        <result column="question_interval_type" property="questionIntervalType" jdbcType="INTEGER"/>
        <result column="question_interval_seconds" property="questionIntervalSeconds" jdbcType="INTEGER"/>
        <result column="training_limit_enabled" property="trainingLimitEnabled" jdbcType="BOOLEAN"/>
        <result column="task_purpose_tag" property="taskPurposeTag" jdbcType="INTEGER"/>
        <result column="judge_type" property="judgeType" jdbcType="INTEGER"/>
        <result column="conv_kb_id" property="convKbId" jdbcType="BIGINT"/>
        <result column="qa_main_id" property="qaMainId" jdbcType="BIGINT"/>
        <result column="learning_status" property="learningStatus" jdbcType="VARCHAR"/>
        <result column="amt_to_be_learned" property="amtToBeLearned" jdbcType="BIGINT"/>
        <result column="amt_has_learned" property="amtHasLearned" jdbcType="BIGINT"/>
        <result column="is_show_resolve" property="isShowResolve" jdbcType="TINYINT"/>
        <result column="is_show_correct" property="isShowCorrect" jdbcType="TINYINT"/>
        <result column="freq_aues_cnt" property="freqAuesCnt" jdbcType="INTEGER"/>
        <result column="is_show_inspect" property="isShowInspect" jdbcType="TINYINT"/>
        <result column="srv_send_cd" property="srvSendCd" jdbcType="BIGINT"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, task_mode, task_type, task_name, task_description, script_id, reception_duration,
        question_interval_type, question_interval_seconds, training_limit_enabled, task_purpose_tag,
        judge_type, conv_kb_id, qa_main_id, learning_status, amt_to_be_learned, amt_has_learned, 
        is_show_resolve, is_show_correct, freq_aues_cnt, is_show_inspect, srv_send_cd, team_id,
        create_time, update_time, creator, updater, version
    </sql>

    <!-- 查询条件 -->
    <sql id="Where_Clause">
        <where>
            trt.team_id = #{teamId}
            <if test="creator != null and creator != ''">
                AND trt.creator = #{creator}
            </if>
            <if test="request.taskMode != null">
                AND trt.task_mode = #{request.taskMode}
            </if>
            <if test="request.taskType != null">
                AND trt.task_type = #{request.taskType}
            </if>
            <if test="request.taskName != null and request.taskName != ''">
                AND trt.task_name LIKE CONCAT('%', #{request.taskName}, '%')
            </if>
            <if test="request.scriptId != null">
                AND trt.script_id = #{request.scriptId}
            </if>
            <if test="request.taskPurposeTag != null">
                AND trt.task_purpose_tag = #{request.taskPurposeTag}
            </if>
            <if test="request.judgeType != null">
                AND trt.judge_type = #{request.judgeType}
            </if>
        </where>
    </sql>

    <!-- 分页查询任务列表 -->
    <select id="selectTaskList" resultType="com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskListDTO">
        SELECT
            trt.id,
            trt.task_mode AS taskMode,
            CASE trt.task_mode
                WHEN 0 THEN '原版案例'
                WHEN 1 THEN 'AI智训'
                WHEN 2 THEN 'AI智训玩法'
                ELSE '未知'
            END AS taskModeName,
            trt.task_type AS taskType,
            CASE trt.task_type
                WHEN 0 THEN '商品知识训练'
                WHEN 1 THEN '实战进阶任务'
                WHEN 2 THEN '高频话术训练'
                ELSE '未知'
            END AS taskTypeName,
            trt.task_name AS taskName,
            trt.task_description AS taskDescription,
            trt.script_id AS scriptId,
            ts.name AS scriptName,
            trt.reception_duration AS receptionDuration,
            trt.task_purpose_tag AS taskPurposeTag,
            CASE trt.task_purpose_tag
                WHEN 0 THEN '训练'
                WHEN 1 THEN '考核'
                WHEN 2 THEN '面试'
                WHEN 3 THEN '其他'
                ELSE '未知'
            END AS taskPurposeTagName,
            trt.judge_type AS judgeType,
            CASE trt.judge_type
                WHEN 0 THEN '单条会话打分'
                WHEN 1 THEN '会话结束打分'
                WHEN 2 THEN '单条、结束都要打'
                ELSE '未知'
            END AS judgeTypeName,
            trt.conv_kb_id AS convKbId,
            tkm.name AS convKbIdValue,
            trt.qa_main_id AS qaMainId,
            tqim.qa_im_name AS qaMainName,
            trt.learning_status AS learningStatus,
            trt.learning_status AS learningStatusCode,
            CASE trt.learning_status
                WHEN 'file_uploading' THEN '文件上传中'
                WHEN 'un_learn' THEN '未学习'
                WHEN 'learning' THEN '学习中'
                WHEN 'learned' THEN '已学习'
                ELSE '未知状态'
            END AS learningStatusValue,
            CASE
                WHEN trt.amt_to_be_learned = 0 THEN '0%'
                ELSE CONCAT(ROUND(((SELECT COUNT(*) FROM train_task_conv_kb_dtl WHERE task_id = trt.id AND learning_status = 'learned') * 100.0 / trt.amt_to_be_learned), 0), '%')
            END AS learningProgressPercent,
            trt.amt_to_be_learned AS amtToBeLearned,
            trt.amt_has_learned AS amtHasLearned,
            trt.is_show_resolve AS isShowResolve,
            trt.is_show_correct AS isShowCorrect,
            trt.freq_aues_cnt AS freqAuesCnt,
            trt.is_show_inspect AS isShowInspect,
            trt.srv_send_cd AS srvSendCd,
            trt.create_time AS createTime,
            trt.update_time AS updateTime,
            trt.creator,
            trt.updater
        FROM train_reception_task trt
        LEFT JOIN train_script ts ON trt.script_id = ts.id
        LEFT JOIN train_kb_tpl_main tkm ON trt.conv_kb_id = tkm.id
        LEFT JOIN train_qa_import_main tqim ON trt.qa_main_id = tqim.id
        <include refid="Where_Clause"/>
        ORDER BY trt.update_time DESC, trt.id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询任务总数 -->
    <select id="countTasks" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM train_reception_task trt
        LEFT JOIN train_script ts ON trt.script_id = ts.id
        LEFT JOIN train_kb_tpl_main tkm ON trt.conv_kb_id = tkm.id
        LEFT JOIN train_qa_import_main tqim ON trt.qa_main_id = tqim.id
        <include refid="Where_Clause"/>
    </select>

    <!-- 根据ID查询任务 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM train_reception_task
        WHERE id = #{id} AND team_id = #{teamId}
    </select>

    <!-- 根据ID查询任务详情 -->
    <select id="selectDetailById" resultType="com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskDetailDTO">
        SELECT
            trt.id,
            trt.task_mode AS taskMode,
            CASE trt.task_mode
                WHEN 0 THEN '原版案例'
                WHEN 1 THEN 'AI智训'
                WHEN 2 THEN 'AI智训玩法'
                ELSE '未知'
            END AS taskModeName,
            trt.task_type AS taskType,
            CASE trt.task_type
                WHEN 0 THEN '商品知识训练'
                WHEN 1 THEN '实战进阶任务'
                WHEN 2 THEN '高频话术训练'
                ELSE '未知'
            END AS taskTypeName,
            trt.task_name AS taskName,
            trt.task_description AS taskDescription,
            trt.script_id AS scriptId,
            ts.name AS scriptName,
            trt.reception_duration AS receptionDuration,
            trt.question_interval_type AS questionIntervalType,
            CASE trt.question_interval_type
                WHEN 0 THEN '随机'
                WHEN 1 THEN '固定'
                ELSE '未知'
            END AS questionIntervalTypeName,
            trt.question_interval_seconds AS questionIntervalSeconds,
            trt.training_limit_enabled AS trainingLimitEnabled,
            trt.task_purpose_tag AS taskPurposeTag,
            CASE trt.task_purpose_tag
                WHEN 0 THEN '训练'
                WHEN 1 THEN '考核'
                WHEN 2 THEN '面试'
                WHEN 3 THEN '其他'
                ELSE '未知'
            END AS taskPurposeTagName,
            trt.judge_type AS judgeType,
            CASE trt.judge_type
                WHEN 0 THEN '单条会话打分'
                WHEN 1 THEN '会话结束打分'
                WHEN 2 THEN '单条、结束都要打'
                ELSE '未知'
            END AS judgeTypeName,
            trt.conv_kb_id AS convKbId,
            trt.qa_main_id AS qaMainId,
            tqim.qa_im_name AS qaMainName,
            trt.learning_status AS learningStatus,
            trt.learning_status AS learningStatusCode,
            CASE trt.learning_status
                WHEN 'file_uploading' THEN '文件上传中'
                WHEN 'un_learn' THEN '未学习'
                WHEN 'learning' THEN '学习中'
                WHEN 'learned' THEN '已学习'
                ELSE '未知状态'
            END AS learningStatusValue,
            CASE
                WHEN trt.amt_to_be_learned = 0 THEN '0%'
                ELSE CONCAT(ROUND(((SELECT COUNT(*) FROM train_task_conv_kb_dtl WHERE task_id = trt.id AND learning_status = 'learned') * 100.0 / trt.amt_to_be_learned), 0), '%')
            END AS learningProgressPercent,
            trt.amt_to_be_learned AS amtToBeLearned,
            trt.amt_has_learned AS amtHasLearned,
            trt.is_show_resolve AS isShowResolve,
            trt.is_show_correct AS isShowCorrect,
            trt.freq_aues_cnt AS freqAuesCnt,
            trt.is_show_inspect AS isShowInspect,
            trt.srv_send_cd AS srvSendCd,
            trt.team_id AS teamId,
            trt.create_time AS createTime,
            trt.update_time AS updateTime,
            trt.creator,
            trt.updater,
            trt.version
        FROM train_reception_task trt
        LEFT JOIN train_script ts ON trt.script_id = ts.id
        LEFT JOIN train_qa_import_main tqim ON trt.qa_main_id = tqim.id
        WHERE trt.id = #{id} AND trt.team_id = #{teamId}
    </select>

    <!-- 插入任务 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.task.TrainReceptionTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_reception_task (
            task_mode, task_type, task_name, task_description, script_id, reception_duration,
            question_interval_type, question_interval_seconds, training_limit_enabled, task_purpose_tag,
            judge_type, conv_kb_id, qa_main_id, learning_status, amt_to_be_learned, amt_has_learned, 
            is_show_resolve, is_show_correct, freq_aues_cnt, is_show_inspect, srv_send_cd,
            team_id, creator, updater, create_time, update_time, version
        ) VALUES (
            #{taskMode}, #{taskType}, #{taskName}, #{taskDescription}, #{scriptId}, #{receptionDuration},
            #{questionIntervalType}, #{questionIntervalSeconds}, #{trainingLimitEnabled}, #{taskPurposeTag},
            #{judgeType}, #{convKbId}, #{qaMainId}, COALESCE(#{learningStatus}, 'learning'),
            COALESCE(#{amtToBeLearned}, 0), COALESCE(#{amtHasLearned}, 0),
            COALESCE(#{isShowResolve}, 0), COALESCE(#{isShowCorrect}, 0), COALESCE(#{freqAuesCnt}, 0), COALESCE(#{isShowInspect}, 0), COALESCE(#{srvSendCd}, 30),
            #{teamId}, #{creator}, #{updater}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新任务 -->
    <update id="update" parameterType="com.yiyi.ai_train_playground.entity.task.TrainReceptionTask">
        UPDATE train_reception_task
        <set>
            <if test="taskMode != null">task_mode = #{taskMode},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="taskDescription != null">task_description = #{taskDescription},</if>
            <if test="scriptId != null">script_id = #{scriptId},</if>
            <if test="receptionDuration != null">reception_duration = #{receptionDuration},</if>
            <if test="questionIntervalType != null">question_interval_type = #{questionIntervalType},</if>
            <if test="questionIntervalSeconds != null">question_interval_seconds = #{questionIntervalSeconds},</if>
            <if test="trainingLimitEnabled != null">training_limit_enabled = #{trainingLimitEnabled},</if>
            <if test="taskPurposeTag != null">task_purpose_tag = #{taskPurposeTag},</if>
            <if test="judgeType != null">judge_type = #{judgeType},</if>
            <if test="convKbId != null">conv_kb_id = #{convKbId},</if>
            <if test="qaMainId != null">qa_main_id = #{qaMainId},</if>
            <if test="learningStatus != null and learningStatus != ''">learning_status = #{learningStatus},</if>
            <if test="amtToBeLearned != null">amt_to_be_learned = #{amtToBeLearned},</if>
            <if test="amtHasLearned != null">amt_has_learned = #{amtHasLearned},</if>
            <if test="isShowResolve != null">is_show_resolve = #{isShowResolve},</if>
            <if test="isShowCorrect != null">is_show_correct = #{isShowCorrect},</if>
            <if test="freqAuesCnt != null">freq_aues_cnt = #{freqAuesCnt},</if>
            <if test="isShowInspect != null">is_show_inspect = #{isShowInspect},</if>
            <if test="srvSendCd != null">srv_send_cd = #{srvSendCd},</if>
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} AND team_id = #{teamId} AND version = #{version}
    </update>

    <!-- 删除任务 -->
    <delete id="deleteById">
        DELETE FROM train_reception_task
        WHERE id = #{id} AND team_id = #{teamId}
    </delete>

    <!-- 批量删除任务 -->
    <delete id="batchDeleteByIds">
        DELETE FROM train_reception_task
        WHERE team_id = #{teamId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新已学习数量（使用乐观锁） -->
    <update id="updateAmtHasLearned">
        UPDATE train_reception_task
        SET amt_has_learned = #{amtHasLearned},
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        WHERE id = #{id} AND version = #{version}
    </update>

    <!-- 更新学习状态 -->
    <update id="updateLearningStatus">
        UPDATE train_reception_task
        SET learning_status = #{learningStatus},
            updater = #{updater},
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 更新已学习数量（使用悲观锁） -->
    <update id="updateAmtHasLearnedWithPessimisticLock">
        UPDATE train_reception_task
        SET amt_has_learned = COALESCE(amt_has_learned, 0) + 1,
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        WHERE id = #{id}
    </update>

    <!-- 批量更新已学习数量（使用悲观锁） -->
    <update id="updateAmtHasLearnedBatchWithPessimisticLock">
        UPDATE train_reception_task
        SET amt_has_learned = COALESCE(amt_has_learned, 0) + #{incrementCount},
            updater = #{updater},
            update_time = NOW(),
            version = version + 1
        WHERE id = (
            SELECT id FROM (
                SELECT id FROM train_reception_task
                WHERE id = #{id}
                FOR UPDATE
            ) AS locked_task
        )
    </update>

    <!-- 查询任务的学习统计信息 -->
    <select id="selectLearningInfo" resultMap="BaseResultMap">
        SELECT id, amt_has_learned, amt_to_be_learned, version, team_id
        FROM train_reception_task
        WHERE id = #{id}
    </select>

    <!-- 使用悲观锁查询任务的学习统计信息 -->
    <select id="selectLearningInfoWithLock" resultMap="BaseResultMap">
        SELECT id, amt_has_learned, amt_to_be_learned, version, team_id
        FROM train_reception_task
        WHERE id = #{id}
        FOR UPDATE
    </select>

</mapper>
