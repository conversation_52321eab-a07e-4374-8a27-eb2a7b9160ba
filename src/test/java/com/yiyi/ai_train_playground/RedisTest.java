package com.yiyi.ai_train_playground;

import com.yiyi.ai_train_playground.service.impl.RedisService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis连接和功能测试
 * 使用application.yml中的Redis配置
 */
@SpringBootTest
public class RedisTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedisService redisService;

    private static final String TEST_KEY_PREFIX = "test:";

    @Test
    public void testRedisConnection() {
        // 测试基本连接
        try {
            stringRedisTemplate.opsForValue().set(TEST_KEY_PREFIX + "connection", "success");
            String result = stringRedisTemplate.opsForValue().get(TEST_KEY_PREFIX + "connection");
            assertEquals("success", result);
            stringRedisTemplate.delete(TEST_KEY_PREFIX + "connection");
            System.out.println("✅ Redis连接测试成功");
        } catch (Exception e) {
            fail("Redis连接失败: " + e.getMessage());
        }
    }

    @Test
    public void testRedisSave(){

        //将一个最简单的二B卵存进redis
        String simpleWord="二B卵";
        redisTemplate.opsForValue().set(simpleWord, "二B卵");

        //也不知道有没有保存成功，再去取下看看
        String result = (String) redisTemplate.opsForValue().get(simpleWord);
        System.out.println("取出来的结果是:"+result);
    }


    /**
     * 错误的用法，这个不是hash的用法
     */
    @Test
    public void testRedisSaveWithoutHash() {

        HashMap<String,String> myMap=new HashMap<>();
        myMap.put("name","zhangsan");
        myMap.put("age","18");
        myMap.put("address","shanghai");

        //存进redis
        redisTemplate.opsForHash().put("session:1234","myMap",myMap);

        //取出来,通过get方式
        Map<String,String> myMap1 = (HashMap<String,String>)redisTemplate.opsForHash().get("session:1234", "myMap");
        System.out.println("按照get的方式取出来的完整的结果:"+myMap1);

        System.out.println("其中age是"+myMap1.get("age"));

        System.out.println("----------------------");

        System.out.println("按照entries的方式取出来");
        Map<Object,Object> myMap2 = redisTemplate.opsForHash().entries("session:1234");
        System.out.println("按照entries的方式取出来的结果:"+myMap2);
    }

    @Test
    public void testRedisHash() {
        //通过hash的方式存进去
        redisTemplate.opsForHash().put("session:1234","name","zhangsan");
        redisTemplate.opsForHash().put("session:1234","age","18");
        redisTemplate.opsForHash().put("session:1234","address","shanghai");

        //一次性全部取出来
        System.out.println("按照entries的方式取出来");
        Map<Object,Object> myMap2 = redisTemplate.opsForHash().entries("session:1234");
        System.out.println("按照entries的方式取出来的结果:"+myMap2);
    }


    @Test
    public void testRedisServiceBasic() {
        // 测试RedisService基本功能
        try {
            // 测试连接
            redisService.testConnection();
            
            // 测试字符串操作
            String key = TEST_KEY_PREFIX + "service:test";
            String value = "Hello RedisService!";
            redisService.setString(key, value);
            String result = redisService.getString(key);
            assertEquals(value, result);
            
            // 测试连接池信息
            Map<String, Object> poolInfo = redisService.getConnectionPoolInfo();
            assertNotNull(poolInfo);
            assertTrue(poolInfo.containsKey("host"));
            assertTrue(poolInfo.containsKey("port"));
            
            // 清理
            redisService.delete(key);
            
            System.out.println("✅ RedisService基本功能测试成功");
            System.out.println("连接池信息: " + poolInfo);
        } catch (Exception e) {
            fail("RedisService基本功能测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testStringOperations() {
        String key = TEST_KEY_PREFIX + "string:test";
        String value = "Hello Redis!";
        
        // 增 - 设置值
        stringRedisTemplate.opsForValue().set(key, value);
        
        // 查 - 获取值
        String result = stringRedisTemplate.opsForValue().get(key);
        assertEquals(value, result);
        
        // 改 - 修改值
        String newValue = "Hello Updated Redis!";
        stringRedisTemplate.opsForValue().set(key, newValue);
        String updatedResult = stringRedisTemplate.opsForValue().get(key);
        assertEquals(newValue, updatedResult);
        
        // 设置过期时间
        stringRedisTemplate.expire(key, 10, TimeUnit.SECONDS);
        Long ttl = stringRedisTemplate.getExpire(key);
        assertTrue(ttl > 0 && ttl <= 10);
        
        // 删 - 删除值
        Boolean deleted = stringRedisTemplate.delete(key);
        assertTrue(deleted);
        String deletedResult = stringRedisTemplate.opsForValue().get(key);
        assertNull(deletedResult);
        
        System.out.println("✅ String操作测试成功");
    }

    @Test
    public void testHashOperations() {
        String key = TEST_KEY_PREFIX + "hash:test";
        String hashKey1 = "field1";
        String hashKey2 = "field2";
        String value1 = "value1";
        String value2 = "value2";
        
        // 增 - 设置Hash值
        stringRedisTemplate.opsForHash().put(key, hashKey1, value1);
        stringRedisTemplate.opsForHash().put(key, hashKey2, value2);
        
        // 查 - 获取Hash值
        Object result1 = stringRedisTemplate.opsForHash().get(key, hashKey1);
        Object result2 = stringRedisTemplate.opsForHash().get(key, hashKey2);
        assertEquals(value1, result1);
        assertEquals(value2, result2);
        
        // 获取所有Hash字段
        Map<Object, Object> allFields = stringRedisTemplate.opsForHash().entries(key);
        assertEquals(2, allFields.size());
        
        // 改 - 修改Hash值
        String newValue1 = "updatedValue1";
        stringRedisTemplate.opsForHash().put(key, hashKey1, newValue1);
        Object updatedResult = stringRedisTemplate.opsForHash().get(key, hashKey1);
        assertEquals(newValue1, updatedResult);
        
        // 删 - 删除Hash字段
        Long deletedCount = stringRedisTemplate.opsForHash().delete(key, hashKey1);
        assertEquals(1L, deletedCount);
        
        // 删除整个Hash
        Boolean deleted = stringRedisTemplate.delete(key);
        assertTrue(deleted);
        
        System.out.println("✅ Hash操作测试成功");
    }

    @Test
    public void testListOperations() {
        String key = TEST_KEY_PREFIX + "list:test";
        String value1 = "item1";
        String value2 = "item2";
        String value3 = "item3";
        
        // 增 - 左侧添加元素
        stringRedisTemplate.opsForList().leftPush(key, value1);
        stringRedisTemplate.opsForList().leftPush(key, value2);
        stringRedisTemplate.opsForList().rightPush(key, value3);
        
        // 查 - 获取列表长度和元素
        Long size = stringRedisTemplate.opsForList().size(key);
        assertEquals(3L, size);
        
        List<String> allItems = stringRedisTemplate.opsForList().range(key, 0, -1);
        assertEquals(3, allItems.size());
        assertTrue(allItems.contains(value1));
        assertTrue(allItems.contains(value2));
        assertTrue(allItems.contains(value3));
        
        // 改 - 修改指定位置的元素
        stringRedisTemplate.opsForList().set(key, 0, "updatedItem");
        String updatedItem = stringRedisTemplate.opsForList().index(key, 0);
        assertEquals("updatedItem", updatedItem);
        
        // 删 - 弹出元素
        String leftPop = stringRedisTemplate.opsForList().leftPop(key);
        assertNotNull(leftPop);
        
        // 删除整个列表
        Boolean deleted = stringRedisTemplate.delete(key);
        assertTrue(deleted);
        
        System.out.println("✅ List操作测试成功");
    }

    @Test
    public void testSetOperations() {
        String key = TEST_KEY_PREFIX + "set:test";
        String value1 = "member1";
        String value2 = "member2";
        String value3 = "member3";
        
        // 增 - 添加Set成员
        stringRedisTemplate.opsForSet().add(key, value1, value2, value3);
        
        // 查 - 获取Set大小和成员
        Long size = stringRedisTemplate.opsForSet().size(key);
        assertEquals(3L, size);
        
        Set<String> members = stringRedisTemplate.opsForSet().members(key);
        assertEquals(3, members.size());
        assertTrue(members.contains(value1));
        assertTrue(members.contains(value2));
        assertTrue(members.contains(value3));
        
        // 检查成员是否存在
        Boolean isMember = stringRedisTemplate.opsForSet().isMember(key, value1);
        assertTrue(isMember);
        
        // 删 - 删除Set成员
        Long removedCount = stringRedisTemplate.opsForSet().remove(key, value1);
        assertEquals(1L, removedCount);
        
        // 删除整个Set
        Boolean deleted = stringRedisTemplate.delete(key);
        assertTrue(deleted);
        
        System.out.println("✅ Set操作测试成功");
    }

    @Test
    public void testObjectSerialization() {
        String key = TEST_KEY_PREFIX + "object:test";
        
        // 创建测试对象
        Map<String, Object> testObject = new HashMap<>();
        testObject.put("name", "张三");
        testObject.put("age", 25);
        testObject.put("city", "北京");
        
        // 增 - 存储对象
        redisTemplate.opsForValue().set(key, testObject);
        
        // 查 - 获取对象
        Object result = redisTemplate.opsForValue().get(key);
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        assertEquals("张三", resultMap.get("name"));
        assertEquals(25, resultMap.get("age"));
        assertEquals("北京", resultMap.get("city"));
        
        // 改 - 修改对象
        testObject.put("age", 26);
        redisTemplate.opsForValue().set(key, testObject);
        
        Object updatedResult = redisTemplate.opsForValue().get(key);
        @SuppressWarnings("unchecked")
        Map<String, Object> updatedMap = (Map<String, Object>) updatedResult;
        assertEquals(26, updatedMap.get("age"));
        
        // 删 - 删除对象
        Boolean deleted = redisTemplate.delete(key);
        assertTrue(deleted);
        
        System.out.println("✅ 对象序列化测试成功");
    }

    @Test
    public void testExpiration() {
        String key = TEST_KEY_PREFIX + "expiration:test";
        String value = "expiring value";
        
        // 设置带过期时间的值
        stringRedisTemplate.opsForValue().set(key, value, 5, TimeUnit.SECONDS);
        
        // 检查值是否存在
        String result = stringRedisTemplate.opsForValue().get(key);
        assertEquals(value, result);
        
        // 检查TTL
        Long ttl = stringRedisTemplate.getExpire(key);
        assertTrue(ttl > 0 && ttl <= 5);
        
        // 等待一段时间后再次检查TTL
        try {
            Thread.sleep(2000); // 等待2秒
            Long remainingTtl = stringRedisTemplate.getExpire(key);
            assertTrue(remainingTtl >= 0 && remainingTtl <= 3);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("✅ 过期时间测试成功");
        
        // 清理：删除可能还存在的key
        stringRedisTemplate.delete(key);
    }

    @Test
    public void testBatchOperations() {
        String keyPattern = TEST_KEY_PREFIX + "batch:";
        
        // 批量设置
        for (int i = 1; i <= 5; i++) {
            stringRedisTemplate.opsForValue().set(keyPattern + i, "value" + i);
        }
        
        // 批量获取keys
        Set<String> keys = stringRedisTemplate.keys(keyPattern + "*");
        assertEquals(5, keys.size());
        
        // 批量删除
        Long deletedCount = stringRedisTemplate.delete(keys);
        assertEquals(5L, deletedCount);
        
        // 验证删除结果
        Set<String> remainingKeys = stringRedisTemplate.keys(keyPattern + "*");
        assertEquals(0, remainingKeys.size());
        
        System.out.println("✅ 批量操作测试成功");
    }

    @Test
    public void testRedisPoolConfiguration() {
        // 测试连接池配置是否生效
        try {
            // 并发测试，验证连接池
            for (int i = 0; i < 10; i++) {
                String key = TEST_KEY_PREFIX + "pool:test:" + i;
                stringRedisTemplate.opsForValue().set(key, "value" + i);
                String result = stringRedisTemplate.opsForValue().get(key);
                assertEquals("value" + i, result);
                stringRedisTemplate.delete(key);
            }
            System.out.println("✅ Redis连接池配置测试成功");
        } catch (Exception e) {
            fail("Redis连接池测试失败: " + e.getMessage());
        }
    }
} 