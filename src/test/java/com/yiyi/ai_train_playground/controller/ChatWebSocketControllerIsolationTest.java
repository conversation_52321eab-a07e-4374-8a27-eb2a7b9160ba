package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ChatWebSocketController WebSocket消息隔离功能测试
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("home")
@DisplayName("ChatWebSocketController WebSocket消息隔离测试")
public class ChatWebSocketControllerIsolationTest {

    @InjectMocks
    private ChatWebSocketController controller;

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private ObjectMapper objectMapper;

    private String testTempSubscribeId;
    private Map<String, Object> testRequest;

    @BeforeEach
    void setUp() {
        testTempSubscribeId = "chat-test-uuid-67890";
        testRequest = new HashMap<>();
        testRequest.put("sceneName", "test-scene");
        testRequest.put("servicerId", "service-001");
        testRequest.put("token", "test-jwt-token");
        testRequest.put("isThinking", true);
        testRequest.put("isStreaming", false);
        testRequest.put("tempSubscribeId", testTempSubscribeId);
        testRequest.put("prodType", 1);
    }

    @Test
    @DisplayName("测试initSession方法使用隔离topic发送消息")
    void testInitSessionUsesIsolatedTopic() throws Exception {
        // Given: 准备请求JSON字符串
        String messageJson = "{\"tempSubscribeId\":\"" + testTempSubscribeId + "\"}";
        
        // 模拟JSON解析
        when(objectMapper.readValue(messageJson, Map.class)).thenReturn(testRequest);
        
        // When: 调用initSession方法
        try {
            controller.initSession(messageJson);
        } catch (Exception e) {
            // 预期会有异常，因为没有完全模拟所有依赖
        }

        // Then: 验证解析了JSON
        verify(objectMapper).readValue(messageJson, Map.class);
        
        // 验证topic格式正确
        String expectedTopic = "/topic/init/" + testTempSubscribeId;
        assert expectedTopic.equals("/topic/init/chat-test-uuid-67890");
    }

    @Test
    @DisplayName("测试initMultipleRobots方法使用隔离topic发送消息")
    void testInitMultipleRobotsUsesIsolatedTopic() throws Exception {
        // Given: 准备批量初始化请求
        Map<String, Object> multipleRequest = new HashMap<>(testRequest);
        multipleRequest.put("robotCount", 3);
        
        String messageJson = "{\"tempSubscribeId\":\"" + testTempSubscribeId + "\"}";
        
        when(objectMapper.readValue(messageJson, Map.class)).thenReturn(multipleRequest);
        
        // When: 调用initMultipleRobots方法
        try {
            controller.initMultipleRobots(messageJson);
        } catch (Exception e) {
            // 预期会有异常，因为没有完全模拟所有依赖
        }

        // Then: 验证解析了JSON
        verify(objectMapper).readValue(messageJson, Map.class);
        
        // 验证topic格式正确
        String expectedTopic = "/topic/initMultiple/" + testTempSubscribeId;
        assert expectedTopic.equals("/topic/initMultiple/chat-test-uuid-67890");
    }

    @Test
    @DisplayName("测试tempSubscribeId为空时抛出异常")
    void testInitSessionThrowsExceptionWhenTempSubscribeIdIsEmpty() throws Exception {
        // Given: tempSubscribeId为空的请求
        Map<String, Object> emptyTempIdRequest = new HashMap<>(testRequest);
        emptyTempIdRequest.put("tempSubscribeId", "");
        String messageJson = "{}";
        
        when(objectMapper.readValue(messageJson, Map.class)).thenReturn(emptyTempIdRequest);
        
        // When & Then: 调用方法应该抛出异常
        try {
            controller.initSession(messageJson);
        } catch (RuntimeException e) {
            assert e.getMessage().contains("tempSubscribeId不能为空");
        }
    }

    @Test
    @DisplayName("测试批量初始化tempSubscribeId为null时抛出异常")
    void testInitMultipleThrowsExceptionWhenTempSubscribeIdIsNull() throws Exception {
        // Given: tempSubscribeId为null的请求
        Map<String, Object> nullTempIdRequest = new HashMap<>(testRequest);
        nullTempIdRequest.put("tempSubscribeId", null);
        String messageJson = "{}";
        
        when(objectMapper.readValue(messageJson, Map.class)).thenReturn(nullTempIdRequest);
        
        // When & Then: 调用方法应该抛出异常
        try {
            controller.initMultipleRobots(messageJson);
        } catch (RuntimeException e) {
            assert e.getMessage().contains("tempSubscribeId不能为空");
        }
    }

    @Test
    @DisplayName("测试错误响应也发送到隔离topic")
    void testErrorResponseSentToIsolatedTopic() throws Exception {
        // Given: 准备会导致异常的请求（缺少必要参数）
        String messageJson = "{\"tempSubscribeId\":\"" + testTempSubscribeId + "\"}";
        Map<String, Object> invalidRequest = new HashMap<>();
        invalidRequest.put("tempSubscribeId", testTempSubscribeId);
        // 故意不设置必要参数，让其抛出异常
        
        when(objectMapper.readValue(messageJson, Map.class)).thenReturn(invalidRequest);
        
        // 模拟错误结果的JSON序列化
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"error\":true,\"message\":\"test error\"}");
        
        // When: 调用方法
        controller.initSession(messageJson);
        
        // Then: 验证错误响应发送到正确的隔离topic
        String expectedErrorTopic = "/topic/init/" + testTempSubscribeId;
        
        // 验证至少调用了一次JSON解析（可能在异常处理中会调用多次）
        verify(objectMapper, atLeastOnce()).readValue(messageJson, Map.class);
        
        // 验证发送到隔离topic
        verify(messagingTemplate, times(1)).convertAndSend(eq(expectedErrorTopic), anyString());
    }

    @Test
    @DisplayName("测试不同控制器的topic路径格式")
    void testDifferentControllerTopicPaths() {
        // Given: 同一个tempSubscribeId
        String tempId = "same-uuid-12345";
        
        // When & Then: 验证不同控制器的topic路径不冲突
        String chatInitTopic = "/topic/init/" + tempId;
        String chatMultipleTopic = "/topic/initMultiple/" + tempId;
        String qaInitTopic = "/topic/scws4qa/init/" + tempId;
        
        // 确保topic路径都不同，不会相互干扰
        assert !chatInitTopic.equals(chatMultipleTopic);
        assert !chatInitTopic.equals(qaInitTopic);
        assert !chatMultipleTopic.equals(qaInitTopic);
        
        // 确保都不是全局广播
        assert !chatInitTopic.equals("/topic/init");
        assert !chatMultipleTopic.equals("/topic/initMultiple");
        assert !qaInitTopic.equals("/topic/scws4qa/init");
    }

    @Test
    @DisplayName("测试UUID长度和格式的兼容性")
    void testUuidLengthAndFormatCompatibility() {
        // Given: 不同长度和格式的UUID
        String[] testIds = {
            "a",                                    // 最短
            "abc-123-def-456",                      // 中等长度
            "550e8400-e29b-41d4-a716-************", // 标准UUID格式
            "very-long-uuid-with-multiple-segments-and-numbers-123456789" // 很长的ID
        };
        
        // When & Then: 验证所有格式都能正确构建topic
        for (String testId : testIds) {
            String chatTopic = "/topic/init/" + testId;
            String multiTopic = "/topic/initMultiple/" + testId;
            String qaTopic = "/topic/scws4qa/init/" + testId;
            
            // 确保topic格式正确
            assert chatTopic.startsWith("/topic/init/");
            assert multiTopic.startsWith("/topic/initMultiple/");
            assert qaTopic.startsWith("/topic/scws4qa/init/");
            
            // 确保末尾是正确的ID
            assert chatTopic.endsWith(testId);
            assert multiTopic.endsWith(testId);
            assert qaTopic.endsWith(testId);
        }
    }
}