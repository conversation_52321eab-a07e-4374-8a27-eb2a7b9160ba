package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.ProductListDTO;
import com.yiyi.ai_train_playground.dto.FlowNodeDTO;
import com.yiyi.ai_train_playground.dto.RelatedImageDTO;
import com.yiyi.ai_train_playground.service.impl.BigModelManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
        "my.doubao.normal.endpoint.name=ep-20250629195408-gtv9c"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ChatWebSocketControllerTest {

    @Autowired
    private ChatWebSocketController chatWebSocketController;

    @MockBean
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private BigModelManager bigModelManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String TEST_SESSION_ID = "test-websocket-session-" + System.currentTimeMillis();

    @Test
    @Order(1)
    @DisplayName("测试closeSession方法 - 正常关闭")
    public void testCloseSession_Success() {
        System.out.println("=== 测试1: closeSession方法 - 正常关闭 ===");

        try {
            // 准备测试数据
            String testSessionId = "test-close-" + System.currentTimeMillis();
            
            // 先在Redis中创建一些测试数据
            String chatLogKey = "chatlog:" + testSessionId;
            String sessionKey = "session:" + testSessionId;
            
            redisTemplate.opsForValue().set(chatLogKey, "test chat data");
            redisTemplate.opsForHash().put(sessionKey, "testKey", "testValue");
            
            // 验证数据存在
            Assertions.assertTrue(redisTemplate.hasKey(chatLogKey), "测试数据应该存在");
            Assertions.assertTrue(redisTemplate.hasKey(sessionKey), "测试数据应该存在");

            // 构建请求消息
            Map<String, Object> request = new HashMap<>();
            request.put("sessionId", testSessionId);
            String requestJson = objectMapper.writeValueAsString(request);

            // 调用closeSession方法
            chatWebSocketController.closeSession(requestJson);

            // 验证Redis数据已被清除
            Assertions.assertFalse(redisTemplate.hasKey(chatLogKey), "聊天记录应该被删除");
            Assertions.assertFalse(redisTemplate.hasKey(sessionKey), "会话信息应该被删除");

            // 验证WebSocket响应被发送
            ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
            ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
            
            verify(messagingTemplate, times(1)).convertAndSend(topicCaptor.capture(), messageCaptor.capture());
            
            String expectedTopic = "/topic/close/" + testSessionId;
            Assertions.assertEquals(expectedTopic, topicCaptor.getValue(), "WebSocket主题应该正确");
            
            // 验证响应消息内容
            String responseMessage = messageCaptor.getValue();
            Map<String, Object> responseMap = objectMapper.readValue(responseMessage, Map.class);
            
            Assertions.assertEquals(true, responseMap.get("success"), "响应应该表示成功");
            Assertions.assertEquals("会话已关闭", responseMap.get("message"), "响应消息应该正确");
            Assertions.assertEquals(testSessionId, responseMap.get("sessionId"), "会话ID应该正确");

            System.out.println("✅ closeSession正常关闭测试通过");
            System.out.println("测试会话ID: " + testSessionId);
            System.out.println("WebSocket主题: " + expectedTopic);
            System.out.println("响应消息: " + responseMessage);

        } catch (Exception e) {
            log.error("closeSession正常关闭测试失败", e);
            throw new RuntimeException(e);
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试closeSession方法 - 空sessionId")
    public void testCloseSession_EmptySessionId() {
        System.out.println("=== 测试2: closeSession方法 - 空sessionId ===");

        try {
            // 构建空sessionId的请求
            Map<String, Object> request = new HashMap<>();
            request.put("sessionId", "");
            String requestJson = objectMapper.writeValueAsString(request);

            // 调用closeSession方法
            chatWebSocketController.closeSession(requestJson);

            // 验证没有发送WebSocket消息（因为sessionId为空）
            verify(messagingTemplate, never()).convertAndSend(any(String.class), any(String.class));

            System.out.println("✅ closeSession空sessionId测试通过");
            System.out.println("空sessionId被正确处理，没有发送WebSocket消息");

        } catch (Exception e) {
            log.error("closeSession空sessionId测试失败", e);
            throw new RuntimeException(e);
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试closeSession方法 - 无效JSON")
    public void testCloseSession_InvalidJson() {
        System.out.println("=== 测试3: closeSession方法 - 无效JSON ===");

        try {
            // 使用无效的JSON字符串
            String invalidJson = "{ invalid json }";

            // 调用closeSession方法
            chatWebSocketController.closeSession(invalidJson);

            // 验证没有发送WebSocket消息（因为JSON解析失败，无法获取sessionId）
            verify(messagingTemplate, never()).convertAndSend(any(String.class), any(String.class));

            System.out.println("✅ closeSession无效JSON测试通过");
            System.out.println("无效JSON被正确处理，没有发送WebSocket消息（因为无法解析sessionId）");

        } catch (Exception e) {
            log.error("closeSession无效JSON测试失败", e);
            throw new RuntimeException(e);
        }
    }

    @BeforeEach
    public void setUp() {
        // 重置Mock对象
        reset(messagingTemplate);
    }

    @Test
    @Order(3)
    @DisplayName("测试buildSystemPromptFromRequest重载方法 - ScriptDetailDTO版本")
    public void testBuildSystemPromptFromRequest_ScriptDetailDTO() {
        try {
            // 创建测试用的ScriptDetailDTO
            ScriptDetailDTO scriptDetail = new ScriptDetailDTO();
            scriptDetail.setBuyerRequirement("我是一个年轻的妈妈，想为宝宝购买安全健康的奶粉");
            scriptDetail.setParentIntentName("商品咨询");
            scriptDetail.setIntentName("奶粉选择");

            // 创建商品列表
            List<ProductListDTO> productList = new ArrayList<>();
            ProductListDTO product = new ProductListDTO();
            product.setExternalProductName("优质婴儿奶粉");
            productList.add(product);
            scriptDetail.setProductList(productList);

            // 创建流程节点
            List<FlowNodeDTO> flowNodes = new ArrayList<>();
            FlowNodeDTO node1 = new FlowNodeDTO();
            node1.setNodeName("询问宝宝年龄");
            node1.setNodeBuyerRequirement("了解宝宝的具体月龄");
            flowNodes.add(node1);

            FlowNodeDTO node2 = new FlowNodeDTO();
            node2.setNodeName("推荐合适奶粉");
            node2.setNodeBuyerRequirement("根据年龄推荐适合的奶粉段数");
            flowNodes.add(node2);
            scriptDetail.setFlowNodes(flowNodes);

            // 创建关联图片
            List<RelatedImageDTO> relateImgs = new ArrayList<>();
            RelatedImageDTO img = new RelatedImageDTO();
            img.setRecognizedText("奶粉营养成分表");
            img.setUrl("http://example.com/image1.jpg");
            relateImgs.add(img);
            scriptDetail.setRelateImgs(relateImgs);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = ChatWebSocketController.class.getDeclaredMethod(
                "buildSystemPromptFromRequest", ScriptDetailDTO.class);
            method.setAccessible(true);

            String result = (String) method.invoke(chatWebSocketController, scriptDetail);

            // 验证结果
            Assertions.assertNotNull(result, "系统提示词不应为空");
            Assertions.assertTrue(result.contains("年轻的妈妈"), "应包含买家需求信息");
            Assertions.assertTrue(result.contains("优质婴儿奶粉"), "应包含商品名称");
            Assertions.assertTrue(result.contains("商品咨询"), "应包含父意图名称");
            Assertions.assertTrue(result.contains("奶粉选择"), "应包含意图名称");

            log.info("ScriptDetailDTO版本的buildSystemPromptFromRequest测试通过");
            log.info("生成的系统提示词: {}", result);

        } catch (Exception e) {
            log.error("测试失败", e);
            Assertions.fail("测试执行失败: " + e.getMessage());
        }
    }

    @AfterEach
    public void tearDown() {
        // 清理测试数据
        try {
            String chatLogKey = "chatlog:" + TEST_SESSION_ID;
            String sessionKey = "session:" + TEST_SESSION_ID;
            redisTemplate.delete(chatLogKey);
            redisTemplate.delete(sessionKey);
        } catch (Exception e) {
            log.warn("清理测试数据失败", e);
        }
    }
}
