package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.controller.jd.JdProductController;
import com.yiyi.ai_train_playground.dto.jd.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListResponse;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 京东商品控制器测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@WebMvcTest(JdProductController.class)
@DisplayName("京东商品控制器测试")
public class JdProductControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private TrainJdProductsService trainJdProductsService;

    @Test
    @DisplayName("测试分页查询京东商品列表 - 成功")
    public void testGetJdProductList_Success() throws Exception {
        // 准备测试数据
        JdPrdListResponse mockResponse = createMockResponse();
        when(trainJdProductsService.findJdProductList(eq(1L), any())).thenReturn(mockResponse);

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-list")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(2))
                .andExpect(jsonPath("$.data.rows").isArray())
                .andExpect(jsonPath("$.data.rows[0].id").value(1))
                .andExpect(jsonPath("$.data.rows[0].brandName").value("小米"))
                .andExpect(jsonPath("$.data.rows[0].title").value("数据分析初级版试用"));

        log.info("✅ 分页查询京东商品列表成功测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 带标题模糊查询")
    public void testGetJdProductList_WithTitle() throws Exception {
        // 准备测试数据
        JdPrdListResponse mockResponse = createMockResponseWithTitle();
        when(trainJdProductsService.findJdProductList(eq(1L), any())).thenReturn(mockResponse);

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-list")
                        .param("jdPrdTitle", "小米手环")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.rows").isArray())
                .andExpect(jsonPath("$.data.rows[0].title").value("小米手环7 NFC版"));

        log.info("✅ 带标题模糊查询测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 空结果")
    public void testGetJdProductList_EmptyResult() throws Exception {
        // 准备测试数据
        JdPrdListResponse mockResponse = createEmptyResponse();
        when(trainJdProductsService.findJdProductList(eq(1L), any())).thenReturn(mockResponse);

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-list")
                        .param("jdPrdTitle", "不存在的商品")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.rows").isArray())
                .andExpect(jsonPath("$.data.rows").isEmpty());

        log.info("✅ 空结果测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 默认参数")
    public void testGetJdProductList_DefaultParams() throws Exception {
        // 准备测试数据
        JdPrdListResponse mockResponse = createMockResponse();
        when(trainJdProductsService.findJdProductList(eq(1L), any())).thenReturn(mockResponse);

        // 执行请求（不传分页参数，使用默认值）
        mockMvc.perform(get("/api/jd-prd-list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(2));

        log.info("✅ 默认参数测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 服务异常")
    public void testGetJdProductList_ServiceException() throws Exception {
        // 模拟服务层异常
        when(trainJdProductsService.findJdProductList(eq(1L), any()))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-list")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("查询京东商品列表失败: 数据库连接失败"));

        log.info("✅ 服务异常测试通过");
    }

    /**
     * 创建模拟响应数据
     */
    private JdPrdListResponse createMockResponse() {
        JdPrdListResponse response = new JdPrdListResponse();
        response.setTotal(2L);

        JdPrdListResponse.JdProductItem item1 = new JdPrdListResponse.JdProductItem();
        item1.setId(1L);
        item1.setBrandId(1L);
        item1.setWareId(12345L);
        item1.setBrandName("小米");
        item1.setLogo("https://img11.360buyimg.com/devfe/jfs/t1/137620/25/33301/62910/63ef1f7eFeba5d9e1/19c5a79dee1137be.jpg");
        item1.setTitle("数据分析初级版试用");
        item1.setStatus("上架");
        item1.setOnlineTime(LocalDateTime.of(2019, 1, 1, 1, 1, 1));
        item1.setOffLineTime(LocalDateTime.of(2019, 12, 31, 23, 59, 59));

        JdPrdListResponse.JdProductItem item2 = new JdPrdListResponse.JdProductItem();
        item2.setId(2L);
        item2.setBrandId(2L);
        item2.setWareId(67890L);
        item2.setBrandName("华为");
        item2.setLogo("https://img11.360buyimg.com/devfe/jfs/t1/example.jpg");
        item2.setTitle("智能手表Pro版");
        item2.setStatus("自主下架");
        item2.setOnlineTime(LocalDateTime.of(2019, 6, 1, 10, 0, 0));
        item2.setOffLineTime(LocalDateTime.of(2019, 12, 1, 18, 0, 0));

        response.setRows(Arrays.asList(item1, item2));
        return response;
    }

    /**
     * 创建带标题查询的模拟响应数据
     */
    private JdPrdListResponse createMockResponseWithTitle() {
        JdPrdListResponse response = new JdPrdListResponse();
        response.setTotal(1L);

        JdPrdListResponse.JdProductItem item = new JdPrdListResponse.JdProductItem();
        item.setId(3L);
        item.setBrandId(1L);
        item.setWareId(11111L);
        item.setBrandName("小米");
        item.setLogo("https://img11.360buyimg.com/devfe/jfs/t1/xiaomi.jpg");
        item.setTitle("小米手环7 NFC版");
        item.setStatus("上架");
        item.setOnlineTime(LocalDateTime.of(2022, 5, 1, 9, 0, 0));
        item.setOffLineTime(null);

        response.setRows(Collections.singletonList(item));
        return response;
    }

    /**
     * 创建空响应数据
     */
    private JdPrdListResponse createEmptyResponse() {
        JdPrdListResponse response = new JdPrdListResponse();
        response.setTotal(0L);
        response.setRows(Collections.emptyList());
        return response;
    }

    @Test
    @DisplayName("测试查询京东商品详情 - 成功")
    public void testGetJdProductDetail_Success() throws Exception {
        // 准备测试数据
        Long wareId = 12345L;
        JdPrdDtlResponse mockResponse = createMockDetailResponse();
        when(trainJdProductsService.findJdProductDetail(eq(1L), eq(wareId))).thenReturn(mockResponse);

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-dtl/{wareId}", wareId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.wareId").value(12345))
                .andExpect(jsonPath("$.data.jdProdDtl").value("小米手环7详细介绍..."));

        log.info("✅ 查询京东商品详情成功测试通过");
    }

    @Test
    @DisplayName("测试查询京东商品详情 - 商品不存在")
    public void testGetJdProductDetail_NotFound() throws Exception {
        // 准备测试数据
        Long wareId = 99999L;
        when(trainJdProductsService.findJdProductDetail(eq(1L), eq(wareId))).thenReturn(null);

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-dtl/{wareId}", wareId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("未找到指定的商品详情"));

        log.info("✅ 商品不存在测试通过");
    }

    @Test
    @DisplayName("测试查询京东商品详情 - 服务异常")
    public void testGetJdProductDetail_ServiceException() throws Exception {
        // 准备测试数据
        Long wareId = 12345L;
        when(trainJdProductsService.findJdProductDetail(eq(1L), eq(wareId)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求
        mockMvc.perform(get("/api/jd-prd-dtl/{wareId}", wareId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("查询京东商品详情失败: 数据库连接失败"));

        log.info("✅ 服务异常测试通过");
    }

    /**
     * 创建模拟商品详情响应
     */
    private JdPrdDtlResponse createMockDetailResponse() {
        JdPrdDtlResponse response = new JdPrdDtlResponse();
        response.setId(1L);
        response.setWareId(12345L);
        response.setJdProdDtl("小米手环7详细介绍...");
        response.setJdProdImgList("https://img11.360buyimg.com/devfe/img1.jpg,https://img11.360buyimg.com/devfe/img2.jpg");
        return response;
    }
}
