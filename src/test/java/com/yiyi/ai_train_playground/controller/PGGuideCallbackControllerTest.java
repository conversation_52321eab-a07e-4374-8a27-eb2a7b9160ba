package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.service.staff.TrainStaffService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * PG Guide对接控制器测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class PGGuideCallbackControllerTest {
    
    @Autowired
    private TrainStaffService trainStaffService;
    
    @Autowired
    private PGGuideCallbackController pgGuideCallbackController;
    
    private static final Long TEST_USER_ID = 9999L;
    private static final Long TEST_TEAM_ID = 1001L;
    private static final String TEST_USERNAME = "pg_guide_test_user";
    private static final String TEST_DISPLAY_NAME = "PG Guide测试用户";
    
    @Test
    @Order(1)
    @DisplayName("测试健康检查接口")
    void testHealthCheck() {
        System.out.println("=== 测试健康检查接口 ===");
        
        var result = pgGuideCallbackController.healthCheck();
        
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(1);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().get("status")).isEqualTo("UP");
        assertThat(result.getData().get("aiGuideBaseUrl")).isNotNull();
        
        System.out.println("健康检查状态: " + result.getData().get("status"));
        System.out.println("AI Guide Base URL: " + result.getData().get("aiGuideBaseUrl"));
        System.out.println("✅ 健康检查测试通过");
    }
    
    @Test
    @Order(2)
    @DisplayName("测试已存在员工的认证")
    @Transactional
    void testAuthenticateExistingUser() {
        System.out.println("=== 测试已存在员工的认证 ===");

        // 先创建一个员工
        TrainStaff staff = createTestStaff();
        staff.setUserId(TEST_USER_ID);
        int createResult = trainStaffService.createStaff(staff);
        assertThat(createResult).isEqualTo(1);

        // 测试认证
        var result = pgGuideCallbackController.authenticateUser(TEST_USER_ID);

        assertThat(result).isNotNull();
        assertThat(result.getViewName()).isNotNull();
        assertThat(result.getViewName()).startsWith("redirect:");

        // 验证跳转URL包含必要的参数
        String redirectUrl = result.getViewName().substring("redirect:".length());
        assertThat(redirectUrl).contains("success=true");
        assertThat(redirectUrl).contains("staffId=" + staff.getId());
        assertThat(redirectUrl).contains("teamId=" + TEST_TEAM_ID);
        assertThat(redirectUrl).contains("username=" + TEST_USERNAME);
        assertThat(redirectUrl).contains("token=");

        System.out.println("跳转URL: " + redirectUrl);
        System.out.println("员工ID: " + staff.getId());
        System.out.println("团队ID: " + TEST_TEAM_ID);
        System.out.println("✅ 已存在员工认证测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试不存在员工的认证（模拟ai_guide接口不可用）")
    @Transactional
    void testAuthenticateNonExistingUser() {
        System.out.println("=== 测试不存在员工的认证 ===");

        // 使用一个不存在的userId
        Long nonExistingUserId = 8888L;

        // 由于ai_guide接口在测试环境中不可用，这个测试会失败
        // 但我们可以验证逻辑是否正确执行
        var result = pgGuideCallbackController.authenticateUser(nonExistingUserId);

        assertThat(result).isNotNull();
        assertThat(result.getViewName()).isNotNull();
        assertThat(result.getViewName()).startsWith("redirect:");

        // 验证跳转到错误页面
        String redirectUrl = result.getViewName().substring("redirect:".length());
        assertThat(redirectUrl).contains("success=false");
        assertThat(redirectUrl).contains("error=");

        System.out.println("错误跳转URL: " + redirectUrl);
        System.out.println("✅ 不存在员工认证测试完成（预期失败，因为ai_guide接口不可用）");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试参数验证")
    @Transactional
    void testParameterValidation() {
        System.out.println("=== 测试参数验证 ===");

        // 测试null userId（这会在路径参数解析时失败，所以我们测试一个无效的userId）
        Long invalidUserId = -1L;

        var result = pgGuideCallbackController.authenticateUser(invalidUserId);

        assertThat(result).isNotNull();
        assertThat(result.getViewName()).isNotNull();
        assertThat(result.getViewName()).startsWith("redirect:");

        // 验证跳转到错误页面
        String redirectUrl = result.getViewName().substring("redirect:".length());
        assertThat(redirectUrl).contains("success=false");

        System.out.println("无效userId错误跳转URL: " + redirectUrl);
        System.out.println("✅ 参数验证测试通过");
    }
    
    @Test
    @Order(5)
    @DisplayName("测试JWT生成功能")
    @Transactional
    void testJwtGeneration() {
        System.out.println("=== 测试JWT生成功能 ===");

        // 创建员工
        TrainStaff staff = createTestStaff();
        staff.setUserId(7777L);
        int createResult = trainStaffService.createStaff(staff);
        assertThat(createResult).isEqualTo(1);

        // 测试认证和JWT生成
        var result = pgGuideCallbackController.authenticateUser(7777L);

        assertThat(result).isNotNull();
        assertThat(result.getViewName()).isNotNull();
        assertThat(result.getViewName()).startsWith("redirect:");

        // 验证跳转URL包含token参数
        String redirectUrl = result.getViewName().substring("redirect:".length());
        assertThat(redirectUrl).contains("success=true");
        assertThat(redirectUrl).contains("token=");
        assertThat(redirectUrl).contains("staffId=" + staff.getId());

        System.out.println("包含JWT的跳转URL: " + redirectUrl);
        System.out.println("✅ JWT生成功能测试通过");
    }

    @Test
    @Order(6)
    @DisplayName("测试重复认证")
    @Transactional
    void testRepeatedAuthentication() {
        System.out.println("=== 测试重复认证 ===");

        // 创建员工
        TrainStaff staff = createTestStaff();
        staff.setUserId(6666L);
        int createResult = trainStaffService.createStaff(staff);
        assertThat(createResult).isEqualTo(1);

        // 第一次认证
        var result1 = pgGuideCallbackController.authenticateUser(6666L);
        assertThat(result1.getViewName()).startsWith("redirect:");

        // 第二次认证
        var result2 = pgGuideCallbackController.authenticateUser(6666L);
        assertThat(result2.getViewName()).startsWith("redirect:");

        // 两次认证都应该成功跳转
        String redirectUrl1 = result1.getViewName().substring("redirect:".length());
        String redirectUrl2 = result2.getViewName().substring("redirect:".length());

        assertThat(redirectUrl1).contains("success=true");
        assertThat(redirectUrl2).contains("success=true");
        assertThat(redirectUrl1).contains("staffId=" + staff.getId());
        assertThat(redirectUrl2).contains("staffId=" + staff.getId());

        System.out.println("第一次认证跳转URL: " + redirectUrl1);
        System.out.println("第二次认证跳转URL: " + redirectUrl2);
        System.out.println("✅ 重复认证测试通过");
    }
    
    /**
     * 创建测试用的员工对象
     */
    private TrainStaff createTestStaff() {
        TrainStaff staff = new TrainStaff();
        staff.setUsername(TEST_USERNAME);
        staff.setPasswordHash("test123456");
        staff.setDisplayName(TEST_DISPLAY_NAME);
        staff.setTeamId(TEST_TEAM_ID);
        staff.setCreator("pg_guide_test");
        staff.setUpdater("pg_guide_test");
        return staff;
    }
}
