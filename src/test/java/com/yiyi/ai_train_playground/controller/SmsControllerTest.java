package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.service.SmsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 短信验证码控制器测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@WebMvcTest(SmsController.class)
class SmsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SmsService smsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testSendCode_Success() throws Exception {
        // Given
        String phone = "13800138000";
        SmsCodeResponse response = new SmsCodeResponse("test-key");
        
        when(smsService.sendVerificationCode(phone)).thenReturn(response);

        // When & Then
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"phone\":\"" + phone + "\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("发送成功"))
                .andExpect(jsonPath("$.data.verificationKey").value("test-key"));

        verify(smsService).sendVerificationCode(phone);
    }

    @Test
    void testSendCode_MissingPhoneParameter() throws Exception {
        // Given - 请求体中没有phone参数
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationKey", "f6e1d78b378643d6bafa847fd7d43071");
        requestBody.put("verificationCode", "9025");

        // When & Then
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号不能为空"));

        verify(smsService, never()).sendVerificationCode(anyString());
    }

    @Test
    void testSendCode_EmptyPhone() throws Exception {
        // Given - phone参数为空字符串
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"phone\":\"\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号不能为空"));

        verify(smsService, never()).sendVerificationCode(anyString());
    }

    @Test
    void testSendCode_NullPhone() throws Exception {
        // Given - phone参数为null
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"phone\":null}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号不能为空"));

        verify(smsService, never()).sendVerificationCode(anyString());
    }

    @Test
    void testSendCode_InvalidPhoneFormat() throws Exception {
        // Given - 手机号格式错误
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"phone\":\"12345678901\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号格式不正确，请输入正确的中国大陆手机号"));

        verify(smsService, never()).sendVerificationCode(anyString());
    }

    @Test
    void testSendCode_PhoneWithSpaces() throws Exception {
        // Given - 手机号包含空格
        String phone = "13800138000";
        SmsCodeResponse response = new SmsCodeResponse("test-key");
        
        when(smsService.sendVerificationCode(phone)).thenReturn(response);

        // When & Then
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"phone\":\"  " + phone + "  \"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("发送成功"))
                .andExpect(jsonPath("$.data.verificationKey").value("test-key"));

        verify(smsService).sendVerificationCode(phone);
    }

    @Test
    void testSendCode_EmptyRequestBody() throws Exception {
        // Given - 空请求体
        mockMvc.perform(post("/api/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号不能为空"));

        verify(smsService, never()).sendVerificationCode(anyString());
    }

    @Test
    void testVerifyAndLogin_Success() throws Exception {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        
        SmsLoginResponse response = new SmsLoginResponse();
        response.setUserId(1L);
        response.setUsername("test_user");
        response.setDisplayName("测试用户");
        response.setMobile("13800138000");
        response.setToken("test-token");
        
        when(smsService.smsLogin(verificationKey, verificationCode, null)).thenReturn(response);

        // When & Then
        mockMvc.perform(post("/api/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"verificationKey\":\"" + verificationKey + "\",\"verificationCode\":\"" + verificationCode + "\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("登录成功"))
                .andExpect(jsonPath("$.data.userId").value(1))
                .andExpect(jsonPath("$.data.username").value("test_user"))
                .andExpect(jsonPath("$.data.token").value("test-token"));

        verify(smsService).smsLogin(verificationKey, verificationCode, null);
    }

    @Test
    void testVerifyAndLogin_MissingVerificationKey() throws Exception {
        // Given - 缺少verificationKey参数
        mockMvc.perform(post("/api/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"verificationCode\":\"1234\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证密钥不能为空"));

        verify(smsService, never()).smsLogin(anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyAndLogin_MissingVerificationCode() throws Exception {
        // Given - 缺少verificationCode参数
        mockMvc.perform(post("/api/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"verificationKey\":\"test-key\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证码不能为空"));

        verify(smsService, never()).smsLogin(anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyAndLogin_InvalidVerificationCodeFormat() throws Exception {
        // Given - 验证码格式错误
        mockMvc.perform(post("/api/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"verificationKey\":\"test-key\",\"verificationCode\":\"123\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证码必须是4位数字"));

        verify(smsService, never()).smsLogin(anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyAndLogin_VerificationCodeWithSpaces() throws Exception {
        // Given - 验证码包含空格
        String verificationKey = "test-key";
        String verificationCode = "1234";
        
        SmsLoginResponse response = new SmsLoginResponse();
        response.setUserId(1L);
        response.setUsername("test_user");
        response.setToken("test-token");
        
        when(smsService.smsLogin(verificationKey, verificationCode, null)).thenReturn(response);

        // When & Then
        mockMvc.perform(post("/api/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"verificationKey\":\"  " + verificationKey + "  \",\"verificationCode\":\"  " + verificationCode + "  \"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("登录成功"));

        verify(smsService).smsLogin(verificationKey, verificationCode, null);
    }
} 