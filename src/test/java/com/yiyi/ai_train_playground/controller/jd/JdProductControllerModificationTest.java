package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListResponse;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * JdProductController修改后的测试类
 * 测试新的搜索功能：支持在一个输入框中同时搜索商品标题、SKU ID、品牌名称
 *
 * <AUTHOR> Assistant
 * @since 2025-01-30
 */
@SpringBootTest
@ActiveProfiles("test")
public class JdProductControllerModificationTest {

    @Autowired
    private JdProductController jdProductController;

    @MockBean
    private TrainJdProductsService trainJdProductsService;

    @Test
    @DisplayName("测试新的搜索功能 - 使用searchKeyword参数")
    public void testGetJdProductListWithSearchKeyword() {
        // 准备测试数据
        Long testTeamId = 1L;
        
        JdPrdListResponse mockResponse = new JdPrdListResponse();
        mockResponse.setTotal(1L);
        mockResponse.setRows(Collections.emptyList());
        
        // 模拟服务层方法
        when(trainJdProductsService.findJdProductList(eq(testTeamId), any(JdPrdListRequest.class)))
                .thenReturn(mockResponse);

        // 使用MockedStatic模拟SecurityUtil
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);

            // 构建请求参数 - 测试搜索关键词
            JdPrdListRequest request = new JdPrdListRequest();
            request.setSearchKeyword("小米手环");
            request.setPage(1);
            request.setPageSize(10);

            // 执行测试
            Result<JdPrdListResponse> result = jdProductController.getJdProductList(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getCode()); // code=1表示成功
            
            JdPrdListResponse response = result.getData();
            assertNotNull(response);
            assertEquals(1L, response.getTotal());
        }
    }

    @Test
    @DisplayName("测试空搜索关键词")
    public void testGetJdProductListWithEmptySearchKeyword() {
        // 准备测试数据
        Long testTeamId = 1L;
        
        JdPrdListResponse mockResponse = new JdPrdListResponse();
        mockResponse.setTotal(0L);
        mockResponse.setRows(Collections.emptyList());
        
        // 模拟服务层方法
        when(trainJdProductsService.findJdProductList(eq(testTeamId), any(JdPrdListRequest.class)))
                .thenReturn(mockResponse);

        // 使用MockedStatic模拟SecurityUtil
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);

            // 构建请求参数 - 测试空搜索关键词
            JdPrdListRequest request = new JdPrdListRequest();
            request.setSearchKeyword("");
            request.setPage(1);
            request.setPageSize(10);

            // 执行测试
            Result<JdPrdListResponse> result = jdProductController.getJdProductList(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getCode()); // code=1表示成功
            
            JdPrdListResponse response = result.getData();
            assertNotNull(response);
            assertEquals(0L, response.getTotal());
        }
    }

    @Test
    @DisplayName("测试null搜索关键词")
    public void testGetJdProductListWithNullSearchKeyword() {
        // 准备测试数据
        Long testTeamId = 1L;
        
        JdPrdListResponse mockResponse = new JdPrdListResponse();
        mockResponse.setTotal(0L);
        mockResponse.setRows(Collections.emptyList());
        
        // 模拟服务层方法
        when(trainJdProductsService.findJdProductList(eq(testTeamId), any(JdPrdListRequest.class)))
                .thenReturn(mockResponse);

        // 使用MockedStatic模拟SecurityUtil
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);

            // 构建请求参数 - 测试null搜索关键词
            JdPrdListRequest request = new JdPrdListRequest();
            request.setSearchKeyword(null);
            request.setPage(1);
            request.setPageSize(10);

            // 执行测试
            Result<JdPrdListResponse> result = jdProductController.getJdProductList(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getCode()); // code=1表示成功
            
            JdPrdListResponse response = result.getData();
            assertNotNull(response);
            assertEquals(0L, response.getTotal());
        }
    }
}
