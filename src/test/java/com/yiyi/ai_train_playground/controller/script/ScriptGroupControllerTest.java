package com.yiyi.ai_train_playground.controller.script;

import com.yiyi.ai_train_playground.entity.ScriptGroup;
import com.yiyi.ai_train_playground.service.ScriptGroupService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import com.yiyi.ai_train_playground.common.Result;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.impl.DefaultClaims;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.DuplicateKeyException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ScriptGroupController测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
public class ScriptGroupControllerTest {

    @Mock
    private ScriptGroupService scriptGroupService;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private ScriptGroupController scriptGroupController;

    private static final String TEST_TOKEN = "Bearer test_token";
    private static final Long TEST_TEAM_ID = 1L;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Mock JWT解析
        Claims claims = new DefaultClaims();
        claims.put("teamId", TEST_TEAM_ID);
        when(jwtUtil.parseToken("test_token")).thenReturn(claims);
    }

    /**
     * 测试正常创建分组
     */
    @Test
    void testSaveSuccess() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setGroupTitle("测试分组");
        
        // Mock服务层返回成功
        when(scriptGroupService.save(any(ScriptGroup.class))).thenReturn(true);
        
        // 执行测试
        Result<Void> result = scriptGroupController.save(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(1), result.getCode());
        assertEquals(TEST_TEAM_ID, scriptGroup.getTeamId());
    }

    /**
     * 测试分组名称重复的情况
     */
    @Test
    void testSaveDuplicateGroupTitle() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setGroupTitle("重复分组名");
        
        // Mock服务层抛出重复键异常
        when(scriptGroupService.save(any(ScriptGroup.class)))
            .thenThrow(new DuplicateKeyException("Duplicate entry '重复分组名' for key 'train_script_group.uniq_team_group_title'"));
        
        // 执行测试
        Result<Void> result = scriptGroupController.save(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("该团队下分组名称已存在，请使用其他名称", result.getMessage());
    }

    /**
     * 测试兼容旧的约束名称
     */
    @Test
    void testSaveDuplicateGroupTitleOldConstraint() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setGroupTitle("重复分组名");
        
        // Mock服务层抛出重复键异常（旧约束名）
        when(scriptGroupService.save(any(ScriptGroup.class)))
            .thenThrow(new DuplicateKeyException("Duplicate entry '重复分组名' for key 'train_script_group.uniq_group_title'"));
        
        // 执行测试
        Result<Void> result = scriptGroupController.save(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("该团队下分组名称已存在，请使用其他名称", result.getMessage());
    }

    /**
     * 测试其他异常情况
     */
    @Test
    void testSaveOtherException() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setGroupTitle("测试分组");
        
        // Mock服务层抛出其他异常
        when(scriptGroupService.save(any(ScriptGroup.class)))
            .thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行测试
        Result<Void> result = scriptGroupController.save(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(500), result.getCode());
        assertTrue(result.getMessage().contains("添加失败"));
        assertTrue(result.getMessage().contains("数据库连接失败"));
    }

    /**
     * 测试正常更新分组
     */
    @Test
    void testUpdateSuccess() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setId(1L);
        scriptGroup.setTeamId(TEST_TEAM_ID);
        scriptGroup.setGroupTitle("更新分组");
        
        // Mock服务层返回成功
        when(scriptGroupService.update(any(ScriptGroup.class))).thenReturn(true);
        
        // 执行测试
        Result<Void> result = scriptGroupController.update(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(1), result.getCode());
    }

    /**
     * 测试更新时分组名称重复
     */
    @Test
    void testUpdateDuplicateGroupTitle() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setId(1L);
        scriptGroup.setTeamId(TEST_TEAM_ID);
        scriptGroup.setGroupTitle("重复分组名");
        
        // Mock服务层抛出重复键异常
        when(scriptGroupService.update(any(ScriptGroup.class)))
            .thenThrow(new DuplicateKeyException("Duplicate entry '重复分组名' for key 'train_script_group.uniq_team_group_title'"));
        
        // 执行测试
        Result<Void> result = scriptGroupController.update(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("该团队下分组名称已存在，请使用其他名称", result.getMessage());
    }

    /**
     * 测试无权限修改其他团队分组
     */
    @Test
    void testUpdateNoPermission() {
        // 准备测试数据
        ScriptGroup scriptGroup = new ScriptGroup();
        scriptGroup.setId(1L);
        scriptGroup.setTeamId(2L); // 不同的团队ID
        scriptGroup.setGroupTitle("其他团队分组");
        
        // 执行测试
        Result<Void> result = scriptGroupController.update(scriptGroup, TEST_TOKEN);
        
        // 验证结果
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("无权修改其他团队的分组", result.getMessage());
        
        // 验证没有调用服务层
        verify(scriptGroupService, never()).update(any(ScriptGroup.class));
    }
}
