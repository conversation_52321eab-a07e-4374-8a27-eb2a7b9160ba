package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;
import com.yiyi.ai_train_playground.service.task.TrainQaRdmService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ScWS4QaAnoController单元测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("home")
public class ScWS4QaAnoControllerTest {

    @Mock
    private TrainQaRdmService trainQaRdmService;

    @InjectMocks
    private ScWS4QaAnoController scWS4QaAnoController;

    /**
     * 测试savDtlIdToDb方法 - 正常情况
     */
    @Test
    public void testSavDtlIdToDb_Success() {
        // 准备测试数据
        String testUuid = "test-uuid-12345";
        Long qaReportDtlId = 100L;
        Long teamId = 1L;

        // 创建QaSimplelDtoWithUUID对象
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = new QaSimplelDtoWithUUID();
        qaSimplelDtoWithUUID.setUuid(testUuid);
        qaSimplelDtoWithUUID.setQuestion("测试问题");
        qaSimplelDtoWithUUID.setAnswer("测试答案");

        // 创建TrainQaRdm对象
        TrainQaRdm trainQaRdm = new TrainQaRdm();
        trainQaRdm.setId(50L);
        trainQaRdm.setUuid(testUuid);
        trainQaRdm.setTeamId(teamId);

        // Mock方法调用
        when(trainQaRdmService.getByUUID(testUuid, teamId)).thenReturn(trainQaRdm);
        when(trainQaRdmService.updateByUUID(any(TrainQaRdm.class))).thenReturn(true);

        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ScWS4QaAnoController.class.getDeclaredMethod(
                    "savDtlIdToDb", QaSimplelDtoWithUUID.class, Long.class, Long.class);
            method.setAccessible(true);
            method.invoke(scWS4QaAnoController, qaSimplelDtoWithUUID, qaReportDtlId, teamId);
        } catch (Exception e) {
            throw new RuntimeException("调用savDtlIdToDb方法失败", e);
        }

        // 验证方法调用
        verify(trainQaRdmService, times(1)).getByUUID(testUuid, teamId);
        verify(trainQaRdmService, times(1)).updateByUUID(argThat(qaRdm -> 
            qaRdm.getUuid().equals(testUuid) && qaRdm.getReportDtlId().equals(qaReportDtlId)
        ));
    }

    /**
     * 测试savDtlIdToDb方法 - qaSimplelDtoWithUUID为null
     */
    @Test
    public void testSavDtlIdToDb_NullQaSimplelDto() {
        Long qaReportDtlId = 100L;
        Long teamId = 1L;

        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ScWS4QaAnoController.class.getDeclaredMethod(
                    "savDtlIdToDb", QaSimplelDtoWithUUID.class, Long.class, Long.class);
            method.setAccessible(true);
            method.invoke(scWS4QaAnoController, null, qaReportDtlId, teamId);
        } catch (Exception e) {
            throw new RuntimeException("调用savDtlIdToDb方法失败", e);
        }

        // 验证不会调用任何service方法
        verify(trainQaRdmService, never()).getByUUID(anyString(), anyLong());
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class));
    }

    /**
     * 测试savDtlIdToDb方法 - uuid为null
     */
    @Test
    public void testSavDtlIdToDb_NullUuid() {
        Long qaReportDtlId = 100L;
        Long teamId = 1L;

        // 创建QaSimplelDtoWithUUID对象，但uuid为null
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = new QaSimplelDtoWithUUID();
        qaSimplelDtoWithUUID.setUuid(null);
        qaSimplelDtoWithUUID.setQuestion("测试问题");

        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ScWS4QaAnoController.class.getDeclaredMethod(
                    "savDtlIdToDb", QaSimplelDtoWithUUID.class, Long.class, Long.class);
            method.setAccessible(true);
            method.invoke(scWS4QaAnoController, qaSimplelDtoWithUUID, qaReportDtlId, teamId);
        } catch (Exception e) {
            throw new RuntimeException("调用savDtlIdToDb方法失败", e);
        }

        // 验证不会调用任何service方法
        verify(trainQaRdmService, never()).getByUUID(anyString(), anyLong());
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class));
    }

    /**
     * 测试savDtlIdToDb方法 - 根据UUID未找到TrainQaRdm记录
     */
    @Test
    public void testSavDtlIdToDb_TrainQaRdmNotFound() {
        String testUuid = "test-uuid-not-found";
        Long qaReportDtlId = 100L;
        Long teamId = 1L;

        // 创建QaSimplelDtoWithUUID对象
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = new QaSimplelDtoWithUUID();
        qaSimplelDtoWithUUID.setUuid(testUuid);
        qaSimplelDtoWithUUID.setQuestion("测试问题");

        // Mock方法调用，返回null表示未找到
        when(trainQaRdmService.getByUUID(testUuid, teamId)).thenReturn(null);

        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ScWS4QaAnoController.class.getDeclaredMethod(
                    "savDtlIdToDb", QaSimplelDtoWithUUID.class, Long.class, Long.class);
            method.setAccessible(true);
            method.invoke(scWS4QaAnoController, qaSimplelDtoWithUUID, qaReportDtlId, teamId);
        } catch (Exception e) {
            throw new RuntimeException("调用savDtlIdToDb方法失败", e);
        }

        // 验证调用getByUUID但不调用updateByUUID
        verify(trainQaRdmService, times(1)).getByUUID(testUuid, teamId);
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class));
    }

    /**
     * 测试savDtlIdToDb方法 - 更新失败
     */
    @Test
    public void testSavDtlIdToDb_UpdateFailed() {
        String testUuid = "test-uuid-update-fail";
        Long qaReportDtlId = 100L;
        Long teamId = 1L;

        // 创建QaSimplelDtoWithUUID对象
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = new QaSimplelDtoWithUUID();
        qaSimplelDtoWithUUID.setUuid(testUuid);
        qaSimplelDtoWithUUID.setQuestion("测试问题");

        // 创建TrainQaRdm对象
        TrainQaRdm trainQaRdm = new TrainQaRdm();
        trainQaRdm.setId(50L);
        trainQaRdm.setUuid(testUuid);
        trainQaRdm.setTeamId(teamId);

        // Mock方法调用，更新返回false表示失败
        when(trainQaRdmService.getByUUID(testUuid, teamId)).thenReturn(trainQaRdm);
        when(trainQaRdmService.updateByUUID(any(TrainQaRdm.class))).thenReturn(false);

        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = ScWS4QaAnoController.class.getDeclaredMethod(
                    "savDtlIdToDb", QaSimplelDtoWithUUID.class, Long.class, Long.class);
            method.setAccessible(true);
            method.invoke(scWS4QaAnoController, qaSimplelDtoWithUUID, qaReportDtlId, teamId);
        } catch (Exception e) {
            throw new RuntimeException("调用savDtlIdToDb方法失败", e);
        }

        // 验证方法都被调用了
        verify(trainQaRdmService, times(1)).getByUUID(testUuid, teamId);
        verify(trainQaRdmService, times(1)).updateByUUID(any(TrainQaRdm.class));
    }
}