package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.dto.converkb.TaskConvKbDtlDetailDTO;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import com.yiyi.ai_train_playground.service.CacheManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * SimuChatWS4ConvController测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-12
 */
@Slf4j
@SpringBootTest
public class SimuChatWS4ConvControllerTest {
    
    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Test
    public void testGetTaskConvKbDtlByTaskId() {
        log.info("测试获取任务会话明细列表");
        
        try {
            Long taskId = 1L;
            Long teamId = 1L;
            
            // 测试获取任务会话明细列表
            List<TaskConvKbDtlDetailDTO> taskConvKbDtlList = trainTaskConvKbDtlService.getTaskConvKbDtlByTaskId(taskId, teamId);
            
            log.info("获取到任务会话明细数量: {}", taskConvKbDtlList != null ? taskConvKbDtlList.size() : 0);
            
            if (taskConvKbDtlList != null && !taskConvKbDtlList.isEmpty()) {
                // 测试缓存功能
                String cacheKey = "task_conv_kb_dtl:" + taskId;
                cacheManager.put(cacheKey, taskConvKbDtlList);
                log.info("任务会话明细已缓存，key: {}", cacheKey);
                
                // 测试从缓存获取
                Object cachedData = cacheManager.get(cacheKey);
                log.info("从缓存获取数据: {}", cachedData != null ? "成功" : "失败");
                
                // 测试随机选择
                TaskConvKbDtlDetailDTO firstDetail = taskConvKbDtlList.get(0);
                log.info("第一个明细ID: {}, finalChatLog长度: {}", 
                        firstDetail.getId(), 
                        firstDetail.getFinalChatLog() != null ? firstDetail.getFinalChatLog().length() : 0);
            }
            
            log.info("测试完成");
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
    
    @Test
    public void testGet3rdSysPrmtLogic() {
        log.info("测试get3rdSysPrmt逻辑");
        
        try {
            Long taskId = 1L;
            Long teamId = 1L;
            
            // 模拟get3rdSysPrmt方法的逻辑
            List<TaskConvKbDtlDetailDTO> taskConvKbDtlList = trainTaskConvKbDtlService.getTaskConvKbDtlByTaskId(taskId, teamId);
            
            if (taskConvKbDtlList == null || taskConvKbDtlList.isEmpty()) {
                log.warn("未找到任务会话明细，taskId: {}", taskId);
                return;
            }
            
            log.info("获取到任务会话明细数量: {}", taskConvKbDtlList.size());
            
            // 缓存到Redis
            String cacheKey = "task_conv_kb_dtl:" + taskId;
            cacheManager.put(cacheKey, taskConvKbDtlList);
            log.debug("任务会话明细已缓存，key: {}", cacheKey);
            
            // 随机选择一个
            java.util.Random random = new java.util.Random();
            int randomIndex = random.nextInt(taskConvKbDtlList.size());
            TaskConvKbDtlDetailDTO selectedDetail = taskConvKbDtlList.get(randomIndex);
            
            String finalChatLog = selectedDetail.getFinalChatLog();
            if (finalChatLog == null || finalChatLog.trim().isEmpty()) {
                log.warn("选中的任务会话明细finalChatLog为空，id: {}", selectedDetail.getId());
                return;
            }
            
            log.info("随机选择了第{}个明细，id: {}, finalChatLog长度: {}", 
                    randomIndex + 1, selectedDetail.getId(), finalChatLog.length());
            
            log.info("模拟get3rdSysPrmt逻辑测试完成");

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testGet3rdSysPrmtByRdm() {
        log.info("测试get3rdSysPrmtByRdm方法（使用MySQL RAND()）");

        try {
            Long taskId = 1L;
            Long teamId = 1L;

            // 多次调用测试随机性
            for (int i = 0; i < 5; i++) {
                TaskConvKbDtlDetailDTO randomDetail = trainTaskConvKbDtlService.getRdmConvDtlByTaskId(taskId, teamId);

                if (randomDetail != null) {
                    String finalChatLog = randomDetail.getFinalChatLog();
                    log.info("第{}次调用 - 随机选择了明细，id: {}, finalChatLog长度: {}",
                            i + 1, randomDetail.getId(),
                            finalChatLog != null ? finalChatLog.length() : 0);

                    if (finalChatLog != null && !finalChatLog.trim().isEmpty()) {
                        log.info("第{}次调用 - finalChatLog内容预览: {}",
                                i + 1, finalChatLog.substring(0, Math.min(50, finalChatLog.length())) + "...");
                    }
                } else {
                    log.warn("第{}次调用 - 未找到任务会话明细", i + 1);
                }
            }

            log.info("get3rdSysPrmtByRdm方法测试完成");

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testGet3rdSysPrmtCacheLogic() {
        log.info("测试get3rdSysPrmt缓存优化逻辑");

        try {
            Long taskId = 1L;
            Long teamId = 1L;
            String cacheKey = "task_conv_kb_dtl:" + taskId;

            // 第一次调用：缓存中没有数据，从数据库获取
            log.info("=== 第一次调用：从数据库获取 ===");

            // 检查缓存是否为空
            Object cachedData = cacheManager.get(cacheKey);
            log.info("缓存中的数据: {}", cachedData != null ? "存在" : "不存在");

            List<TaskConvKbDtlDetailDTO> taskConvKbDtlList = null;

            // 模拟从缓存获取的逻辑
            if (cachedData != null && cachedData instanceof List) {
                @SuppressWarnings("unchecked")
                List<TaskConvKbDtlDetailDTO> cachedList = (List<TaskConvKbDtlDetailDTO>) cachedData;
                taskConvKbDtlList = cachedList;
                log.info("从缓存中获取到任务会话明细数量: {}", taskConvKbDtlList.size());
            }

            // 如果缓存中没有数据，从数据库获取
            if (taskConvKbDtlList == null || taskConvKbDtlList.isEmpty()) {
                log.info("缓存中无数据，从数据库获取任务会话明细，taskId: {}", taskId);
                taskConvKbDtlList = trainTaskConvKbDtlService.getTaskConvKbDtlByTaskId(taskId, teamId);

                if (taskConvKbDtlList != null && !taskConvKbDtlList.isEmpty()) {
                    log.info("从数据库获取到任务会话明细数量: {}", taskConvKbDtlList.size());

                    // 缓存到Redis
                    cacheManager.put(cacheKey, taskConvKbDtlList, 30, java.util.concurrent.TimeUnit.MINUTES);
                    log.info("任务会话明细已缓存，key: {}", cacheKey);
                }
            }

            // 第二次调用：从缓存获取
            log.info("=== 第二次调用：从缓存获取 ===");

            // 再次检查缓存
            Object cachedData2 = cacheManager.get(cacheKey);
            log.info("缓存中的数据: {}", cachedData2 != null ? "存在" : "不存在");

            List<TaskConvKbDtlDetailDTO> taskConvKbDtlList2 = null;

            // 模拟从缓存获取的逻辑
            if (cachedData2 != null && cachedData2 instanceof List) {
                @SuppressWarnings("unchecked")
                List<TaskConvKbDtlDetailDTO> cachedList2 = (List<TaskConvKbDtlDetailDTO>) cachedData2;
                taskConvKbDtlList2 = cachedList2;
                log.info("从缓存中获取到任务会话明细数量: {}", taskConvKbDtlList2.size());
            }

            // 验证缓存是否生效
            if (taskConvKbDtlList2 != null && !taskConvKbDtlList2.isEmpty()) {
                log.info("✅ 缓存逻辑验证成功：第二次调用成功从缓存获取数据");

                // 测试随机选择逻辑
                java.util.Random random = new java.util.Random();
                int randomIndex = random.nextInt(taskConvKbDtlList2.size());
                TaskConvKbDtlDetailDTO selectedDetail = taskConvKbDtlList2.get(randomIndex);

                String finalChatLog = selectedDetail.getFinalChatLog();
                if (finalChatLog != null && !finalChatLog.trim().isEmpty()) {
                    log.info("随机选择了第{}个明细，id: {}, finalChatLog长度: {}",
                            randomIndex + 1, selectedDetail.getId(), finalChatLog.length());
                    log.info("✅ 完整的缓存优化逻辑验证成功");
                } else {
                    log.warn("选中的任务会话明细finalChatLog为空，id: {}", selectedDetail.getId());
                }
            } else {
                log.warn("❌ 缓存逻辑验证失败：第二次调用未能从缓存获取数据");
            }

        } catch (Exception e) {
            log.error("缓存逻辑测试失败", e);
        }
    }
}
