package com.yiyi.ai_train_playground.controller.task;

import com.yiyi.ai_train_playground.dto.task.ChatroomCreateRequest;
import com.yiyi.ai_train_playground.dto.task.ChatroomUpdateRequest;
import com.yiyi.ai_train_playground.service.task.TrainReceptionChatroomService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainReceptionChatroomController 测试类
 * 主要测试任务数量验证功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class TrainReceptionChatroomControllerTest {

    private static final Logger log = LoggerFactory.getLogger(TrainReceptionChatroomControllerTest.class);

    @Autowired
    private TrainReceptionChatroomService chatroomService;

    @Test
    public void testCreateChatroomWithValidTaskCount() {
        log.info("测试创建聊天室 - 有效的任务数量（大于0）");

        try {
            // 准备测试数据 - 1个任务（有效）
            ChatroomCreateRequest request = new ChatroomCreateRequest();
            request.setReceptionSkin("0");
            request.setSceneMode(0);
            request.setReceptionDuration(30);
            request.setTimerDisplay(false);
            request.setEntryFreqMin(1);
            request.setEntryFreqMax(5);

            // 创建任务列表 - 1个任务
            List<ChatroomCreateRequest.ChatroomTaskCreateDTO> taskList = new ArrayList<>();

            ChatroomCreateRequest.ChatroomTaskCreateDTO task1 = new ChatroomCreateRequest.ChatroomTaskCreateDTO();
            task1.setTaskId(1L);
            task1.setTrainingRecycleCnt(0);
            taskList.add(task1);

            request.setTaskList(taskList);

            log.info("准备创建聊天室，任务数量: {}", taskList.size());

            // 调用服务层方法（模拟Controller逻辑）
            Long teamId = 1L;
            String creator = "test_user";

            Long chatroomId = chatroomService.createChatroomWithTasks(request, teamId, creator);

            assertNotNull(chatroomId);
            assertTrue(chatroomId > 0);

            log.info("创建聊天室成功，ID: {}", chatroomId);

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("创建聊天室测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testCreateChatroomWithInvalidTaskCount() {
        log.info("测试创建聊天室 - 无效的任务数量（等于0）");

        try {
            // 准备测试数据 - 0个任务（无效）
            ChatroomCreateRequest request = new ChatroomCreateRequest();
            request.setReceptionSkin("0");
            request.setSceneMode(0);
            request.setReceptionDuration(30);

            // 创建任务列表 - 空列表
            List<ChatroomCreateRequest.ChatroomTaskCreateDTO> taskList = new ArrayList<>();
            request.setTaskList(taskList);

            log.info("准备创建聊天室，任务数量: {} (应该失败)", taskList.size());

            // 模拟Controller中的验证逻辑
            if (request.getTaskList() == null || request.getTaskList().size() <= 0) {
                log.info("验证通过：任务数量检查正确拦截了无效请求");
                assertTrue(true, "任务数量验证正确");
            } else {
                fail("任务数量验证失败：应该拦截0个任务的请求");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("任务数量验证测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testCreateChatroomWithEmptyTaskList() {
        log.info("测试创建聊天室 - 空任务列表");

        try {
            // 准备测试数据 - 空任务列表
            ChatroomCreateRequest request = new ChatroomCreateRequest();
            request.setReceptionSkin("0");
            request.setSceneMode(0);
            request.setReceptionDuration(30);
            request.setTaskList(new ArrayList<>()); // 空列表

            log.info("准备创建聊天室，任务数量: 0 (应该失败)");

            // 模拟Controller中的验证逻辑
            if (request.getTaskList() == null || request.getTaskList().size() <= 0) {
                log.info("验证通过：任务数量检查正确拦截了空任务列表");
                assertTrue(true, "空任务列表验证正确");
            } else {
                fail("任务数量验证失败：应该拦截空任务列表");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("空任务列表验证测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateChatroomWithValidTaskCount() {
        log.info("测试更新聊天室 - 有效的任务数量（大于0）");

        try {
            // 准备更新数据 - 1个任务（有效）
            ChatroomUpdateRequest request = new ChatroomUpdateRequest();
            request.setId(1L);
            request.setReceptionSkin("1");
            request.setSceneMode(1);
            request.setReceptionDuration(45);
            request.setVersion(0L);

            // 创建任务列表 - 1个任务
            List<ChatroomUpdateRequest.ChatroomTaskUpdateDTO> taskList = new ArrayList<>();

            ChatroomUpdateRequest.ChatroomTaskUpdateDTO task1 = new ChatroomUpdateRequest.ChatroomTaskUpdateDTO();
            task1.setTaskId(1L);
            task1.setTrainingRecycleCnt(0);
            taskList.add(task1);

            request.setTaskList(taskList);

            log.info("准备更新聊天室，任务数量: {}", taskList.size());

            // 模拟Controller中的验证逻辑
            if (request.getTaskList() != null && request.getTaskList().size() <= 0) {
                fail("任务数量验证失败：不应该拦截有效的任务数量");
            } else {
                log.info("验证通过：有效任务数量正确通过验证");
                assertTrue(true, "有效任务数量验证正确");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("更新聊天室任务数量验证测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateChatroomWithInvalidTaskCount() {
        log.info("测试更新聊天室 - 无效的任务数量（等于0）");

        try {
            // 准备更新数据 - 0个任务（无效）
            ChatroomUpdateRequest request = new ChatroomUpdateRequest();
            request.setId(1L);
            request.setReceptionSkin("1");
            request.setSceneMode(1);
            request.setReceptionDuration(45);
            request.setVersion(0L);

            // 创建任务列表 - 空列表
            List<ChatroomUpdateRequest.ChatroomTaskUpdateDTO> taskList = new ArrayList<>();
            request.setTaskList(taskList);

            log.info("准备更新聊天室，任务数量: {} (应该失败)", taskList.size());

            // 模拟Controller中的验证逻辑
            if (request.getTaskList() != null && request.getTaskList().size() <= 0) {
                log.info("验证通过：任务数量检查正确拦截了无效更新请求");
                assertTrue(true, "更新任务数量验证正确");
            } else {
                fail("任务数量验证失败：应该拦截0个任务的更新请求");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("更新聊天室任务数量验证测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateChatroomWithNullTaskList() {
        log.info("测试更新聊天室 - null任务列表（不更新任务）");

        try {
            // 准备更新数据 - null任务列表（表示不更新任务）
            ChatroomUpdateRequest request = new ChatroomUpdateRequest();
            request.setId(1L);
            request.setReceptionSkin("1");
            request.setSceneMode(1);
            request.setReceptionDuration(45);
            request.setVersion(0L);
            request.setTaskList(null); // null表示不更新任务列表

            log.info("准备更新聊天室，任务列表为null（不更新任务）");

            // 模拟Controller中的验证逻辑
            if (request.getTaskList() != null && request.getTaskList().size() <= 0) {
                fail("任务数量验证失败：不应该拦截null任务列表");
            } else {
                log.info("验证通过：null任务列表正确跳过验证");
                assertTrue(true, "null任务列表验证正确");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("null任务列表验证测试失败: " + e.getMessage());
        }
    }
}
