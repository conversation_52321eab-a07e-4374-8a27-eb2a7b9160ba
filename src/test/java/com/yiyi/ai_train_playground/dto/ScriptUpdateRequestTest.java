package com.yiyi.ai_train_playground.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ScriptUpdateRequest测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-23
 */
public class ScriptUpdateRequestTest {

    /**
     * 测试prodType字段的设置和获取
     */
    @Test
    void testProdTypeField() {
        ScriptUpdateRequest request = new ScriptUpdateRequest();
        
        // 测试设置和获取prodType
        request.setProdType(0);
        assertEquals(Integer.valueOf(0), request.getProdType(), "prodType应该为0（自主导入）");
        
        request.setProdType(1);
        assertEquals(Integer.valueOf(1), request.getProdType(), "prodType应该为1（JD）");
        
        request.setProdType(2);
        assertEquals(Integer.valueOf(2), request.getProdType(), "prodType应该为2（TB）");
        
        // 测试null值
        request.setProdType(null);
        assertNull(request.getProdType(), "prodType应该可以设置为null");
    }

    /**
     * 测试ScriptUpdateRequest的完整字段设置
     */
    @Test
    void testCompleteFieldsWithProdType() {
        ScriptUpdateRequest request = new ScriptUpdateRequest();
        
        // 设置所有基本字段
        request.setId(1L);
        request.setName("测试剧本");
        request.setGenerationTypeCode(0);
        request.setGroupId(10L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("测试需求");
        request.setOrderPriority(3);
        request.setOrderRemark("测试备注");
        request.setSimulationTool("Simulator_V3");
        request.setProdType(1); // JD商品类型
        request.setVersion(1L);
        request.setIsOfficial(false);
        
        // 验证所有字段
        assertEquals(Long.valueOf(1), request.getId());
        assertEquals("测试剧本", request.getName());
        assertEquals(Integer.valueOf(0), request.getGenerationTypeCode());
        assertEquals(Long.valueOf(10), request.getGroupId());
        assertEquals(Long.valueOf(5), request.getIntentId());
        assertEquals(Long.valueOf(2001), request.getEvaluationPlanId());
        assertEquals("测试需求", request.getBuyerRequirement());
        assertEquals(Integer.valueOf(3), request.getOrderPriority());
        assertEquals("测试备注", request.getOrderRemark());
        assertEquals("Simulator_V3", request.getSimulationTool());
        assertEquals(Integer.valueOf(1), request.getProdType(), "prodType应该为1（JD）");
        assertEquals(Long.valueOf(1), request.getVersion());
        assertEquals(Boolean.FALSE, request.getIsOfficial());
    }
}
