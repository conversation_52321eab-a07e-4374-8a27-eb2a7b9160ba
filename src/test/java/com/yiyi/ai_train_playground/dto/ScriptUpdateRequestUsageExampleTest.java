package com.yiyi.ai_train_playground.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;

/**
 * ScriptUpdateRequest使用示例测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-23
 */
public class ScriptUpdateRequestUsageExampleTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试传统商品类型的剧本更新请求
     */
    @Test
    void testTraditionalProductUpdateRequest() throws Exception {
        ScriptUpdateRequest request = new ScriptUpdateRequest();
        
        // 设置基本信息
        request.setId(1L);
        request.setName("更新后的传统商品剧本");
        request.setGenerationTypeCode(0);
        request.setGroupId(10L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("更新后的买家需求");
        request.setOrderPriority(3);
        request.setOrderRemark("更新后的备注");
        request.setSimulationTool("Simulator_V3");
        request.setProdType(0); // 传统商品类型
        request.setVersion(2L);
        request.setIsOfficial(false);
        
        // 添加商品列表
        ScriptUpdateRequest.ProductUpdateDTO product1 = new ScriptUpdateRequest.ProductUpdateDTO();
        product1.setId(1L);
        product1.setExternalProductId("PROD001");
        product1.setExternalProductName("更新后的商品1");
        product1.setExternalProductLink("https://shop.com/PROD001");
        product1.setExternalProductImage("https://img.com/PROD001.jpg");
        
        ScriptUpdateRequest.ProductUpdateDTO product2 = new ScriptUpdateRequest.ProductUpdateDTO();
        product2.setId(2L);
        product2.setExternalProductId("PROD002");
        product2.setExternalProductName("更新后的商品2");
        product2.setExternalProductLink("https://shop.com/PROD002");
        product2.setExternalProductImage("https://img.com/PROD002.jpg");
        
        request.setProductList(Arrays.asList(product1, product2));
        
        // 验证JSON序列化
        String json = objectMapper.writeValueAsString(request);
        assertNotNull(json);
        assertTrue(json.contains("\"prodType\":0"));
        assertTrue(json.contains("\"name\":\"更新后的传统商品剧本\""));
        
        // 验证JSON反序列化
        ScriptUpdateRequest deserializedRequest = objectMapper.readValue(json, ScriptUpdateRequest.class);
        assertEquals(Integer.valueOf(0), deserializedRequest.getProdType());
        assertEquals("更新后的传统商品剧本", deserializedRequest.getName());
        assertEquals(2, deserializedRequest.getProductList().size());
    }

    /**
     * 测试JD商品类型的剧本更新请求
     */
    @Test
    void testJdProductUpdateRequest() throws Exception {
        ScriptUpdateRequest request = new ScriptUpdateRequest();
        
        // 设置基本信息
        request.setId(2L);
        request.setName("更新后的JD商品剧本");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("更新后的JD商品需求");
        request.setOrderPriority(3);
        request.setOrderRemark("更新后的JD商品备注");
        request.setSimulationTool("Simulator_V3");
        request.setProdType(1); // JD商品类型
        request.setVersion(1L);
        request.setIsOfficial(false);
        
        // 添加JD商品列表（使用SKU ID）
        ScriptUpdateRequest.ProductUpdateDTO jdProduct1 = new ScriptUpdateRequest.ProductUpdateDTO();
        jdProduct1.setId(10L);
        jdProduct1.setExternalProductId("100012345678"); // JD SKU ID
        jdProduct1.setExternalProductName("iPhone 15 Pro Max");
        jdProduct1.setExternalProductLink("https://item.jd.com/100012345678.html");
        jdProduct1.setExternalProductImage("https://img11.360buyimg.com/devfe/jfs/t1/iphone15.jpg");
        
        ScriptUpdateRequest.ProductUpdateDTO jdProduct2 = new ScriptUpdateRequest.ProductUpdateDTO();
        jdProduct2.setId(11L);
        jdProduct2.setExternalProductId("100087654321"); // JD SKU ID
        jdProduct2.setExternalProductName("MacBook Pro 14英寸");
        jdProduct2.setExternalProductLink("https://item.jd.com/100087654321.html");
        jdProduct2.setExternalProductImage("https://img11.360buyimg.com/devfe/jfs/t1/macbook.jpg");
        
        request.setProductList(Arrays.asList(jdProduct1, jdProduct2));
        
        // 验证JSON序列化
        String json = objectMapper.writeValueAsString(request);
        assertNotNull(json);
        assertTrue(json.contains("\"prodType\":1"));
        assertTrue(json.contains("\"name\":\"更新后的JD商品剧本\""));
        assertTrue(json.contains("\"100012345678\""));
        assertTrue(json.contains("\"100087654321\""));
        
        // 验证JSON反序列化
        ScriptUpdateRequest deserializedRequest = objectMapper.readValue(json, ScriptUpdateRequest.class);
        assertEquals(Integer.valueOf(1), deserializedRequest.getProdType());
        assertEquals("更新后的JD商品剧本", deserializedRequest.getName());
        assertEquals(2, deserializedRequest.getProductList().size());
        assertEquals("100012345678", deserializedRequest.getProductList().get(0).getExternalProductId());
        assertEquals("100087654321", deserializedRequest.getProductList().get(1).getExternalProductId());
    }

    /**
     * 测试prodType为null的情况
     */
    @Test
    void testNullProdTypeRequest() throws Exception {
        ScriptUpdateRequest request = new ScriptUpdateRequest();
        
        // 设置基本信息，但不设置prodType
        request.setId(3L);
        request.setName("未指定商品类型的剧本");
        request.setGenerationTypeCode(0);
        request.setProdType(null); // 明确设置为null
        
        // 验证JSON序列化
        String json = objectMapper.writeValueAsString(request);
        assertNotNull(json);
        assertTrue(json.contains("\"prodType\":null"));
        
        // 验证JSON反序列化
        ScriptUpdateRequest deserializedRequest = objectMapper.readValue(json, ScriptUpdateRequest.class);
        assertNull(deserializedRequest.getProdType());
        assertEquals("未指定商品类型的剧本", deserializedRequest.getName());
    }
}
