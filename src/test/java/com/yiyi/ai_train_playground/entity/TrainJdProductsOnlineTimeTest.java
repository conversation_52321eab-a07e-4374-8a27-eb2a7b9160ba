package com.yiyi.ai_train_playground.entity;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试TrainJdProducts实体类的onlineTime字段
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@DisplayName("京东商品实体类上线时间字段测试")
public class TrainJdProductsOnlineTimeTest {

    @Test
    @DisplayName("测试onlineTime字段的设置和获取")
    public void testOnlineTimeField() {
        // 给定
        TrainJdProducts product = new TrainJdProducts();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime onlineTime = now.plusDays(1); // 明天上线
        LocalDateTime offlineTime = now.plusDays(30); // 30天后下线

        // 当 - 设置各种时间字段
        product.setId(1L);
        product.setTeamId(1L);
        product.setWareId(12345L);
        product.setTitle("测试商品");
        product.setBrandName("测试品牌");
        product.setJdPrice(new BigDecimal("99.99"));
        product.setCreated(now);
        product.setModified(now);
        product.setOnlineTime(onlineTime);
        product.setOfflineTime(offlineTime);
        product.setCreator("testUser");
        product.setUpdater("testUser");

        // 那么 - 验证字段设置正确
        assertThat(product.getOnlineTime()).isEqualTo(onlineTime);
        assertThat(product.getOfflineTime()).isEqualTo(offlineTime);
        assertThat(product.getCreated()).isEqualTo(now);
        assertThat(product.getModified()).isEqualTo(now);

        log.info("商品ID: {}", product.getId());
        log.info("商品标题: {}", product.getTitle());
        log.info("创建时间: {}", product.getCreated());
        log.info("修改时间: {}", product.getModified());
        log.info("上线时间: {}", product.getOnlineTime());
        log.info("下线时间: {}", product.getOfflineTime());

        System.out.println("✅ onlineTime字段测试通过");
    }

    @Test
    @DisplayName("测试时间字段的null值处理")
    public void testTimeFieldsWithNullValues() {
        // 给定
        TrainJdProducts product = new TrainJdProducts();

        // 当 - 设置基本字段，时间字段保持null
        product.setId(2L);
        product.setTeamId(1L);
        product.setWareId(12346L);
        product.setTitle("测试商品2");
        product.setBrandName("测试品牌2");
        product.setJdPrice(new BigDecimal("199.99"));
        product.setCreator("testUser");
        product.setUpdater("testUser");

        // 那么 - 验证时间字段可以为null
        assertThat(product.getOnlineTime()).isNull();
        assertThat(product.getOfflineTime()).isNull();
        assertThat(product.getCreated()).isNull();
        assertThat(product.getModified()).isNull();

        log.info("商品ID: {}", product.getId());
        log.info("商品标题: {}", product.getTitle());
        log.info("上线时间: {}", product.getOnlineTime());
        log.info("下线时间: {}", product.getOfflineTime());

        System.out.println("✅ 时间字段null值测试通过");
    }

    @Test
    @DisplayName("测试商品生命周期时间逻辑")
    public void testProductLifecycleTimeLogic() {
        // 给定
        TrainJdProducts product = new TrainJdProducts();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime created = now.minusDays(10);     // 10天前创建
        LocalDateTime onlineTime = now.minusDays(5);   // 5天前上线
        LocalDateTime modified = now.minusDays(2);     // 2天前修改
        LocalDateTime offlineTime = now.plusDays(25);  // 25天后下线

        // 当 - 设置商品生命周期时间
        product.setCreated(created);
        product.setOnlineTime(onlineTime);
        product.setModified(modified);
        product.setOfflineTime(offlineTime);

        // 那么 - 验证时间逻辑合理性
        assertThat(product.getCreated()).isBefore(product.getOnlineTime());
        assertThat(product.getOnlineTime()).isBefore(product.getModified());
        assertThat(product.getModified()).isBefore(product.getOfflineTime());

        log.info("商品生命周期时间验证:");
        log.info("创建时间: {} ({}天前)", product.getCreated(), 10);
        log.info("上线时间: {} ({}天前)", product.getOnlineTime(), 5);
        log.info("修改时间: {} ({}天前)", product.getModified(), 2);
        log.info("下线时间: {} ({}天后)", product.getOfflineTime(), 25);

        System.out.println("✅ 商品生命周期时间逻辑测试通过");
    }
}
