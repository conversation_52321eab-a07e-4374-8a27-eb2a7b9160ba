package com.yiyi.ai_train_playground.interceptor;

import com.yiyi.ai_train_playground.interceptor.tenant.TenantCondition;
import com.yiyi.ai_train_playground.util.SqlUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多表JOIN查询的租户拦截器测试
 * 用于验证在多表场景下team_id和creator条件的注入逻辑
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-31
 */
@SpringBootTest
public class TenantInterceptorMultiTableTest {
    
    @Test
    public void testMultiTableJoin_SimpleSelect() {
        // 测试简单的多表JOIN查询
        String originalSql = """
            SELECT trt.*, ts.name AS scriptName 
            FROM train_reception_task trt 
            LEFT JOIN train_script ts ON trt.script_id = ts.id 
            WHERE trt.task_name = 'test'
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        condition.setCreator("testuser");
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        // 验证条件是否正确添加
        assertTrue(modifiedSql.contains("team_id = 123"));
        assertTrue(modifiedSql.contains("creator = 'testuser'"));
        assertTrue(modifiedSql.contains("trt.task_name = 'test'"));
    }
    
    @Test
    public void testMultiTableJoin_ComplexSelect() {
        // 测试复杂的多表JOIN查询
        String originalSql = """
            SELECT trt.id, trt.task_name, ts.name AS scriptName, tkm.name AS kbName
            FROM train_reception_task trt
            LEFT JOIN train_script ts ON trt.script_id = ts.id
            LEFT JOIN train_kb_tpl_main tkm ON trt.conv_kb_id = tkm.id
            LEFT JOIN train_qa_import_main tqim ON trt.qa_main_id = tqim.id
            WHERE trt.task_mode = 1 AND ts.status = 'active'
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(456L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\n复杂JOIN查询:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        assertTrue(modifiedSql.contains("team_id = 456"));
        assertTrue(modifiedSql.contains("trt.task_mode = 1"));
        assertTrue(modifiedSql.contains("ts.status = 'active'"));
    }
    
    @Test
    public void testMultiTableUpdate() {
        // 测试多表UPDATE（虽然不常见，但需要验证）
        String originalSql = """
            UPDATE train_reception_task trt 
            SET trt.task_name = 'updated_name' 
            WHERE trt.id = 1
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(789L);
        condition.setCreator("admin");
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\nUPDATE查询:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        assertTrue(modifiedSql.contains("team_id = 789"));
        assertTrue(modifiedSql.contains("creator = 'admin'"));
        assertTrue(modifiedSql.contains("trt.id = 1"));
    }
    
    @Test
    public void testMultiTableDelete() {
        // 测试多表DELETE
        String originalSql = """
            DELETE FROM train_reception_task 
            WHERE script_id IN (
                SELECT id FROM train_script WHERE status = 'inactive'
            )
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(999L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\nDELETE查询:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        assertTrue(modifiedSql.contains("team_id = 999"));
    }
    
    @Test
    public void testTableAliasHandling() {
        // 测试表别名的处理逻辑 - 验证改进后的实现能正确处理别名
        String originalSql = """
            SELECT t1.name, t2.description 
            FROM train_staff t1 
            INNER JOIN train_role t2 ON t1.role_id = t2.id 
            WHERE t1.status = 1
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(111L);
        condition.setCreator("testuser");
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\n表别名处理:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        // 验证条件正确添加到主表别名上
        assertTrue(modifiedSql.contains("t1.team_id = 111"));
        assertTrue(modifiedSql.contains("t1.creator = 'testuser'"));
        assertTrue(modifiedSql.contains("t1.status = 1"));
        
        // 确保没有添加裸露的字段名（这会导致SQL歧义错误）
        assertFalse(modifiedSql.matches(".*(?<!t1\\.)team_id = 111.*"));
        assertFalse(modifiedSql.matches(".*(?<!t1\\.)creator = 'testuser'.*"));
    }
    
    @Test
    public void testSubQueryHandling() {
        // 测试子查询的处理
        String originalSql = """
            SELECT * FROM train_reception_task 
            WHERE script_id IN (
                SELECT id FROM train_script WHERE name LIKE '%test%'
            )
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(222L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\n子查询处理:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        assertTrue(modifiedSql.contains("team_id = 222"));
        // 注意：当前实现只会在外层查询添加条件，子查询不会被处理
        // 这可能是一个潜在的安全风险
    }
    
    @Test
    public void testNoAliasHandling() {
        // 测试没有别名的情况
        String originalSql = """
            SELECT * FROM train_reception_task 
            WHERE task_name = 'test'
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(333L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\n无别名处理:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        // 没有别名时应该直接添加字段名
        assertTrue(modifiedSql.contains("team_id = 333"));
        assertTrue(modifiedSql.contains("task_name = 'test'"));
        // 确保没有添加空的别名前缀
        assertFalse(modifiedSql.contains(".team_id"));
    }
    
    @Test
    public void testMixedAliasHandling() {
        // 测试混合场景：部分表有别名，部分表没有别名
        String originalSql = """
            UPDATE train_reception_task trt 
            SET task_status = 'completed' 
            WHERE trt.id = 1
            """;
        
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(555L);
        condition.setCreator("admin");
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        System.out.println("\n混合别名处理:");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("修改后SQL: " + modifiedSql);
        
        // UPDATE语句有别名时应该添加到别名上
        assertTrue(modifiedSql.contains("trt.team_id = 555"));
        assertTrue(modifiedSql.contains("trt.creator = 'admin'"));
        assertTrue(modifiedSql.contains("trt.id = 1"));
    }
}