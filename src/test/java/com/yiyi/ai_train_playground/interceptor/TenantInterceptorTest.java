package com.yiyi.ai_train_playground.interceptor;

import com.yiyi.ai_train_playground.annotation.TenantFilter;
import com.yiyi.ai_train_playground.interceptor.tenant.TenantCondition;
import com.yiyi.ai_train_playground.util.SqlUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多租户拦截器单元测试
 * 
 * <AUTHOR> Train Playground
 * @since 2025-08-30
 */
@SpringBootTest
public class TenantInterceptorTest {
    
    @Test
    public void testSqlUtil_addTenantCondition_select() {
        // 测试SELECT语句添加租户条件
        String originalSql = "SELECT * FROM train_staff WHERE username = 'test'";
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        condition.setCreator("testuser");
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        assertTrue(modifiedSql.contains("team_id = 123"));
        assertTrue(modifiedSql.contains("creator = 'testuser'"));
        assertTrue(modifiedSql.contains("username = 'test'"));
    }
    
    @Test
    public void testSqlUtil_addTenantCondition_update() {
        // 测试UPDATE语句添加租户条件
        String originalSql = "UPDATE train_staff SET display_name = 'new_name' WHERE id = 1";
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        assertTrue(modifiedSql.contains("team_id = 123"));
        assertTrue(modifiedSql.contains("id = 1"));
        assertTrue(modifiedSql.contains("display_name = 'new_name'"));
    }
    
    @Test
    public void testSqlUtil_addTenantCondition_delete() {
        // 测试DELETE语句添加租户条件
        String originalSql = "DELETE FROM train_staff WHERE id = 1";
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        condition.setCreator("testuser");
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        assertTrue(modifiedSql.contains("team_id = 123"));
        assertTrue(modifiedSql.contains("creator = 'testuser'"));
        assertTrue(modifiedSql.contains("id = 1"));
    }
    
    @Test
    public void testSqlUtil_shouldProcess() {
        // 测试SQL是否需要处理的判断
        assertTrue(SqlUtil.shouldProcess("SELECT * FROM train_staff"));
        assertTrue(SqlUtil.shouldProcess("UPDATE train_staff SET name = 'test'"));
        assertTrue(SqlUtil.shouldProcess("DELETE FROM train_staff WHERE id = 1"));
        
        assertFalse(SqlUtil.shouldProcess("INSERT INTO train_staff VALUES (1, 'test')"));
        assertFalse(SqlUtil.shouldProcess("CREATE TABLE test_table (id INT)"));
        assertFalse(SqlUtil.shouldProcess(""));
        assertFalse(SqlUtil.shouldProcess(null));
    }
    
    @Test
    public void testTenantCondition_isEmpty() {
        // 测试TenantCondition的isEmpty方法
        TenantCondition condition = new TenantCondition();
        assertTrue(condition.isEmpty());
        
        condition.setTeamId(123L);
        assertFalse(condition.isEmpty());
        assertTrue(condition.hasTeamId());
        assertFalse(condition.hasCreator());
        
        condition.setCreator("testuser");
        assertFalse(condition.isEmpty());
        assertTrue(condition.hasTeamId());
        assertTrue(condition.hasCreator());
    }
    
    @Test
    public void testSqlUtil_emptyCondition() {
        // 测试空条件时返回原SQL
        String originalSql = "SELECT * FROM train_staff";
        TenantCondition condition = new TenantCondition();
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        assertEquals(originalSql, modifiedSql);
        
        // 测试null条件
        modifiedSql = SqlUtil.addTenantCondition(originalSql, null);
        assertEquals(originalSql, modifiedSql);
    }
    
    @Test
    public void testSqlUtil_noWhereClause() {
        // 测试没有WHERE子句的SQL
        String originalSql = "SELECT * FROM train_staff";
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        assertTrue(modifiedSql.contains("WHERE team_id = 123"));
    }
    
    @Test
    public void testSqlUtil_withExistingWhere() {
        // 测试已有WHERE子句的SQL
        String originalSql = "SELECT * FROM train_staff WHERE status = 1";
        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        
        String modifiedSql = SqlUtil.addTenantCondition(originalSql, condition);
        
        assertTrue(modifiedSql.contains("status = 1"));
        assertTrue(modifiedSql.contains("team_id = 123"));
        assertTrue(modifiedSql.contains("AND"));
    }
    
    @Test
    public void testTenantFilterAnnotation() throws Exception {
        // 创建一个测试接口来验证注解
        class TestMapper {
            @TenantFilter
            public void testMethod() {}
        }
        
        Method method = TestMapper.class.getDeclaredMethod("testMethod");
        TenantFilter annotation = method.getAnnotation(TenantFilter.class);
        
        assertNotNull(annotation);
        assertTrue(annotation.enable());
        assertTrue(annotation.includeTeamId());
        assertTrue(annotation.includeCreator());
        assertEquals(TenantFilter.TeamIdSource.SECURITY_CONTEXT, annotation.teamIdSource());
        assertEquals(TenantFilter.CreatorSource.SECURITY_CONTEXT, annotation.creatorSource());
        assertEquals("teamId", annotation.teamIdParamName());
        assertEquals("creator", annotation.creatorParamName());
        assertEquals("creator", annotation.creatorEntityField());
    }
}