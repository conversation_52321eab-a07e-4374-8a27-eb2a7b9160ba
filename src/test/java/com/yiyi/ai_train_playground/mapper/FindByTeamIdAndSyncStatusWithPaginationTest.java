package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdProdImagesMapper;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdProductsMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试 findByTeamIdAndSyncStatusWithPagination 方法的 jdProdId 参数功能
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class FindByTeamIdAndSyncStatusWithPaginationTest {

    @Autowired
    private TrainJdProductsMapper trainJdProductsMapper;

    @Autowired
    private TrainJdProdImagesMapper trainJdProdImagesMapper;

    @Test
    @DisplayName("测试 TrainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination 新增 jdProdId 参数")
    void testTrainJdProductsMapperWithJdProdId() {
        log.info("=== 测试 TrainJdProductsMapper 新增 jdProdId 参数 ===");

        Long teamId = 1L;
        Integer syncStatus = 0;
        Long jdProdId = 123L;
        Integer offset = 0;
        Integer pageSize = 10;

        // 测试调用新的方法签名
        List<TrainJdProducts> products = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, syncStatus, jdProdId, offset, pageSize);

        assertThat(products).isNotNull();
        log.info("查询结果数量: {}", products.size());

        // 测试 jdProdId 为 null 的情况
        List<TrainJdProducts> allProducts = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, syncStatus, null, offset, pageSize);

        assertThat(allProducts).isNotNull();
        log.info("jdProdId为null时查询结果数量: {}", allProducts.size());
    }

    @Test
    @DisplayName("测试 TrainJdProdImagesMapper.findByTeamIdAndSyncStatusWithPagination 新增 jdProdId 参数")
    void testTrainJdProdImagesMapperWithJdProdId() {
        log.info("=== 测试 TrainJdProdImagesMapper 新增 jdProdId 参数 ===");

        Long teamId = 1L;
        Integer syncStatus = 0;
        Long jdProdId = 123L;
        Integer offset = 0;
        Integer pageSize = 10;

        // 测试调用新的方法签名
        List<TrainJdProdImages> images = trainJdProdImagesMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, syncStatus, jdProdId, offset, pageSize);

        assertThat(images).isNotNull();
        log.info("查询结果数量: {}", images.size());

        // 测试 jdProdId 为 null 的情况
        List<TrainJdProdImages> allImages = trainJdProdImagesMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, syncStatus, null, offset, pageSize);

        assertThat(allImages).isNotNull();
        log.info("jdProdId为null时查询结果数量: {}", allImages.size());
    }

    @Test
    @DisplayName("测试不同参数组合")
    void testDifferentParameterCombinations() {
        log.info("=== 测试不同参数组合 ===");

        Long teamId = 1L;
        Integer offset = 0;
        Integer pageSize = 5;

        // 测试组合1: teamId + syncStatus + jdProdId
        List<TrainJdProducts> result1 = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, 0, 123L, offset, pageSize);
        log.info("组合1 (teamId + syncStatus + jdProdId) 结果数量: {}", result1.size());

        // 测试组合2: teamId + syncStatus (jdProdId=null)
        List<TrainJdProducts> result2 = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, 0, null, offset, pageSize);
        log.info("组合2 (teamId + syncStatus) 结果数量: {}", result2.size());

        // 测试组合3: teamId + jdProdId (syncStatus=null)
        List<TrainJdProducts> result3 = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, null, 123L, offset, pageSize);
        log.info("组合3 (teamId + jdProdId) 结果数量: {}", result3.size());

        // 测试组合4: 只有 teamId (syncStatus=null, jdProdId=null)
        List<TrainJdProducts> result4 = trainJdProductsMapper.findByTeamIdAndSyncStatusWithPagination(
                teamId, null, null, offset, pageSize);
        log.info("组合4 (只有teamId) 结果数量: {}", result4.size());

        // 所有结果都应该是非null的
        assertThat(result1).isNotNull();
        assertThat(result2).isNotNull();
        assertThat(result3).isNotNull();
        assertThat(result4).isNotNull();
    }
}
