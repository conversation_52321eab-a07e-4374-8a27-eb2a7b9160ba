package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.jd.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdAccessTokenMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * TrainJdAccessTokenMapper 测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainJdAccessTokenMapperTest {

    @Autowired
    private TrainJdAccessTokenMapper trainJdAccessTokenMapper;

    private static final Long TEST_USER_ID = 1L;
    private static final Long TEST_TEAM_ID = 1L;
    private static final Long TEST_SHOP_ID = 12345L;
    private static final String TEST_XID = "test-xid-mapper-12345";

    @Test
    @DisplayName("测试 findByShopId 方法")
    void testFindByShopId() {
        log.info("=== 测试 TrainJdAccessTokenMapper.findByShopId 方法 ===");

        // 1. 创建测试数据
        TrainJdAccessToken testToken = createTestToken();
        testToken.setShopId(TEST_SHOP_ID);
        
        // 2. 插入测试数据
        int insertResult = trainJdAccessTokenMapper.insert(testToken);
        assertThat(insertResult).isEqualTo(1);
        assertThat(testToken.getId()).isNotNull(); // 验证自增ID已设置
        log.info("插入测试数据成功，生成ID: {}", testToken.getId());

        // 3. 根据店铺ID查询
        TrainJdAccessToken foundToken = trainJdAccessTokenMapper.findByShopId(TEST_SHOP_ID);
        
        // 4. 验证结果
        assertThat(foundToken).isNotNull();
        assertThat(foundToken.getShopId()).isEqualTo(TEST_SHOP_ID);
        assertThat(foundToken.getXid()).isEqualTo(TEST_XID);
        assertThat(foundToken.getUserId()).isEqualTo(TEST_USER_ID);
        assertThat(foundToken.getTeamId()).isEqualTo(TEST_TEAM_ID);
        assertThat(foundToken.getAccessToken()).isEqualTo("test-access-token");
        assertThat(foundToken.getIsAuthorize()).isEqualTo(1);
        assertThat(foundToken.getIsSyncComplete()).isEqualTo(0);
        
        log.info("根据店铺ID查询成功: shopId={}, xid={}, id={}", 
                foundToken.getShopId(), foundToken.getXid(), foundToken.getId());
    }

    @Test
    @DisplayName("测试 findByShopId 查询不存在的记录")
    void testFindByShopIdNotFound() {
        log.info("=== 测试 findByShopId 查询不存在的记录 ===");

        Long nonExistentShopId = 99999L;
        TrainJdAccessToken foundToken = trainJdAccessTokenMapper.findByShopId(nonExistentShopId);
        
        assertThat(foundToken).isNull();
        log.info("查询不存在的店铺ID，正确返回null");
    }

    @Test
    @DisplayName("测试 findByShopId 返回最新记录")
    void testFindByShopIdReturnsLatest() {
        log.info("=== 测试 findByShopId 返回最新记录 ===");

        // 1. 插入第一条记录
        TrainJdAccessToken firstToken = createTestToken();
        firstToken.setShopId(TEST_SHOP_ID);
        firstToken.setXid(TEST_XID + "-first");
        firstToken.setAccessToken("first-token");
        
        int firstInsert = trainJdAccessTokenMapper.insert(firstToken);
        assertThat(firstInsert).isEqualTo(1);
        log.info("插入第一条记录成功，ID: {}", firstToken.getId());

        // 等待一小段时间确保时间戳不同
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 2. 插入第二条记录（相同shopId）
        TrainJdAccessToken secondToken = createTestToken();
        secondToken.setShopId(TEST_SHOP_ID);
        secondToken.setXid(TEST_XID + "-second");
        secondToken.setAccessToken("second-token");
        
        int secondInsert = trainJdAccessTokenMapper.insert(secondToken);
        assertThat(secondInsert).isEqualTo(1);
        log.info("插入第二条记录成功，ID: {}", secondToken.getId());

        // 3. 查询应该返回最新的记录（按create_time DESC排序）
        TrainJdAccessToken foundToken = trainJdAccessTokenMapper.findByShopId(TEST_SHOP_ID);
        
        // 4. 验证返回的是最新记录
        assertThat(foundToken).isNotNull();
        assertThat(foundToken.getShopId()).isEqualTo(TEST_SHOP_ID);
        assertThat(foundToken.getXid()).isEqualTo(TEST_XID + "-second");
        assertThat(foundToken.getAccessToken()).isEqualTo("second-token");
        assertThat(foundToken.getId()).isEqualTo(secondToken.getId());
        
        log.info("findByShopId正确返回最新记录: xid={}, accessToken={}", 
                foundToken.getXid(), foundToken.getAccessToken());
    }

    @Test
    @DisplayName("测试所有查询方法的一致性")
    void testQueryMethodsConsistency() {
        log.info("=== 测试所有查询方法的一致性 ===");

        // 1. 插入测试数据
        TrainJdAccessToken testToken = createTestToken();
        testToken.setShopId(TEST_SHOP_ID);
        
        int insertResult = trainJdAccessTokenMapper.insert(testToken);
        assertThat(insertResult).isEqualTo(1);
        log.info("插入测试数据成功");

        // 2. 通过不同方法查询同一条记录
        TrainJdAccessToken foundByXid = trainJdAccessTokenMapper.findByXid(TEST_XID);
        TrainJdAccessToken foundByShopId = trainJdAccessTokenMapper.findByShopId(TEST_SHOP_ID);
        TrainJdAccessToken foundByUserAndTeam = trainJdAccessTokenMapper.findByUserIdAndTeamId(TEST_USER_ID, TEST_TEAM_ID);

        // 3. 验证所有查询方法返回的是同一条记录
        assertThat(foundByXid).isNotNull();
        assertThat(foundByShopId).isNotNull();
        assertThat(foundByUserAndTeam).isNotNull();

        assertThat(foundByXid.getId()).isEqualTo(foundByShopId.getId());
        assertThat(foundByShopId.getId()).isEqualTo(foundByUserAndTeam.getId());

        assertThat(foundByXid.getShopId()).isEqualTo(TEST_SHOP_ID);
        assertThat(foundByShopId.getXid()).isEqualTo(TEST_XID);
        assertThat(foundByUserAndTeam.getShopId()).isEqualTo(TEST_SHOP_ID);

        log.info("所有查询方法返回一致的结果，记录ID: {}", foundByXid.getId());
    }

    /**
     * 创建测试用的TrainJdAccessToken对象
     */
    private TrainJdAccessToken createTestToken() {
        TrainJdAccessToken token = new TrainJdAccessToken();
        token.setUserId(TEST_USER_ID);
        token.setTeamId(TEST_TEAM_ID);
        token.setAccessToken("test-access-token");
        token.setExpiresTime(LocalDateTime.now().plusHours(2));
        token.setRefreshToken("test-refresh-token");
        token.setScope("read,write");
        token.setXid(TEST_XID);
        token.setIsAuthorize(1);
        token.setIsSyncComplete(0);
        token.setCreator("test-creator");
        token.setUpdater("test-updater");
        return token;
    }
}
