package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaImportDtlMapper 测试类
 * 重点测试 selectByQaMainId 接口
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/mapper/converkb/TrainQaImportDtlMapperTest.sql")
public class TrainQaImportDtlMapperTest {

    @Autowired
    private TrainQaImportDtlMapper trainQaImportDtlMapper;
    
    @Autowired
    private TrainQaImportMainMapper trainQaImportMainMapper;

    private static final Long TEST_TEAM_ID_1 = 1L;
    private static final Long TEST_TEAM_ID_2 = 2L;
    private static final String TEST_USER_ID = "93";

    private Long qaMainId1; // 主表1的ID
    private Long qaMainId2; // 主表2的ID

    @BeforeEach
    public void setUp() {
        // 创建测试用的主表记录
        qaMainId1 = createTestMainRecord("测试知识库1", TEST_TEAM_ID_1);
        qaMainId2 = createTestMainRecord("测试知识库2", TEST_TEAM_ID_1);
        
        System.out.println("测试准备完成 - 主表ID1: " + qaMainId1 + ", 主表ID2: " + qaMainId2);
    }

    /**
     * 测试插入详情记录
     */
    @Test
    public void testInsert() {
        // 准备测试数据
        TrainQaImportDtl dtlRecord = createTestDtlRecord(qaMainId1, "测试问题", "测试答案", TEST_TEAM_ID_1);
        
        // 执行插入
        int result = trainQaImportDtlMapper.insert(dtlRecord);
        
        // 验证结果
        assertEquals(1, result);
        
        System.out.println("详情记录插入成功");
    }

    /**
     * 重点测试：根据主表ID查询详情记录列表（分页）
     */
    @Test
    public void testSelectByQaMainId_Pagination() {
        // 1. 先插入多条测试数据
        insertMultipleTestDtlRecords();
        
        // 2. 测试第一页查询（主表1）
        List<TrainQaImportDtl> page1 = trainQaImportDtlMapper.selectByQaMainId(qaMainId1, null, 0, 3);
        assertNotNull(page1);
        assertEquals(3, page1.size());
        
        // 验证所有记录都属于同一个主表
        for (TrainQaImportDtl record : page1) {
            assertEquals(qaMainId1, record.getQaMainId());
        }
        
        // 验证排序（应该按创建时间倒序）
        assertTrue(page1.get(0).getCreateTime().isAfter(page1.get(1).getCreateTime()) || 
                  page1.get(0).getCreateTime().isEqual(page1.get(1).getCreateTime()));
        
        System.out.println("=== 主表1第一页查询结果 ===");
        for (TrainQaImportDtl record : page1) {
            System.out.println(String.format("ID: %d, 主表ID: %d, 问题: %s, 创建时间: %s", 
                record.getId(), record.getQaMainId(), record.getQuestion(), record.getCreateTime()));
        }
        
        // 3. 测试第二页查询（主表1）
        List<TrainQaImportDtl> page2 = trainQaImportDtlMapper.selectByQaMainId(qaMainId1, null, 3, 3);
        assertNotNull(page2);
        assertEquals(2, page2.size()); // 主表1总共5条记录，第二页应该有2条
        
        System.out.println("=== 主表1第二页查询结果 ===");
        for (TrainQaImportDtl record : page2) {
            System.out.println(String.format("ID: %d, 主表ID: %d, 问题: %s, 创建时间: %s", 
                record.getId(), record.getQaMainId(), record.getQuestion(), record.getCreateTime()));
        }
        
        // 4. 测试主表隔离（主表2）
        List<TrainQaImportDtl> main2Records = trainQaImportDtlMapper.selectByQaMainId(qaMainId2, null, 0, 10);
        assertNotNull(main2Records);
        assertEquals(3, main2Records.size()); // 主表2应该有3条记录
        
        System.out.println("=== 主表2查询结果 ===");
        for (TrainQaImportDtl record : main2Records) {
            System.out.println(String.format("ID: %d, 主表ID: %d, 问题: %s", 
                record.getId(), record.getQaMainId(), record.getQuestion()));
            assertEquals(qaMainId2, record.getQaMainId()); // 验证主表隔离
        }
    }

    /**
     * 测试无分页查询（limit为null）
     */
    @Test
    public void testSelectByQaMainId_NoLimit() {
        // 插入测试数据
        insertMultipleTestDtlRecords();
        
        // 查询所有记录（不分页）
        List<TrainQaImportDtl> allRecords = trainQaImportDtlMapper.selectByQaMainId(qaMainId1, null, null, null);
        assertNotNull(allRecords);
        assertEquals(5, allRecords.size()); // 主表1应该有5条记录
        
        System.out.println("=== 主表1所有记录（无分页） ===");
        for (TrainQaImportDtl record : allRecords) {
            System.out.println(String.format("ID: %d, 问题: %s, 创建时间: %s", 
                record.getId(), record.getQuestion(), record.getCreateTime()));
        }
    }

    /**
     * 测试空结果查询
     */
    @Test
    public void testSelectByQaMainId_EmptyResult() {
        // 查询不存在的主表ID
        List<TrainQaImportDtl> emptyResult = trainQaImportDtlMapper.selectByQaMainId(999L, null, 0, 10);
        assertNotNull(emptyResult);
        assertTrue(emptyResult.isEmpty());
        
        System.out.println("不存在主表ID的查询结果为空: " + emptyResult.size());
    }

    /**
     * 测试统计功能
     */
    @Test
    public void testCountByQaMainId() {
        // 插入测试数据
        insertMultipleTestDtlRecords();
        
        // 统计主表1的详情记录数
        int count1 = trainQaImportDtlMapper.countByQaMainId(qaMainId1, null);
        assertEquals(5, count1);

        // 统计主表2的详情记录数
        int count2 = trainQaImportDtlMapper.countByQaMainId(qaMainId2, null);
        assertEquals(3, count2);

        // 统计不存在主表的记录数
        int count3 = trainQaImportDtlMapper.countByQaMainId(999L, null);
        assertEquals(0, count3);
        
        System.out.println(String.format("统计结果 - 主表1: %d条, 主表2: %d条, 不存在主表: %d条", 
            count1, count2, count3));
    }

    /**
     * 测试根据团队ID查询
     */
    @Test
    public void testSelectByTeamId() {
        // 插入测试数据
        insertMultipleTestDtlRecords();
        
        // 查询团队1的所有详情记录
        List<TrainQaImportDtl> team1Records = trainQaImportDtlMapper.selectByTeamId(TEST_TEAM_ID_1, 0, 10);
        assertNotNull(team1Records);
        assertEquals(8, team1Records.size()); // 团队1总共8条记录（主表1有5条，主表2有3条）
        
        System.out.println("=== 团队1所有详情记录 ===");
        for (TrainQaImportDtl record : team1Records) {
            System.out.println(String.format("ID: %d, 主表ID: %d, 问题: %s, 团队ID: %d", 
                record.getId(), record.getQaMainId(), record.getQuestion(), record.getTeamId()));
            assertEquals(TEST_TEAM_ID_1, record.getTeamId()); // 验证团队隔离
        }
    }

    /**
     * 测试模糊查询
     */
    @Test
    public void testSelectByQuestionLike() {
        // 插入测试数据
        insertMultipleTestDtlRecords();
        
        // 模糊查询包含"防晒"的问题
        List<TrainQaImportDtl> searchResults = trainQaImportDtlMapper.selectByQuestionLike("防晒", TEST_TEAM_ID_1, 0, 10);
        assertNotNull(searchResults);
        assertTrue(searchResults.size() > 0);
        
        System.out.println("=== 模糊查询'防晒'结果 ===");
        for (TrainQaImportDtl record : searchResults) {
            System.out.println(String.format("ID: %d, 问题: %s", record.getId(), record.getQuestion()));
            assertTrue(record.getQuestion().contains("防晒"));
        }
        
        // 统计模糊查询结果数量
        int count = trainQaImportDtlMapper.countByQuestionLike("防晒", TEST_TEAM_ID_1);
        assertEquals(searchResults.size(), count);
        
        System.out.println("模糊查询统计数量: " + count);
    }

    /**
     * 测试问题存在性检查
     */
    @Test
    public void testExistsByQuestion() {
        // 插入测试数据
        TrainQaImportDtl dtlRecord = createTestDtlRecord(qaMainId1, "唯一问题测试", "测试答案", TEST_TEAM_ID_1);
        trainQaImportDtlMapper.insert(dtlRecord);

        // 检查问题是否存在
        int exists = trainQaImportDtlMapper.existsByQuestion("唯一问题测试", TEST_TEAM_ID_1);
        assertEquals(1, exists);

        // 检查不存在的问题
        int notExists = trainQaImportDtlMapper.existsByQuestion("不存在的问题", TEST_TEAM_ID_1);
        assertEquals(0, notExists);

        // 检查团队隔离
        int teamIsolation = trainQaImportDtlMapper.existsByQuestion("唯一问题测试", TEST_TEAM_ID_2);
        assertEquals(0, teamIsolation);

        System.out.println(String.format("问题存在性检查 - 存在: %d, 不存在: %d, 团队隔离: %d",
            exists, notExists, teamIsolation));
    }

    /**
     * 测试随机获取指定数量的明细
     */
    @Test
    public void testSelectRandomByMainId() {
        // 插入测试数据
        insertMultipleTestDtlRecords();

        // 随机获取3条记录
        List<QaSimpleDto> randomRecords = trainQaImportDtlMapper.selectRandomByMainId(qaMainId1, 3);
        assertNotNull(randomRecords);
        assertEquals(3, randomRecords.size());

        System.out.println("=== 随机获取3条记录 ===");
        for (QaSimpleDto record : randomRecords) {
            System.out.println(String.format("问题: %s, 答案: %s", record.getQuestion(), record.getAnswer()));
            assertNotNull(record.getQuestion());
            assertNotNull(record.getAnswer());
        }

        // 测试获取数量超过总数的情况
        List<QaSimpleDto> allRecords = trainQaImportDtlMapper.selectRandomByMainId(qaMainId1, 100);
        assertNotNull(allRecords);
        assertEquals(5, allRecords.size()); // qaMainId1总共有5条记录

        // 测试获取0条记录
        List<QaSimpleDto> zeroRecords = trainQaImportDtlMapper.selectRandomByMainId(qaMainId1, 0);
        assertNotNull(zeroRecords);
        assertEquals(0, zeroRecords.size());

        System.out.println(String.format("随机获取测试完成 - 请求3条得到: %d条, 请求100条得到: %d条, 请求0条得到: %d条",
            randomRecords.size(), allRecords.size(), zeroRecords.size()));
    }

    /**
     * 测试更新操作
     */
    @Test
    public void testUpdateById() {
        // 插入测试数据
        TrainQaImportDtl dtlRecord = createTestDtlRecord(qaMainId1, "原始问题", "原始答案", TEST_TEAM_ID_1);
        trainQaImportDtlMapper.insert(dtlRecord);
        
        // 查询插入的记录以获取ID
        List<TrainQaImportDtl> records = trainQaImportDtlMapper.selectByQaMainId(qaMainId1, null, 0, 1);
        assertFalse(records.isEmpty());
        Long id = records.get(0).getId();
        
        // 更新记录
        TrainQaImportDtl updateRecord = new TrainQaImportDtl();
        updateRecord.setId(id);
        updateRecord.setQuestion("更新后问题");
        updateRecord.setAnswer("更新后答案");
        updateRecord.setTeamId(TEST_TEAM_ID_1);
        updateRecord.setUpdater("updater_test");
        
        int updateResult = trainQaImportDtlMapper.updateById(updateRecord);
        assertEquals(1, updateResult);
        
        // 验证更新结果
        TrainQaImportDtl updated = trainQaImportDtlMapper.selectById(id);
        assertNotNull(updated);
        assertEquals("更新后问题", updated.getQuestion());
        assertEquals("更新后答案", updated.getAnswer());
        assertEquals("updater_test", updated.getUpdater());
        
        System.out.println(String.format("更新成功 - 新问题: %s, 新答案: %s", 
            updated.getQuestion(), updated.getAnswer()));
    }

    /**
     * 测试删除操作
     */
    @Test
    public void testDeleteById() {
        // 插入测试数据
        TrainQaImportDtl dtlRecord = createTestDtlRecord(qaMainId1, "待删除问题", "待删除答案", TEST_TEAM_ID_1);
        trainQaImportDtlMapper.insert(dtlRecord);
        
        // 查询插入的记录以获取ID
        List<TrainQaImportDtl> records = trainQaImportDtlMapper.selectByQaMainId(qaMainId1, null, 0, 1);
        assertFalse(records.isEmpty());
        Long id = records.get(0).getId();
        
        // 验证记录存在
        TrainQaImportDtl found = trainQaImportDtlMapper.selectById(id);
        assertNotNull(found);
        
        // 删除记录
        int deleteResult = trainQaImportDtlMapper.deleteById(id);
        assertEquals(1, deleteResult);
        
        // 验证记录已删除
        TrainQaImportDtl notFound = trainQaImportDtlMapper.selectById(id);
        assertNull(notFound);
        
        System.out.println("删除操作成功，记录ID: " + id);
    }

    /**
     * 测试根据主表ID删除所有详情记录
     */
    @Test
    public void testDeleteByQaMainId() {
        // 插入测试数据
        insertMultipleTestDtlRecords();
        
        // 验证主表1有详情记录
        int countBefore = trainQaImportDtlMapper.countByQaMainId(qaMainId1, null);
        assertTrue(countBefore > 0);

        // 根据主表ID删除所有详情记录
        int deleteResult = trainQaImportDtlMapper.deleteByQaMainId(qaMainId1);
        assertEquals(countBefore, deleteResult);

        // 验证记录已删除
        int countAfter = trainQaImportDtlMapper.countByQaMainId(qaMainId1, null);
        assertEquals(0, countAfter);

        // 验证其他主表的记录未受影响
        int count2 = trainQaImportDtlMapper.countByQaMainId(qaMainId2, null);
        assertTrue(count2 > 0);
        
        System.out.println(String.format("级联删除成功 - 删除记录数: %d, 主表2剩余: %d", 
            deleteResult, count2));
    }

    /**
     * 插入多条测试详情记录
     */
    private void insertMultipleTestDtlRecords() {
        // 主表1的详情记录
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId1, "防晒干是水", "防晒相关答案1", TEST_TEAM_ID_1));
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId1, "防晒使用方法", "防晒相关答案2", TEST_TEAM_ID_1));
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId1, "有试用吗", "试用相关答案", TEST_TEAM_ID_1));
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId1, "产品成分", "成分相关答案", TEST_TEAM_ID_1));
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId1, "使用效果", "效果相关答案", TEST_TEAM_ID_1));
        
        // 主表2的详情记录
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId2, "价格咨询", "价格相关答案", TEST_TEAM_ID_1));
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId2, "发货时间", "发货相关答案", TEST_TEAM_ID_1));
        trainQaImportDtlMapper.insert(createTestDtlRecord(qaMainId2, "售后服务", "售后相关答案", TEST_TEAM_ID_1));
    }

    /**
     * 创建测试用的主表记录
     */
    private Long createTestMainRecord(String name, Long teamId) {
        String batchNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        
        TrainQaImportMain mainRecord = new TrainQaImportMain();
        mainRecord.setBatchNo(batchNo);
        mainRecord.setQaImName(name);
        mainRecord.setQaImDesc(name + "描述");
        mainRecord.setTeamId(teamId);
        mainRecord.setCreator(TEST_USER_ID);
        mainRecord.setUpdater(TEST_USER_ID);
        
        trainQaImportMainMapper.insert(mainRecord);
        return mainRecord.getId();
    }

    /**
     * 创建测试用的详情记录
     */
    private TrainQaImportDtl createTestDtlRecord(Long qaMainId, String question, String answer, Long teamId) {
        TrainQaImportDtl dtlRecord = new TrainQaImportDtl();
        dtlRecord.setQaMainId(qaMainId);
        dtlRecord.setQuestion(question);
        dtlRecord.setAnswer(answer);
        dtlRecord.setTeamId(teamId);
        dtlRecord.setCreator(TEST_USER_ID);
        dtlRecord.setUpdater(TEST_USER_ID);
        
        return dtlRecord;
    }
}
