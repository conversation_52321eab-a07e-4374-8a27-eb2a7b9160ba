package com.yiyi.ai_train_playground.mapper.converkb;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaImportMainMapper 测试类
 * 重点测试 selectByTeamId 接口
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/mapper/converkb/TrainQaImportMainMapperTest.sql")
public class TrainQaImportMainMapperTest {

    @Autowired
    private TrainQaImportMainMapper trainQaImportMainMapper;

    private static final Long TEST_TEAM_ID_1 = 1L;
    private static final Long TEST_TEAM_ID_2 = 2L;
    private static final String TEST_USER_ID = "93";

    @BeforeEach
    public void setUp() {
        // 清理测试数据
        // 由于使用了@Transactional，每个测试方法结束后会自动回滚
    }

    /**
     * 测试插入主表记录并获取自增ID
     */
    @Test
    public void testInsert() {
        // 准备测试数据
        TrainQaImportMain mainRecord = createTestMainRecord("测试知识库1", TEST_TEAM_ID_1);
        
        // 执行插入
        int result = trainQaImportMainMapper.insert(mainRecord);
        
        // 验证结果
        assertEquals(1, result);
        assertNotNull(mainRecord.getId());
        assertTrue(mainRecord.getId() > 0);
        
        System.out.println("插入成功，生成的ID: " + mainRecord.getId());
        System.out.println("批次号: " + mainRecord.getBatchNo());
    }

    /**
     * 重点测试：根据团队ID查询主表记录列表（分页）
     */
    @Test
    public void testSelectByTeamId_Pagination() {
        // 1. 先插入多条测试数据
        insertMultipleTestRecords();
        
        // 2. 测试第一页查询（团队1）
        List<TrainQaImportMain> page1 = trainQaImportMainMapper.selectByTeamId(TEST_TEAM_ID_1, 0, 2);
        assertNotNull(page1);
        assertEquals(2, page1.size());
        
        // 验证排序（应该按创建时间倒序）
        assertTrue(page1.get(0).getCreateTime().isAfter(page1.get(1).getCreateTime()) || 
                  page1.get(0).getCreateTime().isEqual(page1.get(1).getCreateTime()));
        
        System.out.println("=== 团队1第一页查询结果 ===");
        for (TrainQaImportMain record : page1) {
            System.out.println(String.format("ID: %d, 批次号: %s, 知识库名称: %s, 创建时间: %s", 
                record.getId(), record.getBatchNo(), record.getQaImName(), record.getCreateTime()));
        }
        
        // 3. 测试第二页查询（团队1）
        List<TrainQaImportMain> page2 = trainQaImportMainMapper.selectByTeamId(TEST_TEAM_ID_1, 2, 2);
        assertNotNull(page2);
        assertEquals(1, page2.size()); // 团队1总共3条记录，第二页应该只有1条
        
        System.out.println("=== 团队1第二页查询结果 ===");
        for (TrainQaImportMain record : page2) {
            System.out.println(String.format("ID: %d, 批次号: %s, 知识库名称: %s, 创建时间: %s", 
                record.getId(), record.getBatchNo(), record.getQaImName(), record.getCreateTime()));
        }
        
        // 4. 测试团队隔离（团队2）
        List<TrainQaImportMain> team2Records = trainQaImportMainMapper.selectByTeamId(TEST_TEAM_ID_2, 0, 10);
        assertNotNull(team2Records);
        assertEquals(2, team2Records.size()); // 团队2应该有2条记录
        
        System.out.println("=== 团队2查询结果 ===");
        for (TrainQaImportMain record : team2Records) {
            System.out.println(String.format("ID: %d, 批次号: %s, 知识库名称: %s, 团队ID: %d", 
                record.getId(), record.getBatchNo(), record.getQaImName(), record.getTeamId()));
            assertEquals(TEST_TEAM_ID_2, record.getTeamId()); // 验证团队隔离
        }
    }

    /**
     * 测试无分页查询（limit为null）
     */
    @Test
    public void testSelectByTeamId_NoLimit() {
        // 插入测试数据
        insertMultipleTestRecords();
        
        // 查询所有记录（不分页）
        List<TrainQaImportMain> allRecords = trainQaImportMainMapper.selectByTeamId(TEST_TEAM_ID_1, null, null);
        assertNotNull(allRecords);
        assertEquals(3, allRecords.size()); // 团队1应该有3条记录
        
        System.out.println("=== 团队1所有记录（无分页） ===");
        for (TrainQaImportMain record : allRecords) {
            System.out.println(String.format("ID: %d, 知识库名称: %s, 创建时间: %s", 
                record.getId(), record.getQaImName(), record.getCreateTime()));
        }
    }

    /**
     * 测试空结果查询
     */
    @Test
    public void testSelectByTeamId_EmptyResult() {
        // 查询不存在的团队
        List<TrainQaImportMain> emptyResult = trainQaImportMainMapper.selectByTeamId(999L, 0, 10);
        assertNotNull(emptyResult);
        assertTrue(emptyResult.isEmpty());
        
        System.out.println("不存在团队的查询结果为空: " + emptyResult.size());
    }

    /**
     * 测试统计功能
     */
    @Test
    public void testCountByTeamId() {
        // 插入测试数据
        insertMultipleTestRecords();
        
        // 统计团队1的记录数
        int count1 = trainQaImportMainMapper.countByTeamId(TEST_TEAM_ID_1);
        assertEquals(3, count1);
        
        // 统计团队2的记录数
        int count2 = trainQaImportMainMapper.countByTeamId(TEST_TEAM_ID_2);
        assertEquals(2, count2);
        
        // 统计不存在团队的记录数
        int count3 = trainQaImportMainMapper.countByTeamId(999L);
        assertEquals(0, count3);
        
        System.out.println(String.format("统计结果 - 团队1: %d条, 团队2: %d条, 不存在团队: %d条", 
            count1, count2, count3));
    }

    /**
     * 测试根据批次号查询
     */
    @Test
    public void testSelectByBatchNo() {
        // 插入测试数据
        TrainQaImportMain mainRecord = createTestMainRecord("批次号测试", TEST_TEAM_ID_1);
        trainQaImportMainMapper.insert(mainRecord);
        String batchNo = mainRecord.getBatchNo();
        
        // 根据批次号查询
        TrainQaImportMain found = trainQaImportMainMapper.selectByBatchNo(batchNo, TEST_TEAM_ID_1);
        assertNotNull(found);
        assertEquals(batchNo, found.getBatchNo());
        assertEquals("批次号测试", found.getQaImName());
        
        // 测试团队隔离
        TrainQaImportMain notFound = trainQaImportMainMapper.selectByBatchNo(batchNo, TEST_TEAM_ID_2);
        assertNull(notFound);
        
        System.out.println(String.format("批次号查询成功 - 批次号: %s, 知识库名称: %s", 
            found.getBatchNo(), found.getQaImName()));
    }

    /**
     * 测试更新操作
     */
    @Test
    public void testUpdateById() {
        // 插入测试数据
        TrainQaImportMain mainRecord = createTestMainRecord("原始名称", TEST_TEAM_ID_1);
        trainQaImportMainMapper.insert(mainRecord);
        Long id = mainRecord.getId();
        
        // 更新记录
        TrainQaImportMain updateRecord = new TrainQaImportMain();
        updateRecord.setId(id);
        updateRecord.setQaImName("更新后名称");
        updateRecord.setQaImDesc("更新后描述");
        updateRecord.setTeamId(TEST_TEAM_ID_1);
        updateRecord.setUpdater("updater_test");
        
        int updateResult = trainQaImportMainMapper.updateById(updateRecord);
        assertEquals(1, updateResult);
        
        // 验证更新结果
        TrainQaImportMain updated = trainQaImportMainMapper.selectById(id);
        assertNotNull(updated);
        assertEquals("更新后名称", updated.getQaImName());
        assertEquals("更新后描述", updated.getQaImDesc());
        assertEquals("updater_test", updated.getUpdater());
        
        System.out.println(String.format("更新成功 - 新名称: %s, 新描述: %s", 
            updated.getQaImName(), updated.getQaImDesc()));
    }

    /**
     * 测试删除操作
     */
    @Test
    public void testDeleteById() {
        // 插入测试数据
        TrainQaImportMain mainRecord = createTestMainRecord("待删除记录", TEST_TEAM_ID_1);
        trainQaImportMainMapper.insert(mainRecord);
        Long id = mainRecord.getId();
        
        // 验证记录存在
        TrainQaImportMain found = trainQaImportMainMapper.selectById(id);
        assertNotNull(found);
        
        // 删除记录
        int deleteResult = trainQaImportMainMapper.deleteById(id);
        assertEquals(1, deleteResult);
        
        // 验证记录已删除
        TrainQaImportMain notFound = trainQaImportMainMapper.selectById(id);
        assertNull(notFound);
        
        System.out.println("删除操作成功，记录ID: " + id);
    }

    /**
     * 测试批量删除操作
     */
    @Test
    public void testDeleteByIds() {
        // 插入多条测试数据
        TrainQaImportMain record1 = createTestMainRecord("批量删除1", TEST_TEAM_ID_1);
        TrainQaImportMain record2 = createTestMainRecord("批量删除2", TEST_TEAM_ID_1);
        trainQaImportMainMapper.insert(record1);
        trainQaImportMainMapper.insert(record2);
        
        List<Long> ids = Arrays.asList(record1.getId(), record2.getId());
        
        // 批量删除
        int deleteResult = trainQaImportMainMapper.deleteByIds(ids);
        assertEquals(2, deleteResult);
        
        // 验证记录已删除
        TrainQaImportMain notFound1 = trainQaImportMainMapper.selectById(record1.getId());
        TrainQaImportMain notFound2 = trainQaImportMainMapper.selectById(record2.getId());
        assertNull(notFound1);
        assertNull(notFound2);
        
        System.out.println("批量删除成功，删除记录数: " + deleteResult);
    }

    /**
     * 插入多条测试记录
     */
    private void insertMultipleTestRecords() {
        // 团队1的记录
        trainQaImportMainMapper.insert(createTestMainRecord("知识库A", TEST_TEAM_ID_1));
        trainQaImportMainMapper.insert(createTestMainRecord("知识库B", TEST_TEAM_ID_1));
        trainQaImportMainMapper.insert(createTestMainRecord("知识库C", TEST_TEAM_ID_1));
        
        // 团队2的记录
        trainQaImportMainMapper.insert(createTestMainRecord("知识库X", TEST_TEAM_ID_2));
        trainQaImportMainMapper.insert(createTestMainRecord("知识库Y", TEST_TEAM_ID_2));
    }

    /**
     * 创建测试用的主表记录
     */
    private TrainQaImportMain createTestMainRecord(String name, Long teamId) {
        String batchNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        
        TrainQaImportMain mainRecord = new TrainQaImportMain();
        mainRecord.setBatchNo(batchNo);
        mainRecord.setQaImName(name);
        mainRecord.setQaImDesc(name + "描述");
        mainRecord.setTeamId(teamId);
        mainRecord.setCreator(TEST_USER_ID);
        mainRecord.setUpdater(TEST_USER_ID);
        
        return mainRecord;
    }
}
