package com.yiyi.ai_train_playground.mapper.task;

import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaRdmMapper单元测试
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainQaRdmMapperTest {

    @Autowired
    private TrainQaRdmMapper trainQaRdmMapper;

    /**
     * 测试selectByUUID方法 - 不带团队ID过滤
     */
    @Test
    public void testSelectByUUID_WithoutTeamFilter() {
        // 准备测试数据
        String testUuid = "test-uuid-12345";
        Long teamId = 1L;

        // 创建TrainQaRdm对象并插入
        TrainQaRdm trainQaRdm = new TrainQaRdm();
        trainQaRdm.setUuid(testUuid);
        trainQaRdm.setQuestion("测试问题");
        trainQaRdm.setAnswer("测试答案");
        trainQaRdm.setQuesNo("Q001");
        trainQaRdm.setTeamId(teamId);
        trainQaRdm.setCreator("test-user");
        trainQaRdm.setUpdater("test-user");
        trainQaRdm.setVersion(0L);
        trainQaRdm.setCreateTime(LocalDateTime.now());
        trainQaRdm.setUpdateTime(LocalDateTime.now());

        // 插入测试数据
        int insertResult = trainQaRdmMapper.insert(trainQaRdm);
        assertEquals(1, insertResult, "插入应该成功");
        assertNotNull(trainQaRdm.getId(), "插入后ID不应为空");

        // 测试selectByUUID方法（不带团队ID过滤）
        TrainQaRdm result = trainQaRdmMapper.selectByUUID(testUuid);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertEquals(testUuid, result.getUuid(), "UUID应匹配");
        assertEquals("测试问题", result.getQuestion(), "问题内容应匹配");
        assertEquals("测试答案", result.getAnswer(), "答案内容应匹配");
        assertEquals("Q001", result.getQuesNo(), "问题编号应匹配");
        assertEquals(teamId, result.getTeamId(), "团队ID应匹配");
    }

    /**
     * 测试selectByUUIDWithTeam方法 - 带团队ID过滤
     */
    @Test
    public void testSelectByUUIDWithTeam_WithTeamFilter() {
        // 准备测试数据
        String testUuid = "test-uuid-with-team-67890";
        Long teamId = 2L;
        Long wrongTeamId = 999L;

        // 创建TrainQaRdm对象并插入
        TrainQaRdm trainQaRdm = new TrainQaRdm();
        trainQaRdm.setUuid(testUuid);
        trainQaRdm.setQuestion("团队测试问题");
        trainQaRdm.setAnswer("团队测试答案");
        trainQaRdm.setQuesNo("Q002");
        trainQaRdm.setTeamId(teamId);
        trainQaRdm.setCreator("team-user");
        trainQaRdm.setUpdater("team-user");
        trainQaRdm.setVersion(0L);
        trainQaRdm.setCreateTime(LocalDateTime.now());
        trainQaRdm.setUpdateTime(LocalDateTime.now());

        // 插入测试数据
        int insertResult = trainQaRdmMapper.insert(trainQaRdm);
        assertEquals(1, insertResult, "插入应该成功");

        // 测试selectByUUIDWithTeam方法（正确的团队ID）
        TrainQaRdm correctResult = trainQaRdmMapper.selectByUUIDWithTeam(testUuid, teamId);
        assertNotNull(correctResult, "使用正确团队ID应该能查询到结果");
        assertEquals(testUuid, correctResult.getUuid(), "UUID应匹配");
        assertEquals(teamId, correctResult.getTeamId(), "团队ID应匹配");

        // 测试selectByUUIDWithTeam方法（错误的团队ID）
        TrainQaRdm wrongResult = trainQaRdmMapper.selectByUUIDWithTeam(testUuid, wrongTeamId);
        assertNull(wrongResult, "使用错误团队ID应该查询不到结果");
    }

    /**
     * 测试selectByUUID方法 - 不存在的UUID
     */
    @Test
    public void testSelectByUUID_NotFound() {
        String nonExistentUuid = "non-existent-uuid-99999";

        // 测试不存在的UUID
        TrainQaRdm result = trainQaRdmMapper.selectByUUID(nonExistentUuid);
        assertNull(result, "不存在的UUID应该返回null");
    }

    /**
     * 测试selectByUUIDWithTeam方法 - 不存在的UUID
     */
    @Test
    public void testSelectByUUIDWithTeam_NotFound() {
        String nonExistentUuid = "non-existent-uuid-with-team-99999";
        Long teamId = 1L;

        // 测试不存在的UUID
        TrainQaRdm result = trainQaRdmMapper.selectByUUIDWithTeam(nonExistentUuid, teamId);
        assertNull(result, "不存在的UUID应该返回null");
    }

    /**
     * 测试selectByUUID和selectByUUIDWithTeam方法的差异
     */
    @Test
    public void testSelectByUUID_vs_SelectByUUIDWithTeam() {
        // 准备测试数据 - 两个不同团队的记录使用相同UUID（实际业务中不会发生，但用于测试差异）
        String sameUuid = "same-uuid-different-teams";
        Long teamId1 = 3L;
        Long teamId2 = 4L;

        // 创建第一个记录（团队3）
        TrainQaRdm record1 = new TrainQaRdm();
        record1.setUuid(sameUuid);
        record1.setQuestion("团队3的问题");
        record1.setAnswer("团队3的答案");
        record1.setQuesNo("Q003");
        record1.setTeamId(teamId1);
        record1.setCreator("team3-user");
        record1.setUpdater("team3-user");
        record1.setVersion(0L);
        record1.setCreateTime(LocalDateTime.now());
        record1.setUpdateTime(LocalDateTime.now());

        trainQaRdmMapper.insert(record1);

        // 使用selectByUUID方法（不过滤团队）
        TrainQaRdm resultWithoutFilter = trainQaRdmMapper.selectByUUID(sameUuid);
        assertNotNull(resultWithoutFilter, "selectByUUID应该能找到记录");
        assertEquals(sameUuid, resultWithoutFilter.getUuid(), "UUID应匹配");

        // 使用selectByUUIDWithTeam方法（团队3）
        TrainQaRdm resultWithTeam1 = trainQaRdmMapper.selectByUUIDWithTeam(sameUuid, teamId1);
        assertNotNull(resultWithTeam1, "selectByUUIDWithTeam应该能找到团队3的记录");
        assertEquals(teamId1, resultWithTeam1.getTeamId(), "应该返回团队3的记录");

        // 使用selectByUUIDWithTeam方法（团队4，应该找不到）
        TrainQaRdm resultWithTeam2 = trainQaRdmMapper.selectByUUIDWithTeam(sameUuid, teamId2);
        assertNull(resultWithTeam2, "selectByUUIDWithTeam应该找不到团队4的记录");
    }
}