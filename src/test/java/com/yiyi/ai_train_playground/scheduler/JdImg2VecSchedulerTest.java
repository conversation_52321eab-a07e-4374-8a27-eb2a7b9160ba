package com.yiyi.ai_train_playground.scheduler;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProdImages;
import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.enums.JdSyncStatus;
import com.yiyi.ai_train_playground.service.TrainTeamService;
import com.yiyi.ai_train_playground.service.jd.TrainJdProdImagesService;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * JdImg2VecScheduler 测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class JdImg2VecSchedulerTest {

    @Autowired
    private JdImg2VecScheduler jdImg2VecScheduler;

    @Autowired
    private TrainTeamService trainTeamService;

    @Autowired
    private TrainJdProductsService trainJdProductsService;

    @Autowired
    private TrainJdProdImagesService trainJdProdImagesService;

    @Test
    @DisplayName("测试获取所有团队")
    void testGetAllTeams() {
        log.info("=== 测试获取所有团队 ===");

        // 获取所有团队
        List<TrainTeam> teams = trainTeamService.findAll();
        
        assertThat(teams).isNotNull();
        log.info("获取到 {} 个团队", teams.size());
        
        for (TrainTeam team : teams) {
            log.info("团队信息: id={}, name={}", team.getId(), team.getName());
        }
    }

    @Test
    @DisplayName("测试处理团队商品（模拟数据）")
    void testProcessTeamProductsWithMockData() {
        log.info("=== 测试处理团队商品（模拟数据） ===");

        // 创建测试商品
        TrainJdProducts testProduct = createTestProduct();
        
        // 插入测试商品
        boolean insertResult = trainJdProductsService.insert(testProduct);
        assertThat(insertResult).isTrue();
        log.info("插入测试商品成功: id={}", testProduct.getId());

        // 创建测试图片
        TrainJdProdImages testImage = createTestImage(testProduct);
        
        // 插入测试图片
        int imageInsertResult = trainJdProdImagesService.insert(testImage);
        assertThat(imageInsertResult).isGreaterThan(0);
        log.info("插入测试图片成功: id={}", testImage.getId());

        // 验证数据状态
        List<TrainJdProducts> unSyncProducts = trainJdProductsService.findByTeamIdAndSyncStatusWithPagination(
                testProduct.getTeamId(), JdSyncStatus.UN_SYNC.getCode(), null, 0, 10);
        assertThat(unSyncProducts).isNotEmpty();
        log.info("找到 {} 个未同步商品", unSyncProducts.size());

        List<TrainJdProdImages> unSyncImages = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                testProduct.getTeamId(), JdSyncStatus.UN_SYNC.getCode(), testProduct.getWareId(), 0, 10);
        assertThat(unSyncImages).isNotEmpty();
        log.info("找到 {} 个未同步图片", unSyncImages.size());
    }

    @Test
    @DisplayName("测试定时任务主方法（不实际执行）")
    void testSchedulerMainMethod() {
        log.info("=== 测试定时任务主方法（不实际执行） ===");

        try {
            // 注意：这里只是测试方法不抛异常，不会实际执行完整的处理流程
            // 因为可能需要外部服务（如豆包API、向量数据库等）
            
            // 验证定时任务对象存在
            assertThat(jdImg2VecScheduler).isNotNull();
            log.info("定时任务对象创建成功");
            
            // 可以测试一些基础方法
            List<TrainTeam> teams = trainTeamService.findAll();
            log.info("定时任务可以正常获取团队列表: {} 个团队", teams.size());
            
        } catch (Exception e) {
            log.error("定时任务测试失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试线程池配置")
    void testThreadPoolConfiguration() {
        log.info("=== 测试线程池配置 ===");

        // 验证定时任务对象包含必要的依赖
        assertThat(jdImg2VecScheduler).isNotNull();
        
        // 这里主要验证Spring能够正确注入所有依赖
        log.info("线程池配置测试通过");
    }

    @Test
    @DisplayName("测试状态枚举")
    void testSyncStatusEnum() {
        log.info("=== 测试状态枚举 ===");

        // 验证状态枚举值
        assertThat(JdSyncStatus.UN_SYNC.getCode()).isEqualTo(0);
        assertThat(JdSyncStatus.SYNCING.getCode()).isEqualTo(1);
        assertThat(JdSyncStatus.SYNCED.getCode()).isEqualTo(2);
        
        log.info("状态枚举测试通过: UN_SYNC={}, SYNCING={}, SYNCED={}", 
                JdSyncStatus.UN_SYNC.getCode(), 
                JdSyncStatus.SYNCING.getCode(), 
                JdSyncStatus.SYNCED.getCode());
    }

    /**
     * 创建测试商品
     */
    private TrainJdProducts createTestProduct() {
        TrainJdProducts product = new TrainJdProducts();
        product.setTeamId(1L);
        product.setWareId(12345L);
        product.setShopId(67890L);
        product.setTitle("测试商品");
        product.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());
        product.setCreator("test-scheduler");
        product.setUpdater("test-scheduler");
        product.setCreateTime(LocalDateTime.now());
        product.setUpdateTime(LocalDateTime.now());
        product.setVersion(0L);
        return product;
    }

    /**
     * 创建测试图片
     */
    private TrainJdProdImages createTestImage(TrainJdProducts product) {
        TrainJdProdImages image = new TrainJdProdImages();
        image.setTeamId(product.getTeamId());
        image.setJdProdId(product.getWareId());
        image.setImgUrl("https://example.com/test-image.jpg");
        image.setSyncStatus(JdSyncStatus.UN_SYNC.getCode());
        image.setCreator("test-scheduler");
        image.setUpdater("test-scheduler");
        image.setCreateTime(LocalDateTime.now());
        image.setUpdateTime(LocalDateTime.now());
        image.setVersion(0L);
        return image;
    }
}
