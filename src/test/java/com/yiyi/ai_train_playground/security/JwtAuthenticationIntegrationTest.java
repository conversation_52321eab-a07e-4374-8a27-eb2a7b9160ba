package com.yiyi.ai_train_playground.security;

import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper;
import com.yiyi.ai_train_playground.service.staff.StaffAuthService;
import com.yiyi.ai_train_playground.dto.staff.StaffLoginRequest;
import com.yiyi.ai_train_playground.dto.staff.StaffLoginResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT认证系统集成测试
 * 测试员工登录JWT和认证过滤器的完整流程
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class JwtAuthenticationIntegrationTest {

    @Autowired
    private StaffAuthService staffAuthService;

    @Autowired
    private StaffUserDetailsService staffUserDetailsService;

    @Autowired
    private TrainStaffMapper trainStaffMapper;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    private static final String TEST_USERNAME = "jwttest_user";
    private static final String TEST_PASSWORD = "test123456";
    private Long testStaffId;

    @BeforeEach
    void setUp() {
        // 创建测试员工数据
        TrainStaff testStaff = new TrainStaff();
        testStaff.setUserId(9999L);
        testStaff.setUsername(TEST_USERNAME);
        testStaff.setPasswordHash(passwordEncoder.encode(TEST_PASSWORD));
        testStaff.setDisplayName("JWT测试员工");
        testStaff.setIsLocked(false);
        testStaff.setFailedAttempts(0);
        testStaff.setTeamId(1L);
        testStaff.setCreateTime(LocalDateTime.now());
        testStaff.setUpdateTime(LocalDateTime.now());
        testStaff.setCreator("test");
        testStaff.setUpdater("test");
        testStaff.setVersion(0L);

        // 删除可能存在的测试数据
        TrainStaff existingStaff = trainStaffMapper.selectByUsernameForLogin(TEST_USERNAME);
        if (existingStaff != null) {
            trainStaffMapper.deleteById(existingStaff.getId());
        }

        // 插入测试员工
        trainStaffMapper.insert(testStaff);
        testStaffId = testStaff.getId();
    }

    @Test
    void testStaffLoginAndJwtAuthentication() {
        // 1. 测试员工登录生成JWT
        StaffLoginRequest loginRequest = new StaffLoginRequest();
        loginRequest.setUsername(TEST_USERNAME);
        loginRequest.setPassword(TEST_PASSWORD);
        loginRequest.setRememberMe(false);

        StaffLoginResponse response = staffAuthService.login(loginRequest);
        
        assertNotNull(response);
        assertNotNull(response.getToken());
        assertEquals(TEST_USERNAME, response.getUsername());
        assertEquals(testStaffId, response.getStaffId());
        assertNotNull(response.getTeamId());

        // 2. 测试StaffUserDetailsService能够根据员工ID加载用户详情
        UserDetails userDetails = staffUserDetailsService.loadUserByStaffId(response.getStaffId());
        
        assertNotNull(userDetails);
        assertEquals(TEST_USERNAME, userDetails.getUsername());
        assertTrue(userDetails.isEnabled());
        assertTrue(userDetails.isAccountNonLocked());

        // 3. 验证用户详情中的自定义信息
        assertTrue(userDetails instanceof CustomUserDetails);
        CustomUserDetails customUserDetails = (CustomUserDetails) userDetails;
        assertEquals(response.getStaffId(), customUserDetails.getUserId());
        assertEquals(response.getTeamId(), customUserDetails.getTeamId());
    }

    @Test
    void testStaffUserDetailsServiceLoadByUsername() {
        // 测试通过用户名加载员工信息
        UserDetails userDetails = staffUserDetailsService.loadUserByUsername(TEST_USERNAME);
        
        assertNotNull(userDetails);
        assertEquals(TEST_USERNAME, userDetails.getUsername());
        assertTrue(userDetails.isEnabled());

        // 验证这是员工登录的用户详情
        assertTrue(userDetails instanceof CustomUserDetails);
        CustomUserDetails customUserDetails = (CustomUserDetails) userDetails;
        assertEquals(testStaffId, customUserDetails.getUserId());
        assertNotNull(customUserDetails.getTeamId());
    }

    @Test
    void testStaffUserDetailsServiceNotFound() {
        // 测试加载不存在的员工
        assertThrows(org.springframework.security.core.userdetails.UsernameNotFoundException.class, () -> {
            staffUserDetailsService.loadUserByUsername("nonexistent_staff");
        });

        assertThrows(org.springframework.security.core.userdetails.UsernameNotFoundException.class, () -> {
            staffUserDetailsService.loadUserByStaffId(99999L);
        });
    }
}