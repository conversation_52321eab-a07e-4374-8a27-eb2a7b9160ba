package com.yiyi.ai_train_playground.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.impl.BigModelManager;
import com.yiyi.ai_train_playground.service.impl.DoubaoBigModelServiceImpl;
import com.yiyi.ai_train_playground.service.impl.RedisService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BigModelManager重构后的单元测试
 * 测试重构后使用CacheManager的各种场景
 */
@Slf4j
@SpringBootTest
@DisplayName("BigModelManager重构单元测试")
public class BigModelManagerRefactoredTest {

    @Mock
    private SuperBigModelInterface bigModelService;

    @Mock
    private DoubaoBigModelServiceImpl doubaoBigModelService;

    @Mock
    private TrainTeamMapper trainTeamMapper;

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private RedisService redisService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private VectorSearchService vectorSearchService;

    private BigModelManager bigModelManager;

    private static final String TEST_TOKEN = "test.jwt.token";
    private static final Long TEST_USER_ID = 12345L;
    private static final Long TEST_TEAM_ID = 67890L;
    private static final String TEST_SYSTEM_PROMPT = "你模拟一个客户，与客服进行对话";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        bigModelManager = new BigModelManager();
        
        // 使用反射注入Mock依赖
        try {
            var bigModelServiceField = BigModelManager.class.getDeclaredField("bigModelService");
            bigModelServiceField.setAccessible(true);
            bigModelServiceField.set(bigModelManager, bigModelService);

            var doubaoBigModelServiceField = BigModelManager.class.getDeclaredField("doubaoBigModelService");
            doubaoBigModelServiceField.setAccessible(true);
            doubaoBigModelServiceField.set(bigModelManager, doubaoBigModelService);

            var trainTeamMapperField = BigModelManager.class.getDeclaredField("trainTeamMapper");
            trainTeamMapperField.setAccessible(true);
            trainTeamMapperField.set(bigModelManager, trainTeamMapper);

            var jwtUtilField = BigModelManager.class.getDeclaredField("jwtUtil");
            jwtUtilField.setAccessible(true);
            jwtUtilField.set(bigModelManager, jwtUtil);

            var redisServiceField = BigModelManager.class.getDeclaredField("redisService");
            redisServiceField.setAccessible(true);
            redisServiceField.set(bigModelManager, redisService);

            var objectMapperField = BigModelManager.class.getDeclaredField("objectMapper");
            objectMapperField.setAccessible(true);
            objectMapperField.set(bigModelManager, objectMapper);

            var redisTemplateField = BigModelManager.class.getDeclaredField("redisTemplate");
            redisTemplateField.setAccessible(true);
            redisTemplateField.set(bigModelManager, redisTemplate);

            var cacheManagerField = BigModelManager.class.getDeclaredField("cacheManager");
            cacheManagerField.setAccessible(true);
            cacheManagerField.set(bigModelManager, cacheManager);

            var vectorSearchServiceField = BigModelManager.class.getDeclaredField("vectorSearchService");
            vectorSearchServiceField.setAccessible(true);
            vectorSearchServiceField.set(bigModelManager, vectorSearchService);

        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mocks", e);
        }
    }

    @Test
    @DisplayName("测试会话初始化使用CacheManager存储")
    void testInitSessionUsesCacheManager() {
        log.info("=== 测试1: 会话初始化使用CacheManager存储 ===");

        // Arrange
        String sceneName = "trialOne";
        String servicerId = TEST_USER_ID.toString();
        
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID);
        when(jwtUtil.getTeamIdFromToken(TEST_TOKEN)).thenReturn(TEST_TEAM_ID);
        
        TrainTeam mockTeam = new TrainTeam();
        mockTeam.setName("测试团队");
        when(trainTeamMapper.findById(TEST_TEAM_ID)).thenReturn(mockTeam);
        
        ContextResult mockContextResult = new ContextResult();
        mockContextResult.setId("ctx-test-12345");
        when(doubaoBigModelService.generateContextId(anyString())).thenReturn(mockContextResult);
        
        when(doubaoBigModelService.ntnsWithCtx(any(), anyString())).thenReturn("你好，我想咨询产品信息");

        // Act
        Map<String, Object> result = bigModelManager.initSession(
            sceneName, servicerId, TEST_TOKEN, false, false, TEST_SYSTEM_PROMPT, "product123",0
        );

        // Assert
        assertNotNull(result, "初始化结果不应为null");
        assertTrue(result.containsKey("sessionId"), "结果应包含sessionId");
        assertTrue(result.containsKey("robotName"), "结果应包含robotName");
        assertTrue(result.containsKey("firstMessage"), "结果应包含firstMessage");

        // 验证CacheManager被调用来存储聊天历史
        verify(cacheManager).put(startsWith("chatlog:"), any(List.class), eq(24L), eq(TimeUnit.HOURS));
        
        // 验证CacheManager被调用来存储会话配置
        verify(cacheManager).putHash(startsWith("session:"), any(Map.class), eq(24L), eq(TimeUnit.HOURS));

        log.info("✅ 会话初始化CacheManager存储测试通过");
    }

    @Test
    @DisplayName("测试批量初始化多个会话")
    void testInitMultipleRobotsUsesCacheManager() {
        log.info("=== 测试2: 批量初始化多个会话 ===");

        // Arrange
        String sceneName = "trialFour";
        String[] systemPrompts = {
            "你是买洗碗机的客户",
            "你是买冰箱的客户"
        };
        
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID);
        when(jwtUtil.getTeamIdFromToken(TEST_TOKEN)).thenReturn(TEST_TEAM_ID);
        
        TrainTeam mockTeam = new TrainTeam();
        mockTeam.setName("测试团队");
        when(trainTeamMapper.findById(TEST_TEAM_ID)).thenReturn(mockTeam);
        
        ContextResult mockContextResult = new ContextResult();
        mockContextResult.setId("ctx-test-12345");
        when(doubaoBigModelService.generateContextId(anyString())).thenReturn(mockContextResult);
        
        when(doubaoBigModelService.ntnsWithCtx(any(), anyString())).thenReturn("客户初始消息");

        // Act
        List<Map<String, Object>> results = bigModelManager.initMultipleRobots(
            sceneName, null, TEST_TOKEN, false, false, systemPrompts, 2
        );

        // Assert
        assertNotNull(results, "结果列表不应为null");
        assertEquals(2, results.size(), "应该创建2个机器人会话");

        // 验证CacheManager被调用了4次（2个会话 × 2种操作：聊天历史+会话配置）
        verify(cacheManager, times(2)).put(startsWith("chatlog:"), any(List.class), eq(24L), eq(TimeUnit.HOURS));
        verify(cacheManager, times(2)).putHash(startsWith("session:"), any(Map.class), eq(24L), eq(TimeUnit.HOURS));

        log.info("✅ 批量初始化多个会话CacheManager测试通过");
    }

    @Test
    @DisplayName("测试会话清理使用CacheManager删除")
    void testClearSessionUsesCacheManager() {
        log.info("=== 测试3: 会话清理使用CacheManager删除 ===");

        // Arrange
        String sessionId = "test-session-123";
        when(cacheManager.delete(anyString())).thenReturn(true);

        // Act
        bigModelManager.clearSession(sessionId);

        // Assert
        // 验证删除了聊天记录
        verify(cacheManager).delete("chatlog:" + sessionId);
        // 验证删除了会话配置
        verify(cacheManager).delete("session:" + sessionId);

        log.info("✅ 会话清理CacheManager删除测试通过");
    }

    @Test
    @DisplayName("测试消息处理使用CacheManager获取会话配置")
    void testHandlerAndResponseUsesCacheManagerForConfig() {
        log.info("=== 测试4: 消息处理使用CacheManager获取会话配置 ===");

        // Arrange
        String sessionId = "test-session-456";
        String userMessage = "我想了解这个产品的价格";

        // Mock会话存在检查
        when(cacheManager.exists("session:" + sessionId)).thenReturn(true);

        // Mock会话配置
        Map<Object, Object> sessionConfig = new HashMap<>();
        sessionConfig.put("teamId", TEST_TEAM_ID.toString());
        sessionConfig.put("externalProductId", "product123");
        sessionConfig.put("isThinking", false);
        sessionConfig.put("isStreaming", false);
        sessionConfig.put("sessionContextId", "ctx-test-789");
        when(cacheManager.getHash("session:" + sessionId)).thenReturn(sessionConfig);

        // Mock聊天历史
        List<ChatMessage> chatHistory = Arrays.asList(
            ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("你好，我是AI助手").build()
        );
        when(cacheManager.get("chatlog:" + sessionId)).thenReturn(chatHistory);

        // Mock向量搜索
        when(vectorSearchService.searchByText(anyString(), anyString(), anyString(), anyInt(), anyInt(), anyFloat()))
            .thenReturn("产品相关信息");

        // Mock AI模型调用
        when(doubaoBigModelService.ntnsWithCtx(any(), anyString())).thenReturn("AI回复消息");

        // Act
        Flux<String> response = bigModelManager.handlerAndResponse(sessionId, userMessage);

        // Assert
        assertNotNull(response, "响应不应为null");
        
        // 验证从CacheManager获取会话配置
        verify(cacheManager).getHash("session:" + sessionId);
        
        // 验证从CacheManager获取聊天历史
        verify(cacheManager).get("chatlog:" + sessionId);
        
        // 验证消息处理后更新聊天历史
        verify(cacheManager, times(2)).put(eq("chatlog:" + sessionId), any(List.class), 
                                          eq(24L), eq(TimeUnit.HOURS));

        log.info("✅ 消息处理CacheManager配置获取测试通过");
    }

    @Test
    @DisplayName("测试会话不存在的异常情况")
    void testHandlerAndResponseWithNonExistentSession() {
        log.info("=== 测试5: 会话不存在的异常情况 ===");

        // Arrange
        String sessionId = "non-existent-session";
        String userMessage = "测试消息";
        
        when(cacheManager.exists("session:" + sessionId)).thenReturn(false);

        // Act & Assert
        Flux<String> response = bigModelManager.handlerAndResponse(sessionId, userMessage);
        
        // 验证返回错误流
        assertNotNull(response, "响应不应为null");
        
        // 验证只检查了会话存在性，没有进行其他缓存操作
        verify(cacheManager).exists("session:" + sessionId);
        verify(cacheManager, never()).getHash(anyString());
        verify(cacheManager, never()).get(anyString());

        log.info("✅ 会话不存在异常情况测试通过");
    }

    @Test
    @DisplayName("测试CacheManager异常时的容错处理")
    void testCacheManagerExceptionHandling() {
        log.info("=== 测试6: CacheManager异常时的容错处理 ===");

        // Arrange
        String sessionId = "test-session-exception";
        
        // Mock CacheManager抛出异常
        when(cacheManager.delete(anyString())).thenThrow(new RuntimeException("Redis连接失败"));

        // Act - 清理会话应该不抛出异常
        assertDoesNotThrow(() -> bigModelManager.clearSession(sessionId));

        // Assert
        verify(cacheManager, times(2)).delete(anyString()); // 尝试删除2个key

        log.info("✅ CacheManager异常容错处理测试通过");
    }

    @Test
    @DisplayName("测试无向量搜索的消息处理流程")
    void testHandlerAndResponseWithoutVectorSearch() {
        log.info("=== 测试7: 无向量搜索的消息处理流程 ===");

        // Arrange
        String sessionId = "test-session-no-vector";
        String userMessage = "普通对话消息";

        when(cacheManager.exists("session:" + sessionId)).thenReturn(true);

        // Mock会话配置（缺少teamId或externalProductId）
        Map<Object, Object> sessionConfig = new HashMap<>();
        sessionConfig.put("isThinking", false);
        sessionConfig.put("isStreaming", false);
        sessionConfig.put("sessionContextId", "ctx-test-no-vector");
        // 故意不设置teamId和externalProductId
        when(cacheManager.getHash("session:" + sessionId)).thenReturn(sessionConfig);

        // Mock聊天历史
        when(cacheManager.get("chatlog:" + sessionId)).thenReturn(new ArrayList<>());

        // Mock AI模型调用
        when(doubaoBigModelService.ntnsWithCtx(any(), anyString())).thenReturn("普通AI回复");

        // Act
        Flux<String> response = bigModelManager.handlerAndResponse(sessionId, userMessage);

        // Assert
        assertNotNull(response, "响应不应为null");
        
        // 验证没有调用向量搜索（因为缺少必要参数）
        verify(vectorSearchService, never()).searchByText(anyString(), anyString(), anyString(), anyInt(), anyInt(), anyFloat());
        
        // 验证仍然正常处理消息和更新缓存
        verify(cacheManager).getHash("session:" + sessionId);
        verify(cacheManager).get("chatlog:" + sessionId);

        log.info("✅ 无向量搜索的消息处理流程测试通过");
    }

    @Test
    @DisplayName("测试会话配置的完整性")
    void testSessionConfigCompleteness() {
        log.info("=== 测试8: 会话配置的完整性 ===");

        // 这个测试验证会话配置包含所有必要字段
        // 由于initSession是public方法，我们通过集成测试的方式来验证
        
        String sceneName = "formal";
        
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID);
        when(jwtUtil.getTeamIdFromToken(TEST_TOKEN)).thenReturn(TEST_TEAM_ID);
        
        TrainTeam mockTeam = new TrainTeam();
        mockTeam.setName("正式团队");
        when(trainTeamMapper.findById(TEST_TEAM_ID)).thenReturn(mockTeam);
        
        ContextResult mockContextResult = new ContextResult();
        mockContextResult.setId("ctx-formal-12345");
        when(doubaoBigModelService.generateContextId(anyString())).thenReturn(mockContextResult);
        
        when(doubaoBigModelService.ntnsWithCtx(any(), anyString())).thenReturn("正式训练开始");

        // Act
        Map<String, Object> result = bigModelManager.initSession(
            sceneName, null, TEST_TOKEN, true, true, TEST_SYSTEM_PROMPT, "formal-product",0
        );

        // Assert
        assertNotNull(result);
        
        // 使用ArgumentCaptor验证存储的配置内容
        verify(cacheManager).putHash(startsWith("session:"), argThat(config -> {
            Map<String, Object> sessionConfig = (Map<String, Object>) config;
            return sessionConfig.containsKey("sessionSystemPrompts") &&
                   sessionConfig.containsKey("sessionRobotNames") &&
                   sessionConfig.containsKey("sessionServiceNames") &&
                   sessionConfig.containsKey("isThinking") &&
                   sessionConfig.containsKey("isStreaming") &&
                   sessionConfig.containsKey("teamId") &&
                   sessionConfig.containsKey("externalProductId") &&
                   sessionConfig.containsKey("sessionContextId");
        }), eq(24L), eq(TimeUnit.HOURS));

        log.info("✅ 会话配置完整性测试通过");
    }
}