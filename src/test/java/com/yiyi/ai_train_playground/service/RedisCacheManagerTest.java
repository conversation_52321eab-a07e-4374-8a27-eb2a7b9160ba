
package com.yiyi.ai_train_playground.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RedisCacheManager集成测试
 * 连接真实的Redis实例进行测试
 */
@SpringBootTest
@ActiveProfiles("home") // 指定使用home配置文件，连接真实的Redis
@DisplayName("RedisCacheManager集成测试")
public class RedisCacheManagerTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试从真实的Redis读取未知类型的值")
    void testReadUnknownTypeFromRealRedis() {
        // Arrange
        String key = "chatlog:1de40cdd_93";
        
        // Act
        DataType type = redisTemplate.type(key);
        assertNotNull(type, "无法获取键的数据类型");

        System.out.println("Key '" + key + "' 的数据类型是: " + type.name());

        Object value = null;
        switch (type) {
            case STRING:
                value = redisTemplate.opsForValue().get(key);
                break;
            case LIST:
                value = redisTemplate.opsForList().range(key, 0, -1);
                break;
            case SET:
                value = redisTemplate.opsForSet().members(key);
                break;
            case ZSET:
                value = redisTemplate.opsForZSet().range(key, 0, -1);
                break;
            case HASH:
                value = redisTemplate.opsForHash().entries(key);
                break;
            case NONE:
                fail("Key不存在");
                break;
            default:
                fail("不支持的数据类型: " + type.name());
                break;
        }

        // Assert
        assertNotNull(value, "从Redis获取的值不应为null");

        System.out.println("成功从Redis中读取到key: '" + key + "' 的值:");
        if (value instanceof List || value instanceof Set) {
            ((Iterable<?>) value).forEach(item -> System.out.println(" - " + item.toString()));
        } else if (value instanceof Map) {
            ((Map<?, ?>) value).forEach((k, v) -> System.out.println(" - " + k + ": " + v));
        } else {
            System.out.println(value.toString());
        }
    }
}
