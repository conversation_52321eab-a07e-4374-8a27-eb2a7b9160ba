package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.service.impl.RedisDistributedLockService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis分布式锁服务测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
public class RedisDistributedLockServiceTest {
    
    @Autowired
    private RedisDistributedLockService redisDistributedLockService;
    
    private String testLockKey;
    private String testLockValue;
    
    @BeforeEach
    void setUp() {
        testLockKey = "test:lock:" + UUID.randomUUID().toString();
        testLockValue = UUID.randomUUID().toString();
    }
    
    @Test
    void testTryLockAndUnlock() {
        // 测试获取锁
        boolean lockAcquired = redisDistributedLockService.tryLock(testLockKey, testLockValue, 10);
        assertTrue(lockAcquired, "应该能够成功获取锁");
        
        // 测试锁是否存在
        assertTrue(redisDistributedLockService.isLocked(testLockKey), "锁应该存在");
        
        // 测试获取锁值
        String lockValue = redisDistributedLockService.getLockValue(testLockKey);
        assertEquals(testLockValue, lockValue, "锁值应该匹配");
        
        // 测试释放锁
        boolean unlocked = redisDistributedLockService.unlock(testLockKey, testLockValue);
        assertTrue(unlocked, "应该能够成功释放锁");
        
        // 测试锁是否已释放
        assertFalse(redisDistributedLockService.isLocked(testLockKey), "锁应该已被释放");
    }
    
    @Test
    void testTryLockTwice() {
        // 第一次获取锁
        boolean firstLock = redisDistributedLockService.tryLock(testLockKey, testLockValue, 10);
        assertTrue(firstLock, "第一次应该能够成功获取锁");
        
        // 第二次尝试获取同一个锁（应该失败）
        String anotherValue = UUID.randomUUID().toString();
        boolean secondLock = redisDistributedLockService.tryLock(testLockKey, anotherValue, 10);
        assertFalse(secondLock, "第二次获取锁应该失败");
        
        // 释放锁
        redisDistributedLockService.unlock(testLockKey, testLockValue);
    }
    
    @Test
    void testUnlockWithWrongValue() {
        // 获取锁
        redisDistributedLockService.tryLock(testLockKey, testLockValue, 10);
        
        // 尝试用错误的值释放锁
        String wrongValue = UUID.randomUUID().toString();
        boolean unlocked = redisDistributedLockService.unlock(testLockKey, wrongValue);
        assertFalse(unlocked, "用错误的值释放锁应该失败");
        
        // 锁应该仍然存在
        assertTrue(redisDistributedLockService.isLocked(testLockKey), "锁应该仍然存在");
        
        // 用正确的值释放锁
        redisDistributedLockService.unlock(testLockKey, testLockValue);
    }
    
    @Test
    void testConcurrentLock() throws InterruptedException {
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    String lockValue = UUID.randomUUID().toString();
                    boolean acquired = redisDistributedLockService.tryLock(testLockKey, lockValue, 10);
                    if (acquired) {
                        successCount.incrementAndGet();
                        // 模拟业务处理
                        Thread.sleep(100);
                        redisDistributedLockService.unlock(testLockKey, lockValue);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        // 在并发情况下，只有一个线程应该能够获取锁
        assertEquals(1, successCount.get(), "在并发情况下，只有一个线程应该能够获取锁");
    }
}
