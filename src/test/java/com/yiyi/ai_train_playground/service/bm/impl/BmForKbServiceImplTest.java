package com.yiyi.ai_train_playground.service.bm.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.service.bm.BmForKbService;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatLogService;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.consts.CONSTS;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BmForKbServiceImpl 测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-11
 */
@Slf4j
@SpringBootTest
public class BmForKbServiceImplTest {

    @Autowired
    private BmForKbService bmForKbService;

    @Autowired
    private TrainConvWinchatLogService trainConvWinchatLogService;

    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testProcessResponseContent() {
        log.info("=== 测试processResponseContent方法 ===");
        
        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;
        
        try {
            // 使用反射获取私有方法
            var method = BmForKbServiceImpl.class.getDeclaredMethod("processResponseContent", Object.class);
            method.setAccessible(true);
            
            // 测试1：普通字符串
            String simpleString = "这是一个简单的回复";
            String result1 = (String) method.invoke(serviceImpl, simpleString);
            assertEquals(simpleString, result1);
            log.info("✅ 普通字符串测试通过：{}", result1);
            
            // 测试2：JSON格式字符串（带result数组）
            String jsonString = """
                {
                  "result": [
                    "本次得分85分！良好！扣分点：回答不够详细。正确答案：产品有多种颜色可选",
                    "下一个问题：请问这款产品的保修期是多久？",
                    "下一个问题：这款产品支持哪些支付方式？",
                    "下一个问题：产品的配送范围是什么？"
                  ]
                }
                """;
            String result2 = (String) method.invoke(serviceImpl, jsonString);
            assertTrue(result2.contains("本次得分85分"));
            assertTrue(result2.contains("下一个问题"));
            log.info("✅ JSON格式测试通过：{}", result2.substring(0, Math.min(100, result2.length())) + "...");
            
            // 测试3：null值
            String result3 = (String) method.invoke(serviceImpl, (Object) null);
            assertEquals("", result3);
            log.info("✅ null值测试通过");
            
            log.info("=== processResponseContent方法测试完成 ===");
            
        } catch (Exception e) {
            log.error("测试processResponseContent方法失败", e);
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testTrainConvWinchatLogServiceIntegration() {
        log.info("=== 测试TrainConvWinchatLogService集成 ===");
        
        // 准备测试数据
        Long convDtlId = 0L;
        String sessionId = "test_session_" + System.currentTimeMillis();
        String sender = "测试发送者";
        String content = "测试消息内容";
        Long teamId = 1L;
        
        // 测试创建聊天记录
        var chatLog = trainConvWinchatLogService.createChatLog(
            convDtlId, sessionId, sender, content, 
            LocalDateTime.now(), "buyer", teamId, "system");
        
        // 验证结果
        assertNotNull(chatLog);
        assertNotNull(chatLog.getId());
        assertEquals(convDtlId, chatLog.getConvDtlId());
        assertEquals(sessionId, chatLog.getSessionId());
        assertEquals(sender, chatLog.getSender());
        assertEquals(content, chatLog.getContent());
        assertEquals("buyer", chatLog.getSenderType());
        assertEquals(teamId, chatLog.getTeamId());
        assertEquals("system", chatLog.getCreator());
        
        log.info("✅ TrainConvWinchatLogService集成测试通过, ID: {}", chatLog.getId());
        log.info("=== TrainConvWinchatLogService集成测试完成 ===");
    }

    @Test
    public void testServiceInitialization() {
        log.info("=== 测试服务初始化 ===");
        
        // 验证服务实例
        assertNotNull(bmForKbService, "BmForKbService不应为null");
        assertTrue(bmForKbService instanceof BmForKbServiceImpl, 
                "服务应为BmForKbServiceImpl实例");
        
        assertNotNull(trainConvWinchatLogService, "TrainConvWinchatLogService不应为null");
        
        log.info("✅ 服务初始化测试通过");
        log.info("BmForKbService类型: {}", bmForKbService.getClass().getSimpleName());
        log.info("TrainConvWinchatLogService类型: {}", trainConvWinchatLogService.getClass().getSimpleName());
        log.info("=== 服务初始化测试完成 ===");
    }

    @Test
    public void testGetIntentRecoSysPmt() {
        log.info("=== 测试getIntentRecoSysPmt方法 ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("getIntentRecoSysPmt");
            method.setAccessible(true);

            // 调用方法
            String result = (String) method.invoke(serviceImpl);

            // 验证结果
            assertNotNull(result, "意图识别系统提示词不应为null");
            assertFalse(result.trim().isEmpty(), "意图识别系统提示词不应为空");

            log.info("✅ 获取意图识别系统提示词测试通过");
            log.info("提示词长度: {}", result.length());
            log.info("提示词预览: {}", result.substring(0, Math.min(100, result.length())) + "...");

        } catch (Exception e) {
            log.error("测试getIntentRecoSysPmt方法失败", e);
            // 如果是因为数据库中没有相关数据导致的失败，这是正常的
            if (e.getCause() != null && e.getCause().getMessage().contains("未找到相关提示词")) {
                log.warn("⚠️ 数据库中没有意图识别提示词数据，这是正常的测试环境情况");
            } else {
                fail("测试失败: " + e.getMessage());
            }
        }

        log.info("=== getIntentRecoSysPmt方法测试完成 ===");
    }

    @Test
    public void testJudgeIntent() {
        log.info("=== 测试judgeIntent方法 ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("judgeIntent", String.class, String.class);
            method.setAccessible(true);

            // 准备测试数据
            String clientProblem = "请问这个小米手环有哪些颜色";
            String srvAnswer = "你有男朋友吗";

            // 调用方法
            String result = (String) method.invoke(serviceImpl, clientProblem, srvAnswer);

            // 验证结果
            assertNotNull(result, "意图识别结果不应为null");
            assertFalse(result.trim().isEmpty(), "意图识别结果不应为空");

            log.info("✅ 意图识别测试通过");
            log.info("客户问题: {}", clientProblem);
            log.info("客服回答: {}", srvAnswer);
            log.info("识别结果长度: {}", result.length());
            log.info("识别结果预览: {}", result.substring(0, Math.min(200, result.length())) + "...");

        } catch (Exception e) {
            log.error("测试judgeIntent方法失败", e);
            // 如果是因为依赖服务问题导致的失败，记录但不让测试失败
            if (e.getCause() != null &&
                (e.getCause().getMessage().contains("未找到相关提示词") ||
                 e.getCause().getMessage().contains("ARK_API_KEY"))) {
                log.warn("⚠️ 依赖服务不可用，这是正常的测试环境情况: {}", e.getCause().getMessage());
            } else {
                fail("测试失败: " + e.getMessage());
            }
        }

        log.info("=== judgeIntent方法测试完成 ===");
    }

    @Test
    public void testBigmodelPromptsServiceIntegration() {
        log.info("=== 测试BigmodelPromptsService集成 ===");

        // 验证服务注入
        assertNotNull(bigmodelPromptsService, "BigmodelPromptsService不应为null");

        try {
            // 测试获取意图识别提示词
            String keyword = CONSTS.DEFAULT_INTENT_REPO_KEYWORD;
            log.info("测试关键词: {}", keyword);

            var prompts = bigmodelPromptsService.getPromptsByKeyword(keyword);

            // 验证结果
            assertNotNull(prompts, "提示词列表不应为null");
            log.info("获取到{}条提示词", prompts.size());

            if (!prompts.isEmpty()) {
                log.info("第一条提示词长度: {}", prompts.get(0).length());
                log.info("第一条提示词预览: {}", prompts.get(0).substring(0, Math.min(100, prompts.get(0).length())) + "...");
            }

            log.info("✅ BigmodelPromptsService集成测试通过");

        } catch (Exception e) {
            log.error("BigmodelPromptsService集成测试失败", e);
            // 如果是因为数据库中没有数据，这是正常的
            if (e.getMessage().contains("关键词格式错误") || e.getMessage().contains("未找到")) {
                log.warn("⚠️ 数据库中没有相关数据，这是正常的测试环境情况");
            } else {
                fail("集成测试失败: " + e.getMessage());
            }
        }

        log.info("=== BigmodelPromptsService集成测试完成 ===");
    }

    @Test
    public void testProcessWithIntentRecognition() {
        log.info("=== 测试processWithIntentRecognition方法 ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("processWithIntentRecognition",
                    String.class, String.class, List.class, String.class);
            method.setAccessible(true);

            // 准备测试数据
            String searchText = "请问这个小米手环有哪些颜色";
            String userMessage = "你有男朋友吗";
            List<ChatMessage> messagesForModel = new ArrayList<>();
            messagesForModel.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content("测试消息")
                    .build());
            String contextId = "test-context-id";

            // 调用方法
            String result = (String) method.invoke(serviceImpl, searchText, userMessage, messagesForModel, contextId);

            // 验证结果
            assertNotNull(result, "处理结果不应为null");
            assertFalse(result.trim().isEmpty(), "处理结果不应为空");

            log.info("✅ processWithIntentRecognition测试通过");
            log.info("搜索文本: {}", searchText);
            log.info("用户消息: {}", userMessage);
            log.info("处理结果长度: {}", result.length());
            log.info("处理结果预览: {}", result.substring(0, Math.min(200, result.length())) + "...");

        } catch (Exception e) {
            log.error("测试processWithIntentRecognition方法失败", e);
            // 如果是因为依赖服务问题导致的失败，记录但不让测试失败
            if (e.getCause() != null &&
                (e.getCause().getMessage().contains("未找到相关提示词") ||
                 e.getCause().getMessage().contains("ARK_API_KEY"))) {
                log.warn("⚠️ 依赖服务不可用，这是正常的测试环境情况: {}", e.getCause().getMessage());
            } else {
                fail("测试失败: " + e.getMessage());
            }
        }

        log.info("=== processWithIntentRecognition方法测试完成 ===");
    }

    @Test
    public void testParseSearchTextToString() {
        log.info("=== 测试parseSearchTextToString方法 ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("parseSearchTextToString", String.class);
            method.setAccessible(true);

            // 测试1：正常的JSON格式
            String jsonInput = """
                {
                  "result": [
                    "问题1",
                    "问题2",
                    "问题3"
                  ]
                }
                """;
            String result1 = (String) method.invoke(serviceImpl, jsonInput);
            assertEquals("问题1,问题2,问题3", result1);
            log.info("✅ JSON格式解析测试通过：{}", result1);

            // 测试2：空的result数组
            String emptyArrayJson = """
                {
                  "result": []
                }
                """;
            String result2 = (String) method.invoke(serviceImpl, emptyArrayJson);
            assertEquals("", result2);
            log.info("✅ 空数组解析测试通过：{}", result2);

            // 测试3：没有result字段的JSON
            String noResultJson = """
                {
                  "data": ["test1", "test2"]
                }
                """;
            String result3 = (String) method.invoke(serviceImpl, noResultJson);
            assertEquals(noResultJson, result3);
            log.info("✅ 无result字段JSON测试通过");

            // 测试4：非JSON格式字符串
            String plainText = "这是一个普通的字符串";
            String result4 = (String) method.invoke(serviceImpl, plainText);
            assertEquals(plainText, result4);
            log.info("✅ 普通字符串测试通过：{}", result4);

            // 测试5：null值
            String result5 = (String) method.invoke(serviceImpl, (String) null);
            assertEquals("", result5);
            log.info("✅ null值测试通过");

            // 测试6：空字符串
            String result6 = (String) method.invoke(serviceImpl, "");
            assertEquals("", result6);
            log.info("✅ 空字符串测试通过");

            // 测试7：单个元素的数组
            String singleElementJson = """
                {
                  "result": ["单个问题"]
                }
                """;
            String result7 = (String) method.invoke(serviceImpl, singleElementJson);
            assertEquals("单个问题", result7);
            log.info("✅ 单元素数组测试通过：{}", result7);

        } catch (Exception e) {
            log.error("测试parseSearchTextToString方法失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== parseSearchTextToString方法测试完成 ===");
    }

    @Test
    public void testFormatModelResponse() {
        log.info("=== 测试formatModelResponse方法 ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("formatModelResponse", String.class, String.class);
            method.setAccessible(true);

            // 测试1：包含"考察点"的中文评判结果格式
            String chineseJudgeResponse = """
                {
                  "分析": "完全不匹配，回答与正确答案相反",
                  "得分": 0,
                  "扣分点": ["回答完全错误，与正确答案相反"],
                  "正确答案": "商品享受7天无理由退货",
                  "下一题": "要是我想7天无理由退货，运费是由谁来承担呀？",
                  "考察点": "退货政策"
                }
                """;
            String msgId1 = "test-msg-001";
            String result1 = (String) method.invoke(serviceImpl, chineseJudgeResponse, msgId1);

            // 验证结果包含考察点
            assertTrue(result1.contains("考察点"), "结果应包含考察点字段");
            assertTrue(result1.contains("退货政策"), "结果应包含考察点值");
            assertTrue(result1.contains("\"msgId\":\"" + msgId1 + "\""), "结果应包含正确的msgId");
            log.info("✅ 中文评判结果（含考察点）测试通过");
            log.info("格式化结果: {}", result1);

            // 测试2：不包含"考察点"的中文评判结果格式
            String chineseJudgeWithoutExamPoint = """
                {
                  "分析": "匹配度较高",
                  "得分": 85,
                  "扣分点": ["回答不够详细"],
                  "正确答案": "产品有多种颜色可选",
                  "下一题": "请问这款产品的保修期是多久？"
                }
                """;
            String msgId2 = "test-msg-002";
            String result2 = (String) method.invoke(serviceImpl, chineseJudgeWithoutExamPoint, msgId2);

            // 验证结果不包含考察点但其他字段正常
            assertFalse(result2.contains("考察点"), "结果不应包含考察点字段");
            assertTrue(result2.contains("匹配度较高"), "结果应包含分析内容");
            assertTrue(result2.contains("\"msgId\":\"" + msgId2 + "\""), "结果应包含正确的msgId");
            log.info("✅ 中文评判结果（不含考察点）测试通过");

            // 测试3：普通字符串响应
            String plainResponse = "这是一个普通的回复内容";
            String msgId3 = "test-msg-003";
            String result3 = (String) method.invoke(serviceImpl, plainResponse, msgId3);

            // 验证普通响应格式
            assertTrue(result3.contains(plainResponse), "结果应包含原始回复内容");
            assertTrue(result3.contains("\"msgId\":\"" + msgId3 + "\""), "结果应包含正确的msgId");
            log.info("✅ 普通字符串响应测试通过");

            // 测试4：空响应
            String result4 = (String) method.invoke(serviceImpl, "", "test-msg-004");
            assertTrue(result4.contains("响应为空"), "空响应应有默认提示");
            log.info("✅ 空响应测试通过");

            // 测试5：null msgId
            String result5 = (String) method.invoke(serviceImpl, chineseJudgeResponse, null);
            assertTrue(result5.contains("\"msgId\":\"\""), "null msgId应转为空字符串");
            log.info("✅ null msgId测试通过");

        } catch (Exception e) {
            log.error("测试formatModelResponse方法失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== formatModelResponse方法测试完成 ===");
    }

    @Test
    public void testCleanJsonFormatWithQuoteEscaping() {
        log.info("=== 测试cleanJsonFormat方法处理双引号转义 ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("cleanJsonFormat", String.class);
            method.setAccessible(true);

            // 测试1：用户提供的实际场景 - "正确答案"字段中包含双引号
            String problemJson1 = "{ \"分析\": \"完全不匹配，客服回答未涉及商品套装积分获取及兑换内容\", \"得分\": 0, \"扣分点\": [\"回答未涉及关键信息，与正确答案无关\"], \"正确答案\": \"亲爱哒~您购买的499元【韦雪推荐】的21天抗皱精华套装到货确认收货后24-48小时计入2700积分（含100入会积分）~计入积分之后到我们店铺的\"会员中心\"下滑至\"玩转积分，好礼随心兑换\"页面选择中间的\"19.9元+2688积分\"兑换80ml的极地洁面乳正装哈~\", \"下一题\": \"我想问下，这款产品是支持正装试用的吗？\", \"考察点\": \"是支持正装试用吗\" }";
            String cleaned1 = (String) method.invoke(serviceImpl, problemJson1);

            // 验证修复后的JSON是有效的
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned1, new TypeReference<Map<String, Object>>() {});
            }, "修复后的JSON应该是有效的");

            // 验证双引号被正确转义
            assertTrue(cleaned1.contains("\\\"会员中心\\\""), "会员中心的双引号应该被转义");
            assertTrue(cleaned1.contains("\\\"玩转积分，好礼随心兑换\\\""), "玩转积分的双引号应该被转义");
            assertTrue(cleaned1.contains("\\\"19.9元+2688积分\\\""), "价格的双引号应该被转义");
            log.info("✅ 实际问题场景测试通过");
            log.debug("修复后JSON: {}", cleaned1);

            // 测试2：多个字段都包含双引号的情况
            String problemJson2 = "{ \"分析\": \"客服说\"你好\"但应该说\"您好\"\", \"得分\": 50, \"正确答案\": \"标准答案是\"尊敬的客户\"而不是\"亲爱的\"\", \"考察点\": \"礼貌用语\" }";
            String cleaned2 = (String) method.invoke(serviceImpl, problemJson2);

            // 验证修复后的JSON是有效的
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned2, new TypeReference<Map<String, Object>>() {});
            }, "包含多个双引号的JSON修复后应该是有效的");

            assertTrue(cleaned2.contains("\\\"你好\\\""), "分析字段中的双引号应该被转义");
            assertTrue(cleaned2.contains("\\\"您好\\\""), "分析字段中的双引号应该被转义");
            assertTrue(cleaned2.contains("\\\"尊敬的客户\\\""), "正确答案字段中的双引号应该被转义");
            assertTrue(cleaned2.contains("\\\"亲爱的\\\""), "正确答案字段中的双引号应该被转义");
            log.info("✅ 多字段双引号转义测试通过");

            // 测试3：已经正确格式的JSON不应该被修改
            String validJson = "{ \"分析\": \"完全匹配\", \"得分\": 100, \"正确答案\": \"标准答案内容\", \"考察点\": \"基础知识\" }";
            String cleaned3 = (String) method.invoke(serviceImpl, validJson);
            assertEquals(validJson, cleaned3, "有效的JSON不应该被修改");
            log.info("✅ 有效JSON保持不变测试通过");

            // 测试4：已经转义的双引号不应该被再次转义
            String alreadyEscapedJson = "{ \"分析\": \"客服说\\\"您好\\\"是正确的\", \"得分\": 100, \"正确答案\": \"应该说\\\"您好\\\"\", \"考察点\": \"礼貌用语\" }";
            String cleaned4 = (String) method.invoke(serviceImpl, alreadyEscapedJson);

            // 验证不会产生双重转义
            assertFalse(cleaned4.contains("\\\\\""), "不应该产生双重转义");
            assertTrue(cleaned4.contains("\\\"您好\\\""), "应该保持正确的转义格式");
            
            // 验证结果仍然是有效的JSON
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned4, new TypeReference<Map<String, Object>>() {});
            }, "已转义的JSON处理后仍应有效");
            log.info("✅ 避免双重转义测试通过");

            // 测试5：数组中包含双引号的情况
            String arrayWithQuotesJson = "{ \"分析\": \"部分匹配\", \"得分\": 70, \"扣分点\": [\"缺少\\\"详细说明\\\"\", \"应该说\\\"请稍等\\\"\"], \"正确答案\": \"完整答案\", \"考察点\": \"沟通技巧\" }";
            String cleaned5 = (String) method.invoke(serviceImpl, arrayWithQuotesJson);

            // 验证修复后的JSON是有效的
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned5, new TypeReference<Map<String, Object>>() {});
            }, "包含数组的JSON修复后应该是有效的");
            log.info("✅ 数组中双引号处理测试通过");

            // 测试6：边界情况测试
            String cleaned6 = (String) method.invoke(serviceImpl, "");
            assertEquals("", cleaned6, "空字符串应保持不变");

            String cleaned7 = (String) method.invoke(serviceImpl, (String) null);
            assertNull(cleaned7, "null值应保持不变");

            String nonJsonString = "这不是JSON格式的字符串包含\"双引号\"";
            String cleaned8 = (String) method.invoke(serviceImpl, nonJsonString);
            assertEquals(nonJsonString, cleaned8, "非JSON字符串应保持不变");
            log.info("✅ 边界情况测试通过");

            // 测试7：复杂嵌套JSON
            String complexJson = "{ \"分析\": \"复杂情况\", \"得分\": 80, \"详情\": { \"子字段\": \"包含\\\"嵌套双引号\\\"的内容\" }, \"正确答案\": \"标准答案中有\\\"特殊标记\\\"\", \"考察点\": \"复杂场景\" }";
            String cleaned9 = (String) method.invoke(serviceImpl, complexJson);

            // 验证复杂JSON也能正确处理
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned9, new TypeReference<Map<String, Object>>() {});
            }, "复杂嵌套JSON修复后应该是有效的");
            log.info("✅ 复杂嵌套JSON测试通过");

        } catch (Exception e) {
            log.error("测试cleanJsonFormat双引号转义功能失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== cleanJsonFormat双引号转义功能测试完成 ===");
    }

    @Test
    public void testFormatModelResponseWithMalformedJson() {
        log.info("=== 测试formatModelResponse处理格式错误的JSON ===");

        // 通过反射访问私有方法进行测试
        BmForKbServiceImpl serviceImpl = (BmForKbServiceImpl) bmForKbService;

        try {
            // 使用反射获取私有方法
            Method method = BmForKbServiceImpl.class.getDeclaredMethod("formatModelResponse", String.class, String.class);
            method.setAccessible(true);

            // 测试包含格式错误的JSON响应
            String malformedResponse = """
                {
                  "分析": "完全不匹配，客服回答与正确答案毫无关联",
                  "得分": 0,
                  "扣分点": ["回答完全错误，与正确答案无关"],
                  "正确答案": "应明确说明'建议收货后先试用小样，小样1盒里面有3个阶段，每间隔2天使用一次'"],
                  "下一题": "如果我正在使用其他具有不同功效的产品，会有影响吗？",
                  "考察点": "退货政策"
                }
                """;
            String msgId = "test-malformed-001";
            String result = (String) method.invoke(serviceImpl, malformedResponse, msgId);

            // 验证结果包含所有必要字段
            assertTrue(result.contains("完全不匹配"), "结果应包含分析内容");
            assertTrue(result.contains("退货政策"), "结果应包含考察点");
            assertTrue(result.contains("\"msgId\":\"" + msgId + "\""), "结果应包含正确的msgId");

            // 验证结果是有效的JSON格式
            assertDoesNotThrow(() -> {
                objectMapper.readTree(result);
            }, "返回的结果应该是有效的JSON格式");

            log.info("✅ 格式错误JSON处理测试通过");
            log.info("处理结果: {}", result);

        } catch (Exception e) {
            log.error("测试formatModelResponse处理格式错误JSON失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== formatModelResponse处理格式错误JSON测试完成 ===");
    }
}
