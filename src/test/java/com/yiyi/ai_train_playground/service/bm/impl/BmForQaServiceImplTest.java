package com.yiyi.ai_train_playground.service.bm.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;
import com.yiyi.ai_train_playground.service.bm.BmForQaService;
import com.yiyi.ai_train_playground.service.task.TrainQaRdmService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BmForQaServiceImpl 测试类
 * 测试保存actualQues到数据库的功能
 */
@Slf4j
@SpringBootTest
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/service/bm/impl/BmForQaServiceImplTest.sql")
public class BmForQaServiceImplTest {

    @Autowired
    private BmForQaServiceImpl bmForQaService;

    @Mock
    private TrainQaRdmService trainQaRdmService;

    @Autowired
    private ObjectMapper objectMapper;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_UUID = "test-uuid-12345";
    private static final String TEST_FIRST_MESSAGE = "这是测试的首条消息内容";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试saveActualQuesToDb方法 - 正常情况
     */
    @Test
    public void testSaveActualQuesToDb_Success() throws Exception {
        // 准备测试数据
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = createTestQaSimplelDtoWithUUID();
        TrainQaRdm existingRecord = createTestTrainQaRdm();

        // Mock服务调用
        when(trainQaRdmService.getByUUID(TEST_UUID, TEST_TEAM_ID)).thenReturn(existingRecord);
        when(trainQaRdmService.updateByUUID(any(TrainQaRdm.class))).thenReturn(true);

        // 通过反射调用私有方法
        Method saveActualQuesToDbMethod = BmForQaServiceImpl.class.getDeclaredMethod(
                "saveActualQuesToDb", QaSimplelDtoWithUUID.class, String.class, Long.class);
        saveActualQuesToDbMethod.setAccessible(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            saveActualQuesToDbMethod.invoke(bmForQaService, qaSimplelDtoWithUUID, TEST_FIRST_MESSAGE, TEST_TEAM_ID);
        });

        // 验证调用
        verify(trainQaRdmService, times(1)).getByUUID(TEST_UUID, TEST_TEAM_ID);
        verify(trainQaRdmService, times(1)).updateByUUID(argThat(trainQaRdm -> 
                TEST_FIRST_MESSAGE.equals(trainQaRdm.getActualQuestion())
        ));

        log.info("测试saveActualQuesToDb正常情况 - 通过");
    }

    /**
     * 测试saveActualQuesToDb方法 - qaSimplelDtoWithUUID为null
     */
    @Test
    public void testSaveActualQuesToDb_NullQaSimplelDto() throws Exception {
        // 通过反射调用私有方法
        Method saveActualQuesToDbMethod = BmForQaServiceImpl.class.getDeclaredMethod(
                "saveActualQuesToDb", QaSimplelDtoWithUUID.class, String.class, Long.class);
        saveActualQuesToDbMethod.setAccessible(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            saveActualQuesToDbMethod.invoke(bmForQaService, null, TEST_FIRST_MESSAGE, TEST_TEAM_ID);
        });

        // 验证没有调用服务方法
        verify(trainQaRdmService, never()).getByUUID(anyString(), anyLong());
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class));

        log.info("测试saveActualQuesToDb空参数情况 - 通过");
    }

    /**
     * 测试saveActualQuesToDb方法 - UUID为null
     */
    @Test
    public void testSaveActualQuesToDb_NullUuid() throws Exception {
        // 准备测试数据
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = createTestQaSimplelDtoWithUUID();
        qaSimplelDtoWithUUID.setUuid(null); // 设置UUID为null

        // 通过反射调用私有方法
        Method saveActualQuesToDbMethod = BmForQaServiceImpl.class.getDeclaredMethod(
                "saveActualQuesToDb", QaSimplelDtoWithUUID.class, String.class, Long.class);
        saveActualQuesToDbMethod.setAccessible(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            saveActualQuesToDbMethod.invoke(bmForQaService, qaSimplelDtoWithUUID, TEST_FIRST_MESSAGE, TEST_TEAM_ID);
        });

        // 验证没有调用服务方法
        verify(trainQaRdmService, never()).getByUUID(anyString(), anyLong());
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class));

        log.info("测试saveActualQuesToDb UUID为空情况 - 通过");
    }

    /**
     * 测试saveActualQuesToDb方法 - 记录不存在
     */
    @Test
    public void testSaveActualQuesToDb_RecordNotFound() throws Exception {
        // 准备测试数据
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = createTestQaSimplelDtoWithUUID();

        // Mock服务调用 - 返回null表示记录不存在
        when(trainQaRdmService.getByUUID(TEST_UUID, TEST_TEAM_ID)).thenReturn(null);

        // 通过反射调用私有方法
        Method saveActualQuesToDbMethod = BmForQaServiceImpl.class.getDeclaredMethod(
                "saveActualQuesToDb", QaSimplelDtoWithUUID.class, String.class, Long.class);
        saveActualQuesToDbMethod.setAccessible(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            saveActualQuesToDbMethod.invoke(bmForQaService, qaSimplelDtoWithUUID, TEST_FIRST_MESSAGE, TEST_TEAM_ID);
        });

        // 验证调用
        verify(trainQaRdmService, times(1)).getByUUID(TEST_UUID, TEST_TEAM_ID);
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class)); // 不应该调用更新

        log.info("测试saveActualQuesToDb记录不存在情况 - 通过");
    }

    /**
     * 测试saveActualQuesToDb方法 - 更新失败
     */
    @Test
    public void testSaveActualQuesToDb_UpdateFailed() throws Exception {
        // 准备测试数据
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = createTestQaSimplelDtoWithUUID();
        TrainQaRdm existingRecord = createTestTrainQaRdm();

        // Mock服务调用 - 更新失败
        when(trainQaRdmService.getByUUID(TEST_UUID, TEST_TEAM_ID)).thenReturn(existingRecord);
        when(trainQaRdmService.updateByUUID(any(TrainQaRdm.class))).thenReturn(false);

        // 通过反射调用私有方法
        Method saveActualQuesToDbMethod = BmForQaServiceImpl.class.getDeclaredMethod(
                "saveActualQuesToDb", QaSimplelDtoWithUUID.class, String.class, Long.class);
        saveActualQuesToDbMethod.setAccessible(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            saveActualQuesToDbMethod.invoke(bmForQaService, qaSimplelDtoWithUUID, TEST_FIRST_MESSAGE, TEST_TEAM_ID);
        });

        // 验证调用
        verify(trainQaRdmService, times(1)).getByUUID(TEST_UUID, TEST_TEAM_ID);
        verify(trainQaRdmService, times(1)).updateByUUID(any(TrainQaRdm.class));

        log.info("测试saveActualQuesToDb更新失败情况 - 通过");
    }

    /**
     * 测试saveActualQuesToDb方法 - 异常处理
     */
    @Test
    public void testSaveActualQuesToDb_Exception() throws Exception {
        // 准备测试数据
        QaSimplelDtoWithUUID qaSimplelDtoWithUUID = createTestQaSimplelDtoWithUUID();

        // Mock服务调用 - 抛出异常
        when(trainQaRdmService.getByUUID(TEST_UUID, TEST_TEAM_ID)).thenThrow(new RuntimeException("数据库连接失败"));

        // 通过反射调用私有方法
        Method saveActualQuesToDbMethod = BmForQaServiceImpl.class.getDeclaredMethod(
                "saveActualQuesToDb", QaSimplelDtoWithUUID.class, String.class, Long.class);
        saveActualQuesToDbMethod.setAccessible(true);

        // 执行测试 - 应该不抛出异常（异常被捕获并记录日志）
        assertDoesNotThrow(() -> {
            saveActualQuesToDbMethod.invoke(bmForQaService, qaSimplelDtoWithUUID, TEST_FIRST_MESSAGE, TEST_TEAM_ID);
        });

        // 验证调用
        verify(trainQaRdmService, times(1)).getByUUID(TEST_UUID, TEST_TEAM_ID);
        verify(trainQaRdmService, never()).updateByUUID(any(TrainQaRdm.class));

        log.info("测试saveActualQuesToDb异常处理情况 - 通过");
    }

    /**
     * 创建测试用的QaSimplelDtoWithUUID对象
     */
    private QaSimplelDtoWithUUID createTestQaSimplelDtoWithUUID() {
        return new QaSimplelDtoWithUUID(
                TEST_UUID,
                "测试问题",
                "测试答案",
                null, // actualQuestion
                null, // actualAnswer
                null, // resolve
                "1/10" // quesNo
        );
    }

    /**
     * 创建测试用的TrainQaRdm对象
     */
    private TrainQaRdm createTestTrainQaRdm() {
        TrainQaRdm trainQaRdm = new TrainQaRdm();
        trainQaRdm.setId(1L);
        trainQaRdm.setUuid(TEST_UUID);
        trainQaRdm.setQuestion("测试问题");
        trainQaRdm.setAnswer("测试答案");
        trainQaRdm.setQuesNo("1/10");
        trainQaRdm.setTeamId(TEST_TEAM_ID);
        trainQaRdm.setCreator("testUser");
        trainQaRdm.setUpdater("testUser");
        trainQaRdm.setCreateTime(LocalDateTime.now());
        trainQaRdm.setUpdateTime(LocalDateTime.now());
        trainQaRdm.setVersion(0L);
        return trainQaRdm;
    }

    @Test
    public void testCleanJsonFormat() {
        log.info("=== 测试BmForQaServiceImpl的cleanJsonFormat方法 ===");

        try {
            // 使用反射获取私有方法
            Method method = BmForQaServiceImpl.class.getDeclaredMethod("cleanJsonFormat", String.class);
            method.setAccessible(true);

            // 测试1：修复"正确答案"字段中引号外多出的']'符号
            String malformedJson1 = """
                {
                  "分析": "完全不匹配，客服回答与正确答案毫无关联",
                  "得分": 0,
                  "扣分点": ["回答完全错误，与正确答案无关"],
                  "正确答案": "应明确说明'建议收货后先试用小样，小样1盒里面有3个阶段'"],
                  "下一题": "如果我正在使用其他具有不同功效的产品，会有影响吗？",
                  "考察点": "退货政策"
                }
                """;
            String cleaned1 = (String) method.invoke(bmForQaService, malformedJson1);

            // 验证']'符号被移除
            assertFalse(cleaned1.contains("'\"]\","), "清理后不应包含多余的']'符号");
            assertTrue(cleaned1.contains("'\"，") || cleaned1.contains("'\","), "清理后应保持正确的格式");
            log.info("✅ 修复'正确答案'字段格式错误测试通过");

            // 测试2：修复其他字段的类似问题
            String malformedJson2 = """
                {
                  "分析": "测试内容"],
                  "得分": 85,
                  "正确答案": "正确的答案内容"],
                  "考察点": "测试考察点"
                }
                """;
            String cleaned2 = (String) method.invoke(bmForQaService, malformedJson2);

            // 验证多个字段的']'符号都被移除
            assertFalse(cleaned2.contains("内容\"],"), "清理后不应包含多余的']'符号");
            assertFalse(cleaned2.contains("答案内容\"],"), "清理后不应包含多余的']'符号");
            log.info("✅ 修复多个字段格式错误测试通过");

            // 测试3：正常JSON不应被修改
            String normalJson = """
                {
                  "分析": "正常的分析内容",
                  "得分": 90,
                  "扣分点": ["扣分点1", "扣分点2"],
                  "正确答案": "正常的正确答案",
                  "下一题": "正常的下一题",
                  "考察点": "正常考察点"
                }
                """;
            String cleaned3 = (String) method.invoke(bmForQaService, normalJson);

            // 正常JSON应该保持不变（除了可能的空白字符差异）
            assertTrue(cleaned3.contains("正常的分析内容"), "正常JSON内容应保持不变");
            assertTrue(cleaned3.contains("正常的正确答案"), "正常JSON内容应保持不变");
            log.info("✅ 正常JSON保持不变测试通过");

            // 测试4：空字符串和null值
            String cleaned4 = (String) method.invoke(bmForQaService, "");
            assertEquals("", cleaned4, "空字符串应保持不变");

            String cleaned5 = (String) method.invoke(bmForQaService, (String) null);
            assertNull(cleaned5, "null值应保持不变");
            log.info("✅ 边界值测试通过");

            // 测试5：非JSON字符串
            String nonJson = "这不是JSON格式的字符串";
            String cleaned6 = (String) method.invoke(bmForQaService, nonJson);
            assertEquals(nonJson, cleaned6, "非JSON字符串应保持不变");
            log.info("✅ 非JSON字符串测试通过");

        } catch (Exception e) {
            log.error("测试BmForQaServiceImpl的cleanJsonFormat方法失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== BmForQaServiceImpl的cleanJsonFormat方法测试完成 ===");
    }

    @Test
    public void testFormatModelResponseWithMalformedJson() {
        log.info("=== 测试BmForQaServiceImpl的formatModelResponse处理格式错误的JSON ===");

        try {
            // 使用反射获取私有方法
            Method method = BmForQaServiceImpl.class.getDeclaredMethod("formatModelResponse", String.class, String.class);
            method.setAccessible(true);

            // 测试包含格式错误的JSON响应
            String malformedResponse = """
                {
                  "分析": "完全不匹配，客服回答与正确答案毫无关联",
                  "得分": 0,
                  "扣分点": ["回答完全错误，与正确答案无关"],
                  "正确答案": "应明确说明'建议收货后先试用小样，小样1盒里面有3个阶段，每间隔2天使用一次'"],
                  "下一题": "如果我正在使用其他具有不同功效的产品，会有影响吗？",
                  "考察点": "退货政策"
                }
                """;
            String msgId = "test-malformed-qa-001";
            String result = (String) method.invoke(bmForQaService, malformedResponse, msgId);

            // 验证结果包含所有必要字段
            assertTrue(result.contains("完全不匹配"), "结果应包含分析内容");
            assertTrue(result.contains("退货政策"), "结果应包含考察点");
            assertTrue(result.contains("\"msgId\":\"" + msgId + "\""), "结果应包含正确的msgId");

            // 验证结果是有效的JSON格式
            assertDoesNotThrow(() -> {
                objectMapper.readTree(result);
            }, "返回的结果应该是有效的JSON格式");

            log.info("✅ BmForQaServiceImpl格式错误JSON处理测试通过");
            log.info("处理结果: {}", result);

        } catch (Exception e) {
            log.error("测试BmForQaServiceImpl的formatModelResponse处理格式错误JSON失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== BmForQaServiceImpl的formatModelResponse处理格式错误JSON测试完成 ===");
    }

    /**
     * 测试cleanJsonFormat方法的双引号转义功能
     */
    @Test
    public void testCleanJsonFormatWithQuoteEscaping() {
        log.info("=== 测试BmForQaServiceImpl的cleanJsonFormat双引号转义功能 ===");

        try {
            // 使用反射获取私有方法
            Method method = BmForQaServiceImpl.class.getDeclaredMethod("cleanJsonFormat", String.class);
            method.setAccessible(true);

            // 测试1：实际问题JSON - "正确答案"字段包含双引号
            String actualProblemJson = """
                {
                  "分析": "完全不匹配，客服回答未涉及商品套装积分获取及兑换内容",
                  "得分": 0,
                  "扣分点": ["回答未涉及关键信息，与正确答案无关"],
                  "正确答案": "亲爱哒~您购买的499元【韦雪推荐】的21天抗皱精华套装到货确认收货后24-48小时计入2700积分（含100入会积分）~计入积分之后到我们店铺的"会员中心"下滑至"玩转积分，好礼随心兑换"页面选择中间的"19.9元+2688积分"兑换80ml的极地洁面乳正装哈~",
                  "下一题": "我想问下，这款产品是支持正装试用的吗？",
                  "考察点": "是支持正装试用吗"
                }
                """;
            
            String cleaned1 = (String) method.invoke(bmForQaService, actualProblemJson);

            // 验证修复后的JSON可以正确解析
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned1, new TypeReference<java.util.Map<String, Object>>() {});
            }, "修复后的JSON应该可以正确解析");

            // 验证双引号被正确转义
            assertTrue(cleaned1.contains("\\\"会员中心\\\""), "会员中心的双引号应该被转义");
            assertTrue(cleaned1.contains("\\\"玩转积分，好礼随心兑换\\\""), "玩转积分的双引号应该被转义");
            assertTrue(cleaned1.contains("\\\"19.9元+2688积分\\\""), "19.9元+2688积分的双引号应该被转义");
            
            log.info("✅ 实际问题JSON双引号转义测试通过");
            log.info("修复后的JSON: {}", cleaned1);

            // 测试2：多字段包含双引号
            String multiFieldJson = """
                {
                  "分析": "分析中包含"引号"的内容",
                  "得分": 75,
                  "扣分点": ["扣分点包含"双引号"问题"],
                  "正确答案": "正确答案包含"多个"双引号"测试",
                  "下一题": "下一题也包含"引号"内容",
                  "考察点": "考察点"测试"内容"
                }
                """;
            
            String cleaned2 = (String) method.invoke(bmForQaService, multiFieldJson);

            // 验证修复后的JSON可以正确解析
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned2, new TypeReference<java.util.Map<String, Object>>() {});
            }, "多字段双引号修复后的JSON应该可以正确解析");
            
            log.info("✅ 多字段双引号转义测试通过");

            // 测试3：有效的JSON应该保持不变
            String validJson = """
                {
                  "分析": "正常的分析内容",
                  "得分": 90,
                  "扣分点": ["正常的扣分点"],
                  "正确答案": "正常的正确答案",
                  "下一题": "正常的下一题",
                  "考察点": "正常考察点"
                }
                """;
            
            String cleaned3 = (String) method.invoke(bmForQaService, validJson);

            // 有效JSON应该直接返回，不做修改
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned3, new TypeReference<java.util.Map<String, Object>>() {});
            }, "有效JSON应该可以正确解析");
            
            log.info("✅ 有效JSON保持不变测试通过");

            // 测试4：避免重复转义
            String alreadyEscapedJson = """
                {
                  "正确答案": "已经包含\\\"转义引号\\\"的内容"
                }
                """;
            
            String cleaned4 = (String) method.invoke(bmForQaService, alreadyEscapedJson);

            // 验证不会重复转义
            assertFalse(cleaned4.contains("\\\\\\\""), "不应该重复转义已转义的引号");
            
            log.info("✅ 避免重复转义测试通过");

            // 测试5：数组中的双引号
            String arrayJson = """
                {
                  "扣分点": ["第一个扣分点包含"引号"", "第二个扣分点也有"引号"问题"],
                  "得分": 60
                }
                """;
            
            String cleaned5 = (String) method.invoke(bmForQaService, arrayJson);

            // 验证数组中的双引号也能正确处理
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned5, new TypeReference<java.util.Map<String, Object>>() {});
            }, "数组中双引号修复后的JSON应该可以正确解析");
            
            log.info("✅ 数组双引号转义测试通过");

            // 测试6：边界情况
            String cleaned6 = (String) method.invoke(bmForQaService, "");
            assertEquals("", cleaned6, "空字符串应保持不变");

            String cleaned7 = (String) method.invoke(bmForQaService, (String) null);
            assertNull(cleaned7, "null值应保持不变");
            
            log.info("✅ 边界情况测试通过");

            // 测试7：复杂嵌套JSON
            String complexJson = """
                {
                  "分析": "复杂分析包含"多层"嵌套"引号"",
                  "得分": 85,
                  "正确答案": "答案中有"会员中心"和"积分兑换"功能，还有"19.9元"优惠",
                  "扣分点": ["扣分点1包含"问题"", "扣分点2也有"引号""],
                  "考察点": "考察"复杂情况"处理"
                }
                """;
            
            String cleaned8 = (String) method.invoke(bmForQaService, complexJson);

            // 验证复杂嵌套JSON也能正确处理
            assertDoesNotThrow(() -> {
                objectMapper.readValue(cleaned8, new TypeReference<java.util.Map<String, Object>>() {});
            }, "复杂嵌套JSON修复后应该可以正确解析");
            
            log.info("✅ 复杂嵌套JSON测试通过");

        } catch (Exception e) {
            log.error("测试BmForQaServiceImpl的cleanJsonFormat双引号转义功能失败", e);
            fail("测试失败: " + e.getMessage());
        }

        log.info("=== BmForQaServiceImpl的cleanJsonFormat双引号转义功能测试完成 ===");
    }
}
