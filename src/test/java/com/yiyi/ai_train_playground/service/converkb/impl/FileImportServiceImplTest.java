package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.config.FileProcessingConfig;
import com.yiyi.ai_train_playground.dto.converkb.FileImportResponse;
import com.yiyi.ai_train_playground.service.converkb.FileImportService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件导入服务测试类
 */
@SpringBootTest
@ActiveProfiles("company")
@Transactional
public class FileImportServiceImplTest {
    
    @Autowired
    private FileImportService fileImportService;
    
    @Autowired
    private FileProcessingConfig fileProcessingConfig;
    
    private Long testTeamId = 1L;
    private String testUserId = "test-user";
    
    @BeforeEach
    public void setUp() {
        // 测试前准备工作
    }
    
    @Test
    public void testValidateFile_Success() {
        // 创建有效的文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "测试内容".getBytes(StandardCharsets.UTF_8)
        );

        // 验证文件
        String result = fileImportService.validateFile(file);

        // 验证结果
        assertNull(result); // null表示验证通过
    }

    @Test
    public void testFileProcessingConfigLoading() {
        // 测试配置是否正确加载
        assertNotNull(fileProcessingConfig, "FileProcessingConfig不应该为null");

        // 验证chunkSize配置是否正确加载
        Integer chunkSize = fileProcessingConfig.getChunkSize();
        assertNotNull(chunkSize, "chunkSize不应该为null");

        // 应该是配置文件中的值7168，而不是Java默认值12288
        assertEquals(7168, chunkSize.intValue(), "chunkSize应该是配置文件中的值7168");

        System.out.println("FileProcessingConfig加载的chunkSize值: " + chunkSize);
        System.out.println("完整配置: " + fileProcessingConfig.toString());
    }
    
    @Test
    public void testValidateFile_EmptyFile() {
        // 创建空文件
        MockMultipartFile file = new MockMultipartFile("file", "test.txt", "text/plain", new byte[0]);
        
        // 验证文件
        String result = fileImportService.validateFile(file);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("文件不能为空", result);
    }
    
    @Test
    public void testValidateFile_UnsupportedFormat() {
        // 创建不支持格式的文件
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.pdf", 
            "application/pdf", 
            "测试内容".getBytes(StandardCharsets.UTF_8)
        );
        
        // 验证文件
        String result = fileImportService.validateFile(file);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("不支持的文件格式"));
    }
    
    @Test
    public void testValidateFile_FileSizeExceeded() {
        // 创建超大文件
        byte[] largeContent = new byte[(int) (fileProcessingConfig.getMaxFileSize() + 1)];
        MockMultipartFile file = new MockMultipartFile("file", "test.txt", "text/plain", largeContent);
        
        // 验证文件
        String result = fileImportService.validateFile(file);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("文件大小超过限制"));
    }
    
    @Test
    public void testReadFileContent_TextFile() {
        // 创建文本文件
        String content = "这是一个测试文本文件\n包含多行内容\n用于测试文件读取功能";
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.txt", 
            "text/plain", 
            content.getBytes(StandardCharsets.UTF_8)
        );
        
        // 读取文件内容
        String result = fileImportService.readFileContent(file);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("这是一个测试文本文件"));
        assertTrue(result.contains("包含多行内容"));
        assertTrue(result.contains("用于测试文件读取功能"));
    }
    
    @Test
    public void testReadFileContent_CsvFile() {
        // 创建CSV文件
        String content = "姓名,年龄,职业\n张三,25,程序员\n李四,30,设计师";
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.csv", 
            "text/csv", 
            content.getBytes(StandardCharsets.UTF_8)
        );
        
        // 读取文件内容
        String result = fileImportService.readFileContent(file);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("姓名,年龄,职业"));
        assertTrue(result.contains("张三,25,程序员"));
    }
    
    @Test
    public void testSplitContentIntoChunks() {
        // 准备测试内容
        StringBuilder content = new StringBuilder();
        content.append("以下为一通会话\n");
        content.append("客服：您好，有什么可以帮助您的吗？\n");
        content.append("客户：我想了解一下产品功能\n");
        content.append("客服：好的，我来为您详细介绍\n");
        content.append("会话结束_2024-01-01 10:00:00\n");
        content.append("以下为一通会话\n");
        content.append("客服：您好，欢迎咨询\n");
        content.append("客户：我有一个问题需要咨询\n");
        content.append("会话结束_2024-01-01 11:00:00\n");
        
        // 执行切割
        List<String> chunks = fileImportService.splitContentIntoChunks(content.toString());
        
        // 验证结果
        assertNotNull(chunks);
        assertTrue(chunks.size() > 0);
        
        // 验证每个块都包含内容
        for (String chunk : chunks) {
            assertNotNull(chunk);
            assertTrue(chunk.trim().length() > 0);
        }
    }
    
    @Test
    public void testSplitContentIntoChunks_EmptyContent() {
        // 测试空内容
        List<String> chunks = fileImportService.splitContentIntoChunks("");
        
        // 验证结果
        assertNotNull(chunks);
        assertEquals(0, chunks.size());
    }
    
    @Test
    public void testGetFileExtension() {
        // 测试各种文件名
        assertEquals("txt", fileImportService.getFileExtension("test.txt"));
        assertEquals("xlsx", fileImportService.getFileExtension("data.xlsx"));
        assertEquals("csv", fileImportService.getFileExtension("report.csv"));
        assertEquals("", fileImportService.getFileExtension("noextension"));
        assertEquals("", fileImportService.getFileExtension(""));
        assertEquals("", fileImportService.getFileExtension(null));
    }
    
    @Test
    public void testHasCompleteConversation() {
        // 测试包含完整会话的内容
        String completeContent = "以下为一通会话\n客服：您好\n客户：你好\n会话结束_2024-01-01";
        assertTrue(fileImportService.hasCompleteConversation(completeContent));
        
        // 测试不完整的会话
        String incompleteContent = "以下为一通会话\n客服：您好\n客户：你好";
        assertFalse(fileImportService.hasCompleteConversation(incompleteContent));
        
        // 测试空内容
        assertFalse(fileImportService.hasCompleteConversation(""));
        assertFalse(fileImportService.hasCompleteConversation(null));
    }
    
    @Test
    public void testFindConversationEnd() {
        // 准备测试内容
        String content = "一些内容\n以下为一通会话\n客服：您好\n会话结束_2024-01-01\n更多内容";
        
        // 查找会话结束位置
        int endPos = fileImportService.findConversationEnd(content, 0);
        
        // 验证结果
        assertTrue(endPos > 0);
        assertTrue(endPos <= content.length());
        
        // 测试未找到的情况
        String contentWithoutEnd = "一些内容\n以下为一通会话\n客服：您好";
        int notFoundPos = fileImportService.findConversationEnd(contentWithoutEnd, 0);
        assertEquals(-1, notFoundPos);
    }
    
    @Test
    public void testGeneratePreview() {
        // 测试正常内容
        String content = "这是一个很长的内容，用于测试预览功能的生成效果";
        String preview = fileImportService.generatePreview(content, 10);
        assertEquals("这是一个很长的内容，...", preview);
        
        // 测试短内容
        String shortContent = "短内容";
        String shortPreview = fileImportService.generatePreview(shortContent, 10);
        assertEquals("短内容", shortPreview);
        
        // 测试空内容
        String emptyPreview = fileImportService.generatePreview("", 10);
        assertEquals("", emptyPreview);
    }
    
    @Test
    public void testImportFile_Success() {
        // 创建测试文件
        String content = "以下为一通会话\n客服：您好，有什么可以帮助您的吗？\n客户：我想了解产品\n会话结束_2024-01-01 10:00:00";
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "conversation.txt", 
            "text/plain", 
            content.getBytes(StandardCharsets.UTF_8)
        );
        
        // 执行导入
        FileImportResponse response = fileImportService.importFile(file, testTeamId, testUserId);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getTplId());
        assertTrue(response.getTplId() > 0);
        assertEquals("conversation.txt", response.getOriginalFileName());
        assertEquals("txt", response.getFileType());
        assertNotNull(response.getStartTime());
        assertNotNull(response.getEndTime());
        assertTrue(response.getProcessingTimeMs() >= 0);
        
        // 验证没有错误
        assertTrue(response.getErrorMessages() == null || response.getErrorMessages().isEmpty());
        
        // 验证块信息
        assertTrue(response.getTotalChunks() > 0);
        assertEquals(response.getTotalChunks(), response.getSuccessChunks());
        assertEquals(0, response.getFailedChunks());
    }
    
    @Test
    public void testImportFile_InvalidFile() {
        // 创建无效文件
        MockMultipartFile file = new MockMultipartFile("file", "test.pdf", "application/pdf", new byte[0]);

        // 执行导入
        FileImportResponse response = fileImportService.importFile(file, testTeamId, testUserId);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getErrorMessages());
        assertFalse(response.getErrorMessages().isEmpty());
        assertEquals(0, response.getTotalChunks());
        assertEquals(0, response.getSuccessChunks());
        assertEquals(0, response.getFailedChunks());
    }

    @Test
    public void testLearnStatusEnum() {
        // 测试新增的FILE_UPLOADING枚举值
        assertEquals("file_uploading", com.yiyi.ai_train_playground.enums.LearnStatus.FILE_UPLOADING.getCode());
        assertEquals("文件上传中", com.yiyi.ai_train_playground.enums.LearnStatus.FILE_UPLOADING.getDescription());

        // 测试fromCode方法
        assertEquals(com.yiyi.ai_train_playground.enums.LearnStatus.FILE_UPLOADING,
                    com.yiyi.ai_train_playground.enums.LearnStatus.fromCode("file_uploading"));

        // 测试isValidCode方法
        assertTrue(com.yiyi.ai_train_playground.enums.LearnStatus.isValidCode("file_uploading"));
        assertTrue(com.yiyi.ai_train_playground.enums.LearnStatus.isValidCode("un_learn"));
        assertTrue(com.yiyi.ai_train_playground.enums.LearnStatus.isValidCode("learning"));
        assertTrue(com.yiyi.ai_train_playground.enums.LearnStatus.isValidCode("learned"));
        assertFalse(com.yiyi.ai_train_playground.enums.LearnStatus.isValidCode("invalid_status"));
    }
}
