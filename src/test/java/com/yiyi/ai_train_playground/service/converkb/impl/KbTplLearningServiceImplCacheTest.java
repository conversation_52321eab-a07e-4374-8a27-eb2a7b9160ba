package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.converkb.KbTplLearningService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * KbTplLearningServiceImpl缓存功能测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-16
 */
@Slf4j
@SpringBootTest
public class KbTplLearningServiceImplCacheTest {
    
    @Autowired
    private KbTplLearningService kbTplLearningService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Test
    public void testContextIdCache() {
        log.info("测试ContextId缓存功能");
        
        try {
            // 清理可能存在的缓存
            String testCacheKey = "kb:context:test_*";
            log.info("清理测试缓存");
            
            // 执行知识库模板学习，这会触发ContextId的获取和缓存
            log.info("第一次执行知识库模板学习（应该调用豆包大模型）");
            KbTplLearningService.KbTplLearningResult result1 = kbTplLearningService.processKbTplLearning();
            log.info("第一次执行完成，处理记录数: {}, token消耗: {}", result1.getProcessedCount(), result1.getTotalTokens());
            
            // 再次执行，这次应该从缓存中获取ContextId
            log.info("第二次执行知识库模板学习（应该从缓存获取ContextId）");
            KbTplLearningService.KbTplLearningResult result2 = kbTplLearningService.processKbTplLearning();
            log.info("第二次执行完成，处理记录数: {}, token消耗: {}", result2.getProcessedCount(), result2.getTotalTokens());
            
            log.info("ContextId缓存功能测试完成");
            
        } catch (Exception e) {
            log.error("ContextId缓存功能测试失败", e);
        }
    }
    
    @Test
    public void testCacheKeyGeneration() {
        log.info("测试缓存key生成功能");

        try {
            // 测试相同的系统提示词应该生成相同的缓存key
            String systemPrompt1 = "这是一个测试系统提示词";
            String systemPrompt2 = "这是一个测试系统提示词";
            String systemPrompt3 = "这是另一个测试系统提示词";

            // 通过反射来测试私有方法generateCacheKey
            java.lang.reflect.Method generateCacheKeyMethod =
                KbTplLearningServiceImpl.class.getDeclaredMethod("generateCacheKey", String.class);
            generateCacheKeyMethod.setAccessible(true);

            // 获取KbTplLearningServiceImpl实例
            KbTplLearningServiceImpl serviceImpl = (KbTplLearningServiceImpl) kbTplLearningService;

            String key1 = (String) generateCacheKeyMethod.invoke(serviceImpl, systemPrompt1);
            String key2 = (String) generateCacheKeyMethod.invoke(serviceImpl, systemPrompt2);
            String key3 = (String) generateCacheKeyMethod.invoke(serviceImpl, systemPrompt3);

            log.info("系统提示词1的缓存key: {}", key1);
            log.info("系统提示词2的缓存key: {}", key2);
            log.info("系统提示词3的缓存key: {}", key3);

            // 验证相同的系统提示词生成相同的key
            assert key1.equals(key2) : "相同的系统提示词应该生成相同的缓存key";
            // 验证不同的系统提示词生成不同的key
            assert !key1.equals(key3) : "不同的系统提示词应该生成不同的缓存key";

            log.info("缓存key生成功能测试完成");

        } catch (Exception e) {
            log.error("缓存key生成功能测试失败", e);
        }
    }
    
    @Test
    public void testCacheExpiration() {
        log.info("测试缓存过期功能");
        
        try {
            // 这个测试需要较长时间，在实际环境中可能不适合运行
            // 主要是验证缓存的TTL设置是否正确
            
            log.info("缓存过期功能测试完成（跳过实际过期测试）");
            
        } catch (Exception e) {
            log.error("缓存过期功能测试失败", e);
        }
    }
}
