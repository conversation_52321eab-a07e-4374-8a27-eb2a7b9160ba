package com.yiyi.ai_train_playground.service.converkb.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplMain;
import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;
import com.yiyi.ai_train_playground.enums.LearnStatus;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplMainMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainKbTplPreMapper;
import com.yiyi.ai_train_playground.service.converkb.KbTplLearningService;
import com.yiyi.ai_train_playground.service.converkb.impl.KbTplLearningServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.web.reactive.function.client.WebClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 知识库模板学习服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ActiveProfiles("company")
public class KbTplLearningServiceImplTest {
    
    @Autowired
    private KbTplLearningService kbTplLearningService;

    @Autowired
    private TrainKbTplMainMapper trainKbTplMainMapper;

    @LocalServerPort
    private int port;
    
    @Autowired
    private TrainKbTplPreMapper trainKbTplPreMapper;
    
    private Long testTeamId = 1L;
    private Long testTplId;
    
    @BeforeEach
    void setUp() {
        log.info("开始设置测试数据");
        
        // 创建测试主表记录
        TrainKbTplMain mainRecord = new TrainKbTplMain();
        mainRecord.setName("测试知识库模板");
        mainRecord.setTokens(0L);
        mainRecord.setTeamId(testTeamId);
        mainRecord.setCreator("test");
        mainRecord.setUpdater("test");
        mainRecord.setFileType("txt");
        mainRecord.setLearnStatus(LearnStatus.UN_LEARN.getCode());
        mainRecord.setTokenIn(0L);
        mainRecord.setTokenOut(0L);
        
        int result = trainKbTplMainMapper.insert(mainRecord);
        assertTrue(result > 0);
        assertNotNull(mainRecord.getId());
        testTplId = mainRecord.getId();
        
        log.info("创建测试主表记录成功，tplId: {}", testTplId);
    }
    
    @Test
    @DisplayName("测试处理知识库模板学习任务 - 无数据情况")
    @Transactional
    public void testProcessKbTplLearning_NoData() {
        log.info("测试处理知识库模板学习任务 - 无数据情况");
        
        // 确保没有未学习的数据
        List<TrainKbTplPre> unLearnedRecords = trainKbTplPreMapper.selectByLearnStatusWithLimit(
                LearnStatus.UN_LEARN.getCode(), 20);
        
        if (!unLearnedRecords.isEmpty()) {
            log.info("清理现有未学习数据，数量: {}", unLearnedRecords.size());
            for (TrainKbTplPre record : unLearnedRecords) {
                trainKbTplPreMapper.deleteById(record.getId(), record.getTeamId());
            }
        }
        
        // 执行处理
        KbTplLearningService.KbTplLearningResult result = kbTplLearningService.processKbTplLearning();

        // 验证结果
        assertEquals(0, result.getProcessedCount());
        assertEquals(0, result.getTotalTokens());
        log.info("测试完成，处理数量: {}, token消耗: {}", result.getProcessedCount(), result.getTotalTokens());
    }
    
    @Test
    @DisplayName("测试处理知识库模板学习任务 - 有数据情况")
    @Transactional
    public void testProcessKbTplLearning_WithData() {
        log.info("测试处理知识库模板学习任务 - 有数据情况");
        
        try {
            // 创建测试预处理记录
            createTestPreRecords();
            
            // 验证创建的数据
            List<TrainKbTplPre> unLearnedRecords = trainKbTplPreMapper.selectByLearnStatusWithLimit(
                    LearnStatus.UN_LEARN.getCode(), 20);
            assertTrue(unLearnedRecords.size() > 0);
            log.info("创建了 {} 条测试预处理记录", unLearnedRecords.size());
            
            // 注意：由于测试环境可能没有配置有效的ARK_API_KEY，
            // 这个测试可能会因为LLM调用失败而抛出异常
            // 这是预期的行为，我们主要测试数据查询和基本流程

            Exception thrownException = assertThrows(RuntimeException.class, () -> {
                KbTplLearningService.KbTplLearningResult result = kbTplLearningService.processKbTplLearning();
            });

            log.info("预期的异常: {}", thrownException.getMessage());
            assertTrue(thrownException.getMessage().contains("处理知识库模板学习任务失败"));
            
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            throw e;
        }
    }
    
    @Test
    @DisplayName("测试分页查询未学习状态的记录")
    public void testSelectByLearnStatusWithLimit() {
        log.info("测试分页查询未学习状态的记录");
        
        // 创建测试数据
        createTestPreRecords();
        
        // 查询未学习状态的记录
        List<TrainKbTplPre> records = trainKbTplPreMapper.selectByLearnStatusWithLimit(
                LearnStatus.UN_LEARN.getCode(), 5);
        
        // 验证结果
        assertNotNull(records);
        assertTrue(records.size() <= 5);
        
        for (TrainKbTplPre record : records) {
            assertEquals(LearnStatus.UN_LEARN.getCode(), record.getLearnStatus());
            log.info("查询到记录: id={}, tplId={}, index={}", 
                    record.getId(), record.getTplId(), record.getIndex());
        }
        
        log.info("查询完成，共找到 {} 条记录", records.size());
    }

    @Test
    @DisplayName("测试服务基本功能")
    public void testServiceBasicFunctionality() {
        log.info("测试KbTplLearningService基本功能");

        // 验证服务实例不为null
        assertNotNull(kbTplLearningService, "KbTplLearningService实例不应该为null");

        // 验证服务类型正确
        assertTrue(kbTplLearningService instanceof KbTplLearningServiceImpl,
                  "服务实例应该是KbTplLearningServiceImpl类型");

        log.info("KbTplLearningService基本功能测试通过");
    }

    @Test
    @DisplayName("测试新的多线程处理逻辑")
    public void testProcessSingleRecord() {
        log.info("测试新的多线程处理逻辑");

        try {
            // 验证processSingleRecord方法存在
            Method processSingleRecordMethod = KbTplLearningServiceImpl.class.getDeclaredMethod(
                "processSingleRecord",
                com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre.class,
                String.class
            );
            assertNotNull(processSingleRecordMethod, "processSingleRecord方法应该存在");

            // 验证updateMainTableStatusIfNeeded方法存在
            Method updateMainTableStatusIfNeededMethod = KbTplLearningServiceImpl.class.getDeclaredMethod(
                "updateMainTableStatusIfNeeded",
                Long.class,
                com.yiyi.ai_train_playground.enums.LearnStatus.class
            );
            assertNotNull(updateMainTableStatusIfNeededMethod, "updateMainTableStatusIfNeeded方法应该存在");

            log.info("新的多线程处理方法验证通过");

        } catch (Exception e) {
            log.error("测试新的多线程处理逻辑失败", e);
            fail("测试新的多线程处理逻辑失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试JSON字符串清理功能")
    public void testSanitizeJsonString() {
        log.info("测试JSON字符串清理功能");

        try {
            // 获取私有方法
            Method sanitizeMethod = KbTplLearningServiceImpl.class.getDeclaredMethod("sanitizeJsonString", String.class);
            sanitizeMethod.setAccessible(true);

            // 测试包含控制字符的JSON字符串
            String jsonWithControlChars = "{\n" +
                    "    \"typeCode\": \"0\",\n" +
                    "    \"typeName\": \"售前\",\n" +
                    "    \"content\": \"jd_99aabbcc1122 2025-08-06 10:00:00\nhttps://item.jd.com/100345678901.html\"\n" +
                    "}";

            // 调用清理方法
            String sanitized = (String) sanitizeMethod.invoke(kbTplLearningService, jsonWithControlChars);

            // 验证结果
            assertNotNull(sanitized, "清理后的字符串不应该为null");

            // 验证JSON结构中的换行符（用于格式化）应该被保留
            assertTrue(sanitized.contains("\n"), "JSON结构中的换行符应该被保留");

            // 验证字符串值内部的换行符应该被转义
            assertTrue(sanitized.contains("\\n"), "字符串值内部的换行符应该被转义");

            // 验证清理后的JSON可以被正确解析
            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode node = mapper.readTree(sanitized);
                assertNotNull(node, "清理后的JSON应该可以被正确解析");
                assertEquals("0", node.get("typeCode").asText(), "typeCode应该正确");
                assertEquals("售前", node.get("typeName").asText(), "typeName应该正确");

                // 验证content字段中的换行符被正确处理
                String content = node.get("content").asText();
                assertTrue(content.contains("\n"), "解析后的content应该包含换行符");

            } catch (Exception parseException) {
                fail("清理后的JSON无法被正确解析: " + parseException.getMessage());
            }

            log.info("JSON字符串清理功能测试通过");
            log.debug("原始字符串长度: {}, 清理后长度: {}", jsonWithControlChars.length(), sanitized.length());

        } catch (Exception e) {
            log.error("测试JSON字符串清理功能失败", e);
            fail("测试JSON字符串清理功能失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试控制字符转义功能")
    public void testControlCharacterEscaping() {
        log.info("测试控制字符转义功能");

        try {
            // 获取私有方法
            Method sanitizeMethod = KbTplLearningServiceImpl.class.getDeclaredMethod("sanitizeJsonString", String.class);
            sanitizeMethod.setAccessible(true);

            // 测试各种控制字符
            String jsonWithVariousControlChars = "{\n" +
                    "    \"test1\": \"line1\nline2\",\n" +
                    "    \"test2\": \"tab\there\",\n" +
                    "    \"test3\": \"carriage\rreturn\"\n" +
                    "}";

            // 调用清理方法
            String sanitized = (String) sanitizeMethod.invoke(kbTplLearningService, jsonWithVariousControlChars);

            // 验证清理后的JSON可以被正确解析
            ObjectMapper mapper = new ObjectMapper();
            JsonNode node = mapper.readTree(sanitized);

            // 验证各种控制字符都被正确处理
            assertEquals("line1\nline2", node.get("test1").asText(), "换行符应该被正确处理");
            assertEquals("tab\there", node.get("test2").asText(), "制表符应该被正确处理");
            assertEquals("carriage\rreturn", node.get("test3").asText(), "回车符应该被正确处理");

            log.info("控制字符转义功能测试通过");

        } catch (Exception e) {
            log.error("测试控制字符转义功能失败", e);
            fail("测试控制字符转义功能失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试JSON替换功能 - Germmy测试用例")
    public void testJsonReplaceByGermmy() {
        log.info("测试JSON替换功能 - Germmy测试用例");

        try {
            // 获取私有方法
            Method sanitizeMethod = KbTplLearningServiceImpl.class.getDeclaredMethod("sanitizeJsonString", String.class);
            sanitizeMethod.setAccessible(true);

            // 使用WebClient调用测试接口获取JSON数据
            WebClient webClient = WebClient.builder().build();
            String baseUrl = "http://localhost:" + port;

            log.info("调用测试接口: {}/public/test/json", baseUrl);
            String inputJson = webClient.get()
                    .uri(baseUrl + "/public/test/json")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            assertNotNull(inputJson, "从接口获取的JSON不应该为null");
            log.info("成功从接口获取JSON数据，长度: {}", inputJson.length());

            // 注意：期望的JSON结构会根据实际接口返回的数据动态验证
            // 这里不再硬编码期望的JSON，而是验证清理后的JSON能够正确解析

            // 调用清理方法
            String actualJson = (String) sanitizeMethod.invoke(kbTplLearningService, inputJson);

            log.info("输入JSON长度: {}", inputJson.length());
            log.info("输出JSON长度: {}", actualJson.length());

            // 验证清理后的JSON可以被正确解析
            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualNode = mapper.readTree(actualJson);

            // 验证JSON结构正确
            assertTrue(actualNode.isArray(), "结果应该是数组");
            assertEquals(3, actualNode.size(), "数组应该包含3个元素");

            // 验证第一个元素
            JsonNode firstItem = actualNode.get(0);
            assertEquals("0", firstItem.get("typeCode").asText(), "第一个元素的typeCode应该是0");
            assertEquals("售前", firstItem.get("typeName").asText(), "第一个元素的typeName应该是售前");

            String firstContent = firstItem.get("content").asText();
            assertTrue(firstContent.contains("\n"), "第一个元素的content应该包含换行符");
            assertTrue(firstContent.contains("jd_99aabbcc1122"), "第一个元素应该包含正确的用户ID");
            assertTrue(firstContent.contains("https://item.jd.com/100345678901.html"), "第一个元素应该包含URL");
            assertTrue(firstContent.contains("纽强自营-热情"), "第一个元素应该包含客服名称");
            assertTrue(firstContent.contains("防晒霜和驱蚊液"), "第一个元素应该包含防晒霜和驱蚊液相关内容");
            assertTrue(firstContent.contains("试用装"), "第一个元素应该包含试用装相关内容");
            assertTrue(firstContent.contains("偏油"), "第一个元素应该包含肤质相关内容");

            // 验证第二个元素
            JsonNode secondItem = actualNode.get(1);
            assertEquals("1", secondItem.get("typeCode").asText(), "第二个元素的typeCode应该是1");
            assertEquals("售中", secondItem.get("typeName").asText(), "第二个元素的typeName应该是售中");

            String secondContent = secondItem.get("content").asText();
            assertTrue(secondContent.contains("\n"), "第二个元素的content应该包含换行符");
            assertTrue(secondContent.contains("jd_abcdef123456"), "第二个元素应该包含正确的用户ID");
            assertTrue(secondContent.contains("纽强自营-耐心"), "第二个元素应该包含客服名称");
            assertTrue(secondContent.contains("什么时候能发货"), "第二个元素应该包含发货相关问题");
            assertTrue(secondContent.contains("订单号是 123456789"), "第二个元素应该包含订单号");
            assertTrue(secondContent.contains("修改收货地址"), "第二个元素应该包含地址修改相关内容");
            assertTrue(secondContent.contains("支付方式"), "第二个元素应该包含支付方式相关内容");

            // 验证第三个元素
            JsonNode thirdItem = actualNode.get(2);
            assertEquals("2", thirdItem.get("typeCode").asText(), "第三个元素的typeCode应该是2");
            assertEquals("售后", thirdItem.get("typeName").asText(), "第三个元素的typeName应该是售后");

            String thirdContent = thirdItem.get("content").asText();
            assertTrue(thirdContent.contains("\n"), "第三个元素的content应该包含换行符");
            assertTrue(thirdContent.contains("jd_123456abcdef"), "第三个元素应该包含正确的用户ID");
            assertTrue(thirdContent.contains("纽强自营-专业"), "第三个元素应该包含客服名称");
            assertTrue(thirdContent.contains("质量问题"), "第三个元素应该包含质量问题");
            assertTrue(thirdContent.contains("破损"), "第三个元素应该包含破损相关内容");
            assertTrue(thirdContent.contains("订单号是 987654321"), "第三个元素应该包含订单号");
            assertTrue(thirdContent.contains("补发"), "第三个元素应该包含补发相关内容");

            log.info("Germmy测试用例验证通过");
            log.debug("第一个元素(售前)content内容: {}", firstContent);
            log.debug("第二个元素(售中)content内容: {}", secondContent);
            log.debug("第三个元素(售后)content内容: {}", thirdContent);

        } catch (Exception e) {
            log.error("测试JSON替换功能失败", e);
            fail("测试JSON替换功能失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试JSON替换功能 - CRLF测试用例")
    public void testJsonWithCRLF() {
        log.info("测试JSON替换功能 - CRLF测试用例");

        try {
            // 获取私有方法
            Method sanitizeMethod = KbTplLearningServiceImpl.class.getDeclaredMethod("sanitizeJsonString", String.class);
            sanitizeMethod.setAccessible(true);

            // 使用WebClient调用测试接口获取JSON数据
            WebClient webClient = WebClient.builder().build();
            String baseUrl = "http://localhost:" + port;

            log.info("调用测试接口: {}/public/test/jsonWithCRLF", baseUrl);
            String inputJson = webClient.get()
                    .uri(baseUrl + "/public/test/jsonWithCRLF")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            assertNotNull(inputJson, "从接口获取的JSON不应该为null");
            log.info("成功从接口获取JSON数据，长度: {}", inputJson.length());

            // 调用清理方法
            String actualJson = (String) sanitizeMethod.invoke(kbTplLearningService, inputJson);

            log.info("输入JSON长度: {}", inputJson.length());
            log.info("输出JSON长度: {}", actualJson.length());

            // 验证清理后的JSON可以被正确解析
            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualNode = mapper.readTree(actualJson);

            // 验证JSON结构正确
            assertTrue(actualNode.isArray(), "结果应该是数组");
            assertEquals(2, actualNode.size(), "数组应该包含2个元素");

            // 验证第一个元素
            JsonNode firstItem = actualNode.get(0);
            assertEquals("0", firstItem.get("typeCode").asText(), "第一个元素的typeCode应该是0");
            assertEquals("售前", firstItem.get("typeName").asText(), "第一个元素的typeName应该是售前");

            String firstContent = firstItem.get("content").asText();
            assertTrue(firstContent.contains("\n"), "第一个元素的content应该包含换行符");
            assertTrue(firstContent.contains("jd_9999999999999"), "第一个元素应该包含正确的用户ID");
            assertTrue(firstContent.contains("https://item.jd.com/100233209869.html"), "第一个元素应该包含URL");
            assertTrue(firstContent.contains("纽强自营-热情"), "第一个元素应该包含客服名称");
            assertTrue(firstContent.contains("宝宝两岁了"), "第一个元素应该包含宝宝年龄相关内容");
            assertTrue(firstContent.contains("黄色霜"), "第一个元素应该包含产品推荐内容");
            assertTrue(firstContent.contains("试用装"), "第一个元素应该包含试用装相关内容");

            // 验证第二个元素
            JsonNode secondItem = actualNode.get(1);
            assertEquals("0", secondItem.get("typeCode").asText(), "第二个元素的typeCode应该是0");
            assertEquals("售前", secondItem.get("typeName").asText(), "第二个元素的typeName应该是售前");

            String secondContent = secondItem.get("content").asText();
            assertTrue(secondContent.contains("\n"), "第二个元素的content应该包含换行符");
            assertTrue(secondContent.contains("jd_8888888888888"), "第二个元素应该包含正确的用户ID");
            assertTrue(secondContent.contains("纽强自营-耐心"), "第二个元素应该包含客服名称");
            assertTrue(secondContent.contains("洗护用品"), "第二个元素应该包含洗护用品相关内容");
            assertTrue(secondContent.contains("三个月大"), "第二个元素应该包含宝宝年龄相关内容");
            assertTrue(secondContent.contains("蓝色乳"), "第二个元素应该包含产品推荐内容");
            assertTrue(secondContent.contains("国产品牌"), "第二个元素应该包含品牌相关内容");

            log.info("CRLF测试用例验证通过");
            log.debug("第一个元素(售前)content内容: {}", firstContent);
            log.debug("第二个元素(售前)content内容: {}", secondContent);

        } catch (Exception e) {
            log.error("测试JSON替换功能失败", e);
            fail("测试JSON替换功能失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试预处理记录
     */
    private void createTestPreRecords() {
        for (int i = 0; i < 3; i++) {
            TrainKbTplPre preRecord = new TrainKbTplPre();
            preRecord.setTplId(testTplId);
            preRecord.setContent("测试聊天内容 " + i + "：\n客户：你好，我想了解一下这个产品\n客服：您好，很高兴为您服务");
            preRecord.setIndex(i);
            preRecord.setLearnStatus(LearnStatus.UN_LEARN.getCode());
            preRecord.setTeamId(testTeamId);
            preRecord.setCreator("test");
            preRecord.setUpdater("test");
            
            int result = trainKbTplPreMapper.insert(preRecord);
            assertTrue(result > 0);
            log.info("创建测试预处理记录: id={}, index={}", preRecord.getId(), i);
        }
    }
}
