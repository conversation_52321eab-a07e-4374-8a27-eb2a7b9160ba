package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.converkb.QaImportResponse;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportDtlMapper;
import com.yiyi.ai_train_playground.service.OssService;
import com.yiyi.ai_train_playground.service.converkb.QaImportService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 问答导入服务测试类
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/service/converkb/impl/QaImportServiceImplTest.sql")
public class QaImportServiceImplTest {
    
    @Autowired
    private QaImportService qaImportService;
    
    @Autowired
    private TrainQaImportDtlMapper trainQaImportDtlMapper;

    @MockBean
    private OssService ossService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_USER_ID = "1";

    @BeforeEach
    public void setUp() {
        // Mock OSS服务返回测试URL
        when(ossService.upload(any(), eq(0), eq(TEST_TEAM_ID)))
                .thenReturn("https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp.md/excel/xls_1_" + System.currentTimeMillis() + "_test.xlsx");
    }
    
    @Test
    public void testImportQaExcel_Success() throws IOException {
        // 创建测试Excel文件
        MockMultipartFile file = createTestExcelFile();
        
        // 执行导入
        QaImportResponse response = qaImportService.importQaExcel(file, TEST_TEAM_ID, TEST_USER_ID);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(2, response.getTotalCount());
        assertEquals(2, response.getSuccessCount());
        assertEquals(0, response.getFailCount());
        assertNotNull(response.getSuccessItems());
        assertEquals(2, response.getSuccessItems().size());
        
        // 验证第一条数据
        QaImportResponse.QaItem firstItem = response.getSuccessItems().get(0);
        assertEquals("防晒干是水", firstItem.getQuestion());
        assertTrue(firstItem.getAnswer().contains("亲爱的"));

        // 验证OSS下载链接
        assertNotNull(response.getSuccessExcelUrl()); // 成功导入应该有Excel下载链接
        assertTrue(response.getSuccessExcelUrl().contains("temp.md/excel/")); // 应该在temp.md目录下
        assertNull(response.getFailedExcelUrl()); // 没有失败项，应该为null

        // 验证数据库中的记录
        int count = trainQaImportDtlMapper.existsByQuestion("防晒干是水", TEST_TEAM_ID);
        assertEquals(1, count);
    }
    
    @Test
    public void testValidateQaExcel_EmptyFile() {
        // 创建空文件
        MockMultipartFile file = new MockMultipartFile("file", "test.xlsx", 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", new byte[0]);
        
        // 验证文件
        String result = qaImportService.validateQaExcel(file);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("请选择要上传的Excel文件", result);
    }
    
    @Test
    public void testValidateQaExcel_WrongFormat() {
        // 创建错误格式的文件
        MockMultipartFile file = new MockMultipartFile("file", "test.txt", "text/plain", 
                "测试内容".getBytes(StandardCharsets.UTF_8));
        
        // 验证文件
        String result = qaImportService.validateQaExcel(file);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("文件格式不正确"));
    }
    
    @Test
    public void testValidateQaExcel_WrongHeader() throws IOException {
        // 创建错误表头的Excel文件
        MockMultipartFile file = createWrongHeaderExcelFile();
        
        // 验证文件
        String result = qaImportService.validateQaExcel(file);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("表头格式不正确"));
    }
    
    @Test
    public void testValidateQaExcel_Success() throws IOException {
        // 创建正确格式的Excel文件
        MockMultipartFile file = createTestExcelFile();
        
        // 验证文件
        String result = qaImportService.validateQaExcel(file);
        
        // 验证结果
        assertNull(result); // 验证通过返回null
    }
    
    @Test
    public void testImportQaExcel_DuplicateQuestion() throws IOException {
        // 先导入一次
        MockMultipartFile file1 = createTestExcelFile();
        qaImportService.importQaExcel(file1, TEST_TEAM_ID, TEST_USER_ID);
        
        // 再次导入相同数据
        MockMultipartFile file2 = createTestExcelFile();
        QaImportResponse response = qaImportService.importQaExcel(file2, TEST_TEAM_ID, TEST_USER_ID);
        
        // 验证结果 - 应该有重复错误
        assertNotNull(response);
        assertEquals(2, response.getTotalCount());
        assertEquals(0, response.getSuccessCount());
        assertEquals(2, response.getFailCount());
        assertNotNull(response.getFailedItems());
        assertEquals(2, response.getFailedItems().size());
        
        // 验证错误信息
        QaImportResponse.FailedItem failedItem = response.getFailedItems().get(0);
        assertTrue(failedItem.getReason().contains("问题已存在"));
    }
    
    /**
     * 创建测试用的Excel文件
     */
    private MockMultipartFile createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("问答数据");
        
        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("问题");
        headerRow.createCell(1).setCellValue("答案");
        
        // 创建数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("防晒干是水");
        row1.createCell(1).setCellValue("亲爱的～防晒到货水分的高的状态下是哦～利给用刀搭配防水喷雾合即可的哦～这个是正常现象～并不影响的效果哦～");
        
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("有试用吗");
        row2.createCell(1).setCellValue("亲爱的，若有赠送款，可以用1个哦。");
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        byte[] excelBytes = outputStream.toByteArray();
        
        workbook.close();
        outputStream.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelBytes);
    }
    
    /**
     * 创建错误表头的Excel文件
     */
    private MockMultipartFile createWrongHeaderExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("问答数据");

        // 创建错误表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("错误表头1");
        headerRow.createCell(1).setCellValue("错误表头2");

        // 添加一行数据，避免被判断为"没有数据"
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("测试数据1");
        dataRow.createCell(1).setCellValue("测试数据2");

        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        byte[] excelBytes = outputStream.toByteArray();

        workbook.close();
        outputStream.close();

        return new MockMultipartFile("file", "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelBytes);
    }
}
