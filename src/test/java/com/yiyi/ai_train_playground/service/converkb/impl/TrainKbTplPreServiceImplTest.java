package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.entity.converkb.TrainKbTplPre;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplPreService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 会话知识库模板预处理服务测试类
 */
@SpringBootTest
// @ActiveProfiles("home")
@Transactional
public class TrainKbTplPreServiceImplTest {
    
    @Autowired
    private TrainKbTplPreService trainKbTplPreService;
    
    private Long testTeamId = 1L;
    private String testUserId = "test-user";
    private Long testTplId = 1L;
    
    @BeforeEach
    public void setUp() {
        // 测试前准备工作
    }
    
    @Test
    public void testCreatePreRecord() {
        // 准备测试数据
        String content = "这是一个测试内容块";
        Integer index = 1;
        String learnStatus = "un_learn";
        
        // 执行测试
        Long recordId = trainKbTplPreService.createPreRecord(testTplId, content, index, learnStatus, testTeamId, testUserId);
        
        // 验证结果
        assertNotNull(recordId);
        assertTrue(recordId > 0);
        
        // 验证创建的数据
        TrainKbTplPre record = trainKbTplPreService.getById(recordId, testTeamId);
        assertNotNull(record);
        assertEquals(testTplId, record.getTplId());
        assertEquals(content, record.getContent());
        assertEquals(index, record.getIndex());
        assertEquals(learnStatus, record.getLearnStatus());
        assertEquals(testTeamId, record.getTeamId());
    }
    
    @Test
    public void testCreatePreRecordWithInvalidParams() {
        // 测试空参数
        assertThrows(IllegalArgumentException.class, () -> {
            trainKbTplPreService.createPreRecord(null, "content", 1, "un_learn", testTeamId, testUserId);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            trainKbTplPreService.createPreRecord(testTplId, null, 1, "un_learn", testTeamId, testUserId);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            trainKbTplPreService.createPreRecord(testTplId, "content", null, "un_learn", testTeamId, testUserId);
        });
    }
    
    @Test
    public void testBatchCreatePreRecords() {
        // 准备测试数据
        List<String> contents = Arrays.asList(
            "第一个内容块",
            "第二个内容块", 
            "第三个内容块"
        );
        String learnStatus = "un_learn";
        
        // 执行测试
        int result = trainKbTplPreService.batchCreatePreRecords(testTplId, contents, learnStatus, testTeamId, testUserId);
        
        // 验证结果
        assertEquals(3, result);
        
        // 验证创建的数据
        List<TrainKbTplPre> records = trainKbTplPreService.getByTplId(testTplId, testTeamId);
        assertEquals(3, records.size());
        
        // 验证顺序和内容
        for (int i = 0; i < records.size(); i++) {
            TrainKbTplPre record = records.get(i);
            assertEquals(testTplId, record.getTplId());
            assertEquals(contents.get(i), record.getContent());
            assertEquals(i + 1, record.getIndex());
            assertEquals(learnStatus, record.getLearnStatus());
            assertEquals(testTeamId, record.getTeamId());
        }
    }
    
    @Test
    public void testGetByTplId() {
        // 先创建测试数据
        List<String> contents = Arrays.asList("内容1", "内容2", "内容3");
        trainKbTplPreService.batchCreatePreRecords(testTplId, contents, "un_learn", testTeamId, testUserId);
        
        // 查询数据
        List<TrainKbTplPre> records = trainKbTplPreService.getByTplId(testTplId, testTeamId);
        
        // 验证结果
        assertNotNull(records);
        assertEquals(3, records.size());
        
        // 验证排序（按index升序）
        for (int i = 0; i < records.size(); i++) {
            assertEquals(i + 1, records.get(i).getIndex());
        }
    }
    
    @Test
    public void testGetByTplIdAndLearnStatus() {
        // 创建不同学习状态的测试数据
        trainKbTplPreService.createPreRecord(testTplId, "未学习内容1", 1, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "未学习内容2", 2, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "学习中内容", 3, "learning", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "已学习内容", 4, "learned", testTeamId, testUserId);
        
        // 查询未学习的记录
        List<TrainKbTplPre> unLearnRecords = trainKbTplPreService.getByTplIdAndLearnStatus(testTplId, "un_learn", testTeamId);
        assertEquals(2, unLearnRecords.size());
        
        // 查询学习中的记录
        List<TrainKbTplPre> learningRecords = trainKbTplPreService.getByTplIdAndLearnStatus(testTplId, "learning", testTeamId);
        assertEquals(1, learningRecords.size());
        
        // 查询已学习的记录
        List<TrainKbTplPre> learnedRecords = trainKbTplPreService.getByTplIdAndLearnStatus(testTplId, "learned", testTeamId);
        assertEquals(1, learnedRecords.size());
    }
    
    @Test
    public void testUpdatePreRecord() {
        // 先创建测试数据
        Long recordId = trainKbTplPreService.createPreRecord(testTplId, "原始内容", 1, "un_learn", testTeamId, testUserId);
        
        // 更新记录
        boolean success = trainKbTplPreService.updatePreRecord(recordId, "更新后的内容", 2, "learning", testTeamId, testUserId);
        
        // 验证结果
        assertTrue(success);
        
        // 验证更新后的数据
        TrainKbTplPre record = trainKbTplPreService.getById(recordId, testTeamId);
        assertNotNull(record);
        assertEquals("更新后的内容", record.getContent());
        assertEquals(2, record.getIndex());
        assertEquals("learning", record.getLearnStatus());
    }

    @Test
    public void testUpdatePreRecordDynamicSQL() {
        // 先创建测试数据
        Long recordId = trainKbTplPreService.createPreRecord(testTplId, "原始内容", 1, "un_learn", testTeamId, testUserId);

        // 获取原始记录
        TrainKbTplPre originalRecord = trainKbTplPreService.getById(recordId, testTeamId);
        assertNotNull(originalRecord);

        // 测试1：只更新content字段
        boolean success1 = trainKbTplPreService.updatePreRecord(recordId, "只更新内容", null, null, testTeamId, testUserId);
        assertTrue(success1);

        TrainKbTplPre record1 = trainKbTplPreService.getById(recordId, testTeamId);
        assertEquals("只更新内容", record1.getContent());
        assertEquals(originalRecord.getIndex(), record1.getIndex()); // index应该保持不变
        assertEquals(originalRecord.getLearnStatus(), record1.getLearnStatus()); // learnStatus应该保持不变

        // 测试2：只更新index字段
        boolean success2 = trainKbTplPreService.updatePreRecord(recordId, null, 5, null, testTeamId, testUserId);
        assertTrue(success2);

        TrainKbTplPre record2 = trainKbTplPreService.getById(recordId, testTeamId);
        assertEquals("只更新内容", record2.getContent()); // content应该保持不变
        assertEquals(5, record2.getIndex());
        assertEquals(originalRecord.getLearnStatus(), record2.getLearnStatus()); // learnStatus应该保持不变

        // 测试3：只更新learnStatus字段
        boolean success3 = trainKbTplPreService.updatePreRecord(recordId, null, null, "learning", testTeamId, testUserId);
        assertTrue(success3);

        TrainKbTplPre record3 = trainKbTplPreService.getById(recordId, testTeamId);
        assertEquals("只更新内容", record3.getContent()); // content应该保持不变
        assertEquals(5, record3.getIndex()); // index应该保持不变
        assertEquals("learning", record3.getLearnStatus());

        // 测试4：传入空字符串，应该被忽略
        boolean success4 = trainKbTplPreService.updatePreRecord(recordId, "", null, "", testTeamId, testUserId);
        assertTrue(success4);

        TrainKbTplPre record4 = trainKbTplPreService.getById(recordId, testTeamId);
        assertEquals("只更新内容", record4.getContent()); // content应该保持不变（空字符串被忽略）
        assertEquals(5, record4.getIndex()); // index应该保持不变
        assertEquals("learning", record4.getLearnStatus()); // learnStatus应该保持不变（空字符串被忽略）
    }

    @Test
    public void testUpdateLearnStatus() {
        // 创建测试数据
        trainKbTplPreService.createPreRecord(testTplId, "内容1", 1, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容2", 2, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容3", 3, "learning", testTeamId, testUserId);
        
        // 批量更新学习状态
        int result = trainKbTplPreService.updateLearnStatus(testTplId, "un_learn", "learning", testTeamId, testUserId);
        
        // 验证结果
        assertEquals(2, result);
        
        // 验证更新后的状态
        List<TrainKbTplPre> learningRecords = trainKbTplPreService.getByTplIdAndLearnStatus(testTplId, "learning", testTeamId);
        assertEquals(3, learningRecords.size()); // 原来1个learning + 新更新的2个
    }
    
    @Test
    public void testDeletePreRecord() {
        // 先创建测试数据
        Long recordId = trainKbTplPreService.createPreRecord(testTplId, "待删除内容", 1, "un_learn", testTeamId, testUserId);
        
        // 确认记录存在
        assertNotNull(trainKbTplPreService.getById(recordId, testTeamId));
        
        // 删除记录
        boolean success = trainKbTplPreService.deletePreRecord(recordId, testTeamId);
        
        // 验证结果
        assertTrue(success);
        
        // 验证记录已不存在
        assertNull(trainKbTplPreService.getById(recordId, testTeamId));
    }
    
    @Test
    public void testDeleteByTplId() {
        // 创建测试数据
        trainKbTplPreService.createPreRecord(testTplId, "内容1", 1, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容2", 2, "learning", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容3", 3, "learned", testTeamId, testUserId);
        
        // 确认记录存在
        assertEquals(3, trainKbTplPreService.getByTplId(testTplId, testTeamId).size());
        
        // 删除模板的所有记录
        int result = trainKbTplPreService.deleteByTplId(testTplId, testTeamId);
        
        // 验证结果
        assertEquals(3, result);
        
        // 验证记录已全部删除
        assertEquals(0, trainKbTplPreService.getByTplId(testTplId, testTeamId).size());
    }
    
    @Test
    public void testCountMethods() {
        // 创建测试数据
        trainKbTplPreService.createPreRecord(testTplId, "内容1", 1, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容2", 2, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容3", 3, "learning", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容4", 4, "learned", testTeamId, testUserId);
        
        // 测试总数统计
        Long totalCount = trainKbTplPreService.countByTplId(testTplId, testTeamId);
        assertEquals(4L, totalCount);
        
        // 测试按状态统计
        Long unLearnCount = trainKbTplPreService.countByTplIdAndLearnStatus(testTplId, "un_learn", testTeamId);
        assertEquals(2L, unLearnCount);
        
        Long learningCount = trainKbTplPreService.countByTplIdAndLearnStatus(testTplId, "learning", testTeamId);
        assertEquals(1L, learningCount);
        
        Long learnedCount = trainKbTplPreService.countByTplIdAndLearnStatus(testTplId, "learned", testTeamId);
        assertEquals(1L, learnedCount);
    }
    
    @Test
    public void testGetLearnProgress() {
        // 创建测试数据
        trainKbTplPreService.createPreRecord(testTplId, "内容1", 1, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容2", 2, "un_learn", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容3", 3, "learning", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容4", 4, "learned", testTeamId, testUserId);
        trainKbTplPreService.createPreRecord(testTplId, "内容5", 5, "learned", testTeamId, testUserId);
        
        // 获取学习进度
        TrainKbTplPreService.LearnProgress progress = trainKbTplPreService.getLearnProgress(testTplId, testTeamId);
        
        // 验证结果
        assertNotNull(progress);
        assertEquals(5L, progress.getTotalCount());
        assertEquals(2L, progress.getUnLearnCount());
        assertEquals(1L, progress.getLearningCount());
        assertEquals(2L, progress.getLearnedCount());
        
        // 验证进度百分比
        assertEquals(40.0, progress.getProgressPercentage(), 0.01); // 2/5 = 40%
    }
}
