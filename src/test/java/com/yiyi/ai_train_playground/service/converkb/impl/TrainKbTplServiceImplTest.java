package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.converkb.KbTplCreateRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailRequest;
import com.yiyi.ai_train_playground.dto.converkb.KbTplDetailResponse;
import com.yiyi.ai_train_playground.dto.converkb.KbTplListResponse;
import com.yiyi.ai_train_playground.dto.converkb.KbTplQueryRequest;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 会话知识库模板服务测试类
 */
@SpringBootTest
// @ActiveProfiles("home")
@Transactional
public class TrainKbTplServiceImplTest {
    
    @Autowired
    private TrainKbTplService trainKbTplService;
    
    private Long testTeamId = 1L;
    private String testUserId = "test-user";
    
    /**
     * 创建默认的详情分页请求
     */
    private KbTplDetailRequest createDetailRequest() {
        KbTplDetailRequest request = new KbTplDetailRequest();
        request.setPage(1);
        request.setPageSize(10);
        return request;
    }
    
    @BeforeEach
    public void setUp() {
        // 测试前准备工作
    }
    
    @Test
    public void testCreateKbTpl() {
        // 准备测试数据
        KbTplCreateRequest request = createTestRequest();
        
        // 执行测试
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        
        // 验证结果
        assertNotNull(tplId);
        assertTrue(tplId > 0);
        
        // 验证创建的数据
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest());
        assertNotNull(detail);
        assertEquals(request.getName(), detail.getName());
        assertEquals(0L, detail.getTokens()); // 初始化为0
        assertNotNull(detail.getDetails());
        assertEquals(2, detail.getDetails().getTotal()); // 分页结果中的总数
    }
    
    @Test
    public void testCreateKbTplWithDuplicateName() {
        // 准备测试数据
        KbTplCreateRequest request = createTestRequest();
        
        // 创建第一个模板
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        assertNotNull(tplId);
        
        // 尝试创建同名模板，应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        });
    }
    
    @Test
    public void testGetKbTplDetail() {
        // 创建测试模板
        KbTplCreateRequest request = createTestRequest();
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        
        // 查询详情
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest());
        
        // 验证结果
        assertNotNull(detail);
        assertEquals(tplId, detail.getId());
        assertEquals(request.getName(), detail.getName());
        assertEquals(0L, detail.getTokens());
        assertNotNull(detail.getCreateTime());
        assertNotNull(detail.getVersion());
        assertNotNull(detail.getDetails());
        assertEquals(2, detail.getDetails().getTotal());
        
        // 验证明细内容（删除了sender和senderType字段）
        List<KbTplDetailResponse.KbTplDetailItem> details = detail.getDetails().getRecords();
        assertEquals("您好，有什么可以帮助您的吗？", details.get(0).getContent());
        assertEquals("pre_sales", details.get(0).getTplTypeCode());
    }
    
    @Test
    public void testGetKbTplDetailNotFound() {
        // 查询不存在的模板
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(99999L, testTeamId, createDetailRequest());
        
        // 应该返回null
        assertNull(detail);
    }
    
    @Test
    public void testGetKbTplPageList() {
        // 创建多个测试模板
        for (int i = 1; i <= 3; i++) {
            KbTplCreateRequest request = createTestRequest();
            request.setName("测试模板" + i);
            trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        }
        
        // 分页查询
        KbTplQueryRequest queryRequest = new KbTplQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<KbTplListResponse> result = trainKbTplService.getKbTplPageList(queryRequest, testTeamId);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotal() >= 3);
        assertNotNull(result.getRecords());
        assertTrue(result.getRecords().size() >= 3);
        
        // 验证列表数据
        KbTplListResponse first = result.getRecords().get(0);
        assertNotNull(first.getId());
        assertNotNull(first.getName());
        assertEquals(0L, first.getTokens());
        assertEquals(2, first.getDetailCount()); // 每个模板有2条明细
    }
    
    @Test
    public void testGetKbTplPageListWithFilter() {
        // 创建测试模板
        KbTplCreateRequest request = createTestRequest();
        request.setName("售前客服模板");
        trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        
        // 按名称筛选
        KbTplQueryRequest queryRequest = new KbTplQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        queryRequest.setName("售前");
        
        PageResult<KbTplListResponse> result = trainKbTplService.getKbTplPageList(queryRequest, testTeamId);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotal() >= 1);
        assertTrue(result.getRecords().get(0).getName().contains("售前"));
    }
    
    @Test
    public void testUpdateKbTpl() {
        // 创建测试模板
        KbTplCreateRequest createRequest = createTestRequest();
        Long tplId = trainKbTplService.createKbTpl(createRequest, testTeamId, testUserId);
        
        // 准备更新数据
        KbTplCreateRequest updateRequest = new KbTplCreateRequest();
        updateRequest.setName("更新后的模板名称");
        
        KbTplCreateRequest.KbTplDetailItem item1 = new KbTplCreateRequest.KbTplDetailItem();
        item1.setContent("更新后的对话内容");
        item1.setTplType("after_sale");
        
        updateRequest.setDetails(Arrays.asList(item1));
        
        // 执行更新
        boolean success = trainKbTplService.updateKbTpl(tplId, updateRequest, testTeamId, testUserId);
        
        // 验证结果
        assertTrue(success);
        
        // 验证更新后的数据
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest());
        assertEquals("更新后的模板名称", detail.getName());
        assertEquals(1, detail.getDetails().getTotal());
        assertEquals("更新后的对话内容", detail.getDetails().getRecords().get(0).getContent());
        assertEquals("after_sale", detail.getDetails().getRecords().get(0).getTplTypeCode());
    }
    
    @Test
    public void testDeleteKbTpl() {
        // 创建测试模板
        KbTplCreateRequest request = createTestRequest();
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        
        // 确认模板存在
        assertNotNull(trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest()));
        
        // 删除模板
        boolean success = trainKbTplService.deleteKbTpl(tplId, testTeamId);
        
        // 验证结果
        assertTrue(success);
        
        // 验证模板已不存在
        assertNull(trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest()));
    }
    
    @Test
    public void testCheckNameExists() {
        // 创建测试模板
        KbTplCreateRequest request = createTestRequest();
        String testName = "唯一名称测试";
        request.setName(testName);
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        
        // 检查存在的名称
        assertTrue(trainKbTplService.checkNameExists(testName, testTeamId, null));
        
        // 检查不存在的名称
        assertFalse(trainKbTplService.checkNameExists("不存在的名称", testTeamId, null));
        
        // 排除自身检查（用于更新时）
        assertFalse(trainKbTplService.checkNameExists(testName, testTeamId, tplId));
    }
    
    @Test
    public void testUpdateKbTplTokens() {
        // 创建测试模板
        KbTplCreateRequest request = createTestRequest();
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);
        
        // 更新token数量
        Long newTokens = 1500L;
        boolean success = trainKbTplService.updateKbTplTokens(tplId, newTokens, testTeamId);
        
        // 验证结果
        assertTrue(success);
        
        // 验证更新后的数据
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest());
        assertEquals(newTokens, detail.getTokens());
    }
    
    @Test
    public void testCreateKbTplWithFileTypeAndLearnStatus() {
        // 准备测试数据，包含新字段
        KbTplCreateRequest request = createTestRequest();
        request.setFileType("csv");
        request.setLearnStatus("un_learn");

        // 执行测试
        Long tplId = trainKbTplService.createKbTpl(request, testTeamId, testUserId);

        // 验证结果
        assertNotNull(tplId);
        assertTrue(tplId > 0);

        // 验证创建的数据包含新字段
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest());
        assertNotNull(detail);
        assertEquals(request.getName(), detail.getName());
        assertEquals("csv", detail.getFileType());
        assertEquals("un_learn", detail.getLearnStatus());
        assertEquals(0L, detail.getTokens());
        assertNotNull(detail.getDetails());
        assertEquals(2, detail.getDetails().getTotal());
    }

    @Test
    public void testUpdateKbTplWithNewFields() {
        // 创建测试模板
        KbTplCreateRequest createRequest = createTestRequest();
        createRequest.setFileType("csv");
        createRequest.setLearnStatus("un_learn");
        Long tplId = trainKbTplService.createKbTpl(createRequest, testTeamId, testUserId);

        // 准备更新数据，包含新字段
        KbTplCreateRequest updateRequest = new KbTplCreateRequest();
        updateRequest.setName("更新后的模板名称");
        updateRequest.setFileType("excel");
        updateRequest.setLearnStatus("learning");

        KbTplCreateRequest.KbTplDetailItem item1 = new KbTplCreateRequest.KbTplDetailItem();
        item1.setContent("更新后的对话内容");
        item1.setTplType("after_sale");

        updateRequest.setDetails(Arrays.asList(item1));

        // 执行更新
        boolean success = trainKbTplService.updateKbTpl(tplId, updateRequest, testTeamId, testUserId);

        // 验证结果
        assertTrue(success);

        // 验证更新后的数据包含新字段
        KbTplDetailResponse detail = trainKbTplService.getKbTplDetail(tplId, testTeamId, createDetailRequest());
        assertEquals("更新后的模板名称", detail.getName());
        assertEquals("excel", detail.getFileType());
        assertEquals("learning", detail.getLearnStatus());
        assertEquals(1, detail.getDetails().getTotal());
        assertEquals("更新后的对话内容", detail.getDetails().getRecords().get(0).getContent());
        assertEquals("after_sale", detail.getDetails().getRecords().get(0).getTplTypeCode());
    }

    @Test
    public void testGetKbTplPageListWithNewFields() {
        // 创建多个测试模板，包含不同的新字段值
        KbTplCreateRequest request1 = createTestRequest();
        request1.setName("CSV模板");
        request1.setFileType("csv");
        request1.setLearnStatus("un_learn");
        trainKbTplService.createKbTpl(request1, testTeamId, testUserId);

        KbTplCreateRequest request2 = createTestRequest();
        request2.setName("Excel模板");
        request2.setFileType("excel");
        request2.setLearnStatus("learning");
        trainKbTplService.createKbTpl(request2, testTeamId, testUserId);

        KbTplCreateRequest request3 = createTestRequest();
        request3.setName("TXT模板");
        request3.setFileType("txt");
        request3.setLearnStatus("learned");
        trainKbTplService.createKbTpl(request3, testTeamId, testUserId);

        // 分页查询
        KbTplQueryRequest queryRequest = new KbTplQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);

        PageResult<KbTplListResponse> result = trainKbTplService.getKbTplPageList(queryRequest, testTeamId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotal() >= 3);
        assertNotNull(result.getRecords());
        assertTrue(result.getRecords().size() >= 3);

        // 验证列表数据包含新字段
        boolean foundCsvTemplate = false;
        boolean foundExcelTemplate = false;
        boolean foundTxtTemplate = false;

        for (KbTplListResponse item : result.getRecords()) {
            assertNotNull(item.getId());
            assertNotNull(item.getName());

            if ("CSV模板".equals(item.getName())) {
                assertEquals("csv", item.getFileType());
                assertEquals("un_learn", item.getLearnStatusCode());
                foundCsvTemplate = true;
            } else if ("Excel模板".equals(item.getName())) {
                assertEquals("excel", item.getFileType());
                assertEquals("learning", item.getLearnStatusCode());
                foundExcelTemplate = true;
            } else if ("TXT模板".equals(item.getName())) {
                assertEquals("txt", item.getFileType());
                assertEquals("learned", item.getLearnStatusCode());
                foundTxtTemplate = true;
            }
        }

        assertTrue(foundCsvTemplate);
        assertTrue(foundExcelTemplate);
        assertTrue(foundTxtTemplate);
    }

    /**
     * 测试根据任务ID获取随机知识库明细内容 - 参数验证
     */
    @Test
    public void testGetRandomKbDetailByTaskIdParameterValidation() {
        // 测试参数为null的情况
        String content1 = trainKbTplService.getRandomKbDetailByTaskId(null, testTeamId);
        assertNull(content1, "taskId为null应该返回null");

        String content2 = trainKbTplService.getRandomKbDetailByTaskId(1L, null);
        assertNull(content2, "teamId为null应该返回null");

        String content3 = trainKbTplService.getRandomKbDetailByTaskId(null, null);
        assertNull(content3, "参数都为null应该返回null");

        // 测试不存在的任务ID
        String content4 = trainKbTplService.getRandomKbDetailByTaskId(999999L, testTeamId);
        assertNull(content4, "不存在的任务ID应该返回null");
    }

    /**
     * 测试根据任务ID获取随机知识库明细内容 - 完整功能测试
     */
    @Test
    @Sql(scripts = "classpath:db/com/yiyi/ai_train_playground/service/converkb/impl/kb_tpl_random_test.sql",
         executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void testGetRandomKbDetailByTaskIdWithData() {
        // 测试任务1（有知识库数据）
        String content1 = trainKbTplService.getRandomKbDetailByTaskId(1L, testTeamId);
        assertNotNull(content1, "任务1应该能获取到知识库内容");
        assertTrue(content1.length() > 0, "内容不应该为空");

        // 验证内容是否来自预期的知识库
        assertTrue(content1.contains("您好") || content1.contains("产品") || content1.contains("退换货"),
                  "内容应该来自测试知识库");

        // 测试任务2（有知识库数据）
        String content2 = trainKbTplService.getRandomKbDetailByTaskId(2L, testTeamId);
        assertNotNull(content2, "任务2应该能获取到知识库内容");
        assertTrue(content2.length() > 0, "内容不应该为空");

        // 测试任务3（没有关联知识库）
        String content3 = trainKbTplService.getRandomKbDetailByTaskId(3L, testTeamId);
        assertNull(content3, "任务3没有关联知识库，应该返回null");

        // 测试团队隔离
        String content4 = trainKbTplService.getRandomKbDetailByTaskId(1L, 999L);
        assertNull(content4, "错误的团队ID应该获取不到内容");
    }

    /**
     * 创建测试请求数据
     */
    private KbTplCreateRequest createTestRequest() {
        KbTplCreateRequest request = new KbTplCreateRequest();
        request.setName("测试会话知识库模板");

        // 创建明细数据
        KbTplCreateRequest.KbTplDetailItem item1 = new KbTplCreateRequest.KbTplDetailItem();
        item1.setContent("您好，有什么可以帮助您的吗？");
        item1.setTplType("pre_sales");

        KbTplCreateRequest.KbTplDetailItem item2 = new KbTplCreateRequest.KbTplDetailItem();
        item2.setContent("我想了解一下这个产品的功能");
        item2.setTplType("pre_sales");

        request.setDetails(Arrays.asList(item1, item2));

        return request;
    }
}