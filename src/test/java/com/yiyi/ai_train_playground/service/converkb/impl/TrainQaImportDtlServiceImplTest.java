package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.converkb.QaSimpleDto;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportDtl;
import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportDtlMapper;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportMainMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainQaImportDtlService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaImportDtlService 测试类
 * 测试问答导入明细服务的各种方法
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/service/converkb/impl/TrainQaImportDtlServiceImplTest.sql")
public class TrainQaImportDtlServiceImplTest {

    @Autowired
    private TrainQaImportDtlService trainQaImportDtlService;
    
    @Autowired
    private TrainQaImportDtlMapper trainQaImportDtlMapper;
    
    @Autowired
    private TrainQaImportMainMapper trainQaImportMainMapper;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_USER_ID = "testUser";

    private Long qaMainId1; // 主表1的ID
    private Long qaMainId2; // 主表2的ID

    @BeforeEach
    public void setUp() {
        // 创建测试用的主表记录
        qaMainId1 = createTestMainRecord("测试知识库1", TEST_TEAM_ID);
        qaMainId2 = createTestMainRecord("测试知识库2", TEST_TEAM_ID);
        
        // 插入测试数据
        insertTestData();
        
        log.info("测试准备完成 - 主表ID1: {}, 主表ID2: {}", qaMainId1, qaMainId2);
    }

    /**
     * 测试getAllDtlByMainId方法
     */
    @Test
    public void testGetAllDtlByMainId() {
        // 执行查询
        List<QaSimpleDto> result = trainQaImportDtlService.getAllDtlByMainId(qaMainId1);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size()); // qaMainId1应该有5条记录
        
        log.info("=== getAllDtlByMainId测试结果 ===");
        for (QaSimpleDto dto : result) {
            log.info("问题: {}, 答案: {}", dto.getQuestion(), dto.getAnswer());
            assertNotNull(dto.getQuestion());
            assertNotNull(dto.getAnswer());
        }
    }

    /**
     * 测试getRdmListByMain方法
     */
    @Test
    public void testGetRdmListByMain() {
        // 测试随机获取3条记录
        List<QaSimpleDto> result = trainQaImportDtlService.getRdmListByMain(qaMainId1, 3);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        log.info("=== getRdmListByMain测试结果（获取3条） ===");
        for (QaSimpleDto dto : result) {
            log.info("问题: {}, 答案: {}", dto.getQuestion(), dto.getAnswer());
            assertNotNull(dto.getQuestion());
            assertNotNull(dto.getAnswer());
        }
        
        // 测试获取数量超过总数的情况
        List<QaSimpleDto> allResult = trainQaImportDtlService.getRdmListByMain(qaMainId1, 100);
        assertNotNull(allResult);
        assertEquals(5, allResult.size()); // qaMainId1总共有5条记录
        
        // 测试获取0条记录
        List<QaSimpleDto> zeroResult = trainQaImportDtlService.getRdmListByMain(qaMainId1, 0);
        assertNotNull(zeroResult);
        assertEquals(0, zeroResult.size());
        
        log.info("随机获取测试完成 - 请求3条得到: {}条, 请求100条得到: {}条, 请求0条得到: {}条", 
            result.size(), allResult.size(), zeroResult.size());
    }

    /**
     * 测试getRdmListByMain方法的随机性
     */
    @Test
    public void testGetRdmListByMainRandomness() {
        // 多次调用，验证结果的随机性
        List<QaSimpleDto> result1 = trainQaImportDtlService.getRdmListByMain(qaMainId1, 3);
        List<QaSimpleDto> result2 = trainQaImportDtlService.getRdmListByMain(qaMainId1, 3);

        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(3, result1.size());
        assertEquals(3, result2.size());

        // 注意：由于是随机获取，两次结果可能相同也可能不同，这里只验证基本功能
        log.info("=== 随机性测试 ===");
        log.info("第一次结果:");
        for (QaSimpleDto dto : result1) {
            log.info("  问题: {}", dto.getQuestion());
        }
        log.info("第二次结果:");
        for (QaSimpleDto dto : result2) {
            log.info("  问题: {}", dto.getQuestion());
        }
    }

    /**
     * 测试getDtlListByQaMainId方法（不带问题过滤）
     */
    @Test
    public void testGetDtlListByQaMainIdWithoutQuestion() {
        // 执行查询（不带问题过滤）
        List<TrainQaImportDtl> result = trainQaImportDtlService.getDtlListByQaMainId(qaMainId1, null, 0, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size()); // qaMainId1应该有5条记录

        // 验证统计数量
        int count = trainQaImportDtlService.countByQaMainId(qaMainId1, null);
        assertEquals(5, count);

        log.info("=== getDtlListByQaMainId测试结果（无问题过滤） ===");
        for (TrainQaImportDtl dtl : result) {
            log.info("问题: {}, 答案: {}", dtl.getQuestion(), dtl.getAnswer());
            assertEquals(qaMainId1, dtl.getQaMainId());
        }
    }

    /**
     * 测试getDtlListByQaMainId方法（带问题过滤）
     */
    @Test
    public void testGetDtlListByQaMainIdWithQuestion() {
        // 执行查询（带问题过滤）
        String questionKeyword = "防晒";
        List<TrainQaImportDtl> result = trainQaImportDtlService.getDtlListByQaMainId(qaMainId1, questionKeyword, 0, 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0); // 应该有包含"防晒"的记录
        assertTrue(result.size() <= 5); // 不应该超过qaMainId1的总记录数

        // 验证统计数量
        int count = trainQaImportDtlService.countByQaMainId(qaMainId1, questionKeyword);
        assertEquals(result.size(), count);

        log.info("=== getDtlListByQaMainId测试结果（带问题过滤：{}） ===", questionKeyword);
        for (TrainQaImportDtl dtl : result) {
            log.info("问题: {}, 答案: {}", dtl.getQuestion(), dtl.getAnswer());
            assertEquals(qaMainId1, dtl.getQaMainId());
            assertTrue(dtl.getQuestion().contains(questionKeyword));
        }
    }

    /**
     * 测试getDtlListByQaMainId方法（问题过滤无结果）
     */
    @Test
    public void testGetDtlListByQaMainIdWithQuestionNoResult() {
        // 执行查询（使用不存在的关键词）
        String questionKeyword = "不存在的关键词";
        List<TrainQaImportDtl> result = trainQaImportDtlService.getDtlListByQaMainId(qaMainId1, questionKeyword, 0, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size()); // 应该没有结果

        // 验证统计数量
        int count = trainQaImportDtlService.countByQaMainId(qaMainId1, questionKeyword);
        assertEquals(0, count);

        log.info("=== getDtlListByQaMainId测试结果（无匹配结果：{}） ===", questionKeyword);
        log.info("查询结果数量: {}, 统计数量: {}", result.size(), count);
    }

    /**
     * 创建测试用的主表记录
     */
    private Long createTestMainRecord(String qaImName, Long teamId) {
        TrainQaImportMain mainRecord = new TrainQaImportMain();
        mainRecord.setBatchNo("BATCH_" + System.currentTimeMillis());
        mainRecord.setQaImName(qaImName);
        mainRecord.setQaImDesc("测试知识库描述");
        mainRecord.setTeamId(teamId);
        mainRecord.setCreator(TEST_USER_ID);
        mainRecord.setUpdater(TEST_USER_ID);
        
        int result = trainQaImportMainMapper.insert(mainRecord);
        assertTrue(result > 0);
        assertNotNull(mainRecord.getId());
        
        return mainRecord.getId();
    }

    /**
     * 插入测试数据
     */
    private void insertTestData() {
        // 为qaMainId1插入5条记录
        String[] questions1 = {
            "防晒霜怎么选择？",
            "防晒霜的SPF值是什么意思？", 
            "防晒霜需要卸妆吗？",
            "防晒霜可以当隔离霜用吗？",
            "防晒霜过期了还能用吗？"
        };
        
        String[] answers1 = {
            "选择防晒霜要根据肤质和使用场景来决定...",
            "SPF值表示防晒系数，数值越高防护能力越强...",
            "防晒霜需要用专门的卸妆产品清洁...",
            "防晒霜和隔离霜功能不同，不建议混用...",
            "过期的防晒霜防护效果会下降，不建议使用..."
        };
        
        for (int i = 0; i < questions1.length; i++) {
            TrainQaImportDtl dtl = createTestDtlRecord(qaMainId1, questions1[i], answers1[i], TEST_TEAM_ID);
            trainQaImportDtlMapper.insert(dtl);
        }
        
        // 为qaMainId2插入3条记录
        String[] questions2 = {
            "护肤品的使用顺序是什么？",
            "敏感肌肤如何护理？",
            "面膜多久敷一次比较好？"
        };
        
        String[] answers2 = {
            "护肤品使用顺序：洁面-爽肤水-精华-乳液-面霜...",
            "敏感肌肤需要选择温和的产品，避免刺激性成分...",
            "面膜建议每周2-3次，不要过于频繁..."
        };
        
        for (int i = 0; i < questions2.length; i++) {
            TrainQaImportDtl dtl = createTestDtlRecord(qaMainId2, questions2[i], answers2[i], TEST_TEAM_ID);
            trainQaImportDtlMapper.insert(dtl);
        }
    }

    /**
     * 创建测试用的详情记录
     */
    private TrainQaImportDtl createTestDtlRecord(Long qaMainId, String question, String answer, Long teamId) {
        TrainQaImportDtl dtlRecord = new TrainQaImportDtl();
        dtlRecord.setQaMainId(qaMainId);
        dtlRecord.setQuestion(question);
        dtlRecord.setAnswer(answer);
        dtlRecord.setTeamId(teamId);
        dtlRecord.setCreator(TEST_USER_ID);
        dtlRecord.setUpdater(TEST_USER_ID);
        dtlRecord.setCreateTime(LocalDateTime.now());
        dtlRecord.setUpdateTime(LocalDateTime.now());
        dtlRecord.setVersion(0L);
        
        return dtlRecord;
    }
}
