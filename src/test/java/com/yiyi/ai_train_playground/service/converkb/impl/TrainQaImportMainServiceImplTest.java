package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.entity.converkb.TrainQaImportMain;
import com.yiyi.ai_train_playground.mapper.converkb.TrainQaImportMainMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaImportMainService单元测试
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainQaImportMainServiceImplTest {

    @Autowired
    private TrainQaImportMainServiceImpl trainQaImportMainService;

    @Autowired
    private TrainQaImportMainMapper trainQaImportMainMapper;

    @Test
    @DisplayName("测试根据creator过滤的知识库列表查询")
    public void testGetMainListWithCreator() {
        // Given: 准备测试数据
        Long teamId = 999L; // 使用独立的团队ID避免与现有数据冲突
        String creator1 = "test_user1";
        String creator2 = "test_user2";

        // 创建user1的知识库记录
        TrainQaImportMain main1 = createTestMain("test_batch_001", "测试知识库1", creator1, teamId);
        trainQaImportMainMapper.insert(main1);
        assertNotNull(main1.getId());

        // 创建user2的知识库记录  
        TrainQaImportMain main2 = createTestMain("test_batch_002", "测试知识库2", creator2, teamId);
        trainQaImportMainMapper.insert(main2);
        assertNotNull(main2.getId());

        // When & Then: 测试user1只能查询自己创建的记录
        List<TrainQaImportMain> user1List = trainQaImportMainService.getMainListByTeamId(teamId, creator1, 0, 10);
        int user1Count = trainQaImportMainService.countByTeamId(teamId, creator1);

        assertEquals(1, user1List.size(), "user1应该只能查询到自己创建的1条记录");
        assertEquals(1, user1Count, "user1统计结果应该为1");
        assertEquals(main1.getId(), user1List.get(0).getId(), "user1查询到的应该是自己创建的记录");
        assertEquals("测试知识库1", user1List.get(0).getQaImName());

        // When & Then: 测试user2只能查询自己创建的记录
        List<TrainQaImportMain> user2List = trainQaImportMainService.getMainListByTeamId(teamId, creator2, 0, 10);
        int user2Count = trainQaImportMainService.countByTeamId(teamId, creator2);

        assertEquals(1, user2List.size(), "user2应该只能查询到自己创建的1条记录");
        assertEquals(1, user2Count, "user2统计结果应该为1");
        assertEquals(main2.getId(), user2List.get(0).getId(), "user2查询到的应该是自己创建的记录");
        assertEquals("测试知识库2", user2List.get(0).getQaImName());

        // When & Then: 验证传入空creator参数的情况（使用测试团队ID）
        List<TrainQaImportMain> allList = trainQaImportMainService.getMainListByTeamId(teamId, null, 0, 10);
        int allCount = trainQaImportMainService.countByTeamId(teamId, null);

        assertEquals(2, allList.size(), "测试团队传入null creator应该查询到2条记录");
        assertEquals(2, allCount, "测试团队传入null creator统计应该为2");
        
        // 验证空字符串creator参数的情况
        List<TrainQaImportMain> emptyCreatorList = trainQaImportMainService.getMainListByTeamId(teamId, "", 0, 10);
        int emptyCreatorCount = trainQaImportMainService.countByTeamId(teamId, "");

        assertEquals(2, emptyCreatorList.size(), "空字符串creator应该查询到所有记录");
        assertEquals(2, emptyCreatorCount, "空字符串creator统计应该为2");
    }

    @Test
    @DisplayName("测试条件查询功能 - qa_im_name模糊搜索")
    public void testGetMainListByConditions() {
        // Given: 准备测试数据
        Long teamId = 888L; 
        String creator = "test_conditions_user";

        // 创建多个知识库记录
        TrainQaImportMain main1 = createTestMain("batch_001", "电商客服知识库", creator, teamId);
        trainQaImportMainMapper.insert(main1);
        
        TrainQaImportMain main2 = createTestMain("batch_002", "技术支持知识库", creator, teamId);
        trainQaImportMainMapper.insert(main2);
        
        TrainQaImportMain main3 = createTestMain("batch_003", "电商售后知识库", creator, teamId);
        trainQaImportMainMapper.insert(main3);

        // When & Then: 测试模糊搜索 "电商"
        List<TrainQaImportMain> searchResults = trainQaImportMainService.getMainListByConditions(teamId, creator, "电商", 0, 10);
        int searchCount = trainQaImportMainService.countByConditions(teamId, creator, "电商");

        assertEquals(2, searchResults.size(), "搜索'电商'应该找到2条记录");
        assertEquals(2, searchCount, "统计'电商'应该为2");
        
        // 验证搜索结果都包含"电商"
        assertTrue(searchResults.stream().allMatch(item -> item.getQaImName().contains("电商")), "所有搜索结果都应该包含'电商'");

        // When & Then: 测试搜索 "技术"
        List<TrainQaImportMain> techResults = trainQaImportMainService.getMainListByConditions(teamId, creator, "技术", 0, 10);
        int techCount = trainQaImportMainService.countByConditions(teamId, creator, "技术");

        assertEquals(1, techResults.size(), "搜索'技术'应该找到1条记录");
        assertEquals(1, techCount, "统计'技术'应该为1");
        assertEquals("技术支持知识库", techResults.get(0).getQaImName());

        // When & Then: 测试搜索不存在的关键词
        List<TrainQaImportMain> noResults = trainQaImportMainService.getMainListByConditions(teamId, creator, "不存在的关键词", 0, 10);
        int noCount = trainQaImportMainService.countByConditions(teamId, creator, "不存在的关键词");

        assertEquals(0, noResults.size(), "搜索不存在的关键词应该返回0条记录");
        assertEquals(0, noCount, "统计不存在的关键词应该为0");

        // When & Then: 测试null或空字符串搜索条件（应该返回所有记录）
        List<TrainQaImportMain> nullResults = trainQaImportMainService.getMainListByConditions(teamId, creator, null, 0, 10);
        List<TrainQaImportMain> emptyResults = trainQaImportMainService.getMainListByConditions(teamId, creator, "", 0, 10);
        
        assertEquals(3, nullResults.size(), "null搜索条件应该返回所有3条记录");
        assertEquals(3, emptyResults.size(), "空字符串搜索条件应该返回所有3条记录");
    }

    @Test
    @DisplayName("测试知识库更新功能")
    public void testUpdateMain() {
        // Given: 准备测试数据
        Long teamId = 777L;
        String creator = "test_update_user";
        
        TrainQaImportMain main = createTestMain("update_batch_001", "原始知识库名称", creator, teamId);
        trainQaImportMainMapper.insert(main);
        assertNotNull(main.getId());
        
        Long originalId = main.getId();
        String originalBatchNo = main.getBatchNo();
        LocalDateTime originalCreateTime = main.getCreateTime();

        // When: 更新知识库名称和描述
        String newName = "更新后的知识库名称";
        String newDesc = "更新后的描述信息";
        String updater = "update_operator";
        
        // 等待1秒确保更新时间不同
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        trainQaImportMainService.updateMain(originalId, newName, newDesc, teamId, updater);

        // Then: 验证更新结果 - updateMain方法是void，直接查询验证

        // 查询更新后的记录
        TrainQaImportMain updatedMain = trainQaImportMainMapper.selectById(originalId);
        assertNotNull(updatedMain, "更新后的记录应该存在");

        // 验证更新的字段
        assertEquals(newName, updatedMain.getQaImName(), "知识库名称应该已更新");
        assertEquals(newDesc, updatedMain.getQaImDesc(), "知识库描述应该已更新");
        assertEquals(updater, updatedMain.getUpdater(), "更新人应该已设置");
        // 注：数据库update_time字段有ON UPDATE CURRENT_TIMESTAMP，会自动更新
        assertNotNull(updatedMain.getUpdateTime(), "更新时间应该不为空");

        // 验证不应该被更新的字段  
        assertEquals(originalId, updatedMain.getId(), "ID不应该变化");
        assertEquals(originalBatchNo, updatedMain.getBatchNo(), "批次号不应该变化");
        assertEquals(creator, updatedMain.getCreator(), "创建人不应该变化");
        // 注：由于数据库精度问题，不直接比较创建时间的精确值
        assertNotNull(updatedMain.getCreateTime(), "创建时间应该不为空");

        // When & Then: 测试更新不存在的记录 - 会抛出异常
        assertThrows(RuntimeException.class,
                     () -> trainQaImportMainService.updateMain(999999L, "新名称", "新描述", teamId, updater),
                     "更新不存在的记录应该抛出异常");

        // When & Then: 测试更新其他用户创建的记录（由于没有creator参数验证，这个测试场景跳过）
        // 注：当前updateMain方法签名没有creator参数，无法验证权限
    }

    @Test
    @DisplayName("测试级联删除功能")
    public void testDeleteMainWithDetails() {
        // Given: 准备测试数据（需要先创建主记录和明细记录）
        Long teamId = 666L;
        String creator = "test_delete_user";
        
        TrainQaImportMain main = createTestMain("delete_batch_001", "待删除知识库", creator, teamId);
        trainQaImportMainMapper.insert(main);
        assertNotNull(main.getId());
        
        Long mainId = main.getId();

        // 验证记录存在且teamId正确
        TrainQaImportMain beforeDelete = trainQaImportMainMapper.selectById(mainId);
        assertNotNull(beforeDelete, "删除前记录应该存在");
        assertEquals(teamId, beforeDelete.getTeamId(), "记录的teamId应该正确");

        // When: 执行级联删除
        trainQaImportMainService.deleteMainWithDetails(mainId, teamId);

        // Then: 验证删除结果 - deleteMainWithDetails是void方法，直接验证结果

        // 验证主记录已被删除
        TrainQaImportMain afterDelete = trainQaImportMainMapper.selectById(mainId);
        assertNull(afterDelete, "删除后主记录应该不存在");

        // When & Then: 测试删除不存在的记录 - 会抛出异常
        assertThrows(RuntimeException.class, 
                     () -> trainQaImportMainService.deleteMainWithDetails(999999L, teamId),
                     "删除不存在的记录应该抛出异常");

        // When & Then: 测试删除其他团队的记录 - 会抛出异常
        Long otherTeamId = 777L;
        TrainQaImportMain otherTeamMain = createTestMain("other_team_batch", "其他团队知识库", creator, otherTeamId);
        trainQaImportMainMapper.insert(otherTeamMain);
        
        assertThrows(RuntimeException.class,
                     () -> trainQaImportMainService.deleteMainWithDetails(otherTeamMain.getId(), teamId),
                     "删除其他团队的记录应该抛出异常");
        
        // 验证其他团队的记录仍然存在
        TrainQaImportMain stillExists = trainQaImportMainMapper.selectById(otherTeamMain.getId());
        assertNotNull(stillExists, "其他团队的记录应该仍然存在");
    }

    /**
     * 创建测试用的知识库主记录
     */
    private TrainQaImportMain createTestMain(String batchNo, String qaImName, String creator, Long teamId) {
        TrainQaImportMain main = new TrainQaImportMain();
        main.setBatchNo(batchNo);
        main.setQaImName(qaImName);
        main.setQaImDesc("测试描述");
        main.setTeamId(teamId);
        main.setCreator(creator);
        main.setUpdater(creator);
        main.setCreateTime(LocalDateTime.now());
        main.setUpdateTime(LocalDateTime.now());
        main.setVersion(0L);
        return main;
    }
}