package com.yiyi.ai_train_playground.service.converkb.impl;

import com.yiyi.ai_train_playground.dto.converkb.TaskConvKbDtlDetailDTO;
import com.yiyi.ai_train_playground.entity.converkb.TrainTaskConvKbDtl;
import com.yiyi.ai_train_playground.mapper.converkb.TrainTaskConvKbDtlMapper;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainTaskConvKbDtlServiceImpl 测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class TrainTaskConvKbDtlServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(TrainTaskConvKbDtlServiceImplTest.class);

    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;

    @Autowired
    private TrainTaskConvKbDtlMapper trainTaskConvKbDtlMapper;

    @Test
    public void testGetRdmConvDtlByTaskId() {
        log.info("测试getRdmConvDtlByTaskId方法");

        try {
            Long taskId = 1L;
            Long teamId = 1L;

            // 先准备测试数据
            prepareTestData(taskId, teamId);

            // 测试随机获取方法
            TaskConvKbDtlDetailDTO result = trainTaskConvKbDtlService.getRdmConvDtlByTaskId(taskId, teamId);

            if (result != null) {
                log.info("随机获取成功，明细ID: {}, finalChatLog长度: {}", 
                        result.getId(), 
                        result.getFinalChatLog() != null ? result.getFinalChatLog().length() : 0);
                
                // 验证基本字段
                assertNotNull(result.getId());
                assertEquals(taskId, result.getTaskId());
                assertEquals(teamId, result.getTeamId());
                assertNotNull(result.getFinalChatLog());
                
                log.info("测试通过：随机获取明细成功");
            } else {
                log.warn("未获取到随机明细，可能是测试数据不存在");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("测试getRdmConvDtlByTaskId失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetRdmConvDtlByTaskIdMultipleTimes() {
        log.info("测试getRdmConvDtlByTaskId方法多次调用的随机性");

        try {
            Long taskId = 1L;
            Long teamId = 1L;

            // 先准备测试数据
            prepareTestData(taskId, teamId);

            // 多次调用，验证随机性
            for (int i = 0; i < 5; i++) {
                TaskConvKbDtlDetailDTO result = trainTaskConvKbDtlService.getRdmConvDtlByTaskId(taskId, teamId);
                
                if (result != null) {
                    log.info("第{}次调用，获取到明细ID: {}, finalChatLog: {}", 
                            i + 1, result.getId(), 
                            result.getFinalChatLog() != null ? result.getFinalChatLog().substring(0, Math.min(50, result.getFinalChatLog().length())) + "..." : "null");
                } else {
                    log.warn("第{}次调用，未获取到明细", i + 1);
                }
            }

            log.info("多次随机调用测试完成");

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("测试getRdmConvDtlByTaskId多次调用失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetRdmConvDtlByTaskIdWithNonExistentTask() {
        log.info("测试getRdmConvDtlByTaskId方法处理不存在的任务");

        try {
            Long nonExistentTaskId = 99999L;
            Long teamId = 1L;

            TaskConvKbDtlDetailDTO result = trainTaskConvKbDtlService.getRdmConvDtlByTaskId(nonExistentTaskId, teamId);

            assertNull(result, "不存在的任务应该返回null");
            log.info("测试通过：不存在的任务正确返回null");

        } catch (Exception e) {
            log.error("测试失败", e);
            fail("测试getRdmConvDtlByTaskId处理不存在任务失败: " + e.getMessage());
        }
    }

    @Test
    public void testF2ndAggSysPromptField() {
        log.info("测试f2nd_agg_sys_prompt字段的完整功能");

        try {
            Long taskId = 999L;
            Long teamId = 1L;
            String testF2ndAggSysPrompt = "这是第二步聚合系统提示词的测试内容，用于验证新字段的功能。包含了系统提示词的详细信息和指令。";
            String testFinalChatLog = "这是最终聊天记录的测试内容，用于验证更新功能。";

            // 1. 测试插入包含f2nd_agg_sys_prompt字段的数据
            log.info("1. 测试插入包含f2nd_agg_sys_prompt字段的数据");
            TrainTaskConvKbDtl entity = new TrainTaskConvKbDtl();
            entity.setTaskId(taskId);
            entity.setKbDtlId(100L);
            entity.setLearningStatus("un_learn");
            entity.setF1stRawChatlog("初始聊天记录测试内容");
            entity.setF2ndAggSysPrompt(testF2ndAggSysPrompt);
            entity.setFinalChatLog("初始最终聊天记录");
            entity.setTeamId(teamId);
            entity.setCreator("test_user");
            entity.setUpdater("test_user");

            int insertResult = trainTaskConvKbDtlMapper.insert(entity);
            assertTrue(insertResult > 0, "插入操作应该成功");
            assertNotNull(entity.getId(), "插入后应该有ID");
            log.info("插入成功，生成的ID: {}", entity.getId());

            // 2. 测试查询验证f2nd_agg_sys_prompt字段
            log.info("2. 测试查询验证f2nd_agg_sys_prompt字段");
            TrainTaskConvKbDtl queryResult = trainTaskConvKbDtlMapper.selectById(entity.getId());
            assertNotNull(queryResult, "查询结果不应该为null");
            assertEquals(testF2ndAggSysPrompt, queryResult.getF2ndAggSysPrompt(), "f2nd_agg_sys_prompt字段应该正确保存");
            assertEquals("初始最终聊天记录", queryResult.getFinalChatLog(), "final_chat_log字段应该正确保存");
            log.info("查询验证成功，f2nd_agg_sys_prompt长度: {}", queryResult.getF2ndAggSysPrompt().length());

            // 3. 测试updateLearningResult方法更新f2nd_agg_sys_prompt字段
            log.info("3. 测试updateLearningResult方法更新f2nd_agg_sys_prompt字段");
            String updatedF2ndAggSysPrompt = "更新后的第二步聚合系统提示词内容，验证updateLearningResult方法的功能。";

            int updateResult = trainTaskConvKbDtlMapper.updateLearningResult(
                entity.getId(),
                updatedF2ndAggSysPrompt,
                testFinalChatLog,
                "learned",
                "test_updater",
                queryResult.getVersion()
            );

            assertEquals(1, updateResult, "更新操作应该影响1行");
            log.info("updateLearningResult执行成功，影响行数: {}", updateResult);

            // 4. 验证更新后的数据
            log.info("4. 验证更新后的数据");
            TrainTaskConvKbDtl updatedEntity = trainTaskConvKbDtlMapper.selectById(entity.getId());
            assertNotNull(updatedEntity, "更新后查询结果不应该为null");
            assertEquals(updatedF2ndAggSysPrompt, updatedEntity.getF2ndAggSysPrompt(), "f2nd_agg_sys_prompt应该被正确更新");
            assertEquals(testFinalChatLog, updatedEntity.getFinalChatLog(), "final_chat_log应该被正确更新");
            assertEquals("learned", updatedEntity.getLearningStatus(), "learning_status应该被正确更新");
            assertEquals("test_updater", updatedEntity.getUpdater(), "updater应该被正确更新");
            assertEquals(queryResult.getVersion() + 1, updatedEntity.getVersion(), "version应该递增1");
            log.info("更新验证成功，新版本号: {}", updatedEntity.getVersion());

            // 5. 测试乐观锁机制
            log.info("5. 测试乐观锁机制");
            int optimisticLockResult = trainTaskConvKbDtlMapper.updateLearningResult(
                entity.getId(),
                "乐观锁测试内容",
                "乐观锁测试聊天记录",
                "learned",
                "test_updater",
                queryResult.getVersion() // 使用旧版本号
            );

            assertEquals(0, optimisticLockResult, "使用旧版本号更新应该失败（乐观锁生效）");
            log.info("乐观锁测试成功，旧版本号更新被阻止");

            // 6. 清理测试数据
            log.info("6. 清理测试数据");
            int deleteResult = trainTaskConvKbDtlMapper.deleteById(entity.getId(), teamId);
            assertEquals(1, deleteResult, "删除操作应该成功");
            log.info("测试数据清理完成");

            log.info("f2nd_agg_sys_prompt字段功能测试全部通过！");

        } catch (Exception e) {
            log.error("测试f2nd_agg_sys_prompt字段功能失败", e);
            fail("测试f2nd_agg_sys_prompt字段功能失败: " + e.getMessage());
        }
    }

    /**
     * 测试悲观锁更新学习结果功能
     */
    @Test
    public void testUpdateLearningResultWithPessimisticLock() {
        log.info("开始测试悲观锁更新学习结果功能");

        try {
            Long teamId = 1L;
            Long taskId = 1L;

            // 1. 创建测试数据
            log.info("1. 创建测试数据");
            TrainTaskConvKbDtl entity = new TrainTaskConvKbDtl();
            entity.setTaskId(taskId);
            entity.setKbDtlId(1L);
            entity.setLearningStatus("un_learn");
            entity.setF1stRawChatlog("测试原始聊天记录内容，用于验证悲观锁功能。");
            entity.setTeamId(teamId);
            entity.setCreator("test_creator");
            entity.setUpdater("test_updater");

            int insertResult = trainTaskConvKbDtlMapper.insert(entity);
            assertEquals(1, insertResult, "插入操作应该成功");
            assertNotNull(entity.getId(), "插入后应该有ID");
            log.info("测试数据创建成功，id: {}", entity.getId());

            // 2. 测试悲观锁更新方法
            log.info("2. 测试悲观锁更新学习结果");
            String testF2ndAggSysPrompt = "悲观锁测试的第二步聚合系统提示词内容。";
            String testFinalChatLog = "悲观锁测试的最终聊天记录内容。";

            int updateResult = trainTaskConvKbDtlMapper.updateLearningResultWithPessimisticLock(
                entity.getId(),
                testF2ndAggSysPrompt,
                testFinalChatLog,
                "learned",
                "test_updater"
            );

            assertEquals(1, updateResult, "悲观锁更新操作应该影响1行");
            log.info("悲观锁更新执行成功，影响行数: {}", updateResult);

            // 3. 验证更新结果
            log.info("3. 验证更新结果");
            TrainTaskConvKbDtl queryResult = trainTaskConvKbDtlMapper.selectById(entity.getId());
            assertNotNull(queryResult, "查询结果不应为空");
            assertEquals(testF2ndAggSysPrompt, queryResult.getF2ndAggSysPrompt(), "f2nd_agg_sys_prompt应该被正确更新");
            assertEquals(testFinalChatLog, queryResult.getFinalChatLog(), "final_chat_log应该被正确更新");
            assertEquals("learned", queryResult.getLearningStatus(), "learning_status应该被正确更新");
            assertEquals("test_updater", queryResult.getUpdater(), "updater应该被正确更新");
            assertEquals(Long.valueOf(1), queryResult.getVersion(), "version应该增加1");
            log.info("悲观锁更新结果验证成功");

            // 4. 清理测试数据
            log.info("4. 清理测试数据");
            int deleteResult = trainTaskConvKbDtlMapper.deleteById(entity.getId(), teamId);
            assertEquals(1, deleteResult, "删除操作应该成功");
            log.info("测试数据清理完成");

            log.info("悲观锁更新学习结果功能测试全部通过！");

        } catch (Exception e) {
            log.error("测试悲观锁更新学习结果功能失败", e);
            fail("测试悲观锁更新学习结果功能失败: " + e.getMessage());
        }
    }

    /**
     * 准备测试数据
     */
    private void prepareTestData(Long taskId, Long teamId) {
        try {
            // 检查是否已有测试数据
            int existingCount = trainTaskConvKbDtlMapper.countByTaskId(taskId, teamId);
            
            if (existingCount > 0) {
                log.info("已存在{}条测试数据，跳过数据准备", existingCount);
                return;
            }

            log.info("准备测试数据，taskId: {}, teamId: {}", taskId, teamId);

            // 创建几条测试数据
            for (int i = 1; i <= 3; i++) {
                TrainTaskConvKbDtl entity = new TrainTaskConvKbDtl();
                entity.setTaskId(taskId);
                entity.setKbDtlId((long) i);
                entity.setLearningStatus("un_learn");
                entity.setF1stRawChatlog("测试初始聊天记录" + i);
                entity.setFinalChatLog("测试最终聊天记录" + i + "，这是一个用于测试的聊天记录内容，包含了客户和客服的对话。");
                entity.setTeamId(teamId);
                entity.setCreator("test_user");
                entity.setUpdater("test_user");

                int result = trainTaskConvKbDtlMapper.insert(entity);
                if (result > 0) {
                    log.info("创建测试数据成功，id: {}", entity.getId());
                }
            }

            log.info("测试数据准备完成");

        } catch (Exception e) {
            log.warn("准备测试数据失败，可能已存在数据: {}", e.getMessage());
        }
    }
}
