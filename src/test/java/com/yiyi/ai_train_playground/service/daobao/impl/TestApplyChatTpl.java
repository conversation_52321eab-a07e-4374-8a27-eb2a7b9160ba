package com.yiyi.ai_train_playground.service.daobao.impl;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class TestApplyChatTpl {
    //深度思考模型

    static String JIURU_DIAN = "ep-20250629195408-gtv9c";
    static String JIURU_DIAN_15_256K = "ep-20250807143918-kbcn5";//1.6的接入点


    static String apiKey = System.getenv("ARK_API_KEY");
    //    static String apiKey = SPECIAL_API_KEY;//手写，这个是之前最老的一版，看有没有权限
    //创建一个连接池，最多允许5个空闲连接，空闲连接的最大存活时间为1秒。
    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
    //创建一个调度器，用于管理 HTTP 请求的执行。
    static Dispatcher dispatcher = new Dispatcher();
    //使用 ArkService 构建器创建一个 ArkService 实例，配置了调度器、连接池、API 密钥和基础 URL。
    static ArkService service = ArkService.builder()
            .dispatcher(dispatcher)
            .connectionPool(connectionPool)
            .timeout(Duration.ofMinutes(5))
            .connectTimeout(Duration.ofMinutes(5))
            .callTimeout(Duration.ofMinutes(5))
            .apiKey(apiKey)
            .build();




    @Test
    public void testCreateSession_OrigPrompt() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                你是一个聊天记录分析与生成专家，
                学习一下我给你提供的聊天记录的风格，输出相同风格的会话记录，要2条，注意会话的轮次不能变少，同时列出当前会话属于售前、售中、售后、其他 。严格以json格式输出
                
                提供的文本中每个聊天片段都有明确的开始标记：以下为一通会话，和明确的结束标记：会话结束_时间
                
                输入举例，以下面3个#括起来的为准：
                
                ###
                /*****************以下为一通会话************************************/	
                jd_71e2243fb99e1 2025-08-05 23:22:07	
                https://item.jd.com/100146515514.html?sdx=ehi-lLxFuZiE6JnIYIdaiccltTGVRHtmwmtNsqlGY9WPPe_RLJhY4nzipkrkX2eT	
                纽强自营-豁达 2025-08-05 23:22:07	
                ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨	
                纽强自营-豁达 2025-08-05 23:22:14	
                您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！	
                纽强自营-豁达 2025-08-05 23:22:15	
                在的哦	
                jd_71e2243fb99e1 2025-08-05 23:22:44	
                你好，我需要纽强的宝宝洗发水，沐浴乳，身体乳的在三种试用装链接	
                jd_71e2243fb99e1 2025-08-05 23:22:55	
                宝宝一岁半	
                纽强自营-豁达 2025-08-05 23:23:21	
                洗发水沐浴露暂时没有试用装的链接哦	
                纽强自营-豁达 2025-08-05 23:23:25	
                只有正装的呢	
                jd_71e2243fb99e1 2025-08-05 23:24:45	
                那面霜的有没有试用装	
                纽强自营-豁达 2025-08-05 23:25:09	
                我想了解一下您家宝宝的肤质是怎样的？是偏干、偏油还是混合性肌肤呢？这样我们可以更精准地推荐适合您宝宝的保湿面霜	
                纽强自营-豁达 2025-08-05 23:25:15	
                宝宝肤质会干燥嘛	
                jd_71e2243fb99e1 2025-08-05 23:27:12	
                混合型	
                纽强自营-豁达 2025-08-05 23:27:38	
                目前最小规格的是15g的 您可以看下这款的哈	
                纽强自营-豁达 2025-08-05 23:27:39	
                https://item.jd.com/100121466523.html?sdx=ehi-lLxFu5iE6JnIYIFdiMAmtTKSRHtmwmtNsqlGY9WPPe_RLJhY4nzio03qUWOU&position=search	
                纽强自营-豁达 2025-08-05 23:27:41	
                蓝色款属于滋润型，奶油质地，适合日常护肤、舒缓空调环境或干燥肌引起的不适	
                jd_71e2243fb99e1 2025-08-05 23:30:40	
                那身体乳和洗发水呢？	
                纽强自营-豁达 2025-08-05 23:31:00	
                这个是一体的哈 身体 脸部都是可以使用的哦	
                纽强自营-豁达 2025-08-05 23:31:16	
                洗发水暂时没有试用装的呢 	
                引用：洗发水沐浴露暂时没有试用装的链接哦	
                jd_71e2243fb99e1 2025-08-05 23:31:16	
                洗发水呢？	
                纽强自营-豁达 2025-08-05 23:31:19	
                只有正装的	
                jd_71e2243fb99e1 2025-08-05 23:31:43	
                正装洗发水发个链接给我	
                纽强自营-豁达 2025-08-05 23:32:10	
                https://item.jd.com/100155708001.html?sdx=ehi-lLxFu5iE6JnIYIZZi8YosDCQRHtmwmtNsqlGY9WPPe_RLJhY4nzirEvjVG6Q&position=search	
                纽强自营-豁达 2025-08-05 23:32:21	
                这个洗发沐浴二合一的 您可以看下哦	
                jd_71e2243fb99e1 2025-08-05 23:32:35	
                这个是我们国产的吗？	
                纽强自营-豁达 2025-08-05 23:32:53	
                是国产品牌的哈	
                jd_71e2243fb99e1 2025-08-05 23:33:45	
                c成分这些都安全的吧？	
                纽强自营-豁达 2025-08-05 23:33:59	
                产品成分都是温和不刺激的呢 	
                jd_71e2243fb99e1 2025-08-05 23:34:15	
                谢谢	
                纽强自营-豁达 2025-08-05 23:34:24	
                您太客气了哈	
                /*****************会话结束_时间:2025-08-05 23:44:16******************************/	
                
                /*****************以下为一通会话************************************/	
                wdngfvywzceuzua 2025-08-05 23:21:29	
                https://item.jd.com/100146515514.html?sdx=ehi-lLxFuZiE6JnIYIdaiccltTGVRHtmwmtNsqlGY9WPPe_RLJhY4nzip0DqVm6Y	
                纽强自营-快乐 2025-08-05 23:21:29	
                ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨	
                wdngfvywzceuzua 2025-08-05 23:21:34	
                你好	
                纽强自营-快乐 2025-08-05 23:21:36	
                您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！	
                wdngfvywzceuzua 2025-08-05 23:21:51	
                请问这个可以当面霜用吗	
                纽强自营-快乐 2025-08-05 23:21:57	
                可以的呢	
                纽强自营-快乐 2025-08-05 23:21:58	
                正常情况下纽强产品可以涂抹全身哦，包括眼眶周和口眶周的呢	
                wdngfvywzceuzua 2025-08-05 23:22:59	
                可以每天涂脸和身体当日常保湿吧？	
                纽强自营-快乐 2025-08-05 23:23:07	
                可以的呢	
                wdngfvywzceuzua 2025-08-05 23:23:11	
                好的	
                纽强自营-快乐 2025-08-05 23:23:17	
                纽强产品不含酸类、激素、矿物质油、香料香精和致敏防腐剂，保湿、温和、适用于敏感肌肤，可作为日常护肤品长期使用的呢	
                /*****************会话结束_时间:2025-08-05 23:33:16******************************/	
                
                
                /*****************以下为一通会话************************************/	
                jd_745217cbf355b 2025-08-05 23:16:09	
                可以涂脸吗宝宝	
                纽强自营-希望 2025-08-05 23:16:09	
                ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨	
                纽强自营-希望 2025-08-05 23:16:14	
                您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！	
                纽强自营-希望 2025-08-05 23:16:17	
                产品都是可以全身涂抹的哈，脸部也是可以的呢	
                jd_745217cbf355b 2025-08-05 23:16:33	
                可以脸部也涂是吧	
                jd_745217cbf355b 2025-08-05 23:16:34	
                OK	
                纽强自营-希望 2025-08-05 23:16:44	
                麻烦善良帅气美丽温柔可爱大方的您给我的服务做出一个很满意的点赞赞好嘛~~~~
                直接点第5颗星星就好啦~~
                这样我的盒饭里就可以加一个鸡腿啦~	
                纽强自营-希望 2025-08-05 23:16:44	
                https://dd-static.jd.com/ddimg/jfs/t1/317351/38/20936/26257/6891cbd8F10d02b88/b76eb6781755ff12.jpg	
                /*****************会话结束_时间:2025-08-05 23:26:35******************************/	
                ###
                
                
                
                
                
                
                
                输出举例：[
                                               {
                                               "typeCode": "2",
                                               "typeName": "售后",
                                               "content": "jd_57edeb04e6321 2025-08-05 22:11:34\\n 我买的油压不出来那个盖子不好用。\\n 纽强自营 - 希望 2025-08-05 22:11:53\\n 麻烦您拍摄一下视频呢 \\n 纽强自营 - 希望 2025-08-05 22:11:59\\n 小客服先查看下呢 \\njd_57edeb04e6321 2025-08-05 22:12:52\\n 压不岀来，只是空气 \\n 纽强自营 - 希望 2025-08-05 22:13:16\\n 您拍摄一下视频呢 \\njd_57edeb04e6321 2025-08-05 22:16:48\\nhttps://vod.300hu.com/301/3ed99664vodbjngwcloud1oss/170a5959/1005237899577339905/f0.mp4\\n 纽强自营 - 希望 2025-08-05 22:17:16\\n 产品仓库发出的话都是完好的呢，产品是没有回弹设计的呢，如旋转太多或用力旋转的话是很容易损坏的呢 \\n 纽强自营 - 希望 2025-08-05 22:17:22\\n 产品仓库发出的话都是完好的呢，产品是没有回弹设计的呢，如旋转太多或用力旋转的话是很容易损坏的呢 \\n 纽强自营 - 希望 2025-08-05 22:17:35\\n 为了您的购物体验，小客服可以帮您反馈申请补发一个泵头哈，麻烦您提供一下手机号 \\njd_57edeb04e6321 2025-08-05 22:18:06\\n13505319706\\n 纽强自营 - 希望 2025-08-05 22:18:10\\n 您好，请您核对收货信息哦 \\njd_57edeb04e6321 2025-08-05 22:19:15\\n 谢谢，换个就能用吗 \\n 纽强自营 - 希望 2025-08-05 22:19:24\\n 是的哈 \\njd_57edeb04e6321 2025-08-05 22:20:09\\n 好吧，我试试吧 \\n 纽强自营 - 希望 2025-08-05 22:20:25\\nhttps://vod.300hu.com/301/3ed99664vodbjngwcloud1oss/3439ffb6/1005238862992179201/f0.mp4\\n 纽强自营 - 希望 2025-08-05 22:21:09\\n 这是视频哈，到货后按视频安装和使用哈 \\njd_57edeb04e6321 2025-08-05 22:21:49\\n 好的，谢谢 "
                                               },
                                               {
                                               "typeCode": "0",
                                               "typeName": "售前",
                                               "content": "jd_5b04e194a8bb2 2025-08-05 22:11:47\\n 你好夏天哪一款好？\\n 纽强自营 - 豁达 2025-08-05 22:12:17\\nhttps://item.jd.com/100011579915.html?sdx=ehi-lLxFu5iE6JnIYYJdicEpuTGURHtmwmtNsqlGY9WPPe_RLJhY4nzmpEvkVGGS&position=search\\n 纽强自营 - 豁达 2025-08-05 22:12:19\\n 夏天可以看下这款哦，这款会比较清爽一点呢 \\n 纽强自营 - 豁达 2025-08-05 22:12:20\\n 绿色款属于清爽型，水润质地，适合日常护肤，需要快速渗透与清爽舒缓 "
                                               },
                                               {
                                               "typeCode": "1",
                                               "typeName": "售中",
                                               "content": "jd_4a66a9b3c1803 2025-08-05 21:57:42\\n 现在下单，广东大概多久到 \\n 纽强自营 - 豁达 2025-08-05 21:57:55\\n 小主，京东自营店铺的商品均由京东快递统一配送，我们会根据您的收货地址选择最近的仓库发货，页面也会显示预计的送达时间哦 \\njd_4a66a9b3c1803 2025-08-05 21:58:46\\n 不是今天下单，明天就到吗？京东 \\n 纽强自营 - 豁达 2025-08-05 21:59:16\\n 页面会显示预计送达时间的 您可以看下 \\njd_4a66a9b3c1803 2025-08-05 21:59:49\\n 看到了 #E-s11\\n 纽强自营 - 豁达 2025-08-05 21:59:59\\n 好的哦"
                                               },
                                               {
                                               "typeCode": "3",
                                               "typeName": "其他",
                                               "content": "jd_660704945cabc 2025-08-05 22:18:31\\n 好的 \\n 纽强自营 - 快乐 2025-08-05 22:18:38\\n 嗯呢"
                                               }
                                               ]
                
                用户的输入的聊天记录如下：
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }




    static String contextIdOld = "ctx-20250808174343-9khkq";
    @Test
    public void testUseSessionWith_OrginalPrompt() throws InterruptedException {
        List<ChatMessage> messagesForReqList = new ArrayList<>();



        /*ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("亲 这个洗衣机的屏显一直转圈是什么意思").build();
        messagesForReqList.add(assitantMessage);*/


       /* ChatMessage assitantMessage2 =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(" ").build();
        messagesForReqList.add(assitantMessage2);*/


        String userMessageContent = """
                聊天记录如下:
                
                """;


        userMessageContent = readChatLogFile();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

        messagesForReqList.add(userMessage);


        /*ChatMessage assitantMessage3 =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("不显示数字了，就转圈").build();
        messagesForReqList.add(assitantMessage3);

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content("您可以提供下订单号哈，这边为您查询一下具体情况~").build();
        messagesForReqList.add(userMessage2);


        ChatMessage userMessage3 =
                ChatMessage.builder().role(ChatMessageRole.USER).content("有出现代码吗").build();
        messagesForReqList.add(userMessage3);*/


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdOld)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));

//        Thread.sleep(1000*60*3);
    }


/*    private String readChatLogFile() {
        String filePath = "C:\\projects\\训练场\\基地发来的聊天记录\\聊天记录_1484f4b6c7a44df390fb5da6096b01ee(1).txt";

        try {
            String result=new String(java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(filePath)), "UTF-8");
            System.out.println("文本大小为:"+result.length());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }*/


    public static String readChatLogFile() {
        String filePathHome = "C:\\projects\\ai_playground\\基地发来的聊天记录\\聊天记录_1484f4b6c7a44df390fb5da6096b01ee-short.txt";
        String filePathCompany = "C:\\projects\\训练场\\基地发来的聊天记录\\聊天记录_1484f4b6c7a44df390fb5da6096b01ee-short.txt";

        String filePath = filePathCompany;

        try {
            String result = new String(java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(filePath)), "UTF-8");
            System.out.println("文本大小为:" + result.length());

            // 打印最后10行内容
            String[] lines = result.split("\n");
            System.out.println("最后10行内容:");
            int start = Math.max(0, lines.length - 10);
            for (int i = start; i < lines.length; i++) {
                System.out.println(lines[i]);
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    @Test
    public void testCreateSession_OrigPrompt_simple() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                你是一个聊天记录分析与生成专家，
                学习一下我给你提供的聊天记录的风格，输出相同风格的会话记录，要1条，注意会话的轮次不能变少，同时列出当前会话属于售前、售中、售后、其他 。严格以json格式输出
                
                提供的文本中每个聊天片段都有明确的开始标记：以下为一通会话，和明确的结束标记：会话结束_时间
                
                输入举例，以下面3个#括起来的为准：
                
                ###
                /*****************以下为一通会话************************************/	
                jd_71e2243fb99e1 2025-08-05 23:22:07	
                https://item.jd.com/100146515514.html?sdx=ehi-lLxFuZiE6JnIYIdaiccltTGVRHtmwmtNsqlGY9WPPe_RLJhY4nzipkrkX2eT	
                纽强自营-豁达 2025-08-05 23:22:07	
                ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨	
                纽强自营-豁达 2025-08-05 23:22:14	
                您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！	
                纽强自营-豁达 2025-08-05 23:22:15	
                在的哦	
                jd_71e2243fb99e1 2025-08-05 23:22:44	
                你好，我需要纽强的宝宝洗发水，沐浴乳，身体乳的在三种试用装链接	
                jd_71e2243fb99e1 2025-08-05 23:22:55	
                宝宝一岁半	
                纽强自营-豁达 2025-08-05 23:23:21	
                洗发水沐浴露暂时没有试用装的链接哦	
                纽强自营-豁达 2025-08-05 23:23:25	
                只有正装的呢	
                jd_71e2243fb99e1 2025-08-05 23:24:45	
                那面霜的有没有试用装	
                纽强自营-豁达 2025-08-05 23:25:09	
                我想了解一下您家宝宝的肤质是怎样的？是偏干、偏油还是混合性肌肤呢？这样我们可以更精准地推荐适合您宝宝的保湿面霜	
                纽强自营-豁达 2025-08-05 23:25:15	
                宝宝肤质会干燥嘛	
                jd_71e2243fb99e1 2025-08-05 23:27:12	
                混合型	
                纽强自营-豁达 2025-08-05 23:27:38	
                目前最小规格的是15g的 您可以看下这款的哈	
                纽强自营-豁达 2025-08-05 23:27:39	
                https://item.jd.com/100121466523.html?sdx=ehi-lLxFu5iE6JnIYIFdiMAmtTKSRHtmwmtNsqlGY9WPPe_RLJhY4nzio03qUWOU&position=search	
                纽强自营-豁达 2025-08-05 23:27:41	
                蓝色款属于滋润型，奶油质地，适合日常护肤、舒缓空调环境或干燥肌引起的不适	
                jd_71e2243fb99e1 2025-08-05 23:30:40	
                那身体乳和洗发水呢？	
                纽强自营-豁达 2025-08-05 23:31:00	
                这个是一体的哈 身体 脸部都是可以使用的哦	
                纽强自营-豁达 2025-08-05 23:31:16	
                洗发水暂时没有试用装的呢 	
                引用：洗发水沐浴露暂时没有试用装的链接哦	
                jd_71e2243fb99e1 2025-08-05 23:31:16	
                洗发水呢？	
                纽强自营-豁达 2025-08-05 23:31:19	
                只有正装的	
                jd_71e2243fb99e1 2025-08-05 23:31:43	
                正装洗发水发个链接给我	
                纽强自营-豁达 2025-08-05 23:32:10	
                https://item.jd.com/100155708001.html?sdx=ehi-lLxFu5iE6JnIYIZZi8YosDCQRHtmwmtNsqlGY9WPPe_RLJhY4nzirEvjVG6Q&position=search	
                纽强自营-豁达 2025-08-05 23:32:21	
                这个洗发沐浴二合一的 您可以看下哦	
                jd_71e2243fb99e1 2025-08-05 23:32:35	
                这个是我们国产的吗？	
                纽强自营-豁达 2025-08-05 23:32:53	
                是国产品牌的哈	
                jd_71e2243fb99e1 2025-08-05 23:33:45	
                c成分这些都安全的吧？	
                纽强自营-豁达 2025-08-05 23:33:59	
                产品成分都是温和不刺激的呢 	
                jd_71e2243fb99e1 2025-08-05 23:34:15	
                谢谢	
                纽强自营-豁达 2025-08-05 23:34:24	
                您太客气了哈	
                /*****************会话结束_时间:2025-08-05 23:44:16******************************/	
                
                ###
                
                
                
                
                
                
                
                输出举例：[
                                               {
                                               "typeCode": "2",
                                               "typeName": "售后",
                                               "content": "jd_57edeb04e6321 2025-08-05 22:11:34\\n 我买的油压不出来那个盖子不好用。\\n 纽强自营 - 希望 2025-08-05 22:11:53\\n 麻烦您拍摄一下视频呢 \\n 纽强自营 - 希望 2025-08-05 22:11:59\\n 小客服先查看下呢 \\njd_57edeb04e6321 2025-08-05 22:12:52\\n 压不岀来，只是空气 \\n 纽强自营 - 希望 2025-08-05 22:13:16\\n 您拍摄一下视频呢 \\njd_57edeb04e6321 2025-08-05 22:16:48\\nhttps://vod.300hu.com/301/3ed99664vodbjngwcloud1oss/170a5959/1005237899577339905/f0.mp4\\n 纽强自营 - 希望 2025-08-05 22:17:16\\n 产品仓库发出的话都是完好的呢，产品是没有回弹设计的呢，如旋转太多或用力旋转的话是很容易损坏的呢 \\n 纽强自营 - 希望 2025-08-05 22:17:22\\n 产品仓库发出的话都是完好的呢，产品是没有回弹设计的呢，如旋转太多或用力旋转的话是很容易损坏的呢 \\n 纽强自营 - 希望 2025-08-05 22:17:35\\n 为了您的购物体验，小客服可以帮您反馈申请补发一个泵头哈，麻烦您提供一下手机号 \\njd_57edeb04e6321 2025-08-05 22:18:06\\n13505319706\\n 纽强自营 - 希望 2025-08-05 22:18:10\\n 您好，请您核对收货信息哦 \\njd_57edeb04e6321 2025-08-05 22:19:15\\n 谢谢，换个就能用吗 \\n 纽强自营 - 希望 2025-08-05 22:19:24\\n 是的哈 \\njd_57edeb04e6321 2025-08-05 22:20:09\\n 好吧，我试试吧 \\n 纽强自营 - 希望 2025-08-05 22:20:25\\nhttps://vod.300hu.com/301/3ed99664vodbjngwcloud1oss/3439ffb6/1005238862992179201/f0.mp4\\n 纽强自营 - 希望 2025-08-05 22:21:09\\n 这是视频哈，到货后按视频安装和使用哈 \\njd_57edeb04e6321 2025-08-05 22:21:49\\n 好的，谢谢 "
                                               },
                                               {
                                               "typeCode": "0",
                                               "typeName": "售前",
                                               "content": "jd_5b04e194a8bb2 2025-08-05 22:11:47\\n 你好夏天哪一款好？\\n 纽强自营 - 豁达 2025-08-05 22:12:17\\nhttps://item.jd.com/100011579915.html?sdx=ehi-lLxFu5iE6JnIYYJdicEpuTGURHtmwmtNsqlGY9WPPe_RLJhY4nzmpEvkVGGS&position=search\\n 纽强自营 - 豁达 2025-08-05 22:12:19\\n 夏天可以看下这款哦，这款会比较清爽一点呢 \\n 纽强自营 - 豁达 2025-08-05 22:12:20\\n 绿色款属于清爽型，水润质地，适合日常护肤，需要快速渗透与清爽舒缓 "
                                               },
                                               {
                                               "typeCode": "1",
                                               "typeName": "售中",
                                               "content": "jd_4a66a9b3c1803 2025-08-05 21:57:42\\n 现在下单，广东大概多久到 \\n 纽强自营 - 豁达 2025-08-05 21:57:55\\n 小主，京东自营店铺的商品均由京东快递统一配送，我们会根据您的收货地址选择最近的仓库发货，页面也会显示预计的送达时间哦 \\njd_4a66a9b3c1803 2025-08-05 21:58:46\\n 不是今天下单，明天就到吗？京东 \\n 纽强自营 - 豁达 2025-08-05 21:59:16\\n 页面会显示预计送达时间的 您可以看下 \\njd_4a66a9b3c1803 2025-08-05 21:59:49\\n 看到了 #E-s11\\n 纽强自营 - 豁达 2025-08-05 21:59:59\\n 好的哦"
                                               },
                                               {
                                               "typeCode": "3",
                                               "typeName": "其他",
                                               "content": "jd_660704945cabc 2025-08-05 22:18:31\\n 好的 \\n 纽强自营 - 快乐 2025-08-05 22:18:38\\n 嗯呢"
                                               }
                                               ]
                
                用户的输入的聊天记录如下：
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }


    @Test
    public void testUseSessionWith_OrginalPrompt_simple() throws InterruptedException {
        List<ChatMessage> messagesForReqList = new ArrayList<>();



        /*ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("亲 这个洗衣机的屏显一直转圈是什么意思").build();
        messagesForReqList.add(assitantMessage);*/


       /* ChatMessage assitantMessage2 =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(" ").build();
        messagesForReqList.add(assitantMessage2);*/


        String userMessageContent = """
                聊天记录如下:
                
                """;


        userMessageContent = readChatLogFile();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

        messagesForReqList.add(userMessage);


        /*ChatMessage assitantMessage3 =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("不显示数字了，就转圈").build();
        messagesForReqList.add(assitantMessage3);

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content("您可以提供下订单号哈，这边为您查询一下具体情况~").build();
        messagesForReqList.add(userMessage2);


        ChatMessage userMessage3 =
                ChatMessage.builder().role(ChatMessageRole.USER).content("有出现代码吗").build();
        messagesForReqList.add(userMessage3);*/


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId("ctx-20250808180138-nwb65")
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));

//        Thread.sleep(1000*60*3);
    }


}


