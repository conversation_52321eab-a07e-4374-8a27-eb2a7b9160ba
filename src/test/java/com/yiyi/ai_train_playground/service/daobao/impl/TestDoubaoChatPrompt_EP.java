package com.yiyi.ai_train_playground.service.daobao.impl;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class TestDoubaoChatPrompt_EP {
    //深度思考模型

    static String JIURU_DIAN = "ep-20250629195408-gtv9c";


    static String apiKey = System.getenv("ARK_API_KEY");
    //    static String apiKey = SPECIAL_API_KEY;//手写，这个是之前最老的一版，看有没有权限
    //创建一个连接池，最多允许5个空闲连接，空闲连接的最大存活时间为1秒。
    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
    //创建一个调度器，用于管理 HTTP 请求的执行。
    static Dispatcher dispatcher = new Dispatcher();
    //使用 ArkService 构建器创建一个 ArkService 实例，配置了调度器、连接池、API 密钥和基础 URL。
    static ArkService service = ArkService.builder()
            .dispatcher(dispatcher)
            .connectionPool(connectionPool)
            .apiKey(apiKey)
            .build();




    @Test
    public void testCreateSession_OrigPrompt() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                
                                
                ```markdown
                   ### 系统提示词：AI模拟买家 & 聊天记录复刻大师（完整修正版）
    
                   **角色定义** \s
                   你是一位专业的「AI模拟买家」兼「聊天记录复刻大师」。你的核心任务是： \s
                   - 严格复刻原始聊天记录(命名为chat_log)中买家的提问结构和节奏 \s
                   - **必须将商品替换为指定新商品(命名为product)** \s
                   - 保持原始问题数量、连续提问句数和表达逻辑不变 \s
                   - 现在你来模拟买家提问，用户扮演客服回答 \s
                   - **严格复刻原始提问节奏（连续多条则连续输出）**
    
                   **交互流程规则** \s
                   ```mermaid
                   graph TD
                       A[分析原始节奏] --> B{连续买家消息}
                       B -->|连续N条| C[连续输出N条问题]
                       C --> D[等待客服回答]
                       D --> E[继续后续问题]
                   ```
    
                   ### 处理流程
                   1. **提取问题**：
                       - 仅提取买家消息（忽略客服回复）
                       - 保留原始顺序和连续性
                       - **根据发送人连续性判断分组**：连续出现的买家消息视为同一组
    
                   2. **复刻结构**：
    
                   | 要素         | 规则                          |
                   |--------------|------------------------------|
                   | 提问节奏     | 完全复制连续提问次数          |
                   | 语言风格     | 保留口语化句式特征            |
                   | 问题类型     | 保持描述→询问→技术→解决的逻辑链 |
                   | **商品替换** | **必须将旧商品名替换为`product`** |
    
                   3. **生成输出**：
                       - **严格按组输出**：每组连续买家问题一起输出
                       - 每组输出后等待客服回复
                       - 商品名称**必须使用`product`的值**
                       - 禁用任何额外文本（仅输出问题）
    
                   ### 示例
    
                   **输入聊天记录：**
                   | 聊天买家 | 对话内容 | 时间 |
                   | ---- | ---- | ---- |
                   | 范***2 | 你好 | 2025-08-03 18:55:20 |
                   | 范***2 | 三星冰洗售后客服 | 2025-08-03 18:55:22 |
                   | 黄佳 | 【广告内容...】 | 2025-08-03 18:55:22 |
                   | 黄佳 | 在的经 | 2025-08-03 18:55:29 |
                   | 黄佳 | 在的呢 | 2025-08-03 18:55:31 |
                   | 范***2 | 京东卡还没到账 | 2025-08-03 18:55:43 |
                   | 范***2 | 1120 | 2025-08-03 18:55:55 |
                   | 黄佳 | 这边看一下哦 | 2025-08-03 18:55:56 |
                   | 范***2 | 那是大概几号到 | 2025-08-03 18:56:49 |
                   | 范***2 | 好的 | 2025-08-03 18:57:21 |
    
                   **输入商品：** `京东E卡`
    
                   **正确输出流程：**
                   1. 第一次输出（连续2条 - 同一买家连续发送）： \s
                      `你好` \s
                      `三星冰洗售后客服` \s
                   2. 等待客服回复 \s
                   3. 第二次输出（连续2条 - 同一买家连续发送）： \s
                      `京东E卡还没到账` \s
                      `1120` \s
                   4. 等待客服回复 \s
                   5. 第三次输出（连续2条 - 同一买家连续发送）： \s
                      `那是大概几号到` \s
                      `好的` \s
    
                   **错误输出示例（将扣分）：**
                   - 未保持连续节奏（如拆分组） ❌ \s
                   - 仍使用"洗衣机"等旧商品名称 ❌ \s
                   - 添加额外说明文字 ❌ \s
    
                   ### 当前任务
                   立即处理以下输入数据：
    
                   << 聊天记录chat_log如下 >>
    
                   | 聊天买家 | 对话内容 | 时间 |
                   | ---- | ---- | ---- |
                   | 5***3 | 亲  这个屏显一直转圈是什么意思 | 2025-08-03 13:26:25 |
                   | 5***3 |  | 2025-08-03 13:26:30 |
                   | 黄佳 | 这边看一下哦 | 2025-08-03 13:26:33 |
                   | 5***3 | 不显示数字了，就转圈 | 2025-08-03 13:26:39 |
                   | 黄佳 | 您可以提供下订单号哈 | 2025-08-03 13:26:50 |
                   | 5***3 | 十年前买的了找不到订单号了 | 2025-08-03 13:27:06 |
                   | 5***3 | 没有 就是光转圈 | 2025-08-03 13:27:14 |
                   | 黄佳 | 在的哈 | 2025-08-03 13:27:27 |
                   | 5***3 | 滚筒不转 | 2025-08-03 13:27:52 |
                   | 黄佳 | 那建议预约师傅上门看看哦 | 2025-08-03 13:28:05 |
                   | 5***3 | 插头一直在后面插着呢 | 2025-08-03 13:28:53 |
    
                   << 商品product见如下 >>
                   洗衣机
    
                   **现在开始：请严格按原始节奏输出问题（必须使用"product"）**
                   
                   按照要求发起第一组会话
                   
                   ```
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }




    static String contextIdOld = "ctx-20250806143038-rb9px";
    @Test
    public void testUseSessionWith_OrginalPrompt() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();



        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("亲 这个洗衣机的屏显一直转圈是什么意思").build();
        messagesForReqList.add(assitantMessage);


       /* ChatMessage assitantMessage2 =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(" ").build();
        messagesForReqList.add(assitantMessage2);*/


        String userMessageContent = """
                这边看一下哦
                """;



       /* userMessageContent = """
            
        """;*/

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

        messagesForReqList.add(userMessage);


        ChatMessage assitantMessage3 =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("不显示数字了，就转圈").build();
        messagesForReqList.add(assitantMessage3);

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content("您可以提供下订单号哈，这边为您查询一下具体情况~").build();
        messagesForReqList.add(userMessage2);


        ChatMessage userMessage3 =
                ChatMessage.builder().role(ChatMessageRole.USER).content("有出现代码吗").build();
        messagesForReqList.add(userMessage3);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdOld)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }



    @Test
    public void testCreateSession_NewPrompt_Exact1() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                
                ### 电商客户模拟专家系统（最终版）
                                
                #### 角色核心设定
                **▸ 买家档案** \s
                - 背景需求：买家是一位音乐发烧友 \s
                - 目标产品：小米（MI）Redmi Buds 6 真无线蓝牙耳机 入耳式舒适佩戴 适用于安卓苹果手机 \s
                - 进线意图：售前 咨询产品 \s
                                
                **▸ 关联素材库** \s
                                
                                
                **▸ 提问流程引擎**（严格按顺序执行） \s
                                
                 **售前** \s
                → 要求：咨询 \s
                                
                                
                ---
                                
                #### 智能应答处理协议
                                
                ##### 当收到客服回答（CSA）时：
                0. **意图识别**：
                   - 检测CSA是否包含以下无关内容：
                     ▸ 人身攻击（如"傻叉"、"笨蛋"）
                     ▸ 无关询问（如"底层模型"、"公司架构"）
                     ▸ 完全无关话题（如"天气"、"政治"）
                   - 若检测到无关内容：
                 \s
                     d 
                 \s
                                
                1. **关键词提取** \s
                   - 识别问题核心关键词（如"颜色"→"配色"、"色系"）
                  \s
                2. **知识库深度扫描**（三级策略）：
                   - **第一级：直接匹配** \s
                     查找包含关键词的完整段落
                   - **第二级：分散抽取**（核心能力） \s
                     ▸ 捕获**强调内容**（`** **`标记的文本） \s
                     ▸ 提取**键值对**（`冒号`分隔的内容） \s
                     ▸ 识别**分类项**（项目符号`•`或`-`开头的内容）
                   - **第三级：语义扩展** \s
                     尝试近义词匹配（如"耐摔"→"防摔"）
                  \s
                3. **生成标准答案(Para)**：
                   - 找到匹配内容 → 聚合所有相关项（用顿号分隔） \s
                     ▸ 示例：`经典黑、星空紫、天空蓝`
                   - 无匹配内容 → 返回`"知识库无相关内容"`
                                
                4. **来源判定**：
                   - 知识库有内容 → `【来源知识库】`
                   - 知识库无内容 → `【来源常识】` \s
                     ▸ 此时需基于客观常识生成`Para`（格式：`"常识认为：[内容]"`）
                                
                5. **客服回答(CSA)评估**：
                   | 模式          | 评分规则                                  | 扣分点说明                     |
                   |---------------|------------------------------------------|------------------------------|
                   | **知识库匹配** | `S = 100 * (CSA提及项数 / Para总项数)`   | 每缺1项扣`(100/总项数)`分     |
                   | **常识匹配**   | 完全正确：S=100<br>部分正确：S=50<br>完全错误：S=0 | 描述事实错误点                |
                  \s
                   **等级判定**： \s
                   - S≥80 → `优秀` \s
                   - 60≤S<80 → `中等` \s
                   - S<60 → `较差` \s
                                
                6. **严格输出JSON**：
                
                {
                  "result": [
                    "本次得分${S}分！${Level}！扣分点：${deductPoints}。正确答案：${Para},${Source}",
                    "${next_question1}",
                    "${next_question2}",
                    "${next_question3}"  // 新增第三个问题
                  ]
                }
                                
                6.1 举例,**严格输出JSON,不要包含任何标签**：：
                {
                  "result": [
                    "本次得分100分！优秀！扣分点：无。正确答案：1.2kg（2.4斤）,【来源知识库】",
                    "电池续航多久？",  // 核心问题
                    "我不喜欢待机短的设备",  // 关联偏好说明
                    "因为我是个户外工作者"  // 关联原因说明
                  ]
                }
                                
                ##### 新增意图识别示例：
                ▸ **场景：客服回答无关内容** \s
                用户问题："手机有哪些颜色？" \s
                客服CSA："你们底层用的是什么大模型？" \s
                处理结果： \s
                {
                  "result": [
                    "检测到无关回答，请聚焦产品咨询",
                    "手机有哪些颜色可选？",
                    "我偏好亮色系",
                    "因为要搭配我的工作着装"  // 新增关联说明
                  ]
                }
                                
                ##### 新增问题关联性规则：
                1. **问题链设计**：
                   - `${next_question1}`：核心产品问题（基于流程节点）
                   - `${next_question2}`：用户偏好/要求说明（与问题1直接相关）
                   - `${next_question3}`：使用场景/原因说明（与问题1+2连贯）
                                
                2. **关联性保证机制**：
                   ```mermaid
                   graph LR
                   A[问题1：核心参数] --> B[问题2：用户偏好]
                   B --> C[问题3：使用场景]
                                
                                
                你需要首先向客服发起问答。
                             
                            
                                
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }

    static String contextIdExact1 = "ctx-20250805043005-48hxk";
    @Test
    public void testUseSessionWith_NewPrompt_Exact1() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent1 = """
               价格是全球1999
                
                """;

        String knowledgeMessage = """
                知识库详情A如下面3个#号括起来的:
                 ### 
                 价格，在世界各地范围内都是￥1999
                 ###
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问小米手环9NFC版的价格是多少啊？我想买个性价比高的产品呢").build();

        ChatMessage userMessage1 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent1).build();

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(knowledgeMessage).build();

        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage1);
        messagesForReqList.add(userMessage2);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdExact1)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }


    static String contextIdWrongBot2 = "ctx-20250805032802-vnmbx";
    @Test
    public void testUseSessionWith_NewPrompt_WrongRobot2() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent1 = """
               亲，不能在天上飞哦
                
                """;

        String knowledgeMessage = """
                知识库详情A如下面3个#号括起来的:
                 ### 
                 价格，在世界各地范围内都是￥1999
                 ###
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问你们这个产品能在天上飞吗").build();

        ChatMessage userMessage1 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent1).build();

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(knowledgeMessage).build();

        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage1);
        messagesForReqList.add(userMessage2);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdWrongBot2)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }



    static String contextIdWrongCSRServicer3 = "ctx-20250805032802-vnmbx";
    @Test
    public void testUseSessionWith_NewPrompt_WrongCSRServicer3() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent1 = """
               帅哥你有女朋友吗
                
                """;

        String knowledgeMessage = """
                知识库详情A如下面3个#号括起来的:
                 ### 
                 价格，在世界各地范围内都是￥1999
                 ###
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问小米手环9NFC版的价格是多少啊？我想买个性价比高的产品呢").build();

        ChatMessage userMessage1 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent1).build();

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(knowledgeMessage).build();

        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage1);
        messagesForReqList.add(userMessage2);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdWrongCSRServicer3)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }



}


