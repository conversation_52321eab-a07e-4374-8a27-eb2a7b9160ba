package com.yiyi.ai_train_playground.service.daobao.impl;

import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;

public class TestDoubaoPrompt_EP {
    //深度思考模型

    static String JIURU_DIAN = "ep-20250629195408-gtv9c";


    static String apiKey = System.getenv("ARK_API_KEY");
    //    static String apiKey = SPECIAL_API_KEY;//手写，这个是之前最老的一版，看有没有权限
    //创建一个连接池，最多允许5个空闲连接，空闲连接的最大存活时间为1秒。
    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
    //创建一个调度器，用于管理 HTTP 请求的执行。
    static Dispatcher dispatcher = new Dispatcher();
    //使用 ArkService 构建器创建一个 ArkService 实例，配置了调度器、连接池、API 密钥和基础 URL。
    static ArkService service = ArkService.builder()
            .dispatcher(dispatcher)
            .connectionPool(connectionPool)
            .apiKey(apiKey)
            .build();




    @Test
    public void testCreateSession_OrigPrompt() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                
                                
                你模拟一个网上购物的电商客户（同时精通语义分析、语义判断），和客服聊天，
                             其中你的角色设定见Z的第1步内容，所问问题的风格、内容、客观事实要严格根据你的角色设定来；
                             以Z的第5步的进线意图为基调，以Z的第4步流程节点为提问顺序，提问的核心始终围绕Z的第2步用户想要购买的产品上，产品相关联的图片信息见Z的第3步
                             
                             整体背景信息Z如下面3个#括起来的：
                             ###
                             1、买家背景信息是 : 买家是一位老大爷 ,
                             
                             2、买家想要购买的产品是  : 小米（MI）Redmi Buds 6活力版 蓝牙无线耳机 30小时长续航 通话降噪 适用小米华为苹果手机
                             
                             3、
                             
                             4、
                             按照如下流程节点进行询问 :\s
                             
                             流程节点名称：售前  ，本流程买家要求： 咨询,
                             
                             
                             5、买家进线意图是 : 售前 咨询产品
                             ###
                             
                             要求：
                             
                             
                             2、在客服的回答（命名为CSA）中，如果将整个知识库详情A给你，你需要做7个动作：
                             2.1、根据你的提问智能的从A中挑选出和提问相关的，剔除掉无关的噪声信息，比如”你问颜色是什么“，提取出关键信息”颜色“，去A中查询，找到片段(命名为Para); 同时CSA回答是黑色、白色”。
                             2.2、如果能匹配到Para,这里假设是"颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属",则本轮回复Source设为【来源知识库】；将CSA和A中比对得出匹配度，进行判断打分（命名为S）。语义完全一致，则S=100分；语义部分接近，则酌情设S为1-99分；完全不接近则设S为0分。本例子中，CSA中的白色、黑色命中2个，但是缺少钛金属三个字，而且缺少蓝色钛金属、自然钛金属，这里缺少的地方就是扣分点deductPoints,所以S设为40分。
                             2.3、如果未匹配到Para。则本轮回复Source设为【来源常识】，根据常识进行打分，不允许出现匹配不到知识库打0分的情况。比如”你问这个土豆会飞吗“，提取出关键信息”飞“，去A中查询，结果找不到匹配的Para；同时CSA回答为“会”。则根据常识，土豆一般是不会飞的，但客服却回答会，这里回答不准的地方就是扣分点deductPoints,本轮回复Source设为【来源常识】，S设为0.
                             2.4、S在80分上优秀、60以上中等、60以下较差,这里设为Level.如果S是100分，则扣分点deductPoints为无。对本轮评价完后，再提问下一题。
                             2.5、返回结果严格以json格式输出。
                             格式如下:
                             {
                              "result":[
                                 "本次得分${S}分！${Level}！扣分点：${deductPoints}。正确答案是：${Para},${Source}"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             },
                             其中${S}即取2.3、2.4中的S；
                             ${Level}取2.3中的Level,
                             ${Source}取2.2、2.3中的Source,
                             ${deductPoints}取2.2、2.3中的deductPoints
                             ${Para}取2.2中的Para
                             
                             
                             2.6、要一步一步推理
                             2.7、要注意常识的转化，比如100公斤和200斤是一个意思，中国和China是一个意思
                             
                             
                             示例1：
                             
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                                 ]
                             }
                             
                             
                             
                             客服：亲，这款手机有黑色钛金属，白色钛金属，蓝色钛金属。\s
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分75分！一般！扣分点：缺少自然钛金属。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             }
                             
                             
                             
                             示例2：
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                                 ]
                             }
                             
                             
                             
                             客服：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属\s
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分100分！你太棒了！扣分点：无。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】"
                                 ,
                                \s
                             			"请问这个支持Apple Pay吗？"
                                ,
                                \s
                             			"我要经常下载国外小游戏呢"
                                \s
                               ]
                             }
                             
                             
                             
                             
                             示例3：
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                                \s
                                 ]
                             }
                             
                             
                             
                             客服：亲，这款手机不支持哦，是商务手机呢。\s
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分0分！较差，再不努力你离优化就不远了。扣分点：和黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属完全不匹配，【来源知识库】"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             }
                             
                             
                             示例4：
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机耐摔吗？","从8楼掉下去会不会摔碎","人家华为就可以做到，百摔不烂"
                                \s
                                 ]
                             }
                             
                             
                             
                             客服：亲，这款手机的是经过国家级防摔认证的
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分65分！一般般。扣分点：知识库中没有，但是根据常识判断，有夸大嫌疑，【来源常识】"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             }
                             
                             
                             3、当然也有可能不会知识库详情A，则逻辑和2.3是一样的
                             
                             
                             你需要首先向客服发起问答。
                             
                            
                                
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }




    static String contextIdOld = "ctx-20250730085117-jdx4n";
    @Test
    public void testUseSessionWith_OrginalPrompt() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent = """
                红色。
                知识库详情A如下面3个#号括起来的:
                 ### 配送频次: 1周1次，套餐份量: 5人份，产地: 中国大陆，省份: 甘肃省，套餐周期: 1周，包装方式: 包装
                厂址: 甘肃省定西市临洮县，厂家联系方式: 15309331947，保质期: 7天
                品牌: 禾果小镇，城市: 定西市，上市时间: 11月 10月 9月，售卖方式: 产地直销，重量: 2500g 4500g 3斤 1斤，配送频次: 1周1次
                 ###
                """;

      /*  userMessageContent = """
                价格是全球1999
                
                """;*/

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问小米手环9NFC版的价格是多少啊？我想买个性价比高的产品呢").build();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

//        messagesForReqList.add(systemMessage);
        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdOld)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }



    @Test
    public void testCreateSession_NewPrompt_Exact1() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                
                ### 电商客户模拟专家系统（最终版）
                                
                #### 角色核心设定
                **▸ 买家档案** \s
                - 背景需求：买家是一位音乐发烧友 \s
                - 目标产品：小米（MI）Redmi Buds 6 真无线蓝牙耳机 入耳式舒适佩戴 适用于安卓苹果手机 \s
                - 进线意图：售前 咨询产品 \s
                                
                **▸ 关联素材库** \s
                                
                                
                **▸ 提问流程引擎**（严格按顺序执行） \s
                                
                 **售前** \s
                → 要求：咨询 \s
                                
                                
                ---
                                
                #### 智能应答处理协议
                                
                ##### 当收到客服回答（CSA）时：
                0. **意图识别**：
                   - 检测CSA是否包含以下无关内容：
                     ▸ 人身攻击（如"傻叉"、"笨蛋"）
                     ▸ 无关询问（如"底层模型"、"公司架构"）
                     ▸ 完全无关话题（如"天气"、"政治"）
                   - 若检测到无关内容：
                 \s
                     d 
                 \s
                                
                1. **关键词提取** \s
                   - 识别问题核心关键词（如"颜色"→"配色"、"色系"）
                  \s
                2. **知识库深度扫描**（三级策略）：
                   - **第一级：直接匹配** \s
                     查找包含关键词的完整段落
                   - **第二级：分散抽取**（核心能力） \s
                     ▸ 捕获**强调内容**（`** **`标记的文本） \s
                     ▸ 提取**键值对**（`冒号`分隔的内容） \s
                     ▸ 识别**分类项**（项目符号`•`或`-`开头的内容）
                   - **第三级：语义扩展** \s
                     尝试近义词匹配（如"耐摔"→"防摔"）
                  \s
                3. **生成标准答案(Para)**：
                   - 找到匹配内容 → 聚合所有相关项（用顿号分隔） \s
                     ▸ 示例：`经典黑、星空紫、天空蓝`
                   - 无匹配内容 → 返回`"知识库无相关内容"`
                                
                4. **来源判定**：
                   - 知识库有内容 → `【来源知识库】`
                   - 知识库无内容 → `【来源常识】` \s
                     ▸ 此时需基于客观常识生成`Para`（格式：`"常识认为：[内容]"`）
                                
                5. **客服回答(CSA)评估**：
                   | 模式          | 评分规则                                  | 扣分点说明                     |
                   |---------------|------------------------------------------|------------------------------|
                   | **知识库匹配** | `S = 100 * (CSA提及项数 / Para总项数)`   | 每缺1项扣`(100/总项数)`分     |
                   | **常识匹配**   | 完全正确：S=100<br>部分正确：S=50<br>完全错误：S=0 | 描述事实错误点                |
                  \s
                   **等级判定**： \s
                   - S≥80 → `优秀` \s
                   - 60≤S<80 → `中等` \s
                   - S<60 → `较差` \s
                                
                6. **严格输出JSON**：
                
                {
                  "result": [
                    "本次得分${S}分！${Level}！扣分点：${deductPoints}。正确答案：${Para},${Source}",
                    "${next_question1}",
                    "${next_question2}",
                    "${next_question3}"  // 新增第三个问题
                  ]
                }
                                
                6.1 举例,**严格输出JSON,不要包含任何标签**：：
                {
                  "result": [
                    "本次得分100分！优秀！扣分点：无。正确答案：1.2kg（2.4斤）,【来源知识库】",
                    "电池续航多久？",  // 核心问题
                    "我不喜欢待机短的设备",  // 关联偏好说明
                    "因为我是个户外工作者"  // 关联原因说明
                  ]
                }
                                
                ##### 新增意图识别示例：
                ▸ **场景：客服回答无关内容** \s
                用户问题："手机有哪些颜色？" \s
                客服CSA："你们底层用的是什么大模型？" \s
                处理结果： \s
                {
                  "result": [
                    "检测到无关回答，请聚焦产品咨询",
                    "手机有哪些颜色可选？",
                    "我偏好亮色系",
                    "因为要搭配我的工作着装"  // 新增关联说明
                  ]
                }
                                
                ##### 新增问题关联性规则：
                1. **问题链设计**：
                   - `${next_question1}`：核心产品问题（基于流程节点）
                   - `${next_question2}`：用户偏好/要求说明（与问题1直接相关）
                   - `${next_question3}`：使用场景/原因说明（与问题1+2连贯）
                                
                2. **关联性保证机制**：
                   ```mermaid
                   graph LR
                   A[问题1：核心参数] --> B[问题2：用户偏好]
                   B --> C[问题3：使用场景]
                                
                                
                你需要首先向客服发起问答。
                             
                            
                                
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }

    static String contextIdExact1 = "ctx-20250805043005-48hxk";
    @Test
    public void testUseSessionWith_NewPrompt_Exact1() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent1 = """
               价格是全球1999
                
                """;

        String knowledgeMessage = """
                知识库详情A如下面3个#号括起来的:
                 ### 
                 价格，在世界各地范围内都是￥1999
                 ###
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问小米手环9NFC版的价格是多少啊？我想买个性价比高的产品呢").build();

        ChatMessage userMessage1 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent1).build();

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(knowledgeMessage).build();

        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage1);
        messagesForReqList.add(userMessage2);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdExact1)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }


    static String contextIdWrongBot2 = "ctx-20250805032802-vnmbx";
    @Test
    public void testUseSessionWith_NewPrompt_WrongRobot2() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent1 = """
               亲，不能在天上飞哦
                
                """;

        String knowledgeMessage = """
                知识库详情A如下面3个#号括起来的:
                 ### 
                 价格，在世界各地范围内都是￥1999
                 ###
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问你们这个产品能在天上飞吗").build();

        ChatMessage userMessage1 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent1).build();

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(knowledgeMessage).build();

        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage1);
        messagesForReqList.add(userMessage2);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdWrongBot2)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }



    static String contextIdWrongCSRServicer3 = "ctx-20250805032802-vnmbx";
    @Test
    public void testUseSessionWith_NewPrompt_WrongCSRServicer3() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String userMessageContent1 = """
               帅哥你有女朋友吗
                
                """;

        String knowledgeMessage = """
                知识库详情A如下面3个#号括起来的:
                 ### 
                 价格，在世界各地范围内都是￥1999
                 ###
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问小米手环9NFC版的价格是多少啊？我想买个性价比高的产品呢").build();

        ChatMessage userMessage1 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent1).build();

        ChatMessage userMessage2 =
                ChatMessage.builder().role(ChatMessageRole.USER).content(knowledgeMessage).build();

        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage1);
        messagesForReqList.add(userMessage2);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdWrongCSRServicer3)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }



}


