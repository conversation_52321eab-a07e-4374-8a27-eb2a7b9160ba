package com.yiyi.ai_train_playground.service.daobao.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volcengine.ark.runtime.Const;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingRequest;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingResult;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.schedulers.Schedulers;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;

public class TestDoubao_Model {
    static String MODLE_NAME = "doubao-1.5-vision-lite-250315";
    static String NORMAL_MODLE_NAME = "doubao-1-5-pro-32k-250115";
    static String NORMAL_MODLE_NAME_256 = "doubao-1-5-pro-256k-250115";
    //深度思考模型
    static String THINK_MODLE_NAME = "doubao-1.5-thinking-pro-250415";
    static String EMBED_MODLE_NAME = "doubao-embedding-text-240715";

//    static String JIURU_DIAN = "ep-20250629141129-jx7pj";
    static String JIURU_DIAN = "ep-20250629195408-gtv9c";


    static String apiKey = System.getenv("ARK_API_KEY");
    //    static String apiKey = SPECIAL_API_KEY;//手写，这个是之前最老的一版，看有没有权限
    //创建一个连接池，最多允许5个空闲连接，空闲连接的最大存活时间为1秒。
    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
    //创建一个调度器，用于管理 HTTP 请求的执行。
    static Dispatcher dispatcher = new Dispatcher();
    //使用 ArkService 构建器创建一个 ArkService 实例，配置了调度器、连接池、API 密钥和基础 URL。
    static ArkService service = ArkService.builder()
            .dispatcher(dispatcher)
            .connectionPool(connectionPool)
            .apiKey(apiKey)
            .build();

    @Test
    public void testDanlun() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();
        System.out.println("\n----- standard request -----");
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("").build();
        messages.add(systemMessage);
        messages.add(userMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();
    }


    @Test
    public void testCreateSessoin() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                
                                
                你模拟一个网上购物的电商客户（同时精通语义分析、语义判断），和客服聊天，
                             其中你的角色设定见Z的第1步内容，所问问题的风格、内容、客观事实要严格根据你的角色设定来；
                             以Z的第5步的进线意图为基调，以Z的第4步流程节点为提问顺序，提问的核心始终围绕Z的第2步用户想要购买的产品上，产品相关联的图片信息见Z的第3步
                             
                             整体背景信息Z如下面3个#括起来的：
                             ###
                             1、买家背景信息是 : 买家是一位老大爷 ,
                             
                             2、买家想要购买的产品是  : 小米（MI）Redmi Buds 6活力版 蓝牙无线耳机 30小时长续航 通话降噪 适用小米华为苹果手机
                             
                             3、
                             
                             4、
                             按照如下流程节点进行询问 :\s
                             
                             流程节点名称：售前  ，本流程买家要求： 咨询,
                             
                             
                             5、买家进线意图是 : 售前 咨询产品
                             ###
                             
                             要求：
                             
                             
                             2、在客服的回答（命名为CSA）中，如果将整个知识库详情A给你，你需要做7个动作：
                             2.1、根据你的提问智能的从A中挑选出和提问相关的，剔除掉无关的噪声信息，比如”你问颜色是什么“，提取出关键信息”颜色“，去A中查询，找到片段(命名为Para); 同时CSA回答是黑色、白色”。
                             2.2、如果能匹配到Para,这里假设是"颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属",则本轮回复Source设为【来源知识库】；将CSA和A中比对得出匹配度，进行判断打分（命名为S）。语义完全一致，则S=100分；语义部分接近，则酌情设S为1-99分；完全不接近则设S为0分。本例子中，CSA中的白色、黑色命中2个，但是缺少钛金属三个字，而且缺少蓝色钛金属、自然钛金属，这里缺少的地方就是扣分点deductPoints,所以S设为40分。
                             2.3、如果未匹配到Para。则本轮回复Source设为【来源常识】，根据常识进行打分，不允许出现匹配不到知识库打0分的情况。比如”你问这个土豆会飞吗“，提取出关键信息”飞“，去A中查询，结果找不到匹配的Para；同时CSA回答为“会”。则根据常识，土豆一般是不会飞的，但客服却回答会，这里回答不准的地方就是扣分点deductPoints,本轮回复Source设为【来源常识】，S设为0.
                             2.4、S在80分上优秀、60以上中等、60以下较差,这里设为Level.如果S是100分，则扣分点deductPoints为无。对本轮评价完后，再提问下一题。
                             2.5、返回结果严格以json格式输出。
                             格式如下:
                             {
                              "result":[
                                 "本次得分${S}分！${Level}！扣分点：${deductPoints}。正确答案是：${Para},${Source}"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             },
                             其中${S}即取2.3、2.4中的S；
                             ${Level}取2.3中的Level,
                             ${Source}取2.2、2.3中的Source,
                             ${deductPoints}取2.2、2.3中的deductPoints
                             ${Para}取2.2中的Para
                             
                             
                             2.6、要一步一步推理
                             2.7、要注意常识的转化，比如100公斤和200斤是一个意思，中国和China是一个意思
                             
                             
                             示例1：
                             
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                                 ]
                             }
                             
                             
                             
                             客服：亲，这款手机有黑色钛金属，白色钛金属，蓝色钛金属。\s
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分75分！一般！扣分点：缺少自然钛金属。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             }
                             
                             
                             
                             示例2：
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                                 ]
                             }
                             
                             
                             
                             客服：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属\s
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分100分！你太棒了！扣分点：无。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】"
                                 ,
                                \s
                             			"请问这个支持Apple Pay吗？"
                                ,
                                \s
                             			"我要经常下载国外小游戏呢"
                                \s
                               ]
                             }
                             
                             
                             
                             
                             示例3：
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                                \s
                                 ]
                             }
                             
                             
                             
                             客服：亲，这款手机不支持哦，是商务手机呢。\s
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分0分！较差，再不努力你离优化就不远了。扣分点：和黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属完全不匹配，【来源知识库】"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             }
                             
                             
                             示例4：
                             
                             客户：
                             
                             {
                                 "result": [
                                     "请问这款手机耐摔吗？","从8楼掉下去会不会摔碎","人家华为就可以做到，百摔不烂"
                                \s
                                 ]
                             }
                             
                             
                             
                             客服：亲，这款手机的是经过国家级防摔认证的
                             知识库详情A如下面3个#号括起来的
                             ###
                             颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                             ###
                             
                             
                             客户：
                             
                             {
                              "result":[
                                 "本次得分65分！一般般。扣分点：知识库中没有，但是根据常识判断，有夸大嫌疑，【来源常识】"
                                 ,
                                \s
                             			"这个产品支持5G吗？"
                                ,
                                \s
                             			"我比较喜欢先进的产品呢"
                                \s
                               ]
                             }
                             
                             
                             3、当然也有可能不会知识库详情A，则逻辑和2.3是一样的
                             
                             
                             你需要首先向客服发起问答。
                             
                            
                                
                
                """;


        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }


   /* CreateContextResult{id='ctx-20250730084217-vdftx', model='ep-20250629195408-gtv9c', mode='common_prefix', ttl=86400, truncationStrategy=null, usage=Usage{promptTokens=23, completionTokens=0, totalTokens=23, promptTokensDetails=PromptTokensDetails{cachedTokens=0}, completionTokensDetails=null}}*/


    static String contextId= "ctx-20250730085117-jdx4n";
    @Test
    public void testUseSessionWithoutStream() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();

//        ChatMessage systemMessage =
//                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是一个厨师").build();

        String userMessageContent = """
                红色。
                知识库详情A如下面3个#号括起来的:
                 ### 配送频次: 1周1次，套餐份量: 5人份，产地: 中国大陆，省份: 甘肃省，套餐周期: 1周，包装方式: 包装
                厂址: 甘肃省定西市临洮县，厂家联系方式: 15309331947，保质期: 7天
                品牌: 禾果小镇，城市: 定西市，上市时间: 11月 10月 9月，售卖方式: 产地直销，重量: 2500g 4500g 3斤 1斤，配送频次: 1周1次
                 ###
                """;

        userMessageContent = """
                价格是全球1999
                
                """;

        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("请问小米手环9NFC版的价格是多少啊？我想买个性价比高的产品呢").build();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

//        messagesForReqList.add(systemMessage);
        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextId)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }


    @Test
    public void testUseSessionWithStream() {
        ContextChatCompletionRequest streamChatCompletionRequest = ContextChatCompletionRequest.builder()
                // 设置上下文 ID
                .contextId("ctx-20250629203225-cp5s9")
                // 设置模型
                .model(JIURU_DIAN)
                // 设置用户消息
                .messages(Collections.singletonList(ChatMessage.builder().role(ChatMessageRole.USER)
//                        .content("请利用你的特长，讲下泡妞秘籍，3000内左右")
                        .content("我是谁？你是谁")
                        .build()))
                .build();

        // 发送流式聊天补全请求并处理结果
        service.streamContextChatCompletion(streamChatCompletionRequest)
                // 处理错误
                .doOnError(Throwable::printStackTrace)
                // 阻塞式遍历结果
                .blockingForEach(
                        choice -> {
                            // 如果结果不为空，则打印消息内容
                            if (!choice.getChoices().isEmpty()) {
                                System.out.print(choice.getChoices().get(0).getMessage()
                                        .getContent());
                            }
                        });

    }







    @Test
    public void testDuolun() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("花椰菜是什么？").build(),
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();
    }

    @Test
    public void testStream() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .blockingForEach(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // shutdown service
        service.shutdownExecutor();

    }


    /**
     * 测试多个会话知识库导入，天天找，设置一个标记，姜妹
     * @throws InterruptedException
     */
    @Test
    public void testT2C_32() throws InterruptedException {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();


        String systemMessage_N= """
                
                ```markdown
                              # 系统指令：聊天记录分析与生成引擎
                
                              ## 核心功能
                              1. 解析输入的聊天记录文本
                              2. 自动识别分类：售前(0)、售中(1)、售后(2)、其他(3)
                              3. 为每个分类生成3个模拟对话（N=原始记录中该分类的实际数量）
                              4. 输出标准JSON格式
                
                              ## 处理流程
                
                              ### 第一步：分类统计
                              ```python
                              def analyze_chats(text):
                                  # 统计每个类别的原始对话数量
                                  category_count = {
                                      "0": 0,  # 售前
                                      "1": 0,  # 售中
                                      "2": 0,  # 售后
                                      "3": 0   # 其他
                                  }
                
                                  # 识别每个对话的分类
                                  for dialog in extract_dialogs(text):
                                      category = classify_dialog(dialog)
                                      category_count[category] += 1
                
                                  return category_count
                              ```
                
                              ### 第二步：风格提取与模拟生成
                              ```mermaid
                              graph LR
                                  A[原始文本] --> B[分类识别]
                                  B --> C[售前N0]
                                  B --> D[售中N1]
                                  B --> E[售后N2]
                                  B --> F[其他N3]
                                  C --> G[生成N0个售前对话]
                                  D --> H[生成N1个售中对话]
                                  E --> I[生成N2个售后对话]
                                  F --> J[生成N3个其他对话]
                                  G & H & I & J --> K[JSON输出]
                              ```
                
                              ### 第三步：JSON格式输出
                              [
                                {
                                  "typeCode": "分类代码",
                                  "typeName": "分类名称",
                                  "content": "生成的完整对话文本"
                                },
                                ...
                                // 总数量 = N0 + N1 + N2 + N3
                              ]
                
                              ## 关键规则
                
                              ### 1. 数量确定机制
                              - `N0` = 原始文本中售前(0)类别的对话数量
                              - `N1` = 原始文本中售中(1)类别的对话数量
                              - `N2` = 原始文本中售后(2)类别的对话数量
                              - `N3` = 原始文本中其他(3)类别的对话数量
                              - **总生成量** = N0 + N1 + N2 + N3
                
                              ### 2. 对话生成要求
                              ```python
                              def generate_dialogs(category, count):
                                  dialogs = []
                                  for i in range(count):
                                      dialogs.append({
                                          "typeCode": category,
                                          "typeName": CATEGORY_NAMES[category],
                                          "content": create_style_match_dialog(category)
                                      })
                                  return dialogs
                              ```
                
                              ### 3. 内容规范
                              | 元素 | 生成规则 |
                              |------|----------|
                              | 用户ID | `jd_` + 12位十六进制（如`jd_4a66a9b3c1803`） |
                              | 时间戳 | `YYYY-MM-DD HH:MM:SS` 格式（范围在原始对话时间±3天内） |
                              | 客服标识 | 保持原始格式（如`纽强自营 - 希望`） |
                              | 对话轮次 | 与原始同类对话平均轮次一致（±1轮） |
                
                              ## 分类标准与内容特征
                              | 代码 | 类别 | 特征 | 生成要点 |
                              |------|------|------|----------|
                              | 0 | 售前 | 产品咨询/功能对比 | 包含产品链接、推荐理由 |
                              | 1 | 售中 | 订单/物流查询 | 包含时效确认、订单号 |
                              | 2 | 售后 | 问题解决流程 | 问题描述→解决方案→确认 |
                              | 3 | 其他 | 简短确认/问候 | 不超过2轮对话 |
                
                              ## 输出示例（完整版）
                              [
                               {
                               "typeCode": "2",
                               "typeName": "售后",
                               "content": "jd_57edeb04e6321 2025-08-05 22:11:34\\n 我买的油压不出来那个盖子不好用。\\n 纽强自营 - 希望 2025-08-05 22:11:53\\n 麻烦您拍摄一下视频呢 \\n 纽强自营 - 希望 2025-08-05 22:11:59\\n 小客服先查看下呢 \\njd_57edeb04e6321 2025-08-05 22:12:52\\n 压不岀来，只是空气 \\n 纽强自营 - 希望 2025-08-05 22:13:16\\n 您拍摄一下视频呢 \\njd_57edeb04e6321 2025-08-05 22:16:48\\nhttps://vod.300hu.com/301/3ed99664vodbjngwcloud1oss/170a5959/1005237899577339905/f0.mp4\\n 纽强自营 - 希望 2025-08-05 22:17:16\\n 产品仓库发出的话都是完好的呢，产品是没有回弹设计的呢，如旋转太多或用力旋转的话是很容易损坏的呢 \\n 纽强自营 - 希望 2025-08-05 22:17:22\\n 产品仓库发出的话都是完好的呢，产品是没有回弹设计的呢，如旋转太多或用力旋转的话是很容易损坏的呢 \\n 纽强自营 - 希望 2025-08-05 22:17:35\\n 为了您的购物体验，小客服可以帮您反馈申请补发一个泵头哈，麻烦您提供一下手机号 \\njd_57edeb04e6321 2025-08-05 22:18:06\\n13505319706\\n 纽强自营 - 希望 2025-08-05 22:18:10\\n 您好，请您核对收货信息哦 \\njd_57edeb04e6321 2025-08-05 22:19:15\\n 谢谢，换个就能用吗 \\n 纽强自营 - 希望 2025-08-05 22:19:24\\n 是的哈 \\njd_57edeb04e6321 2025-08-05 22:20:09\\n 好吧，我试试吧 \\n 纽强自营 - 希望 2025-08-05 22:20:25\\nhttps://vod.300hu.com/301/3ed99664vodbjngwcloud1oss/3439ffb6/1005238862992179201/f0.mp4\\n 纽强自营 - 希望 2025-08-05 22:21:09\\n 这是视频哈，到货后按视频安装和使用哈 \\njd_57edeb04e6321 2025-08-05 22:21:49\\n 好的，谢谢 "
                               },
                               {
                               "typeCode": "0",
                               "typeName": "售前",
                               "content": "jd_5b04e194a8bb2 2025-08-05 22:11:47\\n 你好夏天哪一款好？\\n 纽强自营 - 豁达 2025-08-05 22:12:17\\nhttps://item.jd.com/100011579915.html?sdx=ehi-lLxFu5iE6JnIYYJdicEpuTGURHtmwmtNsqlGY9WPPe_RLJhY4nzmpEvkVGGS&position=search\\n 纽强自营 - 豁达 2025-08-05 22:12:19\\n 夏天可以看下这款哦，这款会比较清爽一点呢 \\n 纽强自营 - 豁达 2025-08-05 22:12:20\\n 绿色款属于清爽型，水润质地，适合日常护肤，需要快速渗透与清爽舒缓 "
                               },
                               {
                               "typeCode": "1",
                               "typeName": "售中",
                               "content": "jd_4a66a9b3c1803 2025-08-05 21:57:42\\n 现在下单，广东大概多久到 \\n 纽强自营 - 豁达 2025-08-05 21:57:55\\n 小主，京东自营店铺的商品均由京东快递统一配送，我们会根据您的收货地址选择最近的仓库发货，页面也会显示预计的送达时间哦 \\njd_4a66a9b3c1803 2025-08-05 21:58:46\\n 不是今天下单，明天就到吗？京东 \\n 纽强自营 - 豁达 2025-08-05 21:59:16\\n 页面会显示预计送达时间的 您可以看下 \\njd_4a66a9b3c1803 2025-08-05 21:59:49\\n 看到了 #E-s11\\n 纽强自营 - 豁达 2025-08-05 21:59:59\\n 好的哦"
                               },
                               {
                               "typeCode": "3",
                               "typeName": "其他",
                               "content": "jd_660704945cabc 2025-08-05 22:18:31\\n 好的 \\n 纽强自营 - 快乐 2025-08-05 22:18:38\\n 嗯呢"
                               }
                               ]
                
                              ## 执行要求
                              1. **严格数量匹配**：每个类别生成数量 = 原始文本中该类别数量
                              2. **ID随机性**：生成全新的用户ID（不重复原始ID）
                              3. **时间合理性**：对话时间在原始时间范围内随机分布
                              4. **风格一致性**：
                                 - 保持原始对话的句式特征（如客服重复语句习惯）
                                 - 模仿原始的多媒体使用模式（如图片/视频链接）
                                 - 保留原始的表情符号使用频率
                              5. 严格按照json格式输出
                              6. 只输出json，不要输出任何其他内容
                
                   
                   ```
                
                """;


        String systemMessage_10= """
                
                以下是根据您的要求修改的系统提示词，专注于文本解析、风格模仿和JSON格式输出：
                
                ```markdown
                # 系统指令：聊天记录分析与生成专家
                
                ## 核心任务
                1. 直接解析输入的聊天记录文本
                2. 按四类分类：售前(0)、售中(1)、售后(2)、其他(3)
                3. 生成30个**全新但风格相同**的典型对话场景
                4. 输出标准JSON格式
                
                ## 处理流程
                
                ### 第一步：风格特征提取
                分析以下维度：
                ```python
                style_features = {
                    "语言特征": ["口语化程度", "专业术语密度", "表情符号使用频率"],
                    "交互模式": ["平均对话轮次", "响应时间间隔", "问题解决路径"],
                    "结构特征": ["时间戳格式", "身份标识模式", "多媒体使用习惯"]
                }
                ```
                
                ### 第二步：内容分类与生成
                ```mermaid
                graph TD
                    A[原始文本] --> B{分类识别}
                    B --> C[售前 0：产品咨询/推荐]
                    B --> D[售中 1：订单/物流]
                    B --> E[售后 2：问题解决]
                    B --> F[其他 3：问候/确认]
                    C & D & E & F --> G[风格模仿生成]
                    G --> H[10个新对话场景]
                ```
                
                ### 第三步：JSON格式输出
                严格遵循以下结构：
                [
                  {
                    "typeCode": "分类代码",
                    "typeName": "分类名称",
                    "content": "完整对话文本\\n包含时间戳和身份标识"
                  },
                  ...
                ]
                
                ## 生成规则
                1. **新对话生成要求**：
                   - 完全模仿原始对话的：时间格式、称呼方式、语言风格
                   - 每类至少生成2个场景（共30个）
                   - 保持真实业务场景逻辑
                
                2. **分类标准**：
                   | 代码 | 类别 | 特征 |
                   |---|---|---|
                   | 0 | 售前 | 产品咨询/功能对比/推荐 |
                   | 1 | 售中 | 订单状态/物流查询/库存 |
                   | 2 | 售后 | 故障处理/退换货/使用指导 |
                   | 3 | 其他 | 问候/感谢/无关内容 |
                
                3. **内容规范**：
                   ```python
                   def generate_dialog(type_code):
                       return f"用户ID {时间戳}\\n 用户内容\\n 客服ID {时间戳}\\n 客服内容"
                   ```
                
                ## 输出示例
                [
                  {
                    "typeCode": "2",
                    "typeName": "售后",
                    "content": "jd_57edeb04e6321 2025-08-05 22:11:34\\n 我买的油压不出来...（完整对话）"
                  },
                  {
                    "typeCode": "0",
                    "typeName": "售前",
                    "content": "jd_5b04e194a8bb2 2025-08-05 22:11:47\\n 你好夏天哪一款好？..."
                  }
                ]
                
                ## 执行要求
                1. 生成30个**全新对话**（非原始记录复制）
                2. 对话分布：
                   - 售前(0)：3个
                   - 售中(1)：3个
                   - 售后(2)：3个
                   - 其他(3)：1个
                
                3. 严格保持原始格式特征：
                   - 时间戳格式：YYYY-MM-DD HH:MM:SS
                   - 用户ID前缀：jd_ + 12位十六进制
                   - 客服标识："品牌 - 客服名"
                
                4. 内容要求：
                   - 包含典型问题解决路径（如售后需有：问题描述→解决方案→确认）
                   - 售前对话包含产品链接推荐
                   - 售中对话包含物流时效讨论
                   - 会话轮次不能比原来少
                   
                   
                ```
                
                ## 生成样例（示意）
                [
                  {
                    "typeCode": "1",
                    "typeName": "售中",
                    "content": "jd_4a66a9b3c1803 2025-08-05 21:57:42\\n 现在下单明天能到上海吗？\\n 纽强自营 - 晨曦 2025-08-05 21:57:55\\n 页面显示预计明天18点前送达哦\\njd_4a66a9b3c1803 2025-08-05 21:58:10\\n 能加急吗？\\n 纽强自营 - 晨曦 2025-08-05 21:58:25\\n 已为您备注加急处理"
                  },
                  {
                    "typeCode": "0",
                    "typeName": "售前",
                    "content": "jd_88ff32a1bcd9 2025-08-05 10:15:30\\n 敏感肌能用蓝色款吗？\\n 纽强自营 - 海洋 2025-08-05 10:16:02\\n 蓝色是滋润型，推荐黄色抗敏专用款\\nhttps://item.jd.com/100034567890.html"
                  }
                ]
               
                
                """;


        String systemMessage_myself= """
                
                
                ```markdown
                ### 系统提示词：聊天记录分析与生成专家
                
                #### 角色要求
                1. 您是专业的聊天记录分析与生成专家
                2. **核心任务**：学习提供的聊天记录风格，生成3条相同风格的会话记录
                
                #### 输入规范
                - 输入包含多段聊天记录
                - 每段聊天记录有明确标记：
                  - 开始标记：`/*****************以下为一通会话************************************/`
                  - 结束标记：`/*****************会话结束_时间:[时间]******************************/`
                - 示例输入格式（以三个`#`包裹）：
                  ```
                  ###
                  [/*****************以下为一通会话************************************/
                                          jd_71e2243fb99e1 2025-08-05 23:22:07
                                          https://item.jd.com/100146515514.html?sdx=ehi-lLxFuZiE6JnIYIdaiccltTGVRHtmwmtNsqlGY9WPPe_RLJhY4nzipkrkX2eT
                                          纽强自营-豁达 2025-08-05 23:22:07
                                          ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨
                                          纽强自营-豁达 2025-08-05 23:22:14
                                          您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！
                                          纽强自营-豁达 2025-08-05 23:22:15
                                          在的哦
                                          jd_71e2243fb99e1 2025-08-05 23:22:44
                                          你好，我需要纽强的宝宝洗发水，沐浴乳，身体乳的在三种试用装链接
                                          jd_71e2243fb99e1 2025-08-05 23:22:55
                                          宝宝一岁半
                                          纽强自营-豁达 2025-08-05 23:23:21
                                          洗发水沐浴露暂时没有试用装的链接哦
                                          纽强自营-豁达 2025-08-05 23:23:25
                                          只有正装的呢
                                          jd_71e2243fb99e1 2025-08-05 23:24:45
                                          那面霜的有没有试用装
                                          纽强自营-豁达 2025-08-05 23:25:09
                                          我想了解一下您家宝宝的肤质是怎样的？是偏干、偏油还是混合性肌肤呢？这样我们可以更精准地推荐适合您宝宝的保湿面霜
                                          纽强自营-豁达 2025-08-05 23:25:15
                                          宝宝肤质会干燥嘛
                                          jd_71e2243fb99e1 2025-08-05 23:27:12
                                          混合型
                                          纽强自营-豁达 2025-08-05 23:27:38
                                          目前最小规格的是15g的 您可以看下这款的哈
                                          纽强自营-豁达 2025-08-05 23:27:39
                                          https://item.jd.com/100121466523.html?sdx=ehi-lLxFu5iE6JnIYIFdiMAmtTKSRHtmwmtNsqlGY9WPPe_RLJhY4nzio03qUWOU&position=search
                                          纽强自营-豁达 2025-08-05 23:27:41
                                          蓝色款属于滋润型，奶油质地，适合日常护肤、舒缓空调环境或干燥肌引起的不适
                                          jd_71e2243fb99e1 2025-08-05 23:30:40
                                          那身体乳和洗发水呢？
                                          纽强自营-豁达 2025-08-05 23:31:00
                                          这个是一体的哈 身体 脸部都是可以使用的哦
                                          纽强自营-豁达 2025-08-05 23:31:16
                                          洗发水暂时没有试用装的呢
                                          引用：洗发水沐浴露暂时没有试用装的链接哦
                                          jd_71e2243fb99e1 2025-08-05 23:31:16
                                          洗发水呢？
                                          纽强自营-豁达 2025-08-05 23:31:19
                                          只有正装的
                                          jd_71e2243fb99e1 2025-08-05 23:31:43
                                          正装洗发水发个链接给我
                                          纽强自营-豁达 2025-08-05 23:32:10
                                          https://item.jd.com/100155708001.html?sdx=ehi-lLxFu5iE6JnIYIZZi8YosDCQRHtmwmtNsqlGY9WPPe_RLJhY4nzirEvjVG6Q&position=search
                                          纽强自营-豁达 2025-08-05 23:32:21
                                          这个洗发沐浴二合一的 您可以看下哦
                                          jd_71e2243fb99e1 2025-08-05 23:32:35
                                          这个是我们国产的吗？
                                          纽强自营-豁达 2025-08-05 23:32:53
                                          是国产品牌的哈
                                          jd_71e2243fb99e1 2025-08-05 23:33:45
                                          c成分这些都安全的吧？
                                          纽强自营-豁达 2025-08-05 23:33:59
                                          产品成分都是温和不刺激的呢
                                          jd_71e2243fb99e1 2025-08-05 23:34:15
                                          谢谢
                                          纽强自营-豁达 2025-08-05 23:34:24
                                          您太客气了哈
                                          /*****************会话结束_时间:2025-08-05 23:44:16******************************/	]
                  [ /*****************以下为一通会话************************************/
                                          wdngfvywzceuzua 2025-08-05 23:21:29
                                          https://item.jd.com/100146515514.html?sdx=ehi-lLxFuZiE6JnIYIdaiccltTGVRHtmwmtNsqlGY9WPPe_RLJhY4nzip0DqVm6Y
                                          纽强自营-快乐 2025-08-05 23:21:29
                                          ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨
                                          wdngfvywzceuzua 2025-08-05 23:21:34
                                          你好
                                          纽强自营-快乐 2025-08-05 23:21:36
                                          您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！
                                          wdngfvywzceuzua 2025-08-05 23:21:51
                                          请问这个可以当面霜用吗
                                          纽强自营-快乐 2025-08-05 23:21:57
                                          可以的呢
                                          纽强自营-快乐 2025-08-05 23:21:58
                                          正常情况下纽强产品可以涂抹全身哦，包括眼眶周和口眶周的呢
                                          wdngfvywzceuzua 2025-08-05 23:22:59
                                          可以每天涂脸和身体当日常保湿吧？
                                          纽强自营-快乐 2025-08-05 23:23:07
                                          可以的呢
                                          wdngfvywzceuzua 2025-08-05 23:23:11
                                          好的
                                          纽强自营-快乐 2025-08-05 23:23:17
                                          纽强产品不含酸类、激素、矿物质油、香料香精和致敏防腐剂，保湿、温和、适用于敏感肌肤，可作为日常护肤品长期使用的呢
                                          /*****************会话结束_时间:2025-08-05 23:33:16******************************/]
                
                                          [/*****************以下为一通会话************************************/
                                                           jd_745217cbf355b 2025-08-05 23:16:09
                                                           可以涂脸吗宝宝
                                                           纽强自营-希望 2025-08-05 23:16:09
                                                           ✨纽强冰冰霜全网开售！晒后舒缓，冰凉水润！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨
                                                           纽强自营-希望 2025-08-05 23:16:14
                                                           您好，欢迎光临纽强护理之家，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！
                                                           纽强自营-希望 2025-08-05 23:16:17
                                                           产品都是可以全身涂抹的哈，脸部也是可以的呢
                                                           jd_745217cbf355b 2025-08-05 23:16:33
                                                           可以脸部也涂是吧
                                                           jd_745217cbf355b 2025-08-05 23:16:34
                                                           OK
                                                           纽强自营-希望 2025-08-05 23:16:44
                                                           麻烦善良帅气美丽温柔可爱大方的您给我的服务做出一个很满意的点赞赞好嘛~~~~
                                                           直接点第5颗星星就好啦~~
                                                           这样我的盒饭里就可以加一个鸡腿啦~
                                                           纽强自营-希望 2025-08-05 23:16:44
                                                           https://dd-static.jd.com/ddimg/jfs/t1/317351/38/20936/26257/6891cbd8F10d02b88/b76eb6781755ff12.jpg
                                                           /*****************会话结束_时间:2025-08-05 23:26:35******************************/]
                  ###
                  ```
                
                #### 输出要求
                1. **严格JSON数组格式**（含3个对象）
                2. 每个对象包含字段：
                   ```json
                   {
                     "typeCode": "分类代码",  // 0/1/2/3
                     "typeName": "分类名称",  // 售前/售中/售后/其他
                     "content": "完整会话内容"
                   }
                   ```
                3. **内容格式要求**：
                   - 会话轮次 **不得少于** 原始记录
                   - 输出json中的`content`字段内换行符必须使用 `\n`来代替
                   - 保留所有原始元素：时间戳、用户ID、客服ID、URL链接、特殊符号(✨)
                   - 完全复现原始会话的对话节奏和话术风格
                   - 不要包含开始结束标记，如以下为一通会话，会话结束_时间
                
                #### 分类标准
                | typeCode | typeName | 判断标准 |
                |----------|----------|----------|
                | 0 | 售前 | 产品咨询/推荐/试用装询问 |
                | 1 | 售中 | 订单/物流/支付问题 |
                | 2 | 售后 | 质量问题/退换货/配件补发 |
                | 3 | 其他 | 无法归类到以上三类的会话 |
                
                #### ⚠ 重要注意事项
                1. **严格禁止简化会话轮次**，违例示例：
                   ```json
                   {
                     "typeCode": "0",
                     "typeName": "售前",
                     "content": "jd_abc123 2025-08-06 10:30:00\n好的谢谢\n纽强自营-诚恳 2025-08-06 10:30:05\n麻烦点赞~~"
                   }
                   ```
                   **错误原因**：原始会话通常含6+轮对话，此示例过度简化
                
                2. **换行符处理规范**：
                   ```json
                   // 正确示例
                   "content": "用户 2025-08-05 12:00:00\n第一句话\n客服 2025-08-05 12:00:05\n第二句话"
                
                   // 错误示例
                   "content": "用户 2025-08-05 12:00:00<br/>第一句话"
                   ```
                
                #### 输出示例
                [
                  {
                    "typeCode": "2",
                    "typeName": "售后",
                    "content": "jd_57edeb04e6321 2025-08-05 22:11:34\n我买的油压不出来...\n纽强自营-希望 2025-08-05 22:11:53\n麻烦您拍摄视频"
                  },
                  {
                    "typeCode": "0",
                    "typeName": "售前",
                    "content": "jd_5b04e194a8bb2 2025-08-05 22:11:47\n你好夏天哪款好？\n纽强自营-豁达 2025-08-05 22:12:17\nhttps://item.jd.com/xxx"
                  }
                ]
                
                #### 当前任务
                请分析以下聊天记录并生成3条符合要求的会话：
                ```
               
                
                """;



        String userMessage= TestApplyChatTpl.readChatLogFile();

        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemMessage_myself).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
                /*,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()*/
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

//        Thread.sleep(1000*20);

        // shutdown service
        service.shutdownExecutor();
    }


    /**
     * 测试意图识别
 }
     * @throws InterruptedException
     */
    @Test
    public void testIntentReco() throws InterruptedException {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();


        String systemMessage_N= """
                ```markdown
                 ### 系统提示词（优化版）
                 请根据以下规则评估客户提问与客服回应的意图匹配度：
                 1. **核心原则**：评估客服是否准确识别客户核心意图并给出恰当回应，而非字面匹配
                 2. **特殊场景规则**：
                    - 若客户提问为模糊开场白（如"在吗？"），客服使用标准问候语（如"您好，有什么可以帮您？"）并引导说明需求，视为**完美匹配**
                    - 若客户有明确诉求（如订单查询），客服需直接回应核心问题
                 3. **评分维度**（综合三项得出总分）：
                    | 维度                | 权重 | 说明                                                                 |
                    |---------------------|------|----------------------------------------------------------------------|
                    | 意图识别准确性      | 40%  | 客服是否识别真实意图（如"在吗？"=开启对话）                          |
                    | 回应充分性          | 40%  | 回应是否满足意图（标准问候语满足开场意图）                            |
                    | 信息有效性          | 20%  | 是否提供有效引导/解决方案（如引导说明需求）                           |
                 4. **评分基准**：
                    - 90-100分：完美满足三项维度（如标准问候回应开场白）
                    - 70-89分：基本满足意图但有小瑕疵
                    - <70分：未识别核心意图或严重偏离
                 
                 **处理流程**：
                 1. 解析客户意图 <<intentR>>：{{clientProblem}}
                    - 开场问候类意图标记为 [开场意图]
                 2. 解析客服意图 <<intentS>>：{{srvAnswer}}
                 3. 执行特殊场景规则检测
                 4. 按三维度评分并计算总分
                 
                 **输出要求**：
                 {
                   "score": [0-100],
                   "result": ["匹配"/"不匹配"],
                   "dimension_scores": {
                     "intent_identification": [0-100],
                     "response_adequacy": [0-100],
                     "information_effectiveness": [0-100]
                   },
                   "reason": "20字内评分依据",
                   "client_problem":{{clientProblem}},
                   "srvAnswer":{{srvAnswer}}
                 }
                 
                 **示例对照**（内置到系统）：
                 [客户] 在吗？ → [客服] 您好，有什么可以帮您？
                 → 输出：{
                   "score": 95,
                   "result": "匹配",
                   "dimension_scores": {"intent_identification":100, "response_adequacy":100, "information_effectiveness":90},
                   "reason": "标准问候完美匹配开场意图",
                   "client_problem":"在吗",
                   "srvAnswer":"[客服] 您好，有什么可以帮您？"
                 }
                 ```
                 
                
                """;

        /*systemMessage_N = systemMessage_N.replace("{{clientProblem}}", "请问客服在不在，转人工");
        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "您好，有什么可以帮您？");*/


        /*systemMessage_N = systemMessage_N.replace("{{clientProblem}}", "请问这个小米手环有哪些颜色");
        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "有青色、蓝色");*/

        systemMessage_N = systemMessage_N.replace("{{clientProblem}}", "这个小米（MI）手环10NFC版 新品 运动手环 智能手环有哪些颜色可选");
//        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "你这个傻屌");
        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "今天天气不错");


//        System.out.println("系统提示词：" + systemMessage_N);

        String userMessage= "";

        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemMessage_N).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
                /*,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()*/
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .temperature(0.3)
                .topP(0.9)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

//        Thread.sleep(1000*20);

        // shutdown service
        service.shutdownExecutor();
    }


    @Test
    public void test2nd_to_3rd() throws InterruptedException {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();


        String systemMessage_N= """
                ```markdown
                     # 电商聊天记录修改专家
                     
                     ## 核心任务
                     1. **替换买家身份** → 将原始聊天记录<<chatLogPart>>中的买家身份替换为 `targetRolePart`
                     2. **替换商品信息** → 将原始聊天记录<<chatLogPart>>中的商品信息替换为 `targetProductTitlePart`
                     3. **更新商品知识** → 将原始聊天记录<<chatLogPart>>中商品知识内容替换为 `targetProKnowledgePart`
                     4. **保持原始风格** → 严格保留原始聊天记录<<chatLogPart>>的表达风格（语气/句式/用词习惯）
                     
                     ## 输入数据
                     ### 原始聊天记录<<chatLogPart>>
                     ```
                     jd_8888888888888 2025-09-02 14:30:00
                     https://item.jd.com/10160508281188.html?sdx=ehi-lLxFuZiE6JnJZ4NZjM4iuDGQAAMrsmlNsKZFZdqPPe_RLJhZ5nXjokjhUGWX
                     小米手机通讯扁桃仁 2025-09-02 14:30:05
                     在的
                     jd_8888888888888 2025-09-02 14:30:10
                     这个手机有什么功能啊
                     jd_8888888888888 2025-09-02 14:30:15
                     能拍照很清晰吗
                     小米手机通讯扁桃仁 2025-09-02 14:30:20
                     这款手机拍照功能很强大的，有高清摄像头，能拍出很清晰的照片哦
                     jd_8888888888888 2025-09-02 14:30:25
                     还有其他什么特别的功能不
                     小米手机通讯扁桃仁 2025-09-02 14:30:30
                     它还支持快充功能，充电速度很快的，另外还有智能语音助手哦
                     jd_8888888888888 2025-09-02 14:30:35
                     可插卡吗
                     小米手机通讯扁桃仁 2025-09-02 14:30:40
                     可以插卡的，支持双卡双待呢
                     jd_8888888888888 2025-09-02 14:30:45
                     那它的续航能力怎么样
                     小米手机通讯扁桃仁 2025-09-02 14:30:50
                     电池容量比较大，正常使用一天是没问题的，如果使用频繁的话，配合快充就很方便啦
                     
                     ```
                     
                     ### 目标商品知识库<<targetProKnowledgePart>>
                     ```
                     ```markdown
                     ## 请识别并描述这张图片的内容，转换为Markdown格式。
                     
                     在正常使用状态下可防水、防尘。在受控实验室条件下，经测试、其效果在GB/T4208-2017（国内）标准下达到IP68级别。通过TUV南德2米24小时防水测试，测试条件为：(1)无流动清水，水深2.0米；(2)试验时间24小时；(3)水温与产品温差不大于5℃。本系列产品非防水手机，请勿在潮湿状态下为手机充电、生活场景与实验环境存在差异、日常使用请勿将手机浸入水中，或接触海水等溶液或饮品等液体。淋雨/浸水/溅水后，液体可能堵住麦克、听筒、扬声器的出音/收音管道，导致通话、外放时听/收音异常，甩干后静置晾干可恢复功能。防水、防尘功能可能会因日常使用损耗而下降。
                     ```,## 包装清单
                     手机主机 | 电源适配器 | USB Type-C 数据线 | 手机保护壳 | 屏幕保护膜（出厂贴在手机上） | 插针 | 说明书（含三包凭证）
                     
                     | 物品       | 描述         |
                     |------------|--------------|
                     | 包装盒     | Redmi Note 14 Pro 包装盒 |
                     | 手机主机   | Redmi Note 14 Pro 手机 |
                     | 手机保护壳 | 配套手机保护壳 |
                     | 说明书     | 含三包凭证 |
                     | 插针       | 用于取卡 |
                     | 电源适配器 | 45W 电源适配器 |
                     | USB Type-C 数据线 | 配套数据线 |
                     
                     *此清单仅针对整机包装进行说明，根据销售套餐不同，整机包装之外所提供的配件套装略有不同，以购买时用户选择为准。,```markdown
                     ## 天玑 7300-Ultra
                     采用台积电 4nm 旗舰制程工艺，搭载 2.5GHZ 高频大核，性能进一步提升的同时，功耗更低、续航更长。轻松满足日常生活中拍照、语音、视频的流畅体验，手机耐用又耐玩。
                     
                     | 属性       | 值             |
                     |------------|----------------|
                     | 制程工艺   | 台积电 4nm     |
                     | GPU 提升   | 约 +35%        |
                     | CPU 主频   | 2.5 GHz        |
                     | GPU 功耗降低 | 约 -46%        |
                     
                     *CPU、GPU 测试结果来源小米实验室，基于天玑 7300-Ultra 对比天玑 7050 平台测得，实际情况会因具体测试环境、具体软件版本不同而略有差异，以实际情况为准。*
                     ```,```markdown
                     ## 三重TÜV莱茵认证 低亮度 细腻调光，柔和到无感
                     
                     | 属性               | 值                           |
                     |--------------------|------------------------------|
                     | 调光策略           | 1920 Hz | DC                         |
                     | 自动亮度细腻调节   | 20000 级                     |
                     | TÜV莱茵认证       | TÜV莱茵硬件级低蓝光认证、TÜV莱茵节律友好认证、TÜV莱茵无频闪认证 |
                     | S++认证说明       | S++认证通过中国质量认证中心认证，测评根据中国信通院和中山大学中山眼科中心制定的 CQC/PV120011-2023 标准，本产品非医疗器械产品，不具备治疗功能。 |
                     | 认证证书编号       | 莱茵硬件级低蓝光认证证书编号：Q 50645276；莱茵无频闪认证证书编号：Q 50645281；莱茵节律友好认证证书编号：Q 50645035。 |
                     | 调光说明           | 80nits 以下为 1920Hz 高频 PWM 调光，80nits 以上为 DC 调光。无感用以形容调光过程细腻流畅，可能会因个人使用感受存在差异。 |
                     ```,| 属性 | 值 |
                     | ---- | ---- |
                     | 屏幕尺寸 | 6.67" |
                     | 像素排列 | Pixel 类钻排列 |
                     | 像素密度 | 446PPI |
                     | 亮度 | 3000nits |
                     | 屏幕色深 | 12bit 更高屏幕色深，687 亿色彩显示 |
                     | 色域 | P3 广色域 |
                     | 屏幕刷新率 | 120Hz 屏幕至高刷新率可达 |
                     | 瞬时触控采样率 | 2560Hz |
                     | 认证 | Dolby VISION，HDR10+，ZREAL 帧享超高清认证 |
                     
                     * 部分应用至高支持 120Hz 屏幕刷新率，不同应用界面或游戏画面下，屏幕刷新率可能存在差异，以实际使用体验为准。
                     * 2560Hz 瞬时采样率仅在部分游戏中生效，不同软件版本间存在差异，具体请以实际系统支持为准。,## 旗舰级 1.5K 高光屏 超清高亮 不负高光时刻
                     
                     屏幕发光材料，让亮度达到3000nits，至高可达 120Hz 刷新率，2560Hz瞬时触控采样率，不仅画面质感细腻，游戏操控更流畅，丰富绚丽的色彩，太阳下依旧清晰可见。,```markdown
                     ## OIS + EIS 双重防抖 追拍快拍，稳定成像
                     
                     OIS 与 EIS 双重防抖技术，确保稳定成像。不管是日常抓拍，还是夜景捕捉，画面更明亮、细节更清晰。
                     ```,```markdown
                     ## Sony's LYT-600大光圈超感相机
                     
                     | 属性       | 值                                       |
                     |------------|------------------------------------------|
                     | 主摄       | 50MP，f/1.5大光圈，1.96"大底，1.6μm 4in1大像素，OIS光学防抖 |
                     | 超广角     | 8MP                                      |
                     | 微距       | 2MP                                      |
                     ```,```markdown
                     ## 居中DECO光织矩阵
                     
                     ### 外观更旗舰
                     
                     | 属性       | 值             |
                     |------------|----------------|
                     | 设计特点   | 居中对称，温润优雅 |
                     | 机身曲线   | 前后双曲，黄金曲率 |
                     | 设计风格   | 圆润DECO，秩序美学 |
                     ```,```markdown
                     ## 无标题
                     
                     | 属性       | 值              |
                     |------------|-----------------|
                     | 电池容量   | 5500mAh         |
                     | 快充       | 45W             |
                     | 电池认证   | TÜV南德长寿命电池认证，4年耐用 |
                     | 技术       | 激活潜藏锂离子，小米海星算法；智慧充电引擎2.0，多场景充电效率优化 |
                     
                     *小米实验室使用电池单体，在25℃标准环境下，按照一天一次满充满放进行测试，约等于使用4年后剩余容量≥80%，可能因环境、与软件不同有所差异。*
                     *产品通过TÜV南德长寿命认证，认证编号ZZGCN 099551 0361，电池单体在实验室标准环境下1800次循环后电池容量≥80%。*
                     *电池容量典型值为5500mAh，电池容量额定值为5400mAh。*
                     ```,```markdown
                     ## 旗舰体验
                     
                     ### 续航更旗舰
                     
                     5500mAh \s
                     固态电解质电池 \s
                     四年超长寿命
                     
                     搭载 5500mAh 大容量电池，尽可畅享超长续航，配合 45W 快充，从早到晚，无忧畅玩一整天。在小米海星算法助力下，电池健康度提升，使用四年，依旧长续航。
                     ```,```markdown
                     ## 买小米手机领国家补贴至高优惠500元
                     
                     - ⭐ **促销信息**：
                       - 国家补贴，至高优惠500元
                       - 限量领取
                     
                     | 属性       | 值                                                          |
                     |------------|-------------------------------------------------------------|
                     | 目前开放地区 | 上海、重庆、山西、辽宁、吉林、黑龙江、内蒙古、江苏、山东、安徽、浙江、福建、湖北、湖南、广东、广西、江西、四川、海南、云南、陕西、河北、宁夏 |
                     
                     **注意事项**：国家补贴不支持价保，限部分机型部分版本可用，具体以下单为准。
                     ```,```markdown
                     ## 更防水
                     
                     IP68 防尘防水
                     通过 TÜV 南德
                     2 米 24 小时防水测试
                     
                     **意外泡水 捞起也能用**
                     
                     防水能力再精进，新升级 IP68 防尘防水，不仅实现多场景防水，更通过 TÜV 南德 2 米 24 小时防水测试。轻松面对日常洒溅，遭遇浸泡或意外落水，也能从容以对。
                     ```,```markdown
                     ## 通过 1.8 米高强跌落测试 TÜV 南德整机五星品质认证
                     
                     数十万次品质测试，成就了新的金刚品质。手机还通过 1.8 米大理石地面正面跌落测试等，多种环境的严苛考验。千锤百炼，只为经得起真实考验。
                     
                     TÜV 南德整机五星品质认证
                     
                     * 通过 1.8 米超高强度跌落测试，指通过小米实验室 1.8m 大理石正面跌落测试，数据来自于小米实验室，可能因为测试环境不同等原因，造成测试结果不同。手机作为精密电子设备，跌落、撞击等存在损坏风险，使用时请注意尽量避免跌落、撞击、划伤等。
                     * 产品通过 TÜV 南德整机五星品质认证，证书编号 Z2GCN 099551 0360。
                     ```,```markdown
                     请识别并描述这张图片的内容，转换为Markdown格式。
                     
                     *正面、背面及屏幕防护能力提升数据为对比康宁 GG5 玻璃得出，数据来源于小米实验室，实际情况会因测试环境、测试条件等不同略有差异。*
                     *手机属于精密电子设备，跌落、撞击存在损坏风险，使用时请注意避免跌落、撞击、划伤等。*
                     ```,```markdown
                     ## 金刚品质
                     
                     ### 更抗摔
                     Redmi 金刚架构
                     抗摔性能更提升
                     
                     可靠的 Redmi 金刚架构再进化，正面采用康宁®大猩猩®玻璃 Victus®2，内外加固，整机抗摔能力更提升。日常防刮防蹭，无惧意外跌落。
                     ```,## 5000万像素索尼大光圈超感相机
                     
                     | 属性       | 值                                                         |
                     |------------|------------------------------------------------------------|
                     | 传感器     | Sony's LYT-600                                             |
                     | 光圈       | f/1.5                                                      |
                     | 屏幕       | 1.5K 高光屏，莱茵三重认证，3000nits 亮度                   |
                     | 芯片       | 天玑 7300-Ultra，4nm 台积电旗舰工艺                       |
                     | 外观       | 前后双曲，居中DECO，光织矩阵，舒适触感                      |
                     
                     ### 核心卖点
                     - **5000万像素 Sony's LYT-600 传感器**，f/1.5大光圈
                     - **1.5K 高光屏**，莱茵三重认证，3000nits 亮度
                     - **天玑 7300-Ultra** 芯片，4nm 台积电旗舰工艺
                     - **前后双曲**设计，居中DECO，光织矩阵，舒适触感
                     
                     ### 产品说明
                     *在正常使用状态下可防水、防尘。在受控实验室条件下，经测试、其效果在 GB/T4208-2017 (国内) 标准下达到 IP68 级别。通过 TÜV 南德 2 米 24 小时防水测试，测试条件为：(1)无流动清水，水深 2.0 米；(2)试验时间 24 小时；(3)水温与产品温差不大于 5℃。本系列产品非防水手机，请勿在潮湿状态下为手机充电、生活场景与实验环境存在差异，日常使用请勿将手机浸入水中，或接触海水等溶液或饮品等液体。淋雨/浸水/溅水后，液体可能堵住麦克、听筒、扬声器的出音/收音管道，导致通话、外放时听/收音异常，甩干后静置晾干可恢复功能。防水、防尘功能可能会因日常使用损耗而下降。*
                     
                     *小米实验室使用电池单体，在 25℃ 标准环境下，按照一天一次满充满放进行测试，约等于使用 4 年后剩余容量 ≥80%，可能因环境、与软件不同有所差异。*
                     
                     *产品通过 TÜV 南德长寿命电池认证，认证编号 Z2GCN 099551 0361，电池单体在实验室标准环境下 1800 次循环后电池容量 ≥80%。*
                     
                     *更抗摔、更防水、更长续航及品质大换代、抗摔新高度、防水新高度、续航新革命等措辞，均为对比上代 Redmi Note 系列产品得出。数据来源于小米实验室，实际情况会因测试环境、测试条件等不同略有差异。*,```markdown
                     ## 新一代小金刚 开启品质大换代
                     
                     ### Redmi 金刚架构 抗摔性能更提升
                     康宁®大猩猩®玻璃 Victus®2 | TÜV 南德整机五星品质认证
                     
                     ### IP68 防尘防水*
                     通过 TÜV 南德 2 米 24 小时防水测试
                     
                     ### 5500mAh 固态电解质电池
                     4 年耐用 | TÜV 南德长寿命电池认证
                     ```,```markdown
                     ## Note 14 Pro \s
                     | 属性       | 值         |
                     |------------|------------|
                     | 特点       | 更抗摔，更防水，更长续航 |
                     
                     https://img.alicdn.com/xxx_main.jpg (此处假设图片链接，实际需根据真实情况替换)
                     ```,```markdown
                     ## 国家补贴云闪付支付流程
                     
                     | 步骤 | 内容                                                         |
                     |------|--------------------------------------------------------------|
                     | 1    | 小米15 骁龙8至尊版 5G，光学高清镜头，功能超级光解，超声波指纹，国家补贴15%，至高补贴500元，券后价¥3999，原价¥4499.00 |
                     | 2    | 换卡支付，最高可减18元。支付页面选择云闪付方式支付，微信支付，云闪付（有机会享国家补贴） |
                     | 3    | 订单信息：京东商城平台商户，优惠信息：消费品补贴 - ¥500.00，跳转云闪付优惠价支付 |
                     
                     **¥3999.00** \s
                     ~~原价¥4499.00~~ \s
                     
                     - ⭐ **补贴力度**：部分地区国家补贴15%，至高补贴500元
                     - ⭐ **领用范围**：是否可使用云闪付支付优惠以具体地区和实际支付页面为准，国补具体上线城市及活动规则可点击国补会场查看或咨询客服了解详情
                     ```,### 国家补贴领取流程
                     
                     **¥4799** \s
                     ~~原价无相关信息~~ \s
                     
                     | 属性 | 值 |
                     | ---- | ---- |
                     | 已售 | 2000 |
                     | 颜色 | 黑色、白色、丁香紫等5款可选 |
                     | 型号 | 小米15 新品5G小米手机 小米15 12G+512G 【官方标配国家补贴】 |
                     | 物流 | 京东快递 |
                     
                     - 🎁 **赠品**：无相关信息
                     - ⚖️ **补贴**：国家补贴15%，至高补贴500元，政府补贴单本可再减15%，PLUS额外省40元，最高返259京豆
                     
                     > 🔵 **【立即购买】**
                     > 🔵 **【立即认证并领取】**
                     > 🔵 **【立即领取】**
                     
                     步骤一：商品详情页主图腰带及主图底部的补贴领取入口可一键领取
                     
                     步骤二：领取国家补贴，点击立即认证并领取，若领取过其他地区或在其他渠道领取过补贴，点击一键换绑
                     
                     补贴可用地区：安徽六安市、阜阳市、合肥市、淮南市... 展开
                     
                     已收货地址为您自动匹配“上海手机”补贴
                     
                     当前地址：江苏 不可用
                     当前地址：上海 可用
                     
                     |类别|详情|类别|详情|
                     | ---- | ---- | ---- | ---- |
                     |品牌|小米（MI）|商品编号|10147019721214|
                     |CPU型号|天玑7300 - Ultra|机身颜色|红色|
                     |特征特质|NFC，红外遥控|屏幕分辨率|1.5K|
                     |风格|炫彩，轻奢，科技|屏幕材质|OLED曲面屏|
                     |后摄主像素|5000万像素|三防标准|IP68|
                     |机型|小米 Redmi Note14 Pro|入网型号|24090RA29C|
                     |上市日期|2024 - 09 - 26|国补备案型号|Redmi Note 14 Pro|
                     |机身颜色|红色|机身重量|190g|
                     |机身内存|512GB|存储卡|不支持|
                     |运行内存|12GB|屏幕刷新率|120Hz|
                     |屏幕特色|SGS认证|屏幕尺寸|6.67英寸|
                     |无线充电|以官网信息为准|充电功率|45W|
                     |系统|小米澎湃os|5G网络|支持5G|
                     |4G网络|4G FDD - LTE；4G TD - LTE|SIM卡数量|2个|
                     |充电接口|Type - C|生物识别|屏幕指纹|
                     |拍照特色|光学防抖|前置主像素|2000万像素|
                     ```
                     
                     ### 目标买家身份<<targetRolePart>>
                     ```
                     买家是一位爱赶时髦的大叔，喜欢体验新鲜、好玩的电子产品
                     ```
                     
                     ### 目标商品标题<<targetProductTitlePart>>
                     ```
                     小米（MI）Redmi 红米note14pro 国家补贴 新品5G小米红米手机
                     ```
                     
                     ## 输出要求
                     ✅ **直接输出修改后的完整聊天记录**（不包含任何额外标志或说明文字） \s
                     ✅ 保持原始对话逻辑和时间线 \s
                     ✅ 将指定身份/商品/知识库自然融入对话 \s
                     ❌ 禁止修改时间戳、售后流程等非指定内容 \s
                     ❌ 禁止添加任何标记（如【生成 **全新聊天记录**】） \s
                     
                     ## 输出格式示例
                     ```
                     一位年轻时尚的小伙子 2025-08-06 10:10:10
                     [消息内容]
                     
                     小米自营-热情 2025-08-06 10:10:10
                     [消息内容]
                     
                     [身份] [时间]
                     [消息内容]
                     ...
                     ```
                     （严格遵循此纯文本格式，包含身份+时间戳+消息内容的三段式结构）
                     ```
                     
                 
                
                """;

        /*systemMessage_N = systemMessage_N.replace("{{clientProblem}}", "请问客服在不在，转人工");
        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "您好，有什么可以帮您？");*/


        /*systemMessage_N = systemMessage_N.replace("{{clientProblem}}", "请问这个小米手环有哪些颜色");
        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "有青色、蓝色");*/

//        systemMessage_N = systemMessage_N.replace("{{clientProblem}}", "这个小米（MI）手环10NFC版 新品 运动手环 智能手环有哪些颜色可选");
//        systemMessage_N = systemMessage_N.replace("{{srvAnswer}}", "今天天气不错");


//        System.out.println("系统提示词：" + systemMessage_N);

        String userMessage= "";

        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemMessage_N).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
                /*,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()*/
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .temperature(0.3)
                .topP(0.9)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

//        Thread.sleep(1000*20);

        // shutdown service
        service.shutdownExecutor();
    }

    @Test
    public void testGetCustomerName() throws InterruptedException {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();


        String systemMessage_N= """
                ### **修改后的系统提示词 (System Prompt)**
                        
                        **角色 (Role):** \s
                        你是一个专业的对话记录分析AI，专门用于在电商或客服聊天场景中，根据名称标识准确识别客户（Customer）。
                        
                        **任务 (Task):** \s
                        当用户提供一个用“/”分隔的名称字符串时，你必须先解析字符串提取所有名称，然后严格遵循分析逻辑，找出哪个名称最可能是客户名称，并按要求格式返回结果。
                        
                        **分析步骤与逻辑 (Steps & Logic):**
                        1. **解析输入：** 将输入字符串按“/”分割成多个名称，去除首尾空格，得到一个名称列表。
                        2. **特征识别：** 分析每个名称的语义特征。
                            - **客户 (Customer) 特征:** 名称通常具有**个人化、描述性、非正式**。例如：网名（如“清风徐来”）、个人特征描述（如“一位年轻的小伙子”）、或个人兴趣标签（如“喜欢时尚”）。其核心目的是个性化表达。
                            - **客服服务方 (Servicer) 特征:** 名称通常具有**商业化、官方性、品牌属性**。例如：包含品牌名（如“小米”）、公司名、店铺名、产品描述（如“红米手机”）、或职责身份（如“客服”）。其核心目的是商业识别。
                        3. **比较判断：** 对比所有名称，找到最符合客户特征的名称（即最个人化、描述性的名称）。忽略明显是客服服务的名称。
                        4. **输出准备：** 只输出最可能的客户名称字符串。
                        
                        **输出格式 (Output Format):** \s
                        你必须仅返回一个**纯净的、无任何额外注释的JSON对象**，包含识别出的客户名称。格式必须严格遵循： \s
                        `{"customerName": "提取的客户名称字符串"}`
                        
                        **要求 (Requirements):**
                        - 禁止添加任何额外的解释、开场白或结束语。
                        - 确保JSON格式正确且可被解析。
                        - 直接输出分析后的结果。
                        
                        **示例 (Example):**
                        - 输入: "一位爱赶时髦的大叔/小米（MI）Redmi 红米note14pro 国家补贴 新品5G小米红米手机/小米手机通讯扁桃仁"
                        - 输出: {"customerName": "一位爱赶时髦的大叔"}
                        
                        **用户输入的名称字符串如下:**
                        * {{input_string}}
                        
                        ***
                        
                        基于以上提示词，对输入字符串进行分析：
                        
                        
                        ***
                        
                        
                     
                
                
                """;


        systemMessage_N = systemMessage_N.replace("{{input_string}}", "一位爱赶时髦的大叔/小米（MI）Redmi 红米note14pro 国家补贴 新品5G小米红米手机/小米手机通讯扁桃仁");
//        systemMessage_N = systemMessage_N.replace("{{customer}}", "一位年轻的小伙子，喜欢时尚");



        String userMessage= "";

        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemMessage_N).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));


        // shutdown service
        service.shutdownExecutor();
    }

    @Test
    public void testYibu() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String prompt = """
                你是一个电商内容处理专家。请处理以下商品详情内容：
                    1. 移除所有无关的广告和推广内容
                    2. 保留所有产品规格、参数和功能描述
                    3. 保留所有图片和重要表格
                    4. 精简重复内容
                    5. 保持HTML结构完整
                                
                """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamUserMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(20000);
        // shutdown service
        service.shutdownExecutor();

    }


    @Test
    public void testHtmlToMd() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String prompt = """
                # 系统角色与任务指令
                                   
                                   **你是一位专业的 Markdown 格式化专家。** 你的核心任务是将用户输入的、结构杂乱的原始文本，精确、高效、美观地转换为标准、易读的 Markdown 格式。
                                   
                                   ## 核心处理规则
                                   
                                   1.  **严格保留原始信息：** 绝对**不能**添加、删除或修改原始文本中的任何信息（包括错别字、语法错误、数据、标点符号）。只添加必要的 Markdown 语法符号进行结构化。
                                   2.  **智能识别结构：**
                                       *   **标题：** 识别明显的标题行（如行首/尾有空格、特殊符号、字号/字体变化、加粗、下划线等视觉线索，或文本本身具有标题特征）。使用 `#` (H1) 到 `######` (H6) 表示标题层级。**优先使用 H2 (`##`) 作为顶级标题**，除非有明确更强的标题线索（如报告大标题）。谨慎判断层级。
                                       *   **列表：**
                                           *   **无序列表：** 识别行首的 `-`, `*`, `•`, `·`, `▪` 等符号或明显的项目符号段落。统一转换为 `- `。
                                           *   **有序列表：** 识别行首的数字（如 `1.`, `2)`, `(3)` 等）或字母序列。统一转换为 `1. `, `2. ` 等格式。**确保列表项缩进一致**。
                                           *   **嵌套列表：** 识别缩进或次级符号，使用空格缩进表示嵌套层级（如 ` - ` 表示二级列表）。
                                       *   **代码块：** 识别包裹在引号、方框、特殊背景色中的代码片段，或包含明显代码关键字、格式的文本块。使用 \\`\\`\\` 或 \\`\\`\\`language （如能识别语言）包裹。行内代码用反引号 \\` 包裹。
                                       *   **引用块：** 识别行首的 `>`, `"`, `「` 等符号，或明显引用、注释性的段落。使用 `> ` 表示。
                                       *   **链接：** 识别包含 `http://`, `https://`, `www.` 的 URL，将其转换为 `[描述](URL)` 格式。**如果原始文本没有提供链接描述，则使用 URL 本身作为描述**（即 `[URL]`）。
                                       *   **图片：** 识别常见的图片 URL 结尾（`.jpg`, `.png`, `.gif`, `.svg` 等）或包含 `![` 的文本片段。转换为 `![替代文本](图片URL)`。**如果原始文本没有替代文本，使用空字符串 `""` 或一个空格 `" "`**。
                                       *   **加粗/斜体：** 识别被 `**`、`__`、`<b>` 包裹或字体明显加粗的文本，转换为 `**加粗**`。识别被 `*`、`_`、`<i>` 包裹或字体明显倾斜的文本，转换为 `*斜体*`。**优先使用 `**` 和 `*`**。
                                       *   **表格：** 识别对齐的列（通过空格、制表符或 `|` 分隔）。尝试转换为 Markdown 表格。如果结构过于混乱或识别失败，**保持原样或转换为代码块**，并添加注释 `<!-- 原始表格结构复杂，已保留 -->`。
                                       *   **水平线：** 识别由连续的 `---`, `***`, `___` 组成的行。转换为 `---`。
                                       *   **换行与段落：** 明显的空行（一行或多行）表示段落分隔，转换为两个换行（一个空行）。单一行尾换行保持为一个换行（除非是列表项内）。
                                   3.  **处理歧义：**
                                       *   如果对某段文本的结构（如是否是标题、列表项层级）存在不确定性，**优先保持其作为普通段落**，避免过度结构化。
                                       *   如果原始文本格式极其混乱且规则难以应用，**确保信息完整保留**，可添加注释 `<!-- 原始格式复杂区域 -->` 并尽量按逻辑分段。
                                   4.  **输出要求：**
                                       *   输出的 **必须是且仅是** 转换后的 Markdown 文本。
                                       *   **不要**添加任何解释性文字、前缀（如“好的，转换后的 Markdown 如下：”）或后缀。
                                       *   确保生成的 Markdown 语法正确，能被标准解析器渲染。
                                   
                                   ## 示例 (仅供理解规则，非必需输出)
                                   
                                   *   **输入杂乱文本：**
                                       ```
                                       重要通知!! (加粗)
                                       会议时间： 明天下午 3点
                                       地点： 大楼 A - 201 会议室
                                       议程：
                                       - 项目A进展 (负责人: 张三)
                                         - 前端
                                         - 后端
                                       - 项目B预算讨论
                                       请访问链接：https://example.com/meeting-docs
                                       ```
                                   *   **期望 Markdown 输出：**
                                       ```
                                       ## 重要通知!!
                                       会议时间： 明天下午 3点
                                       地点： 大楼 A - 201 会议室
                                       议程：
                                       - 项目A进展 (负责人: 张三)
                                         - 前端
                                         - 后端
                                       - 项目B预算讨论
                                       请访问链接：[https://example.com/meeting-docs](https://example.com/meeting-docs)
                                       ```
                                   
                                   ## 开始执行
                                   
                                   **请严格遵循以上规则处理用户随后提供的每一段输入文本。** 用户输入可能包含任何形式的杂乱内容。你的唯一响应就是转换后的 Markdown。
                                   
                                   用户输入的内容如下：
                                
                """;

        String userPrompt = """
                商品详情 品牌 阿米洛（Varmilo） 商品编号 10083506954652 店铺 阿米洛官方旗舰店 兼容系统 Windows，MacOS 背光灯效 单光 插拔类型 不支持热插拔 连接方式 有线 类型 非客制化机械键盘 按键数 61-70键 同时连接设备 1台 颜色 拼色 型号 金属68键 键帽材质 PBT 供电方式 有线供电 键帽字符工艺 热升华 电池容量 1000-3999mAh 键盘结构 其他 包装清单 勇士翱翔机械键盘*1 彩盒*1 质量承诺 耐久性标签 吊牌 质检报告 CCC证书 实时温控 检验报告 售后保障 卖家服务 京东承诺 京东平台卖家销售并发货的商品，由平台卖家提供发票和相应的售后服务。请您放心购买！ 注：因厂家会在没有任何提前通知的情况下更改产品包装、产地或者一些附件，本司不能确保客户收到的货物与商城图片、产地、附件说明完全一致。只能确保为原厂正货！并且保证与当时市场上同样主流新品一致。若本商城没有及时更新，请大家谅解！ 正品行货 京东商城向您保证所售商品均为正品行货，京东自营商品开具机打发票或电子发票。 权利声明： 京东上的所有商品信息、客户评价、商品咨询、网友讨论等内容，是京东重要的经营资源，未经许可，禁止非法转载使用。 注：本站商品信息均来自于合作方，其真实性、准确性和合法性由信息拥有者（合作方）负责。本站不提供任何保证，并不承担任何法律责任。 预估到手价/到手价：是在商品标价基础上减去各种折扣、可用优惠政策叠加金额之后的一种预估的价格，计算公式示例：预估到手价=商品标价-优惠券-满减（或折扣）-新客优惠（如有）-商家会员优惠（如有），用户在进入结算页面之后，根据满足条件可享的活动（如所购商品超出商品促销价限购数量）、优惠券组合，可能导致最终到手价与显示预估到手价有差异和变化，并且不同用户可能因为可以享受的优惠不同（造成这种不同的原因包括是否属于新客或商家会员，可用优惠券差异、购买多件商品而满减活动仅支持享受1次等）而导致最终到手价不同，并且可能出现未登录和登录状态下的到手价差异。 京东价：京东价为商品的销售价，是您最终决定是否购买商品的依据。 划线价：商品展示的划横线价格为参考价，并非原价，该价格可能是品牌专柜标价、商品吊牌价或由品牌供应商提供的正品零售价（如厂商指导价、建议零售价等）或其他真实有依据的价格；由于地区、时间的差异性和市场行情波动，品牌专柜标价、商品吊牌价等可能会与您购物时展示的不一致，该价格仅供您参考。 
                折扣：如无特殊说明，折扣指销售商在原价、或划线价（如品牌专柜标价、商品吊牌价、厂商指导价、厂商建议零售价）等某一价格基础上计算出的优惠比例或优惠金额；如有疑问，
                您可在购买前联系销售商进行咨询。 异常问题：商品促销信息以商品详情页“促销”栏中的信息为准；商品的具体售价以订单结算页价格为准；
                如您发现活动商品售价或促销信息有异常，建议购买前先联系销售商咨询。
                                                                        
                    """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(prompt).build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(userPrompt).build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamUserMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(20000);
        // shutdown service
        service.shutdownExecutor();

    }

    @Test
    public void testRobotImitate() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                                
                ```markdown
                 ### 系统提示词：AI模拟买家 & 聊天记录复刻大师（完整修正版）
                
                 **角色定义** \s
                 你是一位专业的「AI模拟买家」兼「聊天记录复刻大师」。你的核心任务是：
                 - 严格复刻原始聊天记录chatLogPart中买家的提问结构和节奏
                 - 保持原始问题数量、连续提问句数和表达逻辑不变
                 - 现在你来模拟买家提问，用户扮演客服回答
                 - **严格复刻原始提问节奏（连续多条则连续输出）**
                
                 **交互流程规则**
                 ```mermaid
                 graph TD
                     A[分析原始节奏] --> B{连续提问次数}
                     B -->|N条连续| C[连续输出N条问题]
                     C --> D[等待客服回答]
                     D --> E[继续后续问题]
                 ```
                
                 ### 处理流程
                 1. **提取问题**：
                     - 仅提取买家消息（忽略客服回复）
                     - 保留原始顺序和连续性
                
                 2. **复刻结构**：
                
                 | 要素         | 规则                          |
                 |--------------|------------------------------|
                 | 提问节奏     | 完全复制连续提问次数          |
                 | 语言风格     | 保留口语化句式特征            |
                 | 问题类型     | 保持描述→询问→技术→解决的逻辑链 |
                
                 3. **生成输出**：
                     - **严格复刻原始节奏**：连续多条则连续输出
                     - 每组连续问题后等待客服回复
                     - 禁用任何额外文本（仅输出问题）
                
                 ### 示例
                
                 **输入聊天记录1：**
                
                
                 | 聊天买家 | 对话内容 | 时间 |
                 | ---- | ---- | ---- |
                 | 范***2 | 你好 | 2025-08-03 18:55:20 |
                 | 范***2 | 三星冰洗售后客服 | 2025-08-03 18:55:22 |
                 | 黄佳 | 【三星清凉夏日~冰爽放价】<br/><br/>✨国家补贴部分地区可领！限时优惠至高20%（至高立减2000元）[【点击这里】](https://pro.m.jd.com/mall/active/2biNptXDeXfpCALKd9GaGAZYxLQb/index.html)<br/>✨ 超薄嵌入式变频三系统家用大容量冰箱[【点击这里】](https://3.cn/2fA-4yd3?jkl=@ODPOxCWm3mlI@)<br/>✨超大容量爱宠人士必备洗烘套装[【点击这里】](https://3.cn/2fA-5edA?jkl=@ECo7Y5cy6UNm@)<br/>✨655L超大容量对开门一级能效冰箱[【点击这里】](https://3.cn/2jF-yrb1)<br/>✨超薄平嵌热销款19分钟快速护理机[【点击这里】](https://3.cn/2jFzua-9)<br/>✨直播间专属活动，数量有限，先到先得[【点击这里】](https://lives.jd.com/#/36826295?origin=3&appid=jdzb&activityKey=fans_pop_floating&id=36826295)<br/>咨询✨客服✨，领取专属优惠券 | 2025-08-03 18:55:22 |
                 | 黄佳 | 在的经 | 2025-08-03 18:55:29 |
                 | 黄佳 | 在的呢 | 2025-08-03 18:55:31 |
                 | 范***2 | 京东卡还没到账 | 2025-08-03 18:55:43 |
                 | 范***2 | 1120 | 2025-08-03 18:55:55 |
                 | 黄佳 | 这边看一下哦 | 2025-08-03 18:55:56 |
                 | 黄佳 | E卡已经给您登记过了呢，登记之日起算30个工作日内会给您安排好的哈，E卡使用时效一般有36个月的，建议您耐心等待呢 | 2025-08-03 18:56:19 |
                 | 黄佳 | 看到还在申请当中呢 | 2025-08-03 18:56:24 |
                 | 黄佳 | 辛苦再等待一下哈 | 2025-08-03 18:56:30 |
                 | 范***2 | 那是大概几号到 | 2025-08-03 18:56:49 |
                 | 黄佳 | 建议您在8.20号左右再过来咨询看看呢 | 2025-08-03 18:57:10 |
                 | 范***2 | 好的 | 2025-08-03 18:57:21 |
                 | 黄佳 | 好的呢#E - s21 | 2025-08-03 18:57:34 |
                
                 **正确输出流程1：**
                 1. 第一次输出（连续2条）： \s
                    {
                    "result": [
                    "你好",
                    "三星冰洗售后客服"
                    ]
                    }
                 2. 等待客服回复
                 3. 第二次输出（连续2条）：
                    {
                    "result": [
                    "京东卡还没到账",
                    "1120"
                    ]
                    }
                 4. 等待客服回复
                 5. 第三次输出（连续1条）：
                    {
                    "result": [
                    "那是大概几号到"
                    ]
                    }
                 6. 等待客服回复
                 7. 第四次输出（连续1条）：
                    {
                    "result": [
                    "好的"
                    ]
                    }
                
                 **错误输出示例（将扣分）：**
                 - 未保持连续节奏（如拆分成单条） ❌
                 - 添加额外说明文字 ❌
                
                 ### 当前任务
                 立即处理以下输入数据：
                 << chatLogPart >>
                
                 年轻时尚小伙 2025-08-05 23:58:12
                 https://item.jd.com/100146515514.html?sdx=ehi-lLxFuZiE6JnIYIdaiccltTGVRHtmwmtNsqlGY9WPPe_RLJhY4nzipkrkX2eT
                 小米自营-积极 2025-08-05 23:58:12
                 ✨小米手环10NFC版全网开售！时尚出众，功能强大！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨
                 小米自营-积极 2025-08-05 23:58:18
                 您好，欢迎光临小米智能生活馆，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！
                 小米自营-积极 2025-08-05 23:58:19
                 在的哦
                 年轻时尚小伙 2025-08-05 23:58:46
                 你好，我想问问小米手环10NFC版有试用装吗
                 年轻时尚小伙 2025-08-05 23:58:57
                 我平时喜欢运动，很注重时尚感
                 小米自营-积极 2025-08-05 23:59:23
                 小米手环10NFC版暂时没有试用装的链接哦
                 小米自营-积极 2025-08-05 23:59:27
                 只有正装的呢
                 年轻时尚小伙 2025-08-05 23:59:47
                 那小米手环10NFC版最小规格是怎样的
                 小米自营-积极 2025-09-01 00:00:09
                 我想了解一下您平时使用的手机系统是安卓还是iOS呢？这样我们可以更精准地确认手环和您手机的兼容性哦
                 小米自营-积极 2025-09-01 00:00:15
                 您手机系统会有什么特殊设置嘛
                 年轻时尚小伙 2025-09-01 00:02:12
                 我用的是安卓系统，版本是Android 10
                 小米自营-积极 2025-09-01 00:02:38
                 NFC版主体净重是16.25g（不含腕带），主体尺寸是46.57*22.54*10.95mm（不含心率凸台） 您可以看下这款的哈
                 小米自营-积极 2025-09-01 00:02:39
                 https://item.jd.com/100121466523.html?sdx=ehi-lLxFu5iE6JnIYIFdiMAmtTKSRHtmwmtNsqlGY9WPPe_RLJhY4nzio03qUWOU&position=search
                 小米自营-积极 2025-09-01 00:02:41
                 这款手环屏幕是1.72英寸AMOLED屏，分辨率达到212*520，支持全屏触摸操作，而且全局亮度1500nits，还支持亮度自动调节，显示超清晰，很适合追求时尚和科技感的您日常佩戴哦
                 年轻时尚小伙 2025-09-01 00:05:40
                 那它的续航能力怎么样呢？
                 小米自营-积极 2025-09-01 00:06:00
                 在典型模式下，NFC版续航能达到18天呢；如果开启AOD模式，续航大概是8天；重载模式下续航是7天。这样的续航表现，能很好地满足您日常运动和生活使用哦。不过目前只有正装哦
                 小米自营-积极 2025-09-01 00:06:16
                 暂时没有试用装的呢\s
                 引用：小米手环10NFC版暂时没有试用装的链接哦
                 年轻时尚小伙 2025-09-01 00:06:16
                 续航这方面还有其他信息嘛？
                 小米自营-积极 2025-09-01 00:06:19
                 以上就是比较详细的续航信息啦
                 年轻时尚小伙 2025-09-01 00:06:43
                 正装小米手环10NFC版发个链接给我
                 小米自营-积极 2025-09-01 00:07:10
                 https://item.jd.com/100155708001.html?sdx=ehi-lLxFu5iE6JnIYIZZi8YosDCQRHtmwmtNsqlGY9WPPe_RLJhY4nzirEvjVG6Q&position=search
                 小米自营-积极 2025-09-01 00:07:21
                 这款小米手环10NFC版很不错的，您可以看下哦
                 年轻时尚小伙 2025-09-01 00:07:35
                 这个是国产的吗？
                 小米自营-积极 2025-09-01 00:07:53
                 是国产品牌的哈
                 年轻时尚小伙 2025-09-01 00:08:45
                 成分安全吗？
                 小米自营-积极 2025-09-01 00:08:59
                 手环的材料都是安全无害的呢，它的外壳是铝合金中框 + 纤维高强聚合物，腕带是TPU材质，使用起来很放心\s
                 年轻时尚小伙 2025-09-01 00:09:15
                 谢谢
                 小米自营-积极 2025-09-01 00:09:24
                 您太客气了哈
                 ###
                 **现在开始：请输出第一个分组提问，且仅一个分组）**
                                
                """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("亲，有货的呢，请问你喜欢哪个颜色的？").build();
        streamMessages.add(streamSystemMessage);
//        streamMessages.add(streamUserMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(20000);
        // shutdown service
        service.shutdownExecutor();

    }


    @Test
    public void testRobotShangrao() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage =


        systemtemMessage= """
                
                你模拟一个电商客户，按照我给你的大纲来控制节奏:1、问小米手环的颜色。2、问小米手环的规格。3、问订单几天到货。4、问有什么售后。5、问有什么优惠。当然，这是主线，也不要死板，也要保持一个真人聊天的感觉，你的口头禅是绝绝子。
                我模拟客服，一句一句来，现在模拟开始
                """;




        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("都有蓝色、红色。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】").build()
                ,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();



    }

    @Test
    public void testFreASInShangrao() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                """;


        systemtemMessage= """
                
                你模拟一个电商客户对客服进行提问，现在客户问的是关于某个商品的问题ques？你将这个问题丰富一下，每次换不同的花样，但核心意思不能变。
                这个问题的正确答案是answ.
                客服回答后的命名为csAnsw,让answ和csAnsw进行比对，如果语义完全匹配，给100分；如果一点也不匹配，给0分；如果部分匹配，则按匹配个数/总个数给分;
                我会不停提供给你ques、answ、csAnsw的值，如下所示，然后你给我打分即可，
                比如：
                ques为:有试用吗,csAnsw为:有的哦，可以试用1个哦~,answ为:亲爱哒，若有赠送同款小样，可以试用1个哦~
                
                正装跟其他赠品不要拆封使用，若不合适，是支持退货哒~
                
                则你给的答案为：100分，完全匹配，扣分点无。
                
                
                
                """;




        System.out.println("\n----- multiple rounds request -----");

        systemtemMessage = systemtemMessage.replace("{{ques}}","有试用吗");


        String userMessage = "ques为:{{ques}},csAnsw为:{{csAnsw}},answ为:{{answ}}";

        userMessage=userMessage.replace("{{ques}}","使用方法");
        userMessage=userMessage.replace("{{csAnsw}}","使用青春胶囊");
        userMessage=userMessage.replace("{{answ}}","亲爱哒~21天密集抗皱胶囊精华使用方法如下：\n" +
                "\uD83D\uDC49使用量及频率：\n" +
                "\uD83D\uDC49基础护理：每晚1粒，每天共1粒\n" +
                " \uD83D\uDC49最佳护理：早晚各1粒，每天共2粒（脸部涂抹，请勿食用，远离儿童）\n" +
                " \uD83D\uDC49使用顺序：\n" +
                "【第1天-第7天】\n" +
                "使用青春胶囊（银色）\n" +
                "【第8天-第14天】\n" +
                "使用赋能胶囊（香槟色）\n" +
                "【第15天-第21天】\n" +
                "使用凝时胶囊（金色）\n" +
                "【21天完成后，再继续从银色循环】");

        String assistantMessage = "部分匹配，得分约14.3分，扣分点：客户询问使用方法，客服仅提及使用青春胶囊，未涵盖完整的使用量、频率、顺序等关键信息，与正确答案匹配度低。 ";

        String userMessage2 = "ques为:{{ques}},csAnsw为:{{csAnsw}},answ为:{{answ}}";
        userMessage2=userMessage2.replace("{{ques}}","金胶黑胶什么区别");
        userMessage2=userMessage2.replace("{{csAnsw}}","一个是金的，一个是黑的");
        userMessage2=userMessage2.replace("{{answ}}","亲爱的~金胶主要针对肌肤干燥的人群~改善肤色不均，修护肌肤氧化损伤造成的干燥暗沉~\n" +
                "黑胶主要针对有抗皱需求的人群~改善肌肤松弛，淡化细纹，延缓肌肤老化的哦~\n" +
                "建议金胶和黑胶一起搭配使用哦~");


        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
                ,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage2).build()
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();



    }



    @Test
    public void testFreSr1st() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                """;


        systemtemMessage= """
                
                你模拟一个电商客户对客服进行提问，现在客户问的是关于某个商品的问题(命名为ques)？你将这个问题ques丰富一下，每次换不同的花样，但核心意思不能变。
                这个问题的正确答案(命名为answ).
                客服回答(命名为csAnsw),让answ和csAnsw进行比对，如果语义完全匹配，给100分；如果一点也不匹配，给0分；如果部分匹配，则按匹配个数/总个数给分;
                然后你给客服回答csAnsw打分，打完分后提问下一题(命名为nextQues)
               
              首次输出示例:
              请问这个可以试用吗？
              
              
                
              第二次输出示例：
               部分匹配，得分约14.3分，扣分点：客户询问使用方法，客服仅提及使用青春胶囊，未涵盖完整的使用量、频率、顺序等关键信息，与正确答案匹配度低。
                [下一题]：可以试用吗？
                
              第三次输出示例3：
              部分匹配，得分约50分，扣分点：未提供清水洗净的选项，与正确答案匹部分匹配。
                 [下一题]：请问有什么优惠？
                
                第一个问题是:{{ques}},现在从头开始模拟，你先向客服发起第一个提问。
                
                """;




        System.out.println("\n----- multiple rounds request -----");

        systemtemMessage = systemtemMessage.replace("{{ques}}","如何使用?");

        String userMessage1="";

        String assistantMessage = "请问这个可以试用吗？";

        String userMessage = "csAnsw:{{csAnsw}},answ:{{answ}},nextQues:{{nextQues}}";

//        userMessage=userMessage.replace("{{ques}}","产品一共多少组");
        userMessage=userMessage.replace("{{csAnsw}}","直接涂脸上~");
        userMessage=userMessage.replace("{{answ}}","亲爱哒，若有赠送同款小样，可以试用1个哦~\n" +
                "\n" +
                "正装跟其他赠品不要拆封使用，若不合适，是支持退货哒~支持试用10粒哈");
        userMessage=userMessage.replace("{{nextQues}}","金胶黑胶什么区别");




        /*String userMessage2 = "ques为:{{ques}},csAnsw为:{{csAnsw}},answ为:{{answ}}";
        userMessage2=userMessage2.replace("{{ques}}","金胶黑胶什么区别");
        userMessage2=userMessage2.replace("{{csAnsw}}","一个是金的，一个是黑的");
        userMessage2=userMessage2.replace("{{answ}}","亲爱的~金胶主要针对肌肤干燥的人群~改善肤色不均，修护肌肤氧化损伤造成的干燥暗沉~\n" +
                "黑胶主要针对有抗皱需求的人群~改善肌肤松弛，淡化细纹，延缓肌肤老化的哦~\n" +
                "建议金胶和黑胶一起搭配使用哦~");*/


        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage1).build()
                /*,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()*/
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();



    }


    @Test
    public void testFreSr1st_CreateCtx() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                ```markdown
                # 角色与任务
                你是一名电商客服助手。你的任务是**丰富和优化**客户提出的原始问题（称为`ques`），使其更像一个真实、具体的客户咨询。

                # 优化规则
                1.  **核心不变**：必须完全保留原始问题的核心诉求和意图。
                2.  **丰富表达**：为原始问题添加合理的细节，例如：
                    *   **购买场景**：补充客户可能的使用场景或动机（如“送礼物”、“搬家购置”）。
                    *   **具体细节**：加入型号、颜色、尺寸等具体信息，或更精确的使用条件。
                    *   **情感因素**：融入客户可能存在的疑虑、期望或情绪化表达（如“很急用”、“担心不合适”）。
                3.  **自然真实**：使问题看起来像一个真实的人在提问，避免生硬和机械化的重复。
                4.  **唯一输出**：你的响应有且仅有一条优化后的问题，无需任何额外解释或标注。

                # 处理流程
                - 输入：用户的原始问题 `{{ques}}`
                - 输出：一条经过丰富和优化的、自然的新客户问题。

                # 示例
                **原始输入Ques**: “这个衣服掉色吗？”
                **优化输出**: “你好，请问这件黑色T恤第一次洗的时候会严重掉色吗？混合其他浅色衣服一起洗会不会被染脏？”

                **原始输入Ques**: “什么时候发货？”
                **优化输出**: “我刚下单了这款手机，页面显示预售。想确认一下具体的发货时间，因为这周末我要出差，希望最好能在这之前收到，谢谢！”

                **原始输入Ques**: “这个包好用吗？”
                **优化输出**: “看中了你们这款通勤双肩包，想问问电脑隔层真的能保护15寸的MacBook吗？另外背带的设计长时间背着会不会感觉勒肩膀？”
                ```
                用户的原始问题： `{{ques}}`
                
                             
                            
                                
                
                """;


        systemMessage = systemMessage.replace("{{ques}}","正装多少粒");



        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }




    static String contextId1st = "ctx-20250828171826-xpv24";
    @Test
    public void testFreSr1st_UseCtx() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();




        String assistantMessage = "这个正装到底有多少粒呀？";

        String userMessageContent = "";



        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content("").build();

//        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextId1st)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }


    @Test
    public void testFreSr2nd() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                """;


        systemtemMessage= """
                
                ## 系统提示词（只需设置一次）
                
                ```markdown
                作为电商客服质量评估专家，请严格按照以下JSON格式输出评估结果：
                
                {
                  "analysis": "匹配度分析描述",
                  "score": 分数,
                  "deductionPoints": ["扣分点1", "扣分点2"],
                  "correctAnswer": "正确答案参考",
                  "nextQuestion": "丰富后的下一问题"
                }
                
                ## 评估规则：
                1. 对比标准答案(answ)与客服回答(csAnsw)的语义匹配度
                2. 评分标准：
                   - 100分：语义完全一致
                   - 0分：语义完全无关
                   - 按比例评分：语义部分匹配时计算匹配度
                3. 明确列出所有扣分点和正确答案参考
                4. 对下一问题(nextQues)进行丰富表达，保持核心语义但变换表达方式
                
                ## 丰富问题的方法：
                - 改变句式结构
                - 使用同义词替换
                - 增加礼貌用语
                - 添加使用场景描述
                - 调整语气和表达方式
                
                ## 示例参考：
                
                **示例输入：**
                answ: "这款手机支持5G网络"
                csAnsw: "是的，这款手机支持5G网络"
                nextQues: "手机的电池容量是多少"
                
                **示例输出：**
                {
                  "analysis": "完全匹配，客服回答准确无误",
                  "score": 100,
                  "deductionPoints": [],
                  "correctAnswer": "应明确说明'支持5G网络'",
                  "nextQuestion": "我经常外出使用，比较关心电池续航，请问这款手机的电池容量多大？"
                }
                
                请始终输出纯JSON格式，不要任何额外解释。
                
                """;




        System.out.println("\n----- multiple rounds request -----");

//        systemtemMessage = systemtemMessage.replace("{{ques}}","正装多少粒");

        String userMessage1="";

//        String assistantMessage = "部分匹配，得分约14.3分，扣分点：客户询问使用方法，客服仅提及使用青春胶囊，未涵盖完整的使用量、频率、顺序等关键信息，与正确答案匹配度低。下一题: 金胶黑胶什么区别";
        String assistantMessage = "这个正装到底有多少粒呀？";

//        String userMessage = "csAnsw:{{csAnsw}},answ:{{answ}},nextQues:{{nextQues}}";
        String userMessage = "请评估以下内容：\n" +
                "answ: \"{{answ}}\"\n" +
                "csAnsw: \"{{csAnsw}}\"\n" +
                "nextQues: \"{{nextQues}}\"";

//        userMessage=userMessage.replace("{{ques}}","产品一共多少组");
        userMessage=userMessage.replace("{{csAnsw}}","你这个傻屌~");
        userMessage=userMessage.replace("{{answ}}","正装60粒哈亲爱的~");
        userMessage=userMessage.replace("{{nextQues}}","多久会有效果");




        /*String userMessage2 = "ques为:{{ques}},csAnsw为:{{csAnsw}},answ为:{{answ}}";
        userMessage2=userMessage2.replace("{{ques}}","金胶黑胶什么区别");
        userMessage2=userMessage2.replace("{{csAnsw}}","一个是金的，一个是黑的");
        userMessage2=userMessage2.replace("{{answ}}","亲爱的~金胶主要针对肌肤干燥的人群~改善肤色不均，修护肌肤氧化损伤造成的干燥暗沉~\n" +
                "黑胶主要针对有抗皱需求的人群~改善肌肤松弛，淡化细纹，延缓肌肤老化的哦~\n" +
                "建议金胶和黑胶一起搭配使用哦~");*/


        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
                /*,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()*/
                /*,ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage2).build()*/
        );

        ChatCompletionRequest.ChatCompletionRequestResponseFormat format = getFormat();

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .responseFormat(format)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();



    }



    private  ChatCompletionRequest.ChatCompletionRequestResponseFormat getFormat() {

        String format = null;
        ObjectMapper mapper = new ObjectMapper();

        // 构造消息列表（包含 system 和 user 角色）
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content("你是一位数学辅导老师，需详细展示解题步骤")
                .build());
        messages.add(ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("用中文解方程组：8x + 9 = 32 和 x + y = 1")
                .build());

        // 生成 JSON Schema
        String schemaJson = "{\n" +
                "  \"type\": \"object\",\n" +
                "  \"properties\": {\n" +
                "    \"steps\": {\n" +
                "      \"type\": \"array\",\n" +
                "      \"items\": {\n" +
                "        \"$ref\": \"#/definitions/Step\"\n" +
                "      }\n" +
                "    },\n" +
                "    \"finalAnswer\": {\n" +
                "      \"type\": \"string\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"definitions\": {\n" +
                "    \"Step\": {\n" +
                "      \"type\": \"object\",\n" +
                "      \"properties\": {\n" +
                "        \"explanation\": {\n" +
                "          \"type\": \"string\"\n" +
                "        },\n" +
                "        \"output\": {\n" +
                "          \"type\": \"string\"\n" +
                "        }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        JsonNode schemaNode = null;
        try {
            schemaNode = mapper.readTree(schemaJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 配置响应格式
        ChatCompletionRequest.ChatCompletionRequestResponseFormat responseFormat = new ChatCompletionRequest.ChatCompletionRequestResponseFormat(
                "json_schema",
                new ResponseFormatJSONSchemaJSONSchemaParam(
                        "math_response",
                        "数学题解答的结构化响应",
                        schemaNode,
                        true
                )
        );

        return  responseFormat;
    }


    @Test
    public void testQaReport_CreateCtx() {



        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                ```markdown
                  # 角色定位
                  你是一名专业的电商客服质量评估专家，具备丰富的客服对话分析经验和评分能力。
                  
                  # 核心任务
                  根据提供的客服对话记录和单题评分明细，准确计算平均分并按标准格式输出汇总报告。
                  
                  # 输入数据格式
                  每次接收的数据包含以下字段：
                  - **servName**: 客服姓名（仅在首次输入中出现）
                  - **servNo**: 客服编号（仅在首次输入中出现） \s
                  - **quesNo**: 题号信息（格式：当前题号/总题数）
                  - **custQues**: 客户提问内容
                  - **servAnsw**: 客服回答内容
                  - **itemScore**: 单题得分（纯数字格式）
                  - **itemResolve**: 评分详情（JSON格式）
                  
                  ## itemResolve结构说明
                  ```json
                  {
                    "分析": "对回答质量的分析描述",
                    "扣分点": ["扣分原因1", "扣分原因2"],
                    "正确答案": "标准回答格式"
                  }
                  ```
                  
                  # 处理规则
                  1. **信息保留**：客服姓名和编号仅在首次输入中出现，后续处理需保留这些信息
                  2. **分数计算**：计算所有题目得分的算术平均分
                     - 计算公式：总分 = 所有题目得分之和 / 题目数量
                     - 示例：2题分别得100分和0分，平均分为50分
                  3. **格式要求**：严格遵循指定的输出格式
                  
                  # 输出格式规范
                  
                  ## 汇总信息
                  ```
                  客服：{客服姓名}
                  客服编号：{客服编号}
                  总分：{平均分}分
                  ```
                  
                  ## 每题详情
                  ```
                  题目编号：{题号}
                  客户：{客户问题}
                  客服：{客服回答}
                  评分：{得分}分
                  解析：{分析内容}
                   |- 扣分点：{扣分点内容}
                   |- 正确答案：{标准答案}
                  ```
                  
                  # 完整示例
                  
                  ## 输入示例
                  ```
                  servName:张晓多
                  servNo：0001
                  
                  quesNo:1/20
                  custQues:你们这个商品有多少优惠？
                  servAnsw：买3赠1
                  itemScore:90
                  itemResolve:{
                    "分析": "大部分匹配，缺少部分说明",
                    "扣分点": ["没有提及现在搞活动"],
                    "正确答案": "买3赠1哦，现在搞活动"
                  }
                  
                  quesNo:2/20
                  custQues:你们这个咋漏水？
                  servAnsw：我们这个是密封的，不会漏水的
                  itemScore:0分
                  itemResolve:
                  {
                    "分析": "完全不匹配",
                    "扣分点": ["没有安抚客户","没有说明为啥漏水"],
                    "正确答案": "我们这个一般在没有破损的情况下是不会漏水的哦"
                  }
                  
                  ```
                  
                  ## 输出示例
                  ```
                  客服：张晓多
                  客服编号：0001
                  总分：45分
                  
                  题目编号：1/20
                  客户：你们这个商品有多少优惠？
                  客服：买3赠1
                  评分：90分
                  解析：
                  - 分析：大部分匹配，缺少部分说明。
                  - 扣分点：没有提及现在搞活动
                  - 正确答案：买3赠1哦，现在搞活动
                  
                  题目编号：2/20
                  客户：你们这个咋漏水？
                  客服：我们这个是密封的，不会漏水的
                  评分：0分
                  解析：
                  - 分析：完全不匹配。
                  - 扣分点：没有安抚客户、没有说明为啥漏水
                  - 正确答案：我们这个一般在没有破损的情况下是不会漏水的哦
                                
                
                """;

        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);
    }




    static String contextId2nd = "ctx-20250829222826-45hvc";
    @Test
    public void testQaReport_UseCtx() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();
//        String assistantMessage = "部分匹配，得分约14.3分，扣分点：客户询问使用方法，客服仅提及使用青春胶囊，未涵盖完整的使用量、频率、顺序等关键信息，与正确答案匹配度低。下一题: 金胶黑胶什么区别";
        String assistantMessage = "这个正装到底有多少粒呀？";

//        String userMessageContent = "请评估以下内容：csAnsw:{{csAnsw}},answ:{{answ}},nextQues:{{nextQues}}";
        String userMessageContent = """
                
                请对以下内容打分：
                
                servName:韩信
                servNo：0003
                                
                                
                quesNo:1/20
                custQues:你们这个水果新鲜吗？
                servAnsw：必须新鲜
                itemScore:90
                itemResolve:{
                  "分析": "大部分匹配，缺少部分说明",
                  "扣分点": ["没有提及运送方式"],
                  "正确答案": "新鲜哦，是空运过来的"
                }
                                
                quesNo:2/20
                custQues:你们这个商品怎么漏水？
                servAnsw：因为你不会用呀
                itemScore:0
                itemResolve:{
                  "分析": "一点也不匹配",
                  "扣分点": ["没有礼貌"],
                  "正确答案": "亲，需要用前摇一摇哦"
                }
                
                """;

       /* userMessageContent=userMessageContent.replace("{{csAnsw}}","正装60粒哈亲爱的~");
        userMessageContent=userMessageContent.replace("{{answ}}","正装60粒哈亲爱的~");
        userMessageContent=userMessageContent.replace("{{nextQues}}","多久会有效果");*/


        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

//        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextId2nd)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }


    @Test
    public void testFreSr2nd_CreateCtx() {


        List<ChatMessage> messagesForReqList = new ArrayList<>();


        String systemMessage= """
                
                  ## 系统提示词（只需设置一次）
                
                ```markdown
                作为电商客服质量评估专家，请严格按照以下JSON格式输出评估结果：
                
                {
                  "analysis": "匹配度分析描述",
                  "score": 分数,
                  "deductionPoints": ["扣分点1", "扣分点2"],
                  "correctAnswer": "正确答案参考",
                  "nextQuestion": "丰富后的下一问题"
                }
                
                ## 评估规则：
                1. 对比标准答案(answ)与客服回答(csAnsw)的语义匹配度
                2. 评分标准：
                   - 100分：语义完全一致
                   - 0分：语义完全无关
                   - 按比例评分：语义部分匹配时计算匹配度
                3. 明确列出所有扣分点和正确答案参考
                4. 对下一问题(nextQues)进行丰富表达，保持核心语义但变换表达方式
                
                ## 丰富问题的方法：
                - 改变句式结构
                - 使用同义词替换
                - 增加礼貌用语
                - 添加使用场景描述
                - 调整语气和表达方式
                
                ## 示例参考：
                
                **示例输入：**
                answ: "这款手机支持5G网络"
                csAnsw: "是的，这款手机支持5G网络"
                nextQues: "手机的电池容量是多少"
                
                **示例输出：**
                {
                  "analysis": "完全匹配，客服回答准确无误",
                  "score": 100,
                  "deductionPoints": [],
                  "correctAnswer": "应明确说明'支持5G网络'",
                  "nextQuestion": "我经常外出使用，比较关心电池续航，请问这款手机的电池容量多大？"
                }
                
                请始终输出纯JSON格式，不要任何额外解释。
                
                             
                            
                                
                
                """;





        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemMessage)
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model(JIURU_DIAN)
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        String resp = service.createContext(req).toString();
        System.out.println(resp);

    }




    static String contextIdQaReport = "ctx-20250828213420-vlxx4";
    @Test
    public void testFreSr2nd_UseCtx() {
        List<ChatMessage> messagesForReqList = new ArrayList<>();




//        String assistantMessage = "部分匹配，得分约14.3分，扣分点：客户询问使用方法，客服仅提及使用青春胶囊，未涵盖完整的使用量、频率、顺序等关键信息，与正确答案匹配度低。下一题: 金胶黑胶什么区别";
        String assistantMessage = "这个正装到底有多少粒呀？";

        String userMessageContent = "请评估以下内容：csAnsw:{{csAnsw}},answ:{{answ}},nextQues:{{nextQues}}";

//        userMessage=userMessage.replace("{{ques}}","产品一共多少组");
        userMessageContent=userMessageContent.replace("{{csAnsw}}","正装60粒哈亲爱的~");
        userMessageContent=userMessageContent.replace("{{answ}}","正装60粒哈亲爱的~");
        userMessageContent=userMessageContent.replace("{{nextQues}}","多久会有效果");


        ChatMessage assitantMessage =
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();

        ChatMessage userMessage =
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageContent).build();

//        messagesForReqList.add(assitantMessage);
        messagesForReqList.add(userMessage);


        ContextChatCompletionRequest req =
                ContextChatCompletionRequest.builder()
                        .contextId(contextIdQaReport)
                        .model(JIURU_DIAN)
                        .messages(messagesForReqList)
                        .build();


        service.createContextChatCompletion(req)
                .getChoices()
                .forEach(choice -> System.out.println(choice.getMessage().getContent()));
    }


    @Test
    public void testFreAsk() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                """;


        systemtemMessage= """
                
                你模拟一个电商客户对客服进行提问，现在客户问的是关于某个商品的问题ques,你将这个问题丰富一下，每次换不同的花样，但核心意思不能变。
                这个问题的正确答案是answ.
                客服回答后的命名为csAnsw,让answ和csAnsw进行比对，如果语义完全匹配，给100分；如果一点也不匹配，给0分；如果部分匹配，则按匹配个数/总个数给分;
                我会不停提供给你ques、answ、csAnsw的值，如下所示，然后你给我打分即可，
                比如：
                ques为:有试用吗,csAnsw为:有的哦，可以试用1个哦~,answ为:亲爱哒，若有赠送同款小样，可以试用1个哦~
                
                正装跟其他赠品不要拆封使用，若不合适，是支持退货哒~
                
                则你给的答案为：100分，完全匹配，扣分点无。
                
                
                
                """;




        System.out.println("\n----- multiple rounds request -----");

        systemtemMessage = systemtemMessage.replace("{{ques}}","有试用吗");


        String userMessage = "ques为:{{ques}},csAnsw为:{{csAnsw}},answ为:{{answ}}";

        userMessage=userMessage.replace("{{ques}}","使用方法");
        userMessage=userMessage.replace("{{csAnsw}}","使用青春胶囊");
        userMessage=userMessage.replace("{{answ}}","亲爱哒~21天密集抗皱胶囊精华使用方法如下：\n" +
                "\uD83D\uDC49使用量及频率：\n" +
                "\uD83D\uDC49基础护理：每晚1粒，每天共1粒\n" +
                " \uD83D\uDC49最佳护理：早晚各1粒，每天共2粒（脸部涂抹，请勿食用，远离儿童）\n" +
                " \uD83D\uDC49使用顺序：\n" +
                "【第1天-第7天】\n" +
                "使用青春胶囊（银色）\n" +
                "【第8天-第14天】\n" +
                "使用赋能胶囊（香槟色）\n" +
                "【第15天-第21天】\n" +
                "使用凝时胶囊（金色）\n" +
                "【21天完成后，再继续从银色循环】");

        String assistantMessage = "部分匹配，得分约14.3分，扣分点：客户询问使用方法，客服仅提及使用青春胶囊，未涵盖完整的使用量、频率、顺序等关键信息，与正确答案匹配度低。 ";

        String userMessage2 = "ques为:{{ques}},csAnsw为:{{csAnsw}},answ为:{{answ}}";
        userMessage2=userMessage2.replace("{{ques}}","金胶黑胶什么区别");
        userMessage2=userMessage2.replace("{{csAnsw}}","一个是金的，一个是黑的");
        userMessage2=userMessage2.replace("{{answ}}","亲爱的~金胶主要针对肌肤干燥的人群~改善肤色不均，修护肌肤氧化损伤造成的干燥暗沉~\n" +
                "黑胶主要针对有抗皱需求的人群~改善肌肤松弛，淡化细纹，延缓肌肤老化的哦~\n" +
                "建议金胶和黑胶一起搭配使用哦~");


        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build()
                ,
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage2).build()
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();



    }


    @Test
    public void testJudgeFake() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                                
                你模拟一个电商客户，精通语义分析，我现在给你一段提问A、一个回答AN，完整知识库详情T，你根据A从T中搜索出语义最关联的片段P，让P和AN比对，
                如果语义完全接近，则给100分；如果语义部分接近，给1-99分；如果语义完全不接近，则给0分。
                                
                示例如下面3个#括起来的：
                ###
                                
                示例1：
                输入：
                提问A：有哪些颜色啊？
                回答AN：有黑色、黄色、青色。完整知识库详情T：“颜色选项：黑色、黄色、青色，大小：小 中 大，产地：上海 浙江 江苏”
                                
                输出：
                这是完全匹配，给100分。
                                
                示例2：
                输入：
                提问A：有哪些颜色啊？
                回答AN：有黑色、黄色。完整知识库详情T：“颜色选项：黑色、黄色、青色，大小：小 中 大，产地：上海 浙江 江苏”
                                
                输出：
                这是部分匹配，给67分。扣分点是缺少青色。
                                
                示例3：
                输入：
                提问A：有哪些颜色啊？
                回答AN：有白色。完整知识库详情T：“颜色选项：黑色、黄色、青色，大小：小 中 大，产地：上海 浙江 江苏”
                                
                输出：
                这是完全不匹配，给0分。扣分点是和黑色、黄色、青色无一匹配。
                                
                                
                ###
                                
                """;

        String assistantMessage = """
                请问重量是多大啊？
                                
                """;

        String userMessage = """
                亲，2500g 4500g 3斤。
                完整知识库详情T：“颜色选项：黑色、黄色、青色，大小：小 中 大，产地：上海 浙江 江苏,重量: 2500g 4500g 3斤 1斤”
                                
                """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build();
        final ChatMessage streamAssistantMessage = ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamAssistantMessage);
        streamMessages.add(streamUserMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();


        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(20000);
        // shutdown service
        service.shutdownExecutor();

    }


    @Test
    public void testJudgeReal() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                                
                你模拟一个网上购物的电商客户（同时精通语义分析、语义判断），和客服聊天，
                   其中你的角色设定见Z的第1步内容，所问问题的风格、内容、客观事实要严格根据你的角色设定来；
                   以Z的第5步的进线意图为基调，以Z的第4步流程节点为提问顺序，提问的核心始终围绕Z的第2步用户想要购买的产品上，产品相关联的图片信息见Z的第3步
                   
                   整体背景信息Z如下面3个#括起来的：
                   ###
                   1、买家背景信息是 : 买家是一位中年大叔 ,
                   
                   2、买家想要购买的产品是  : 产自甘肃定西，昼夜温差大，积累大量糖分，口感香甜软糯，独特地理环境孕育，闲时来一口，体验人间美味
                   
                   3、
                   
                   4、
                   按照如下流程节点进行询问 :
                   
                   流程节点名称：售前  ，本流程买家要求： 咨询,
                   
                   
                   5、买家进线意图是 : 售前 咨询产品
                   ###
                   
                   要求：
                   
                   1、假设整个知识库详情A如下面3个#括起来的：
                   ###
                   “颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。”，
                   ###
                   
                   
                   2、在客服的回答（命名为CSA）中，会将上面整个知识库详情A给你，你需要做4个动作：
                   2.1、根据你的提问智能的从A中挑选出和提问相关的，剔除掉无关紧要的信息，比如”你问颜色是什么“，提取出关键信息”颜色“，去A中查询，找到片段(命名为Para)”颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属“，然后将CSA和Para比对得出匹配度，进行判断打分（命名为S）。语义完全一致，则给100分；语义部分接近，则酌情给1-99分；完全不接近则给0分。这里是完全一致，则给100分。
                   2.2、如果能匹配到Para,则将正确答案来源置为“知识库”；如果未匹配到Para，则根据常识判断，将正确答案来源置为“常识”
                   2.3、S在80分上优秀、60以上中等、60以下较差，然后给出正确答案，正确答案要加来源，来源要加【】括起来，比如【来源知识库】、【来源常识判断】再提问下一题。
                   2.4、返回结果严格以json格式输出。
                   2.5、要一步一步推理
                   2.6、要注意常识的转化，比如100公斤和200斤是一个意思，中国和China是一个意思
                   
                   
                   示例1：
                   
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                       ]
                   }
                   
                   
                   
                   客服：亲，这款手机有黑色钛金属，白色钛金属，蓝色钛金属。
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分75分！一般般！扣分点：缺少自然钛金属。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，git "
                       ,
                      
                   			"这个产品支持5G吗？"
                      ,
                      
                   			"我比较喜欢先进的产品呢"
                      
                     ]
                   }
                   
                   
                   
                   示例2：
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                       ]
                   }
                   
                   
                   
                   客服：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分100分！你太棒了！扣分点：无。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】"
                       ,
                      
                   			"请问这个支持Apple Pay吗？"
                      ,
                      
                   			"我要经常下载国外小游戏呢"
                      
                     ]
                   }
                   
                   
                   
                   
                   示例3：
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                      
                       ]
                   }
                   
                   
                   
                   客服：亲，是粉色哦。
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分0分！较差，再不努力你离优化就不远了。扣分点：和黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属完全不匹配，【来源知识库】"
                       ,
                      
                   			"这个产品支持5G吗？"
                      ,
                      
                   			"我比较喜欢先进的产品呢"
                      
                     ]
                   }
                   
                   示例4：
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机防水吗","我经常在水上工作，手机掉水里也要拿上来继续用"
                      
                       ]
                   }
                   
                   
                   
                   客服：亲，是可以的哦
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分0分！较差，再不努力你离优化就不远了。扣分点：和Iphone手机不防水不匹配，有夸大嫌疑，【来源常识判断】"
                       ,
                      
                   			"这个产品支持5G吗？"
                      ,
                      
                   			"我比较喜欢先进的产品呢"
                      
                     ]
                   }
                   
                   
                   
                   
                   你需要首先向客服发起问答。
                                
                """;

        String assistantMessage = """
                请问重量是多大啊？
                                
                """;

        String userMessage = """
                亲，2500g 4500g 1.5公斤。
                完整知识库详情T：“品牌: 禾果小镇，城市: 定西市，上市时间: 11月 10月 9月，售卖方式: 产地直销，重量: 2500g 4500g 3斤 1斤，配送频次: 1周1次”
                                
                """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build();
        final ChatMessage streamAssistantMessage = ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamAssistantMessage);
        streamMessages.add(streamUserMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(10000);
        // shutdown service
        service.shutdownExecutor();

    }


    @Test
    public void testRobotOfJiangmei() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                                
                你模拟一个网上购物的电商客户（同时精通语义分析、语义判断），和客服聊天，
                   其中你的角色设定见Z的第1步内容，所问问题的风格、内容、客观事实要严格根据你的角色设定来；
                   以Z的第5步的进线意图为基调，以Z的第4步流程节点为提问顺序，提问的核心始终围绕Z的第2步用户想要购买的产品上，产品相关联的图片信息见Z的第3步
                   
                   整体背景信息Z如下面3个#括起来的：
                   ###
                   1、买家背景信息是 : 买家是一位中年大叔 ,
                   
                   2、买家想要购买的产品是  : 产自甘肃定西，昼夜温差大，积累大量糖分，口感香甜软糯，独特地理环境孕育，闲时来一口，体验人间美味
                   
                   3、
                   
                   4、
                   按照如下流程节点进行询问 :
                   
                   流程节点名称：售前  ，本流程买家要求： 咨询,
                   
                   
                   5、买家进线意图是 : 售前 咨询产品
                   ###
                   
                   要求：
                   
                   1、假设整个知识库详情A如下面3个#括起来的：
                   ###
                   “颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。”，
                   ###
                   
                   
                   2、在客服的回答（命名为CSA）中，会将上面整个知识库详情A给你，你需要做4个动作：
                   2.1、根据你的提问智能的从A中挑选出和提问相关的，剔除掉无关紧要的信息，比如”你问颜色是什么“，提取出关键信息”颜色“，去A中查询，找到片段(命名为Para)”颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属“，然后将CSA和Para比对得出匹配度，进行判断打分（命名为S）。语义完全一致，则给100分；语义部分接近，则酌情给1-99分；完全不接近则给0分。这里是完全一致，则给100分。
                   2.2、如果能匹配到Para,则将正确答案来源置为“知识库”；如果未匹配到Para，则根据常识判断，将正确答案来源置为“常识”
                   2.3、S在80分上优秀、60以上中等、60以下较差，然后给出正确答案，正确答案要加来源，来源要加【】括起来，比如【来源知识库】、【来源常识判断】再提问下一题。
                   2.4、返回结果严格以json格式输出。
                   2.5、要一步一步推理
                   2.6、要注意常识的转化，比如100公斤和200斤是一个意思，中国和China是一个意思
                   
                   
                   示例1：
                   
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                       ]
                   }
                   
                   
                   
                   客服：亲，这款手机有黑色钛金属，白色钛金属，蓝色钛金属。
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分75分！一般般！扣分点：缺少自然钛金属。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，git "
                       ,
                      
                   			"这个产品支持5G吗？"
                      ,
                      
                   			"我比较喜欢先进的产品呢"
                      
                     ]
                   }
                   
                   
                   
                   示例2：
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                       ]
                   }
                   
                   
                   
                   客服：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分100分！你太棒了！扣分点：无。正确答案是：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，【来源知识库】"
                       ,
                      
                   			"请问这个支持Apple Pay吗？"
                      ,
                      
                   			"我要经常下载国外小游戏呢"
                      
                     ]
                   }
                   
                   
                   
                   
                   示例3：
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机有哪些颜色可选啊？","我比较喜欢鲜艳的颜色呢"
                      
                       ]
                   }
                   
                   
                   
                   客服：亲，是粉色哦。
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分0分！较差，再不努力你离优化就不远了。扣分点：和黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属完全不匹配，【来源知识库】"
                       ,
                      
                   			"这个产品支持5G吗？"
                      ,
                      
                   			"我比较喜欢先进的产品呢"
                      
                     ]
                   }
                   
                   示例4：
                   
                   客户：
                   
                   {
                       "result": [
                           "请问这款手机防水吗","我经常在水上工作，手机掉水里也要拿上来继续用"
                      
                       ]
                   }
                   
                   
                   
                   客服：亲，是可以的哦
                   知识库详情A如下面3个#号括起来的
                   ###
                   颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
                   ###
                   
                   
                   客户：
                   
                   {
                    "result":[
                       "本次得分0分！较差，再不努力你离优化就不远了。扣分点：和Iphone手机不防水不匹配，有夸大嫌疑，【来源常识判断】"
                       ,
                      
                   			"这个产品支持5G吗？"
                      ,
                      
                   			"我比较喜欢先进的产品呢"
                      
                     ]
                   }
                   
                   
                   
                   
                   你需要首先向客服发起问答。
                                
                """;

        String assistantMessage = """
                请问重量是多大啊？
                                
                """;

        String userMessage = """
                亲，2500g 4500g 1.5公斤。
                完整知识库详情T：“品牌: 禾果小镇，城市: 定西市，上市时间: 11月 10月 9月，售卖方式: 产地直销，重量: 2500g 4500g 3斤 1斤，配送频次: 1周1次”
                                
                """;

        userMessage="";

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build();
//        final ChatMessage streamAssistantMessage = ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build();
        streamMessages.add(streamSystemMessage);
//        streamMessages.add(streamAssistantMessage);
        streamMessages.add(streamUserMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(10000);
        // shutdown service
        service.shutdownExecutor();

    }



    @Test
    public void testDbZhinengHuida() throws InterruptedException {


        String systemtemMessage = """
                 你模拟一个客户C（兼语义解析专家，擅长语义解析），你提一个问题（命名为Q）,客服CS会回你消息(命名为An)，
                 同时会将知识库(命名为K)一起发你，你需要先解析Q，提取出关键特征，比如你问什么这款洗碗机是什么颜色？提取出关键特征“颜色”（命名为FE），
                 然后从K中根据FE提取出相匹配的片段（命名为Para），并将Para进一步拆分到最小的词组，比如Para是：“重量：1kg 2kg 4kg 4500g”，拆分成Para1:“1kg”、Para2:"2kg"、Para3:"4kg"、Para4:"4500g"，
                 将客服的回答An和Para1-Para4分别去比对：
                 如果匹配到：
                     如果语义完全匹配，给100分；如果一点也不匹配，给0分；如果部分匹配，则按匹配个数/总个数给分
                     ，并给出正确答案、以及正确答案的来源，并说出具体扣分的点。
                               
                如果匹配不到：
                     则根据常识判断，不要出现匹配不到知识库给0分的情况。
                 
                 要求：
                 1、如果能匹配到Para,则将正确答案来源置为“知识库”；如果未匹配到Para，则根据常识判断，将正确答案来源置为“常识”
                 2、输出时，将匹配到的Para、Para1-ParaN、特征FE、分析过程一并输出。
                 3、一步一步推理。
                 4、要注意常识的转化，比如100公斤和200斤是一个意思，中国和China是一个意思
                 
                 
                 以下文本片段（命名为TEXTCHUNK）
                 ###
                 颜色：黄色、红色、黑色
                 大小：小 中 大
                 重量：1kg 2kg 4kg 4500g
                 产地：深圳华为
                 品牌：天之娇子
                 
                 ###
                 
                 举例1：
                 商品是一款洗碗机。
                                 
                 C：你好，请问这款洗碗机的颜色是什么？
                 CS：亲，这款洗碗机的颜色是红色呢。
                知识库见如下3个#括起来的部分（K）：
                 ###
                                 
                 TEXTCHUNK
                                 
                 ###            
                                 
                 你的回答如下：
                 得分33分，正确答案是：黄色、红色、黑色，来源【知识库】。扣分点：缺少黄色、黑色
                 匹配到的Para：黄色、红色、黑色，Para1:“黄色”、Para2:"红色"、Para3:"黑色"
                 匹配的特征FE：颜色
                 分析过程：回答的是红色，而正确答案是黄色、红色、黑色 ，只匹配到了其中1个，未匹配到的是2个，因此得分为1/(1+2)*100=33分      
                               
                举例2：
                 商品是一款智能手表。
                                 
                 C：你好，请问这款洗碗机的颜色是什么？
                 CS：亲，这款洗碗机的颜色是红色、黄色、黑色。
                知识库见如下3个#括起来的部分（K）：
                 ###
                                 
                TEXTCHUNK
                                 
                 ###            
                                 
                 你的回答如下：
                 得分100分，正确答案是：黄色、红色、黑色，来源【知识库】。扣分点：无
                 匹配到的Para：黄色、红色、黑色，Para1:“黄色”、Para2:"红色"、Para3:"黑色"
                 匹配的特征FE：颜色
                 分析过程：回答的是红色、黄色、黑色，而正确答案是黄色、红色、黑色 ，匹配到了所有3个，未匹配到的是0个，因此得分为3/3*100=10分   
                 
                 举例3：
                 商品是一款智能手表。
                                 
                 C：你好，请问这款洗碗机的颜色是什么？
                 CS：亲，这款洗碗机的颜色是白色呢。
                知识库见如下3个#括起来的部分（K）：
                 ###
                                 
                 TEXTCHUNK
                                 
                 ###            
                                 
                 你的回答如下：
                 得分0分，正确答案是：黄色、红色、黑色，来源【知识库】。扣分点：缺少红色、黄色、黑色
                 匹配到的Para：黄色、红色、黑色，Para1:“黄色”、Para2:"红色"、Para3:"黑色"            
                 匹配的特征FE：颜色
                 分析过程：回答的是白色，而正确答案是黄色、红色、黑色 ，只匹配到了其中0个，未匹配到的是3个，因此得分为0/(3)*100=0分   
                 
                 
                  举例4：
                 商品是一款洗碗机。
                                 
                 C：你好，请问这款洗碗机是什么材质？
                 CS：亲，这款洗碗机的材质是不锈钢的呢。
                知识库见如下3个#括起来的部分（K）：
                 ###
                                 
                 TEXTCHUNK
                                 
                 ###            
                                 
                 你的回答如下：
                 得分50分，正确答案是：一般洗碗机的材质是不锈钢、塑料，来源【常识判断】。扣分点：缺少塑料   
                 匹配到的Para：一般洗碗机的材质是不锈钢、塑料，Para1:“不锈钢”、Para2:"塑料"               
                 匹配的特征FE：材质
                 分析过程：回答的是不锈钢，而正确答案是不锈钢、塑料 ，只匹配到了其中1个，未匹配到的是1个，因此得分为1/(1+1)*100=50分   
                                
                 """;


        String assistantMessage = """
                你好，请问这款洗碗机的重量是怎样的？
                """;

        String userMessage = """
                亲，这款洗碗机分别有50公斤，200斤 120斤呢
                所有问题的参考答案见如下3个#括起来的部分：
                        ###
                        品牌: 博世
                                     
                        型号: SHSM63W55N
                                     
                        产品类型: 洗碗机
                                     
                        安装方式: 嵌入式（需橱柜适配）
                                     
                        容量: 14套餐具（适合4-6口之家）
                                     
                        外部尺寸(高×宽×深): 845毫米 × 598毫米 × 573毫米
                                     
                        内部结构: 双层可调搁架 + 餐具篮
                                     
                        颜色/材质: 不锈钢拉丝
                                     
                        能效等级: A+++（高效节能）
                                     
                        年耗电量: 270千瓦时（标准测试）
                                     
                        年耗水量: 2800升（标准测试）
                                     
                        噪音水平: 44分贝（安静运行）
                                     
                        洗涤程序: 自动、强力、节能、快速30分钟、玻璃呵护、预冲洗
                                     
                        干燥系统: 热交换冷凝干燥
                                     
                        特殊功能: 延迟启动(1-24小时)、儿童安全锁、半载程序（节能选项）
                                     
                        智能功能: Wi-Fi连接、手机App远程控制
                                     
                        水压要求: 0.04-1.0兆帕
                                     
                        电源要求: 220-240伏, 50赫兹
                                     
                        进水管长度: 1.5米（标配）
                                     
                        排水方式: 自动循环排水
                                     
                        重量: 100公斤 60公斤  70斤 80斤
                                     
                        保修期: 2年整机保修
                                     
                        认证标准: CE、RoHS、中国能效标识
                        ###
                """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage systemMessageCM = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build();
        final ChatMessage assistantMessageCM = ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistantMessage).build();
        final ChatMessage userMessageCM = ChatMessage.builder().role(ChatMessageRole.USER).content(userMessage).build();
        messages.add(systemMessageCM);
        messages.add(assistantMessageCM);
        messages.add(userMessageCM);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(THINK_MODLE_NAME)
                .messages(messages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(2 * 60 * 1000);
        // shutdown service
        service.shutdownExecutor();


    }

    @Test
    public void testRobotFenkuai() throws InterruptedException {


        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String systemtemMessage = """
                                
                一、你是一个电商详情分块处理专家。请处理以下商品详情内容：
                      1. 移除所有无关的html标签、转义字符，包括但不限于<p><strong>等html标签，&lt、\\n等转义字符
                      2. 严格保留所有产品规格、参数和功能描述，内容一个字也不能缺
                      3. 精简重复内容
                  
                  
                  二、要求：
                  1、根据输入的分块数量，生成指定的块数；根据输入的重叠字数，设置相应的重叠区块
                  2、每个块中都是以键值对key、value形式出现。
                  3、在一个键值对key、value中出现的冒号：属于value内部内容，不能拆分。如颜色：黑色、红色：洋红、黄色，这个里面的：洋红属于对红色的解释，是完整的内容，不能拆分。
                  4、输出成json数组格式。
                  
                  
                  三、各种复杂情况参考下面3个#括起来的
                  
                  ###
                  1、在一行中表示:
                  品牌：苹果，型号：iPhone 15 Pro，屏幕尺寸：6.1英寸，屏幕类型：Super Retina XDR OLED，分辨率：2556 x 1179像素，像素密度：460 PPI，刷新率：ProMotion自适应10-120Hz，处理器：A17 Pro芯片（6核CPU，6核GPU），内存：8GB LPDDR5 RAM，存储容量：128GB/256GB/512GB/1TB NVMe SSD，操作系统：iOS 17（可升级至未来版本）
                  
                  2、可能以多行形式表示：
                  品牌：苹果，
                  型号：iPhone 15 Pro，
                  屏幕尺寸：6.1英寸，
                  屏幕类型：Super Retina XDR OLED，
                  分辨率：2556 x 1179像素，
                  
                  3、可能以多段+多行形式
                  品牌：苹果，
                  型号：iPhone 15 Pro，
                  
                  屏幕尺寸：6.1英寸，
                  屏幕类型：Super Retina XDR OLED，
                  
                  4、杂乱的：
                  比如：品牌：苹果，型号：iPhone 15 Pro，屏幕尺寸：6.1英寸:大号版本，一个手拿 不下，屏幕类型：Super Retina XDR OLED。
                  ###
                  
                  
                  
                  
                  4、假设输入是按200个每块，重叠40后，
                  输出成如下3个#括起来的：
                  ###
                  [
                    "品牌：苹果，型号：iPhone 15 Pro，屏幕尺寸：6.1英寸:超大屏幕:大号版本，屏幕类型：Super Retina XDR OLED，分辨率：2556 x 1179像素，像素密度：460 PPI，刷新率：ProMotion自适应：智能适应10-120Hz，处理器：A17 Pro芯片（6核CPU，6核GPU），内存：8GB LPDDR5 RAM，存储容量：128GB/256GB/512GB/1TB NVMe SSD，操作系统：iOS 17（可升级至未来版本），后置摄像头：48MP主摄（f/1.78光圈）+12MP超广角（f/2.2）",
                    "存储容量：128GB/256GB/512GB/1TB NVMe SSD，操作系统：iOS 17（可升级至未来版本），后置摄像头：48MP主摄（f/1.78光圈）+12MP超广角（f/2.2）+12MP长焦（f/2.8，3倍光学变焦），前置摄像头：12MP TrueDepth（f/1.9），视频录制：4K 60fps HDR，支持Dolby Vision，电池容量：3274mAh，充电方式：20W有线快充，15W MagSafe无线充电，7.5W Qi无线充电，防水防尘等级：IP68（最深6米，30分钟），重量：187克，尺寸：146.6毫米（高）×70.6毫米（宽）",
                    "充电方式：20W有线快充，15W MagSafe无线充电，7.5W Qi无线充电，防水防尘等级：IP68（最深6米，30分钟），重量：187克，尺寸：146.6毫米（高）×70.6毫米（宽）×7.8毫米（厚），颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异）",
                    "材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公"
                  ]
                  
                  
                  ###
                  
                  
                  
                  
                                
                """;


        String userMessageStr = """
                                
                将如下3个#括起来的内容，按300字1个块，重叠60字进行分块。
                                
                ###
                                
                品牌：华为，型号：Mate 60 Pro，<strong>屏幕尺寸</strong>：6.82英寸OLED曲面屏:四曲面设计，
                分辨率：2720 × 1260像素，像素密度：456 PPI，刷新率：1-120Hz LTPO自适应刷新率，
                屏幕特性：300Hz触控采样率，1440Hz高频PWM调光，HDR10+认证，峰值亮度1800尼特，
                                
                处理器：麒麟9000s芯片（1×2.62GHz Cortex-X2 + 3×2.15GHz Cortex-A710 + 4×1.53GHz Cortex-A510），
                &lt;NPU：双大核+微核架构&gt;，内存：12GB LPDDR5X RAM，存储：256GB/512GB/1TB UFS 4.0，
                操作系统：HarmonyOS 4.0（支持Android应用兼容），
                                
                相机系统：<p>后置三摄：</p>
                主摄：50MP超光变（f/1.4-f/4.0可变光圈，OIS），
                超广角：12MP自由曲面（f/2.2，120°视野），
                长焦：48MP潜望式（f/3.5，OIS，3.5倍光学变焦，100倍数字变焦），
                前置：13MP超广角（f/2.4）+ 3D深感摄像头，
                视频能力：8K@30fps，4K@60fps HDR10+，支持电影模式&AIS防抖，
                                
                电池续航：5000mAh硅碳负极电池，充电：88W有线超级快充（20分钟充满100%），50W无线超级快充，20W反向无线充电，
                防水防尘：IP68级别（最深2米，30分钟），机身：163.65mm×79mm×8.1mm，重量：225g，
                材质：昆仑玻璃2.0（正面），纳米微晶陶瓷（背面），铝合金中框，
                                
                网络支持：5G双卡双待（Sub-6GHz & mmWave），Wi-Fi 7（802.11be），蓝牙5.3，NFC，红外遥控，
                定位：北斗三代（B1C+B2a）+GPS（L1+L5）+Galileo（E1+E5a）+GLONASS（L1）+QZSS（L1+L5），
                生物识别：3D人脸识别+屏下光学指纹，安全芯片：独立安全存储芯片，
                                
                音频：立体声双扬声器（华为Histen 8.0），支持LDAC/LHDC高清编码，无3.5mm耳机孔，
                传感器：气压计，色温传感器，霍尔传感器，陀螺仪，指南针，激光对焦传感器，
                特殊功能：卫星通信（支持北斗卫星消息+天通卫星通话），应急模式（1%电量待机12小时），智感支付，AI隔空操控，
                                
                包装清单：手机主机×1，88W充电器×1，USB-C数据线×1，透明保护壳×1，取卡针×1，快速指南×1，三包凭证×1，
                保修政策：1年主机保修，6个月电池保修，15天换新，环保认证：EPEAT金牌，TCO9.0认证，可回收率95%，
                价格：6999元（256GB），7999元（512GB），8999元（1TB），发布日期：2023年8月29日，
                                
                适用场景：商务办公，影像创作，移动游戏，户外探险，AI应用开发，
                特色技术：方舟引擎（性能优化），鸿鹄影像（计算摄影），灵犀通信（信号增强），玄武架构（抗摔设计），
                颜色：雅丹黑（素皮），南糯紫（素皮），雅川青（玻璃），白沙银（纳米微晶陶瓷），
                                
                附加参数：
                1. 显示增强：自然色彩显示，护眼模式，电子书模式
                2. 摄影功能：XD Fusion Pro图像引擎，10通道多光谱传感器，全焦段夜景模式
                3. 视频创作：AI跟拍导演，4K延时摄影，双景录像（前后同时录制）
                4. 游戏性能：GPU Turbo X，超帧游戏引擎，石墨烯散热系统（0.4mm超薄VC均热板）
                5. 智能体验：多屏协同3.0，超级中转站，智感扫码（0.5秒极速识别）
                6. 卫星功能：在无地面网络时，可发送北斗卫星消息（支持多条位置生成轨迹地图），拨打接听卫星电话
                ###
                                
                """;

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemtemMessage).build();
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(userMessageStr).build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(userMessage);
        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(streamMessages)
                .build();

        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );

        // just wait for result
        Thread.sleep(120 * 1000);
        // shutdown service
        service.shutdownExecutor();

    }


    @Test
    public void testFunctionCall() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        System.out.println("\n----- function call request -----");
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("北京今天天气如何？").build();
        messages.add(userMessage);

        final List<ChatTool> tools = Arrays.asList(
                new ChatTool(
                        "function",
                        new ChatFunction.Builder()
                                .name("get_current_weather")
                                .description("获取给定地点的天气")
                                .parameters(new Weather(
                                        "object",
                                        new HashMap<String, Object>() {{
                                            put("location", new HashMap<String, String>() {{
                                                put("type", "string");
                                                put("description", "T地点的位置信息，比如北京");
                                            }});
                                            put("unit", new HashMap<String, Object>() {{
                                                put("type", "string");
                                                put("enum", Arrays.asList("摄氏度", "华氏度"));
                                            }});
                                        }},
                                        Collections.singletonList("location")
                                ))
                                .build()
                )
        );


        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .tools(tools)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(System.out::println);

        // shutdown service
        service.shutdownExecutor();
    }

    /**
     * 静态内部类
     */
    public static class Weather {
        public String type;
        public Map<String, Object> properties;
        public List<String> required;

        public Weather(String type, Map<String, Object> properties, List<String> required) {
            this.type = type;
            this.properties = properties;
            this.required = required;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }

        public List<String> getRequired() {
            return required;
        }

        public void setRequired(List<String> required) {
            this.required = required;
        }
    }


    @Test
    public void testCustomHeader() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        System.out.println("\n----- standard request -----");
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
        messages.add(systemMessage);
        messages.add(userMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest, new HashMap<String, String>() {{
            put(Const.CLIENT_REQUEST_HEADER, "20240627115839147D61D8875537A133C1");
        }}).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();
    }


    @Test
    public void testException() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        System.out.println("\n----- streaming request -----");
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(MODLE_NAME)
                .messages(streamMessages)
                .build();

        try {
            service.streamChatCompletion(streamChatCompletionRequest)
                    .doOnError(Throwable::printStackTrace)
                    .blockingForEach(
                            choice -> {
                                if (choice.getChoices().size() > 0) {
                                    System.out.print(choice.getChoices().get(0).getMessage().getContent());
                                }
                            }
                    );
        } catch (ArkHttpException e) {
            System.out.print(e.toString());
        }

    }




    @Test
    public void testConcurrent() {
        System.out.println("\n----- standard request -----");
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
        messages.add(systemMessage);
        messages.add(userMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));
        // 请求结束需要关闭服务
        service.shutdownExecutor();
    }


    @Test
    public void testEmbed() {
        System.out.println("\n----- embeddings request -----");


        List<String> inputs = new ArrayList<>();
        inputs.add("你好，请问价格是多少");
        inputs.add("你好，请问快递怎么还没到");
        EmbeddingRequest embeddingRequest = EmbeddingRequest.builder()
                .model(EMBED_MODLE_NAME)
                .input(inputs)
                .build();


        EmbeddingResult res = service.createEmbeddings(embeddingRequest);
        System.out.println(res);


        // shutdown service after all requests are finished
        service.shutdownExecutor();
    }

    @Test
    public void testPromptCache() {

        List<ChatMessage> messagesForReqList = new ArrayList<>();

        ChatMessage elementForMessagesForReqList0 =
                ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content("你是李雷，你只会说“我是李雷”")
                        .build();
        messagesForReqList.add(elementForMessagesForReqList0);

        CreateContextRequest req =
                CreateContextRequest.builder()
                        .model("ep-202504101*****-*****")
                        .mode("common_prefix")
                        .messages(messagesForReqList)
                        .build();

        service.createContext(req).toString();
    }


    @Test
    public void testDbTimeElapsed() {
        String apiKey = System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        String sysPmt= """
                
                
                                       # 角色设定
                                      **电商聊天记录修改专家**
                                      
                                      ## 核心任务
                                      1. 将聊天记录chatLogPart中的「买家身份」替换为targetRolePart
                                      2. 将聊天记录chatLogPart中的「商品信息」替换为targetProductTitlePart
                                      3. 将聊天记录chatLogPart中的「商品信息知识库」替换为targetProKnowledgePart
                                      4. 严格模仿原始聊天记录(chatLogPart)的表达风格（语气/句式/用词习惯）
                                      
                                      ## 输入数据
                                      ### chatLogPart
                                      jd_6789abcdef123 2025-08-05 23:45:12
                                      https://item.jd.com/100233209869.html?sdx=ehi-lLxFuZiE6JnIY4BfjsYpuDaZRHtmwmtNsqlGY9WPPe_RLJhY4nztoEHnVGKV
                                      纽强自营-梦想 2025-08-05 23:45:12
                                      ✨小米手环10NFC版全网开售！时尚百搭，运动监测全功能！下单抢限量礼赠！✨<br/><br/>✨关注店铺，0元入会，解锁专属礼券和惊喜活动，购物更超值！✨
                                      纽强自营-梦想 2025-08-05 23:45:16
                                      您好，欢迎光临小米智能设备专区，我是您的专属顾问，请您稍等片刻，我会尽快查看聊天记录并为您服务，感谢您的耐心等待！
                                      jd_6789abcdef123 2025-08-05 23:45:24
                                      这个手环的腕带可以调节吗？适合我的手腕吗
                                      纽强自营-梦想 2025-08-05 23:45:32
                                      腕带长度支持135-210mm调节，您可以根据手腕尺寸调整，适配大多数人群呢
                                      
                                      ### targetProKnowledgePart
                                      ## 小米手环10NFC版
                                      ### 时尚出众
                                      | 属性 | 值 |
                                      | ---- | ---- |
                                      | 腕带可调长度 | 135-210mm |
                                      | 腕带材料 | TPU |
                                      | 系统语言 | 简体中文、繁体中文、英文 |
                                      | 支持系统 | Android 8.0 或 iOS 14.0及以上版本 |
                                      | 支持APP | 小米运动健康 |
                                      
                                      ### targetRolePart
                                      买家是一位年轻的小伙子，喜欢时尚
                                      
                                      ### targetProductTitlePart
                                      小米（MI）手环10NFC版 新品 运动手环 智能手环
                                      
                                      ## 输出要求
                                      ✅ 生成全新聊天记录
                                      ✅ 保留原始对话逻辑和场景
                                      ✅ 确保targetRolePart、targetProductTitlePart、targetProKnowledgePart自然融入
                                      ❌ 禁止修改非指定内容（如时间、售后流程等）
                                         
                                
                """;


        System.out.println("\n----- multiple rounds request -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(sysPmt).build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("").build()
//                ,
//                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
//                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()
        );

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(NORMAL_MODLE_NAME)
                .messages(messages)
                .build();

        service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

        // shutdown service
        service.shutdownExecutor();
    }



}


