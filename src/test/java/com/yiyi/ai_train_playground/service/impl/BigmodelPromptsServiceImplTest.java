package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.mapper.BigmodelPromptsMapper;
import com.yiyi.ai_train_playground.service.CacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BigmodelPromptsServiceImpl 单元测试
 * 测试缓存功能的正确性
 */
@ExtendWith(MockitoExtension.class)
class BigmodelPromptsServiceImplTest {

    @Mock
    private BigmodelPromptsMapper bigmodelPromptsMapper;

    @Mock
    private CacheManager cacheManager;

    @InjectMocks
    private BigmodelPromptsServiceImpl bigmodelPromptsService;

    private List<String> mockPrompts;
    private String testKeyword;
    private String expectedCacheKey;

    @BeforeEach
    void setUp() {
        testKeyword = "test-keyword:S";
        expectedCacheKey = "bigmodel_prompts:" + testKeyword;
        mockPrompts = Arrays.asList(
            "这是第一个测试提示词",
            "这是第二个测试提示词"
        );
    }

    @Test
    void testGetPromptsByKeyword_CacheHit() {
        // Given: 缓存中有数据
        when(cacheManager.get(expectedCacheKey, List.class))
            .thenReturn(Optional.of(mockPrompts));

        // When: 调用服务方法
        List<String> result = bigmodelPromptsService.getPromptsByKeyword(testKeyword);

        // Then: 验证结果
        assertEquals(mockPrompts, result);
        assertEquals(2, result.size());
        
        // 验证只调用了缓存，没有调用数据库
        verify(cacheManager).get(expectedCacheKey, List.class);
        verify(bigmodelPromptsMapper, never()).findSysPromptsByKeyword(anyString());
        verify(cacheManager, never()).put(anyString(), any(), any());
    }

    @Test
    void testGetPromptsByKeyword_CacheMiss() {
        // Given: 缓存未命中，数据库有数据
        when(cacheManager.get(expectedCacheKey, List.class))
            .thenReturn(Optional.empty());
        when(bigmodelPromptsMapper.findSysPromptsByKeyword("test-keyword"))
            .thenReturn(mockPrompts);

        // When: 调用服务方法
        List<String> result = bigmodelPromptsService.getPromptsByKeyword(testKeyword);

        // Then: 验证结果
        assertEquals(mockPrompts, result);
        assertEquals(2, result.size());
        
        // 验证调用了缓存查询、数据库查询和缓存存储
        verify(cacheManager).get(expectedCacheKey, List.class);
        verify(bigmodelPromptsMapper).findSysPromptsByKeyword("test-keyword");
        verify(cacheManager).put(eq(expectedCacheKey), eq(mockPrompts), any());
    }

    @Test
    void testGetPromptsByKeyword_BothSU() {
        // Given: 查询系统和用户提示词
        String keyword = "test-keyword:SU";
        String cacheKey = "bigmodel_prompts:" + keyword;
        
        when(cacheManager.get(cacheKey, List.class))
            .thenReturn(Optional.empty());
        when(bigmodelPromptsMapper.findBothPromptsByKeyword("test-keyword"))
            .thenReturn(mockPrompts);

        // When: 调用服务方法
        List<String> result = bigmodelPromptsService.getPromptsByKeyword(keyword);

        // Then: 验证结果
        assertEquals(mockPrompts, result);
        verify(bigmodelPromptsMapper).findBothPromptsByKeyword("test-keyword");
        verify(cacheManager).put(eq(cacheKey), eq(mockPrompts), any());
    }

    @Test
    void testGetPromptsByKeyword_OnlyU() {
        // Given: 只查询用户提示词
        String keyword = "test-keyword:U";
        String cacheKey = "bigmodel_prompts:" + keyword;
        
        when(cacheManager.get(cacheKey, List.class))
            .thenReturn(Optional.empty());
        when(bigmodelPromptsMapper.findUsrPromptsByKeyword("test-keyword"))
            .thenReturn(mockPrompts);

        // When: 调用服务方法
        List<String> result = bigmodelPromptsService.getPromptsByKeyword(keyword);

        // Then: 验证结果
        assertEquals(mockPrompts, result);
        verify(bigmodelPromptsMapper).findUsrPromptsByKeyword("test-keyword");
        verify(cacheManager).put(eq(cacheKey), eq(mockPrompts), any());
    }

    @Test
    void testGetPromptsByKeyword_CacheStorageException() {
        // Given: 缓存存储失败但不影响结果返回
        when(cacheManager.get(expectedCacheKey, List.class))
            .thenReturn(Optional.empty());
        when(bigmodelPromptsMapper.findSysPromptsByKeyword("test-keyword"))
            .thenReturn(mockPrompts);
        doThrow(new RuntimeException("缓存存储失败"))
            .when(cacheManager).put(anyString(), any(), any());

        // When: 调用服务方法
        List<String> result = bigmodelPromptsService.getPromptsByKeyword(testKeyword);

        // Then: 仍然能正常返回结果
        assertEquals(mockPrompts, result);
        verify(bigmodelPromptsMapper).findSysPromptsByKeyword("test-keyword");
    }

    @Test
    void testGetPromptsByKeyword_InvalidKeyword_Null() {
        // When & Then: 空关键词应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            bigmodelPromptsService.getPromptsByKeyword(null);
        });
    }

    @Test
    void testGetPromptsByKeyword_InvalidKeyword_Empty() {
        // When & Then: 空关键词应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            bigmodelPromptsService.getPromptsByKeyword("");
        });
    }

    @Test
    void testGetPromptsByKeyword_InvalidKeyword_NoColon() {
        // When & Then: 格式错误的关键词应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            bigmodelPromptsService.getPromptsByKeyword("test-keyword");
        });
    }

    @Test
    void testGetPromptsByKeyword_InvalidKeyword_InvalidType() {
        // When & Then: 无效类型的关键词应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            bigmodelPromptsService.getPromptsByKeyword("test-keyword:X");
        });
    }

    @Test
    void testGetPromptsByKeyword_DatabaseException() {
        // Given: 数据库查询失败
        when(cacheManager.get(expectedCacheKey, List.class))
            .thenReturn(Optional.empty());
        when(bigmodelPromptsMapper.findSysPromptsByKeyword("test-keyword"))
            .thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then: 应该抛出运行时异常
        assertThrows(RuntimeException.class, () -> {
            bigmodelPromptsService.getPromptsByKeyword(testKeyword);
        });
    }
}