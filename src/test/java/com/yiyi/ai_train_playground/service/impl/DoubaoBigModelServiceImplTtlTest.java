package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.config.DoubaoConfig;
import com.yiyi.ai_train_playground.model.ContextResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * DoubaoBigModelServiceImpl TTL配置测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-16
 */
@Slf4j
@SpringBootTest
public class DoubaoBigModelServiceImplTtlTest {
    
    @Autowired
    private DoubaoBigModelServiceImpl doubaoBigModelService;
    
    @Autowired
    private DoubaoConfig doubaoConfig;
    
    @Test
    public void testCtxExpireConfiguration() {
        log.info("测试ctx-expire配置");
        
        try {
            // 验证配置是否正确加载
            Integer ctxExpire = doubaoConfig.getCtxExpire();
            log.info("配置文件中的ctx-expire值: {}", ctxExpire);
            
            // 验证默认值
            assert ctxExpire != null : "ctx-expire配置不应为null";
            assert ctxExpire.equals(604800) : "ctx-expire默认值应为604800秒";
            
            log.info("✅ ctx-expire配置验证通过，值为: {} 秒（{} 天）", ctxExpire, ctxExpire / 86400);
            
        } catch (Exception e) {
            log.error("ctx-expire配置测试失败", e);
        }
    }
    
    @Test
    public void testGenerateContextIdWithTtl() {
        log.info("测试带TTL的ContextId生成");
        
        try {
            String testSystemPrompt = "这是一个测试系统提示词，用于验证TTL参数是否正确传递";
            
            log.info("开始生成ContextId，系统提示词: {}", testSystemPrompt);
            log.info("配置的TTL值: {} 秒", doubaoConfig.getCtxExpire());
            
            // 调用generateContextId方法
            ContextResult contextResult = doubaoBigModelService.generateContextId(testSystemPrompt);
            
            // 验证结果
            assert contextResult != null : "ContextResult不应为null";
            assert contextResult.getId() != null : "ContextId不应为null";
            assert contextResult.getTtl() != null : "TTL不应为null";
            
            log.info("✅ ContextId生成成功");
            log.info("ContextId: {}", contextResult.getId());
            log.info("Model: {}", contextResult.getModel());
            log.info("Mode: {}", contextResult.getMode());
            log.info("TTL: {} 秒", contextResult.getTtl());
            
            // 验证TTL值是否与配置一致
            Integer expectedTtl = doubaoConfig.getCtxExpire();
            Integer actualTtl = contextResult.getTtl();
            
            log.info("期望TTL: {}, 实际TTL: {}", expectedTtl, actualTtl);
            
            if (actualTtl.equals(expectedTtl)) {
                log.info("✅ TTL值与配置一致");
            } else {
                log.warn("⚠️ TTL值与配置不一致，可能是豆包API返回的默认值");
            }
            
        } catch (Exception e) {
            log.error("带TTL的ContextId生成测试失败", e);
            // 不抛出异常，因为可能是网络或API问题
        }
    }
    
    @Test
    public void testDoubaoConfigStructure() {
        log.info("测试DoubaoConfig配置结构");
        
        try {
            // 验证配置结构完整性
            assert doubaoConfig != null : "DoubaoConfig不应为null";
            assert doubaoConfig.getNormal() != null : "Normal配置不应为null";
            assert doubaoConfig.getNormal().getEndpoint() != null : "Endpoint配置不应为null";
            assert doubaoConfig.getNormal().getEndpoint().getName() != null : "Endpoint名称不应为null";
            assert doubaoConfig.getCtxExpire() != null : "CtxExpire配置不应为null";
            
            log.info("✅ DoubaoConfig配置结构验证通过");
            log.info("Normal Endpoint: {}", doubaoConfig.getNormal().getEndpoint().getName());
            log.info("Ctx Expire: {} 秒", doubaoConfig.getCtxExpire());
            
        } catch (Exception e) {
            log.error("DoubaoConfig配置结构测试失败", e);
        }
    }
}
