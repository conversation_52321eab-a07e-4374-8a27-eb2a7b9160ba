package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 豆包大模型超时配置测试
 * 验证修复后的超时配置是否正常工作
 */
@Slf4j
@SpringBootTest
public class DoubaoBigModelTimeoutTest {

    @Autowired
    private SuperBigModelInterface doubaoBigModelService;

    @Test
    public void testTimeoutConfiguration() {
        log.info("=== 豆包大模型超时配置测试开始 ===");
        log.info("测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        try {
            // 测试简单的调用，验证服务是否正常初始化
            String systemPrompt = "你是一个测试助手，请简短回复。";
            String userPrompt = "请说'测试成功'";
            
            log.info("开始调用豆包API，测试超时配置...");
            long startTime = System.currentTimeMillis();
            
            String response = doubaoBigModelService.ntnsOnce(systemPrompt, userPrompt);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("API调用成功！");
            log.info("响应内容: {}", response);
            log.info("调用耗时: {} ms", duration);
            
            // 验证响应不为空
            assert response != null && !response.trim().isEmpty() : "响应内容不能为空";
            
            log.info("=== 超时配置测试通过 ===");
            
        } catch (Exception e) {
            log.error("超时配置测试失败", e);
            throw e;
        }
    }

    @Test
    public void testLongRunningRequest() {
        log.info("=== 长时间运行请求测试开始 ===");
        
        try {
            // 构造一个可能需要较长时间处理的请求
            String systemPrompt = """
                你是一个详细的分析师，请对用户的问题进行深入分析。
                请提供详细、全面的回答，包含多个方面的考虑。
                """;
            
            String userPrompt = """
                请详细分析电商平台中客户服务的重要性，包括以下方面：
                1. 客户满意度的影响因素
                2. 客服响应时间的重要性
                3. 多渠道客服支持的优势
                4. 客服质量评估标准
                5. 客服培训的关键要素
                请每个方面都详细展开说明，提供具体的例子和建议。
                """;
            
            log.info("开始长时间请求测试...");
            long startTime = System.currentTimeMillis();
            
            String response = doubaoBigModelService.ntnsOnce(systemPrompt, userPrompt);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("长时间请求完成！");
            log.info("响应长度: {} 字符", response.length());
            log.info("调用耗时: {} ms ({} 秒)", duration, duration / 1000.0);
            
            // 验证响应不为空且有一定长度
            assert response != null && response.length() > 100 : "长时间请求应该返回较详细的内容";
            
            log.info("=== 长时间运行请求测试通过 ===");
            
        } catch (Exception e) {
            log.error("长时间运行请求测试失败", e);
            // 如果是超时异常，记录详细信息
            if (e.getMessage().contains("timeout") || e.getMessage().contains("超时")) {
                log.error("检测到超时异常，可能需要进一步调整超时配置");
            }
            throw e;
        }
    }
}
