package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.jd.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.jd.JdPrdListResponse;
import com.yiyi.ai_train_playground.mapper.jd.TrainJdProductsMapper;
import com.yiyi.ai_train_playground.service.jd.impl.TrainJdProductsServiceImpl;
import com.yiyi.ai_train_playground.vo.JdProductListVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 京东商品列表查询服务测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("京东商品列表查询服务测试")
public class JdProductListServiceTest {

    @Mock
    private TrainJdProductsMapper trainJdProductsMapper;

    @InjectMocks
    private TrainJdProductsServiceImpl trainJdProductsService;

    @Test
    @DisplayName("测试分页查询京东商品列表 - 成功")
    public void testFindJdProductList_Success() {
        // 准备测试数据
        Long teamId = 1L;
        JdPrdListRequest request = new JdPrdListRequest();
        request.setPage(1);
        request.setPageSize(10);
        
        List<JdProductListVO> mockVoList = createMockVoList();
        Long mockTotal = 2L;
        
        // 模拟Mapper方法
        when(trainJdProductsMapper.countJdProductList(eq(teamId), isNull()))
                .thenReturn(mockTotal);
        when(trainJdProductsMapper.findJdProductListWithPagination(eq(teamId), isNull(), eq(0), eq(10)))
                .thenReturn(mockVoList);
        
        // 执行测试
        JdPrdListResponse response = trainJdProductsService.findJdProductList(teamId, request);
        
        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getTotal()).isEqualTo(mockTotal);
        assertThat(response.getShopId()).isEqualTo(654321L);
        assertThat(response.getRows()).hasSize(2);
        assertThat(response.getRows().get(0).getId()).isEqualTo(1L);
        assertThat(response.getRows().get(0).getBrandName()).isEqualTo("小米");
        assertThat(response.getRows().get(0).getWareId()).isEqualTo(12345L);
        assertThat(response.getRows().get(0).getTitle()).isEqualTo("小米手环7");
        assertThat(response.getRows().get(0).getStatus()).isEqualTo("上架");
        assertThat(response.getRows().get(0).getLogo()).isEqualTo("https://img11.360buyimg.com/devfe/logo1.jpg");
        
        log.info("✅ 分页查询京东商品列表成功测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 带标题模糊查询")
    public void testFindJdProductList_WithTitle() {
        // 准备测试数据
        Long teamId = 1L;
        JdPrdListRequest request = new JdPrdListRequest();
        request.setSearchKeyword("小米手环");
        request.setPage(1);
        request.setPageSize(10);

        List<JdProductListVO> mockVoList = Collections.singletonList(createMockVoList().get(0));
        Long mockTotal = 1L;

        // 模拟Mapper方法
        when(trainJdProductsMapper.countJdProductList(eq(teamId), eq("小米手环")))
                .thenReturn(mockTotal);
        when(trainJdProductsMapper.findJdProductListWithPagination(eq(teamId), eq("小米手环"), eq(0), eq(10)))
                .thenReturn(mockVoList);
        
        // 执行测试
        JdPrdListResponse response = trainJdProductsService.findJdProductList(teamId, request);
        
        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getTotal()).isEqualTo(mockTotal);
        assertThat(response.getRows()).hasSize(1);
        assertThat(response.getRows().get(0).getTitle()).isEqualTo("小米手环7");
        
        log.info("✅ 带标题模糊查询测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 空结果")
    public void testFindJdProductList_EmptyResult() {
        // 准备测试数据
        Long teamId = 1L;
        JdPrdListRequest request = new JdPrdListRequest();
        request.setSearchKeyword("不存在的商品");
        request.setPage(1);
        request.setPageSize(10);

        // 模拟Mapper方法
        when(trainJdProductsMapper.countJdProductList(eq(teamId), eq("不存在的商品")))
                .thenReturn(0L);
        when(trainJdProductsMapper.findJdProductListWithPagination(eq(teamId), eq("不存在的商品"), eq(0), eq(10)))
                .thenReturn(Collections.emptyList());
        
        // 执行测试
        JdPrdListResponse response = trainJdProductsService.findJdProductList(teamId, request);
        
        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getTotal()).isEqualTo(0L);
        assertThat(response.getRows()).isEmpty();
        
        log.info("✅ 空结果测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 参数为空")
    public void testFindJdProductList_NullParams() {
        // 执行测试 - teamId为空
        JdPrdListResponse response1 = trainJdProductsService.findJdProductList(null, new JdPrdListRequest());
        
        // 验证结果
        assertThat(response1).isNotNull();
        assertThat(response1.getTotal()).isEqualTo(0L);
        assertThat(response1.getRows()).isEmpty();
        
        // 执行测试 - request为空
        JdPrdListResponse response2 = trainJdProductsService.findJdProductList(1L, null);
        
        // 验证结果
        assertThat(response2).isNotNull();
        assertThat(response2.getTotal()).isEqualTo(0L);
        assertThat(response2.getRows()).isEmpty();
        
        log.info("✅ 参数为空测试通过");
    }

    @Test
    @DisplayName("测试分页查询京东商品列表 - 数据库异常")
    public void testFindJdProductList_DatabaseException() {
        // 准备测试数据
        Long teamId = 1L;
        JdPrdListRequest request = new JdPrdListRequest();
        request.setPage(1);
        request.setPageSize(10);
        
        // 模拟Mapper方法抛出异常
        when(trainJdProductsMapper.countJdProductList(eq(teamId), isNull()))
                .thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            trainJdProductsService.findJdProductList(teamId, request);
        });
        
        assertThat(exception.getMessage()).contains("分页查询京东商品列表失败");
        assertThat(exception.getMessage()).contains("数据库连接失败");
        
        log.info("✅ 数据库异常测试通过");
    }

    /**
     * 创建模拟VO列表
     */
    private List<JdProductListVO> createMockVoList() {
        List<JdProductListVO> voList = new ArrayList<>();
        
        JdProductListVO vo1 = new JdProductListVO();
        vo1.setId(1L);
        vo1.setBrandId(1L);
        vo1.setWareId(12345L);
        vo1.setBrandName("小米");
        vo1.setShopId(654321L);
        vo1.setLogo("logo1.jpg");
        vo1.setTitle("小米手环7");
        vo1.setStatus(8); // 上架
        vo1.setOnlineTime(LocalDateTime.of(2022, 5, 1, 9, 0, 0));
        vo1.setOfflineTime(LocalDateTime.of(2023, 5, 1, 9, 0, 0));
        
        JdProductListVO vo2 = new JdProductListVO();
        vo2.setId(2L);
        vo2.setBrandId(2L);
        vo2.setWareId(67890L);
        vo2.setBrandName("华为");
        vo2.setShopId(654321L); // 使用相同的shopId
        vo2.setLogo("logo2.jpg");
        vo2.setTitle("华为手表GT3");
        vo2.setStatus(2); // 自主下架
        vo2.setOnlineTime(LocalDateTime.of(2022, 3, 1, 10, 0, 0));
        vo2.setOfflineTime(LocalDateTime.of(2022, 12, 1, 18, 0, 0));
        
        voList.add(vo1);
        voList.add(vo2);
        
        return voList;
    }
}
