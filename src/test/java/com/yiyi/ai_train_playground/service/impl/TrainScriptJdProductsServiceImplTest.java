package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainScriptJdProducts;
import com.yiyi.ai_train_playground.service.jd.TrainScriptJdProductsService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainScriptJdProductsService 测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-23
 */
@SpringBootTest
@Transactional
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class TrainScriptJdProductsServiceImplTest {
    
    @Autowired
    private TrainScriptJdProductsService trainScriptJdProductsService;
    
    private TrainScriptJdProducts createTestScriptJdProduct(Long teamId, Long scriptId, Long trJdSkuId) {
        TrainScriptJdProducts scriptJdProduct = new TrainScriptJdProducts();
        scriptJdProduct.setTeamId(teamId);
        scriptJdProduct.setScriptId(scriptId);
        scriptJdProduct.setTrJdSkuId(trJdSkuId);
        scriptJdProduct.setCreator("test_user");
        scriptJdProduct.setUpdater("test_user");
        scriptJdProduct.setVersion(0L);
        return scriptJdProduct;
    }
    
    @Test
    @Order(1)
    @DisplayName("测试插入剧本京东商品关联")
    public void testInsert() {
        System.out.println("=== 测试插入剧本京东商品关联 ===");
        
        TrainScriptJdProducts scriptJdProduct = createTestScriptJdProduct(1L, 1001L, 2001L);
        
        boolean result = trainScriptJdProductsService.insert(scriptJdProduct);
        
        assertTrue(result, "插入应该成功");
        assertNotNull(scriptJdProduct.getId(), "插入后应该生成ID");
        
        System.out.println("插入成功，关联ID: " + scriptJdProduct.getId());
        System.out.println("✅ 插入测试通过");
    }
    
    @Test
    @Order(2)
    @DisplayName("测试根据剧本ID查询关联")
    public void testSelectByScriptIdAndTeamId() {
        System.out.println("=== 测试根据剧本ID查询关联 ===");
        
        // 先插入几个关联记录
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1002L, 2001L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1002L, 2002L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1002L, 2003L));
        
        // 查询关联记录
        List<TrainScriptJdProducts> results = trainScriptJdProductsService.selectByScriptIdAndTeamId(1002L, 1L);
        
        assertTrue(results.size() >= 3, "应该至少有3个关联记录");
        
        // 验证所有记录都属于指定剧本和团队
        for (TrainScriptJdProducts item : results) {
            assertEquals(1002L, item.getScriptId());
            assertEquals(1L, item.getTeamId());
        }
        
        System.out.println("查询成功，关联数量: " + results.size());
        System.out.println("✅ 查询测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试查询京东SKU ID列表")
    public void testSelectJdSkuIdsByScriptIdAndTeamId() {
        System.out.println("=== 测试查询京东SKU ID列表 ===");
        
        // 先插入几个关联记录
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1003L, 3001L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1003L, 3002L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1003L, 3003L));
        
        // 查询SKU ID列表
        List<Long> skuIds = trainScriptJdProductsService.selectJdSkuIdsByScriptIdAndTeamId(1003L, 1L);
        
        assertTrue(skuIds.size() >= 3, "应该至少有3个SKU ID");
        assertTrue(skuIds.contains(3001L), "应该包含SKU ID 3001");
        assertTrue(skuIds.contains(3002L), "应该包含SKU ID 3002");
        assertTrue(skuIds.contains(3003L), "应该包含SKU ID 3003");
        
        System.out.println("查询SKU ID成功，数量: " + skuIds.size());
        System.out.println("✅ SKU ID查询测试通过");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试批量插入")
    public void testBatchInsert() {
        System.out.println("=== 测试批量插入 ===");
        
        List<TrainScriptJdProducts> scriptJdProductsList = Arrays.asList(
            createTestScriptJdProduct(1L, 1004L, 4001L),
            createTestScriptJdProduct(1L, 1004L, 4002L),
            createTestScriptJdProduct(1L, 1004L, 4003L)
        );
        
        int result = trainScriptJdProductsService.batchInsert(scriptJdProductsList);
        
        assertEquals(3, result, "应该插入3个关联记录");
        
        // 验证插入结果
        List<TrainScriptJdProducts> insertedRecords = trainScriptJdProductsService.selectByScriptIdAndTeamId(1004L, 1L);
        assertTrue(insertedRecords.size() >= 3, "应该至少有3个关联记录");
        
        System.out.println("批量插入成功，插入数量: " + result);
        System.out.println("✅ 批量插入测试通过");
    }
    
    @Test
    @Order(5)
    @DisplayName("测试根据ID查询")
    public void testSelectById() {
        System.out.println("=== 测试根据ID查询 ===");
        
        // 先插入一个关联记录
        TrainScriptJdProducts scriptJdProduct = createTestScriptJdProduct(1L, 1005L, 5001L);
        trainScriptJdProductsService.insert(scriptJdProduct);
        
        // 根据ID查询
        TrainScriptJdProducts foundRecord = trainScriptJdProductsService.selectById(scriptJdProduct.getId());
        
        assertNotNull(foundRecord, "应该能查询到记录");
        assertEquals(1L, foundRecord.getTeamId());
        assertEquals(1005L, foundRecord.getScriptId());
        assertEquals(5001L, foundRecord.getTrJdSkuId());
        
        System.out.println("根据ID查询成功，记录ID: " + foundRecord.getId());
        System.out.println("✅ ID查询测试通过");
    }
    
    @Test
    @Order(6)
    @DisplayName("测试根据ID更新")
    public void testUpdateById() {
        System.out.println("=== 测试根据ID更新 ===");
        
        // 先插入一个关联记录
        TrainScriptJdProducts scriptJdProduct = createTestScriptJdProduct(1L, 1006L, 6001L);
        trainScriptJdProductsService.insert(scriptJdProduct);
        
        // 更新记录
        scriptJdProduct.setTrJdSkuId(6002L);
        scriptJdProduct.setUpdater("updated_user");
        scriptJdProduct.setVersion(1L);
        
        boolean result = trainScriptJdProductsService.updateById(scriptJdProduct);
        
        assertTrue(result, "更新应该成功");
        
        // 验证更新结果
        TrainScriptJdProducts updatedRecord = trainScriptJdProductsService.selectById(scriptJdProduct.getId());
        assertEquals(6002L, updatedRecord.getTrJdSkuId());
        assertEquals("updated_user", updatedRecord.getUpdater());
        assertEquals(1L, updatedRecord.getVersion());
        
        System.out.println("更新成功，新SKU ID: " + updatedRecord.getTrJdSkuId());
        System.out.println("✅ 更新测试通过");
    }
    
    @Test
    @Order(7)
    @DisplayName("测试根据京东SKU ID查询关联的剧本")
    public void testSelectByJdSkuIdAndTeamId() {
        System.out.println("=== 测试根据京东SKU ID查询关联的剧本 ===");
        
        // 插入几个关联记录，使用相同的SKU ID
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1007L, 7001L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1008L, 7001L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1009L, 7001L));
        
        // 根据SKU ID查询关联的剧本
        List<TrainScriptJdProducts> results = trainScriptJdProductsService.selectByJdSkuIdAndTeamId(7001L, 1L);
        
        assertTrue(results.size() >= 3, "应该至少有3个关联记录");
        
        // 验证所有记录都使用相同的SKU ID
        for (TrainScriptJdProducts item : results) {
            assertEquals(7001L, item.getTrJdSkuId());
            assertEquals(1L, item.getTeamId());
        }
        
        System.out.println("根据SKU ID查询成功，关联剧本数量: " + results.size());
        System.out.println("✅ SKU ID关联查询测试通过");
    }
    
    @Test
    @Order(8)
    @DisplayName("测试删除剧本关联")
    public void testDeleteByScriptIdAndTeamId() {
        System.out.println("=== 测试删除剧本关联 ===");
        
        // 先插入几个关联记录
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1010L, 8001L));
        trainScriptJdProductsService.insert(createTestScriptJdProduct(1L, 1010L, 8002L));
        
        // 确认记录存在
        List<TrainScriptJdProducts> beforeDelete = trainScriptJdProductsService.selectByScriptIdAndTeamId(1010L, 1L);
        assertTrue(beforeDelete.size() >= 2, "删除前应该有关联记录");
        
        // 删除关联记录
        boolean result = trainScriptJdProductsService.deleteByScriptIdAndTeamId(1010L, 1L);
        
        assertTrue(result, "删除应该成功");
        
        // 确认记录已删除
        List<TrainScriptJdProducts> afterDelete = trainScriptJdProductsService.selectByScriptIdAndTeamId(1010L, 1L);
        assertEquals(0, afterDelete.size(), "删除后应该没有关联记录");
        
        System.out.println("删除成功");
        System.out.println("✅ 删除测试通过");
    }
    
    @Test
    @Order(9)
    @DisplayName("测试根据ID删除")
    public void testDeleteById() {
        System.out.println("=== 测试根据ID删除 ===");
        
        // 先插入一个关联记录
        TrainScriptJdProducts scriptJdProduct = createTestScriptJdProduct(1L, 1011L, 9001L);
        trainScriptJdProductsService.insert(scriptJdProduct);
        
        // 确认记录存在
        assertNotNull(trainScriptJdProductsService.selectById(scriptJdProduct.getId()));
        
        // 根据ID删除
        boolean result = trainScriptJdProductsService.deleteById(scriptJdProduct.getId());
        
        assertTrue(result, "删除应该成功");
        
        // 确认记录已删除
        assertNull(trainScriptJdProductsService.selectById(scriptJdProduct.getId()));
        
        System.out.println("根据ID删除成功");
        System.out.println("✅ ID删除测试通过");
    }
    
    @Test
    @Order(10)
    @DisplayName("测试批量删除")
    public void testBatchDeleteByIds() {
        System.out.println("=== 测试批量删除 ===");
        
        // 先插入几个关联记录
        TrainScriptJdProducts record1 = createTestScriptJdProduct(1L, 1012L, 10001L);
        TrainScriptJdProducts record2 = createTestScriptJdProduct(1L, 1012L, 10002L);
        TrainScriptJdProducts record3 = createTestScriptJdProduct(1L, 1012L, 10003L);
        
        trainScriptJdProductsService.insert(record1);
        trainScriptJdProductsService.insert(record2);
        trainScriptJdProductsService.insert(record3);
        
        List<Long> idsToDelete = Arrays.asList(record1.getId(), record2.getId(), record3.getId());
        
        // 批量删除
        boolean result = trainScriptJdProductsService.batchDeleteByIds(idsToDelete, 1L);
        
        assertTrue(result, "批量删除应该成功");
        
        // 确认记录已删除
        assertNull(trainScriptJdProductsService.selectById(record1.getId()));
        assertNull(trainScriptJdProductsService.selectById(record2.getId()));
        assertNull(trainScriptJdProductsService.selectById(record3.getId()));
        
        System.out.println("批量删除成功，删除数量: " + idsToDelete.size());
        System.out.println("✅ 批量删除测试通过");
    }
    
    @Test
    @Order(11)
    @DisplayName("测试参数验证")
    public void testParameterValidation() {
        System.out.println("=== 测试参数验证 ===");
        
        // 测试null参数
        assertFalse(trainScriptJdProductsService.insert(null));
        assertEquals(0, trainScriptJdProductsService.batchInsert(null));
        assertFalse(trainScriptJdProductsService.deleteByScriptIdAndTeamId(null, 1L));
        assertFalse(trainScriptJdProductsService.deleteByScriptIdAndTeamId(1L, null));
        assertTrue(trainScriptJdProductsService.selectByScriptIdAndTeamId(null, 1L).isEmpty());
        assertTrue(trainScriptJdProductsService.selectByScriptIdAndTeamId(1L, null).isEmpty());
        assertNull(trainScriptJdProductsService.selectById(null));
        assertFalse(trainScriptJdProductsService.updateById(null));
        assertFalse(trainScriptJdProductsService.deleteById(null));
        
        // 测试无效对象
        TrainScriptJdProducts invalidRecord = new TrainScriptJdProducts();
        assertFalse(trainScriptJdProductsService.insert(invalidRecord));
        
        invalidRecord.setId(999L);
        assertFalse(trainScriptJdProductsService.updateById(invalidRecord));
        
        System.out.println("✅ 参数验证测试通过");
    }
}
