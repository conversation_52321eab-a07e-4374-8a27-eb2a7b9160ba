package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.ScriptUpdateRequest;
import com.yiyi.ai_train_playground.dto.ProductListDTO;
import com.yiyi.ai_train_playground.dto.ScriptQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptGroupQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.service.TrainScriptService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 剧本创建服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainScriptServiceImplCreateTest {

    @Autowired
    private TrainScriptService trainScriptService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_CREATOR = "test_user";

    @Test
    public void testCreateScriptWithRelatedData_Success() {
        // 准备测试数据
        ScriptCreateRequest request = createTestScriptRequest();

        // 执行测试
        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertNotNull(scriptId);
        assertTrue(scriptId > 0);
        log.info("创建剧本成功，scriptId={}", scriptId);
    }

    @Test
    public void testCreateScriptWithRelatedData_NullRequest() {
        // 测试空请求
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(null, TEST_TEAM_ID, TEST_CREATOR);
        });
        assertEquals("请求参数不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_NullTeamId() {
        // 测试空团队ID
        ScriptCreateRequest request = createTestScriptRequest();
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, null, TEST_CREATOR);
        });
        assertEquals("团队ID不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_NullCreator() {
        // 测试空创建人
        ScriptCreateRequest request = createTestScriptRequest();
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, null);
        });
        assertEquals("创建人不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_EmptyCreator() {
        // 测试空字符串创建人
        ScriptCreateRequest request = createTestScriptRequest();
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, "");
        });
        assertEquals("创建人不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_EmptyName() {
        // 测试空剧本名称
        ScriptCreateRequest request = createTestScriptRequest();
        request.setName("");
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);
        });
        assertEquals("剧本名称不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_OnlyScript() {
        // 测试只创建剧本，不包含关联数据
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("测试剧本-仅主体");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("测试需求");
        request.setOrderPriority(3);
        request.setSimulationTool("Simulator_V3");
        request.setProdType(0); // 默认自主导入

        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId);
        assertTrue(scriptId > 0);
        log.info("创建纯剧本成功，scriptId={}", scriptId);
    }

    @Test
    public void testCreateScriptWithRelatedData_WithProducts() {
        // 测试创建剧本和商品
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("测试剧本-包含商品");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("测试需求");
        request.setOrderPriority(3);
        request.setSimulationTool("Simulator_V3");
        request.setProdType(0); // 默认自主导入

        // 添加商品列表
        List<ScriptCreateRequest.ProductCreateDTO> productList = Arrays.asList(
            createTestProduct("TEST001", "测试商品1"),
            createTestProduct("TEST002", "测试商品2")
        );
        request.setProductList(productList);

        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId);
        assertTrue(scriptId > 0);
        log.info("创建剧本和商品成功，scriptId={}", scriptId);
    }

    @Test
    public void testBatchDeleteScripts_Success() {
        // 准备测试数据 - 先创建一些剧本
        ScriptCreateRequest request1 = new ScriptCreateRequest();
        request1.setName("测试剧本-批量删除1");
        request1.setGenerationTypeCode(0);
        request1.setGroupId(13L);
        request1.setIntentId(5L);
        request1.setEvaluationPlanId(2001L);
        request1.setBuyerRequirement("测试需求1");
        request1.setOrderPriority(3);
        request1.setSimulationTool("Simulator_V3");
        request1.setProdType(0); // 默认自主导入

        ScriptCreateRequest request2 = new ScriptCreateRequest();
        request2.setName("测试剧本-批量删除2");
        request2.setGenerationTypeCode(0);
        request2.setGroupId(13L);
        request2.setIntentId(5L);
        request2.setEvaluationPlanId(2001L);
        request2.setBuyerRequirement("测试需求2");
        request2.setOrderPriority(3);
        request2.setSimulationTool("Simulator_V3");
        request2.setProdType(0); // 默认自主导入

        Long scriptId1 = trainScriptService.createScriptWithRelatedData(request1, TEST_TEAM_ID, TEST_CREATOR);
        Long scriptId2 = trainScriptService.createScriptWithRelatedData(request2, TEST_TEAM_ID, TEST_CREATOR);

        // 执行批量删除测试
        String ids = scriptId1 + "," + scriptId2;
        boolean result = trainScriptService.batchDeleteScripts(ids, TEST_TEAM_ID);

        // 验证结果
        assertTrue(result);
        log.info("批量删除剧本测试通过：ids={}, teamId={}", ids, TEST_TEAM_ID);
    }

    @Test
    public void testBatchDeleteScripts_EmptyIds() {
        // 准备测试数据
        String ids = "";
        Long teamId = TEST_TEAM_ID;

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts(ids, teamId);
        });

        // 验证异常信息
        assertEquals("剧本ID列表不能为空", exception.getMessage());
        log.info("空ID列表异常测试通过");
    }

    @Test
    public void testBatchDeleteScripts_InvalidIds() {
        // 准备测试数据
        String ids = "1,abc,3";
        Long teamId = TEST_TEAM_ID;

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts(ids, teamId);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("剧本ID格式不正确"));
        log.info("无效ID格式异常测试通过");
    }

    @Test
    public void testBatchDeleteScripts_NullTeamId() {
        // 准备测试数据
        String ids = "1,2,3";
        Long teamId = null;

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts(ids, teamId);
        });

        // 验证异常信息
        assertEquals("团队ID不能为空", exception.getMessage());
        log.info("空团队ID异常测试通过");
    }

    /**
     * 创建测试用的剧本请求
     */
    private ScriptCreateRequest createTestScriptRequest() {
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("数码相机选购指南：全画幅VS微单");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("摄影爱好者，预算1万元，主要拍摄人像和风景");
        request.setOrderIsRemarked(null);
        request.setOrderPriority(3);
        request.setOrderRemark("优先处理：客户对专业术语有疑问");
        request.setSimulationTool("Simulator_V3");
        request.setProdType(0); // 默认自主导入

        // 商品列表
        List<ScriptCreateRequest.ProductCreateDTO> productList = Arrays.asList(
            createTestProduct("838591834200", "无线蓝牙耳机1"),
            createTestProduct("838591834201", "无线蓝牙耳机2"),
            createTestProduct("838591834202", "无线蓝牙耳机3")
        );
        request.setProductList(productList);

        // 关联图片
        List<ScriptCreateRequest.RelatedImageCreateDTO> relateImgs = Arrays.asList(
            createTestImage(1, 1, "剧本封面图", "https://cdn.example.com/scripts/1/cover.jpg"),
            createTestImage(2, 1, "宣传视频", "https://cdn.example.com/videos/1/promo.mp4"),
            createTestImage(1, 2, "道具设计图", "https://design-storage.com/props/1/sketch.jpg")
        );
        request.setRelateImgs(relateImgs);

        // 流程节点
        List<ScriptCreateRequest.FlowNodeCreateDTO> flowNodes = Arrays.asList(
            createTestFlowNode("开场导入", "主持人需在5分钟内完成背景介绍，营造悬疑氛围"),
            createTestFlowNode("第一轮搜证", "每位玩家限时15分钟搜查3个场景"),
            createTestFlowNode("最终推理", "要求主持人控制讨论节奏")
        );
        request.setFlowNodes(flowNodes);

        return request;
    }

    /**
     * 创建测试商品
     */
    private ScriptCreateRequest.ProductCreateDTO createTestProduct(String externalId, String name) {
        ScriptCreateRequest.ProductCreateDTO product = new ScriptCreateRequest.ProductCreateDTO();
        product.setExternalProductId(externalId);
        product.setExternalProductName(name);
        product.setExternalProductLink("https://shop.com/" + externalId);
        product.setExternalProductImage("https://img.com/" + externalId + ".jpg");
        return product;
    }

    /**
     * 创建测试图片
     */
    private ScriptCreateRequest.RelatedImageCreateDTO createTestImage(Integer mediaType, Integer uploadType, String text, String url) {
        ScriptCreateRequest.RelatedImageCreateDTO image = new ScriptCreateRequest.RelatedImageCreateDTO();
        image.setMediaType(mediaType);
        image.setUploadType(uploadType);
        image.setRecognizedText(text);
        image.setUrl(url);
        return image;
    }

    /**
     * 创建测试流程节点
     */
    private ScriptCreateRequest.FlowNodeCreateDTO createTestFlowNode(String nodeName, String requirement) {
        ScriptCreateRequest.FlowNodeCreateDTO flowNode = new ScriptCreateRequest.FlowNodeCreateDTO();
        flowNode.setNodeName(nodeName);
        flowNode.setNodeBuyerRequirement(requirement);
        return flowNode;
    }

    /**
     * 测试创建JD商品类型的剧本
     */
    @Test
    @Order(2)
    void testCreateScriptWithJdProducts() {
        // 创建JD商品类型的剧本请求
        ScriptCreateRequest request = createTestJdScriptRequest();

        // 执行创建
        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertNotNull(scriptId);
        assertTrue(scriptId > 0);

        log.info("JD商品剧本创建成功，scriptId: {}", scriptId);
    }

    /**
     * 创建测试用的JD商品剧本请求
     */
    private ScriptCreateRequest createTestJdScriptRequest() {
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("JD商品测试剧本");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("JD商品测试需求");
        request.setOrderPriority(3);
        request.setOrderRemark("JD商品测试备注");
        request.setSimulationTool("Simulator_V3");
        request.setProdType(1); // JD商品类型

        // JD商品列表（使用数字ID）
        List<ScriptCreateRequest.ProductCreateDTO> productList = Arrays.asList(
            createTestProduct("100012345678", "JD测试商品1"),
            createTestProduct("100012345679", "JD测试商品2")
        );
        request.setProductList(productList);

        return request;
    }

    /**
     * 测试查询JD商品类型的剧本详情
     */
    @Test
    @Order(3)
    void testGetScriptDetailByType() {
        // 先创建一个JD商品类型的剧本
        ScriptCreateRequest request = createTestJdScriptRequest();
        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId);
        assertTrue(scriptId > 0);

        // 查询剧本详情
        ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(scriptId, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(scriptDetail);
        assertEquals(scriptId, scriptDetail.getId());
        assertEquals("JD商品测试剧本", scriptDetail.getName());

        // 验证商品列表
        assertNotNull(scriptDetail.getProductList());
        assertEquals(2, scriptDetail.getProductList().size());

        // 验证第一个商品
        ProductListDTO product1 = scriptDetail.getProductList().get(0);
        assertEquals("100012345678", product1.getExternalProductId());
        assertEquals("JD商品-100012345678", product1.getExternalProductName());
        assertEquals("https://item.jd.com/100012345678.html", product1.getExternalProductLink());

        log.info("JD商品剧本详情查询成功，scriptId: {}, 商品数量: {}", scriptId, scriptDetail.getProductList().size());
    }

    /**
     * 测试查询传统商品类型的剧本详情
     */
    @Test
    @Order(4)
    void testGetScriptDetailByTypeTraditional() {
        // 先创建一个传统商品类型的剧本
        ScriptCreateRequest request = createTestScriptRequest();
        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId);
        assertTrue(scriptId > 0);

        // 查询剧本详情
        ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(scriptId, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(scriptDetail);
        assertEquals(scriptId, scriptDetail.getId());
        assertEquals("数码相机选购指南：全画幅VS微单", scriptDetail.getName());

        // 验证商品列表
        assertNotNull(scriptDetail.getProductList());
        assertEquals(3, scriptDetail.getProductList().size());

        // 验证第一个商品（应该是传统商品）
        ProductListDTO product1 = scriptDetail.getProductList().get(0);
        assertNotNull(product1.getExternalProductId());
        assertNotNull(product1.getExternalProductName());

        log.info("传统商品剧本详情查询成功，scriptId: {}, 商品数量: {}", scriptId, scriptDetail.getProductList().size());
    }

    /**
     * 测试getScriptDetailByType方法返回的prodType字段
     */
    @Test
    public void testGetScriptDetailByType_ProdTypeField() {
        log.info("=== 测试getScriptDetailByType方法返回的prodType字段 ===");

        // 测试自主导入商品类型 (prodType = 0)
        ScriptCreateRequest selfImportRequest = new ScriptCreateRequest();
        selfImportRequest.setName("自主导入商品测试剧本");
        selfImportRequest.setGenerationTypeCode(0);
        selfImportRequest.setGroupId(13L);
        selfImportRequest.setIntentId(5L);
        selfImportRequest.setEvaluationPlanId(2001L);
        selfImportRequest.setBuyerRequirement("自主导入商品测试需求");
        selfImportRequest.setOrderPriority(3);
        selfImportRequest.setSimulationTool("Simulator_V3");
        selfImportRequest.setProdType(0); // 自主导入商品

        Long selfImportScriptId = trainScriptService.createScriptWithRelatedData(selfImportRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(selfImportScriptId);

        // 查询自主导入商品剧本详情
        ScriptDetailDTO selfImportDetail = trainScriptService.getScriptDetailByType(selfImportScriptId, TEST_TEAM_ID);
        assertNotNull(selfImportDetail);
        assertEquals(Integer.valueOf(0), selfImportDetail.getProdType());
        log.info("自主导入商品剧本详情查询成功，prodType={}", selfImportDetail.getProdType());

        // 测试JD商品类型 (prodType = 1)
        ScriptCreateRequest jdRequest = createTestJdScriptRequest();
        Long jdScriptId = trainScriptService.createScriptWithRelatedData(jdRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(jdScriptId);

        // 查询JD商品剧本详情
        ScriptDetailDTO jdDetail = trainScriptService.getScriptDetailByType(jdScriptId, TEST_TEAM_ID);
        assertNotNull(jdDetail);
        assertEquals(Integer.valueOf(1), jdDetail.getProdType());
        log.info("JD商品剧本详情查询成功，prodType={}", jdDetail.getProdType());

        log.info("✅ prodType字段测试通过");
    }

    /**
     * 测试getScriptDetail方法返回的prodType字段
     */
    @Test
    public void testGetScriptDetail_ProdTypeField() {
        log.info("=== 测试getScriptDetail方法返回的prodType字段 ===");

        // 创建自主导入商品类型的剧本
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("普通剧本详情测试");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("普通剧本详情测试需求");
        request.setOrderPriority(3);
        request.setSimulationTool("Simulator_V3");
        request.setProdType(0); // 自主导入商品

        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId);

        // 查询剧本详情
        ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetail(scriptId, TEST_TEAM_ID);
        assertNotNull(scriptDetail);
        assertEquals(Integer.valueOf(0), scriptDetail.getProdType());
        log.info("普通剧本详情查询成功，prodType={}", scriptDetail.getProdType());

        log.info("✅ getScriptDetail方法prodType字段测试通过");
    }

    /**
     * 测试更新传统商品类型剧本（prodType=0）
     */
    @Test
    public void testUpdateScript_TraditionalProducts() {
        log.info("=== 测试更新传统商品类型剧本 ===");

        // 1. 先创建一个传统商品类型的剧本
        ScriptCreateRequest createRequest = new ScriptCreateRequest();
        createRequest.setName("传统商品剧本");
        createRequest.setGenerationTypeCode(0);
        createRequest.setGroupId(13L);
        createRequest.setIntentId(5L);
        createRequest.setEvaluationPlanId(2001L);
        createRequest.setBuyerRequirement("传统商品需求");
        createRequest.setOrderPriority(3);
        createRequest.setSimulationTool("Simulator_V3");
        createRequest.setProdType(0); // 传统商品

        // 添加商品列表
        List<ScriptCreateRequest.ProductCreateDTO> productList = new ArrayList<>();
        ScriptCreateRequest.ProductCreateDTO product = new ScriptCreateRequest.ProductCreateDTO();
        product.setExternalProductId("PROD001");
        product.setExternalProductName("传统商品1");
        product.setExternalProductLink("http://example.com/prod1");
        product.setExternalProductImage("http://example.com/img1.jpg");
        productList.add(product);
        createRequest.setProductList(productList);

        Long scriptId = trainScriptService.createScriptWithRelatedData(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId);

        // 2. 准备更新请求
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(scriptId);
        updateRequest.setName("更新后的传统商品剧本");
        updateRequest.setGenerationTypeCode(0);
        updateRequest.setGroupId(13L);
        updateRequest.setIntentId(5L);
        updateRequest.setEvaluationPlanId(2001L);
        updateRequest.setBuyerRequirement("更新后的传统商品需求");
        updateRequest.setOrderPriority(3);
        updateRequest.setSimulationTool("Simulator_V3");
        updateRequest.setProdType(0); // 保持传统商品类型
        updateRequest.setVersion(1L);
        updateRequest.setIsOfficial(false);

        // 更新商品列表
        List<ScriptUpdateRequest.ProductUpdateDTO> updateProductList = new ArrayList<>();
        ScriptUpdateRequest.ProductUpdateDTO updateProduct = new ScriptUpdateRequest.ProductUpdateDTO();
        updateProduct.setExternalProductId("PROD002");
        updateProduct.setExternalProductName("更新后的传统商品");
        updateProduct.setExternalProductLink("http://example.com/prod2");
        updateProduct.setExternalProductImage("http://example.com/img2.jpg");
        updateProductList.add(updateProduct);
        updateRequest.setProductList(updateProductList);

        // 3. 执行更新
        boolean result = trainScriptService.updateScriptWithRelatedData(updateRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertTrue(result);

        // 4. 验证更新结果
        ScriptDetailDTO updatedDetail = trainScriptService.getScriptDetail(scriptId, TEST_TEAM_ID);
        assertNotNull(updatedDetail);
        assertEquals("更新后的传统商品剧本", updatedDetail.getName());
        assertEquals(Integer.valueOf(0), updatedDetail.getProdType());
        assertEquals(1, updatedDetail.getProductList().size());
        assertEquals("PROD002", updatedDetail.getProductList().get(0).getExternalProductId());

        log.info("✅ 传统商品类型剧本更新测试通过");
    }

    /**
     * 测试更新JD商品类型剧本（prodType=1）
     */
    @Test
    public void testUpdateScript_JdProducts() {
        log.info("=== 测试更新JD商品类型剧本 ===");

        // 1. 先创建一个JD商品类型的剧本
        ScriptCreateRequest createRequest = createTestJdScriptRequest();
        Long scriptId = trainScriptService.createScriptWithRelatedData(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId);

        // 2. 准备更新请求
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(scriptId);
        updateRequest.setName("更新后的JD商品剧本");
        updateRequest.setGenerationTypeCode(0);
        updateRequest.setGroupId(13L);
        updateRequest.setIntentId(5L);
        updateRequest.setEvaluationPlanId(2001L);
        updateRequest.setBuyerRequirement("更新后的JD商品需求");
        updateRequest.setOrderPriority(3);
        updateRequest.setSimulationTool("Simulator_V3");
        updateRequest.setProdType(1); // 保持JD商品类型
        updateRequest.setVersion(1L);
        updateRequest.setIsOfficial(false);

        // 更新JD商品列表
        List<ScriptUpdateRequest.ProductUpdateDTO> updateProductList = new ArrayList<>();
        ScriptUpdateRequest.ProductUpdateDTO updateProduct = new ScriptUpdateRequest.ProductUpdateDTO();
        updateProduct.setExternalProductId("100099887766"); // 新的JD SKU ID
        updateProduct.setExternalProductName("更新后的JD商品");
        updateProduct.setExternalProductLink("https://item.jd.com/100099887766.html");
        updateProduct.setExternalProductImage("https://img11.360buyimg.com/devfe/jfs/t1/updated.jpg");
        updateProductList.add(updateProduct);
        updateRequest.setProductList(updateProductList);

        // 3. 执行更新
        boolean result = trainScriptService.updateScriptWithRelatedData(updateRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertTrue(result);

        // 4. 验证更新结果
        ScriptDetailDTO updatedDetail = trainScriptService.getScriptDetailByType(scriptId, TEST_TEAM_ID);
        assertNotNull(updatedDetail);
        assertEquals("更新后的JD商品剧本", updatedDetail.getName());
        assertEquals(Integer.valueOf(1), updatedDetail.getProdType());
        // 注意：JD商品类型的剧本，商品信息存储在train_script_jd_products表中
        // 这里我们主要验证prodType字段和基本信息的更新
        log.info("JD商品剧本更新成功，prodType={}", updatedDetail.getProdType());

        log.info("✅ JD商品类型剧本更新测试通过");
    }

    /**
     * 测试groupId为1时存储为NULL，查询时返回1的逻辑
     */
    @Test
    public void testGroupIdHandling() {
        log.info("=== 测试groupId处理逻辑 ===");

        // 1. 测试创建剧本时groupId为1的情况
        ScriptCreateRequest createRequest = new ScriptCreateRequest();
        createRequest.setName("测试groupId处理-创建");
        createRequest.setGenerationTypeCode(0);
        createRequest.setGroupId(1L); // 设置为1，应该存储为NULL
        createRequest.setIntentId(5L);
        createRequest.setEvaluationPlanId(2001L);
        createRequest.setBuyerRequirement("测试groupId处理需求");
        createRequest.setOrderPriority(3);
        createRequest.setSimulationTool("Simulator_V3");
        createRequest.setProdType(0);

        Long scriptId = trainScriptService.createScriptWithRelatedData(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId);
        log.info("创建剧本成功，scriptId={}", scriptId);

        // 2. 查询剧本详情，验证groupId返回为1
        ScriptDetailDTO scriptDetail = trainScriptService.getScriptDetailByType(scriptId, TEST_TEAM_ID);
        assertNotNull(scriptDetail);
        assertEquals(Long.valueOf(1L), scriptDetail.getGroupId());
        log.info("查询剧本详情成功，groupId={}", scriptDetail.getGroupId());

        // 3. 测试更新剧本时groupId为1的情况
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(scriptId);
        updateRequest.setName("测试groupId处理-更新");
        updateRequest.setGenerationTypeCode(0);
        updateRequest.setGroupId(1L); // 设置为1，应该存储为NULL
        updateRequest.setIntentId(5L);
        updateRequest.setEvaluationPlanId(2001L);
        updateRequest.setBuyerRequirement("更新后的groupId处理需求");
        updateRequest.setOrderPriority(3);
        updateRequest.setSimulationTool("Simulator_V3");
        updateRequest.setProdType(0);
        updateRequest.setVersion(1L);
        updateRequest.setIsOfficial(false);

        boolean updateResult = trainScriptService.updateScriptWithRelatedData(updateRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertTrue(updateResult);
        log.info("更新剧本成功");

        // 4. 再次查询剧本详情，验证groupId仍然返回为1
        ScriptDetailDTO updatedDetail = trainScriptService.getScriptDetailByType(scriptId, TEST_TEAM_ID);
        assertNotNull(updatedDetail);
        assertEquals(Long.valueOf(1L), updatedDetail.getGroupId());
        assertEquals("测试groupId处理-更新", updatedDetail.getName());
        log.info("更新后查询剧本详情成功，groupId={}", updatedDetail.getGroupId());

        // 5. 测试创建和更新时groupId不为1的情况
        ScriptCreateRequest createRequest2 = new ScriptCreateRequest();
        createRequest2.setName("测试groupId处理-非1值");
        createRequest2.setGenerationTypeCode(0);
        createRequest2.setGroupId(13L); // 设置为非1值，应该正常存储
        createRequest2.setIntentId(5L);
        createRequest2.setEvaluationPlanId(2001L);
        createRequest2.setBuyerRequirement("测试非1值groupId需求");
        createRequest2.setOrderPriority(3);
        createRequest2.setSimulationTool("Simulator_V3");
        createRequest2.setProdType(0);

        Long scriptId2 = trainScriptService.createScriptWithRelatedData(createRequest2, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId2);

        ScriptDetailDTO scriptDetail2 = trainScriptService.getScriptDetailByType(scriptId2, TEST_TEAM_ID);
        assertNotNull(scriptDetail2);
        assertEquals(Long.valueOf(13L), scriptDetail2.getGroupId());
        log.info("非1值groupId测试成功，groupId={}", scriptDetail2.getGroupId());

        log.info("✅ groupId处理逻辑测试通过");
    }

    /**
     * 测试列表查询中groupId的处理逻辑
     */
    @Test
    public void testGroupIdHandlingInList() {
        log.info("=== 测试列表查询中groupId处理逻辑 ===");

        // 1. 创建一个groupId为1的剧本
        ScriptCreateRequest createRequest1 = new ScriptCreateRequest();
        createRequest1.setName("列表测试-groupId为1");
        createRequest1.setGenerationTypeCode(0);
        createRequest1.setGroupId(1L); // 设置为1，应该存储为NULL
        createRequest1.setIntentId(5L);
        createRequest1.setEvaluationPlanId(2001L);
        createRequest1.setBuyerRequirement("列表测试需求1");
        createRequest1.setOrderPriority(3);
        createRequest1.setSimulationTool("Simulator_V3");
        createRequest1.setProdType(0);

        Long scriptId1 = trainScriptService.createScriptWithRelatedData(createRequest1, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId1);

        // 2. 创建一个groupId为13的剧本
        ScriptCreateRequest createRequest2 = new ScriptCreateRequest();
        createRequest2.setName("列表测试-groupId为13");
        createRequest2.setGenerationTypeCode(0);
        createRequest2.setGroupId(13L);
        createRequest2.setIntentId(5L);
        createRequest2.setEvaluationPlanId(2001L);
        createRequest2.setBuyerRequirement("列表测试需求2");
        createRequest2.setOrderPriority(3);
        createRequest2.setSimulationTool("Simulator_V3");
        createRequest2.setProdType(0);

        Long scriptId2 = trainScriptService.createScriptWithRelatedData(createRequest2, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId2);

        // 3. 查询列表，验证groupId字段
        ScriptQueryRequest queryRequest = new ScriptQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);

        PageResult<ScriptListDTO> pageResult = trainScriptService.getScriptList(queryRequest, TEST_TEAM_ID);
        assertNotNull(pageResult);
        assertTrue(pageResult.getTotal() >= 2);

        // 4. 验证列表中的groupId字段
        boolean foundGroupId1 = false;
        boolean foundGroupId13 = false;

        for (ScriptListDTO script : pageResult.getRecords()) {
            if (script.getId().equals(scriptId1)) {
                assertEquals(Long.valueOf(1L), script.getGroupId());
                foundGroupId1 = true;
                log.info("找到groupId为1的剧本：id={}, groupId={}", script.getId(), script.getGroupId());
            } else if (script.getId().equals(scriptId2)) {
                assertEquals(Long.valueOf(13L), script.getGroupId());
                foundGroupId13 = true;
                log.info("找到groupId为13的剧本：id={}, groupId={}", script.getId(), script.getGroupId());
            }
        }

        assertTrue(foundGroupId1, "应该找到groupId为1的剧本");
        assertTrue(foundGroupId13, "应该找到groupId为13的剧本");

        log.info("✅ 列表查询中groupId处理逻辑测试通过");
    }

    /**
     * 测试根据分组ID查询剧本列表的逻辑
     */
    @Test
    public void testGetScriptListByGroup() {
        log.info("=== 测试根据分组ID查询剧本列表 ===");

        // 1. 创建不同groupId的剧本用于测试
        ScriptCreateRequest createRequest1 = new ScriptCreateRequest();
        createRequest1.setName("分组测试-默认分组");
        createRequest1.setGenerationTypeCode(0);
        createRequest1.setGroupId(1L); // 将存储为NULL
        createRequest1.setIntentId(5L);
        createRequest1.setEvaluationPlanId(2001L);
        createRequest1.setBuyerRequirement("默认分组测试需求");
        createRequest1.setOrderPriority(3);
        createRequest1.setSimulationTool("Simulator_V3");
        createRequest1.setProdType(0);

        Long scriptId1 = trainScriptService.createScriptWithRelatedData(createRequest1, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId1);

        ScriptCreateRequest createRequest2 = new ScriptCreateRequest();
        createRequest2.setName("分组测试-分组13");
        createRequest2.setGenerationTypeCode(0);
        createRequest2.setGroupId(13L);
        createRequest2.setIntentId(5L);
        createRequest2.setEvaluationPlanId(2001L);
        createRequest2.setBuyerRequirement("分组13测试需求");
        createRequest2.setOrderPriority(3);
        createRequest2.setSimulationTool("Simulator_V3");
        createRequest2.setProdType(0);

        Long scriptId2 = trainScriptService.createScriptWithRelatedData(createRequest2, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId2);

        ScriptCreateRequest createRequest3 = new ScriptCreateRequest();
        createRequest3.setName("分组测试-分组25");
        createRequest3.setGenerationTypeCode(0);
        createRequest3.setGroupId(25L);
        createRequest3.setIntentId(5L);
        createRequest3.setEvaluationPlanId(2001L);
        createRequest3.setBuyerRequirement("分组25测试需求");
        createRequest3.setOrderPriority(3);
        createRequest3.setSimulationTool("Simulator_V3");
        createRequest3.setProdType(0);

        Long scriptId3 = trainScriptService.createScriptWithRelatedData(createRequest3, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId3);

        // 2. 测试查询所有分组 (scriptGroupIds = "-1")
        ScriptGroupQueryRequest queryRequest1 = new ScriptGroupQueryRequest();
        queryRequest1.setScriptGroupIds("-1");
        queryRequest1.setPage(1);
        queryRequest1.setPageSize(20);

        PageResult<ScriptListDTO> result1 = trainScriptService.getScriptListByGroup(queryRequest1, TEST_TEAM_ID);
        assertNotNull(result1);
        assertTrue(result1.getTotal() >= 3);
        log.info("查询所有分组成功，总数：{}", result1.getTotal());

        // 3. 测试查询默认分组 (scriptGroupIds = "1"，对应group_id为NULL)
        ScriptGroupQueryRequest queryRequest2 = new ScriptGroupQueryRequest();
        queryRequest2.setScriptGroupIds("1");
        queryRequest2.setPage(1);
        queryRequest2.setPageSize(10);

        PageResult<ScriptListDTO> result2 = trainScriptService.getScriptListByGroup(queryRequest2, TEST_TEAM_ID);
        assertNotNull(result2);
        assertTrue(result2.getTotal() >= 1);

        // 验证返回的记录中包含我们创建的默认分组剧本
        boolean foundDefaultGroup = result2.getRecords().stream()
                .anyMatch(script -> script.getId().equals(scriptId1) && script.getGroupId().equals(1L));
        assertTrue(foundDefaultGroup, "应该找到默认分组的剧本");
        log.info("查询默认分组成功，总数：{}", result2.getTotal());

        // 4. 测试查询指定分组 (scriptGroupIds = "13")
        ScriptGroupQueryRequest queryRequest3 = new ScriptGroupQueryRequest();
        queryRequest3.setScriptGroupIds("13");
        queryRequest3.setPage(1);
        queryRequest3.setPageSize(10);

        PageResult<ScriptListDTO> result3 = trainScriptService.getScriptListByGroup(queryRequest3, TEST_TEAM_ID);
        assertNotNull(result3);
        assertTrue(result3.getTotal() >= 1);

        // 验证返回的记录中包含我们创建的分组13剧本
        boolean foundGroup13 = result3.getRecords().stream()
                .anyMatch(script -> script.getId().equals(scriptId2) && script.getGroupId().equals(13L));
        assertTrue(foundGroup13, "应该找到分组13的剧本");
        log.info("查询分组13成功，总数：{}", result3.getTotal());

        // 5. 测试查询指定分组 (scriptGroupIds = "25")
        ScriptGroupQueryRequest queryRequest4 = new ScriptGroupQueryRequest();
        queryRequest4.setScriptGroupIds("25");
        queryRequest4.setPage(1);
        queryRequest4.setPageSize(10);

        PageResult<ScriptListDTO> result4 = trainScriptService.getScriptListByGroup(queryRequest4, TEST_TEAM_ID);
        assertNotNull(result4);
        assertTrue(result4.getTotal() >= 1);

        // 验证返回的记录中包含我们创建的分组25剧本
        boolean foundGroup25 = result4.getRecords().stream()
                .anyMatch(script -> script.getId().equals(scriptId3) && script.getGroupId().equals(25L));
        assertTrue(foundGroup25, "应该找到分组25的剧本");
        log.info("查询分组25成功，总数：{}", result4.getTotal());

        // 6. 测试查询多个分组 (scriptGroupIds = "13,25")
        ScriptGroupQueryRequest queryRequest5 = new ScriptGroupQueryRequest();
        queryRequest5.setScriptGroupIds("13,25");
        queryRequest5.setPage(1);
        queryRequest5.setPageSize(10);

        PageResult<ScriptListDTO> result5 = trainScriptService.getScriptListByGroup(queryRequest5, 1L);
        assertNotNull(result5);
        assertTrue(result5.getTotal() >= 2); // 应该包含分组13和25的记录

        log.info("查询多个分组(13,25)成功，总数：{}", result5.getTotal());

        // 7. 测试查询包含默认分组的多个分组 (scriptGroupIds = "1,13")
        ScriptGroupQueryRequest queryRequest6 = new ScriptGroupQueryRequest();
        queryRequest6.setScriptGroupIds("1,13");
        queryRequest6.setPage(1);
        queryRequest6.setPageSize(20);

        PageResult<ScriptListDTO> result6 = trainScriptService.getScriptListByGroup(queryRequest6, 1L);
        assertNotNull(result6);
        assertTrue(result6.getTotal() >= 4); // 应该包含默认分组和分组13的记录

        log.info("查询包含默认分组的多个分组(1,13)成功，总数：{}", result6.getTotal());

        log.info("✅ 根据分组ID查询剧本列表测试通过");
    }

    /**
     * 测试修复bug：当输入scriptGroupIds=31,25时，不应该查询出group_id为null的记录
     */
    @Test
    public void testBugFixScriptGroupIds31And25() {
        log.info("=== 测试bug修复：scriptGroupIds=31,25不应该查询出未分组记录 ===");

        // 1. 创建一个未分组的剧本 (group_id为NULL，对应前端显示为groupId=1)
        ScriptCreateRequest createRequest1 = new ScriptCreateRequest();
        createRequest1.setName("Bug测试-未分组剧本");
        createRequest1.setGenerationTypeCode(0);
        createRequest1.setGroupId(1L); // 将存储为NULL
        createRequest1.setIntentId(5L);
        createRequest1.setEvaluationPlanId(2001L);
        createRequest1.setBuyerRequirement("未分组测试需求");
        createRequest1.setOrderPriority(3);
        createRequest1.setSimulationTool("Simulator_V3");
        createRequest1.setProdType(0);

        Long scriptId1 = trainScriptService.createScriptWithRelatedData(createRequest1, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId1);
        log.info("创建未分组剧本成功，scriptId={}", scriptId1);

        // 2. 创建一个分组31的剧本
        ScriptCreateRequest createRequest2 = new ScriptCreateRequest();
        createRequest2.setName("Bug测试-分组31剧本");
        createRequest2.setGenerationTypeCode(0);
        createRequest2.setGroupId(31L);
        createRequest2.setIntentId(5L);
        createRequest2.setEvaluationPlanId(2001L);
        createRequest2.setBuyerRequirement("分组31测试需求");
        createRequest2.setOrderPriority(3);
        createRequest2.setSimulationTool("Simulator_V3");
        createRequest2.setProdType(0);

        Long scriptId2 = trainScriptService.createScriptWithRelatedData(createRequest2, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId2);
        log.info("创建分组31剧本成功，scriptId={}", scriptId2);

        // 3. 创建一个分组25的剧本
        ScriptCreateRequest createRequest3 = new ScriptCreateRequest();
        createRequest3.setName("Bug测试-分组25剧本");
        createRequest3.setGenerationTypeCode(0);
        createRequest3.setGroupId(25L);
        createRequest3.setIntentId(5L);
        createRequest3.setEvaluationPlanId(2001L);
        createRequest3.setBuyerRequirement("分组25测试需求");
        createRequest3.setOrderPriority(3);
        createRequest3.setSimulationTool("Simulator_V3");
        createRequest3.setProdType(0);

        Long scriptId3 = trainScriptService.createScriptWithRelatedData(createRequest3, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(scriptId3);
        log.info("创建分组25剧本成功，scriptId={}", scriptId3);

        // 4. 测试查询scriptGroupIds=31,25，应该只返回分组31和25的记录，不包含未分组的记录
        ScriptGroupQueryRequest queryRequest = new ScriptGroupQueryRequest();
        queryRequest.setScriptGroupIds("31,25");
        queryRequest.setPage(1);
        queryRequest.setPageSize(20);

        PageResult<ScriptListDTO> result = trainScriptService.getScriptListByGroup(queryRequest, TEST_TEAM_ID);
        assertNotNull(result);
        log.info("查询scriptGroupIds=31,25的结果，总数：{}", result.getTotal());

        // 5. 验证结果：应该包含分组31和25的记录，但不应该包含未分组的记录
        boolean foundGroup31 = false;
        boolean foundGroup25 = false;
        boolean foundUnGrouped = false;

        for (ScriptListDTO script : result.getRecords()) {
            log.info("查询结果记录：id={}, name={}, groupId={}", script.getId(), script.getName(), script.getGroupId());

            if (script.getId().equals(scriptId1) && script.getGroupId().equals(1L)) {
                foundUnGrouped = true;
                log.warn("❌ 发现了未分组的记录，这是bug！id={}, groupId={}", script.getId(), script.getGroupId());
            } else if (script.getId().equals(scriptId2) && script.getGroupId().equals(31L)) {
                foundGroup31 = true;
                log.info("✅ 找到分组31的记录：id={}, groupId={}", script.getId(), script.getGroupId());
            } else if (script.getId().equals(scriptId3) && script.getGroupId().equals(25L)) {
                foundGroup25 = true;
                log.info("✅ 找到分组25的记录：id={}, groupId={}", script.getId(), script.getGroupId());
            }
        }

        // 6. 断言验证
        assertFalse(foundUnGrouped, "查询scriptGroupIds=31,25时不应该包含未分组的记录");
        assertTrue(foundGroup31, "应该找到分组31的记录");
        assertTrue(foundGroup25, "应该找到分组25的记录");

        log.info("✅ Bug修复测试通过：scriptGroupIds=31,25只查询指定分组，不包含未分组记录");
    }
}
