package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.service.jd.GetTdFromJsonService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * GetTdFromJsonService的单元测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest(properties = {
//    "jd.json.file.path=src/test/resources/test_bot_product.json"
    "jd.json.file.path=C:/Users/<USER>/Downloads/temp/bot_product.json"
})
@ActiveProfiles("test")
class GetTdFromJsonServiceImplTest {

    @Autowired
    private GetTdFromJsonService getTdFromJsonService;
    
    @Test
    @DisplayName("测试从JSON文件读取并转换为TrainJdProducts列表")
    void testGetTrainJdProductsFromJson() {
        // 执行测试
        List<TrainJdProducts> result = getTdFromJsonService.getTrainJdProductsFromJson();
        
        // 验证结果
        assertThat(result).isNotNull();
        log.info("成功读取并转换了{}条TrainJdProducts记录", result.size());
        
        // 如果有数据，验证第一条记录的基本字段
        if (!result.isEmpty()) {
            TrainJdProducts firstProduct = result.get(0);
            assertThat(firstProduct).isNotNull();
            log.info("第一条记录信息 - wareId: {}, title: {}, brandName: {}", 
                    firstProduct.getWareId(), firstProduct.getTitle(), firstProduct.getBrandName());

            for(TrainJdProducts product : result){
                System.out.println(product);

                // 验证日期字段是否被正确解析
                log.info("日期字段信息 - created: {}, modified: {}, onlineTime: {}, offlineTime: {}",
                        product.getCreated(), product.getModified(),
                        product.getOnlineTime(), product.getOfflineTime());
            }
        }
    }

    @Test
    @DisplayName("测试convertMapToTrainJdProducts方法 - 验证日期字段解析")
    void testConvertMapToTrainJdProducts_DateFields() throws Exception {
        // 准备测试数据
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("ware_id", 12345L);
        rawData.put("title", "测试商品");
        rawData.put("brand_name", "测试品牌");

        // 添加日期字段
        rawData.put("created", "2025-01-15 10:30:00");
        rawData.put("modified", "2025-01-16 15:45:30");
        rawData.put("online_time", "2025-01-10 09:00:00");
        rawData.put("offline_time", "2025-12-31 23:59:59");

        // 获取GetTdFromJsonServiceImpl实例
        GetTdFromJsonServiceImpl serviceImpl = (GetTdFromJsonServiceImpl) getTdFromJsonService;

        // 使用反射调用私有方法
        Method method = GetTdFromJsonServiceImpl.class.getDeclaredMethod("convertMapToTrainJdProducts", Map.class);
        method.setAccessible(true);
        TrainJdProducts result = (TrainJdProducts) method.invoke(serviceImpl, rawData);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getWareId()).isEqualTo(12345L);
        assertThat(result.getTitle()).isEqualTo("测试商品");
        assertThat(result.getBrandName()).isEqualTo("测试品牌");

        // 验证日期字段不为null
        assertThat(result.getCreated()).isNotNull();
        assertThat(result.getModified()).isNotNull();
        assertThat(result.getOnlineTime()).isNotNull();
        assertThat(result.getOfflineTime()).isNotNull();

        // 验证具体日期值
        assertThat(result.getCreated()).isEqualTo(LocalDateTime.of(2025, 1, 15, 10, 30, 0));
        assertThat(result.getModified()).isEqualTo(LocalDateTime.of(2025, 1, 16, 15, 45, 30));
        assertThat(result.getOnlineTime()).isEqualTo(LocalDateTime.of(2025, 1, 10, 9, 0, 0));
        assertThat(result.getOfflineTime()).isEqualTo(LocalDateTime.of(2025, 12, 31, 23, 59, 59));

        log.info("日期字段解析测试通过 - created: {}, modified: {}, onlineTime: {}, offlineTime: {}",
                result.getCreated(), result.getModified(),
                result.getOnlineTime(), result.getOfflineTime());
    }
}
