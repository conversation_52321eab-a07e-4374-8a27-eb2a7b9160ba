package com.yiyi.ai_train_playground.service.jd.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 京东商品同步服务JSON转换功能测试
 * 专注测试JSON转换逻辑，不依赖京东SDK
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@ActiveProfiles("test")
public class JdProductSyncServiceJsonTest {
    
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }
    
    @Test
    void testJsonConversionLogic() {
        // 测试JSON转换的核心逻辑
        try {
            // 准备测试数据 - 模拟features字段
            Set<Object> featuresSet = new HashSet<>();
            MockFeature feature1 = new MockFeature("feature1", new String[]{"value1", "value2"}, "特色服务1");
            MockFeature feature2 = new MockFeature("feature2", new String[]{"value3"}, "特色服务2");
            featuresSet.add(feature1);
            featuresSet.add(feature2);
            
            // 模拟转换逻辑
            List<Map<String, Object>> jsonList = new ArrayList<>();
            for (Object item : featuresSet) {
                Map<String, Object> featureMap = new HashMap<>();
                featureMap.put("featureKey", getFieldAsString(item, "featureKey"));
                featureMap.put("featureValue", getFieldValue(item, "featureValue"));
                featureMap.put("featureCn", getFieldAsString(item, "featureCn"));
                jsonList.add(featureMap);
            }
            
            String result = objectMapper.writeValueAsString(jsonList);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains("feature1"));
            assertTrue(result.contains("特色服务1"));
            
            // 验证JSON格式正确
            List<?> parsedList = objectMapper.readValue(result, List.class);
            assertEquals(2, parsedList.size());
            
        } catch (Exception e) {
            fail("JSON转换测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testMultiCatePropsJsonConversion() {
        try {
            // 准备测试数据 - 模拟multiCateProps字段
            Set<Object> mcpSet = new HashSet<>();
            MockMultiCateProp mcp1 = new MockMultiCateProp("233846", new String[]{"1335194"}, new String[]{"QC协议"}, null, null);
            MockMultiCateProp mcp2 = new MockMultiCateProp("233847", new String[]{"1335202"}, new String[]{"氮化镓"}, null, null);
            mcpSet.add(mcp1);
            mcpSet.add(mcp2);
            
            // 模拟转换逻辑
            List<Map<String, Object>> jsonList = new ArrayList<>();
            for (Object item : mcpSet) {
                Map<String, Object> mcpMap = new HashMap<>();
                mcpMap.put("attrId", getFieldAsString(item, "attrId"));
                mcpMap.put("attrValues", getFieldValue(item, "attrValues"));
                mcpMap.put("attrValueAlias", getFieldValue(item, "attrValueAlias"));
                mcpMap.put("expands", getFieldValue(item, "expands"));
                mcpMap.put("units", getFieldAsString(item, "units"));
                jsonList.add(mcpMap);
            }
            
            String result = objectMapper.writeValueAsString(jsonList);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains("233846"));
            assertTrue(result.contains("QC协议"));
            assertTrue(result.contains("氮化镓"));
            
            // 验证JSON格式正确
            List<?> parsedList = objectMapper.readValue(result, List.class);
            assertEquals(2, parsedList.size());
            
            // 验证JSON结构
            @SuppressWarnings("unchecked")
            Map<String, Object> firstItem = (Map<String, Object>) parsedList.get(0);
            assertTrue(firstItem.containsKey("attrId"));
            assertTrue(firstItem.containsKey("attrValues"));
            assertTrue(firstItem.containsKey("attrValueAlias"));
            
        } catch (Exception e) {
            fail("MultiCateProps JSON转换测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testExpectedJsonFormat() {
        // 测试期望的JSON格式
        try {
            // 创建期望的JSON结构
            List<Map<String, Object>> expectedMcp = new ArrayList<>();
            Map<String, Object> mcpItem1 = new HashMap<>();
            mcpItem1.put("attrId", "233846");
            mcpItem1.put("attrValues", Arrays.asList("1335194"));
            mcpItem1.put("attrValueAlias", Arrays.asList("QC协议"));
            expectedMcp.add(mcpItem1);
            
            Map<String, Object> mcpItem2 = new HashMap<>();
            mcpItem2.put("attrId", "233847");
            mcpItem2.put("attrValues", Arrays.asList("1335202"));
            mcpItem2.put("attrValueAlias", Arrays.asList("氮化镓"));
            expectedMcp.add(mcpItem2);
            
            String expectedJson = objectMapper.writeValueAsString(expectedMcp);
            
            // 验证JSON格式符合要求
            assertNotNull(expectedJson);
            assertTrue(expectedJson.contains("\"attrId\":\"233846\""));
            assertTrue(expectedJson.contains("\"attrValues\":[\"1335194\"]"));
            assertTrue(expectedJson.contains("\"attrValueAlias\":[\"QC协议\"]"));
            
            System.out.println("期望的JSON格式示例:");
            System.out.println(expectedJson);
            
        } catch (Exception e) {
            fail("期望JSON格式测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testNullHandling() {
        // 测试null值处理
        try {
            List<Map<String, Object>> jsonList = new ArrayList<>();
            
            // 添加包含null值的项
            Map<String, Object> itemWithNulls = new HashMap<>();
            itemWithNulls.put("attrId", "123");
            itemWithNulls.put("attrValues", null);
            itemWithNulls.put("attrValueAlias", null);
            jsonList.add(itemWithNulls);
            
            String result = objectMapper.writeValueAsString(jsonList);
            
            assertNotNull(result);
            assertTrue(result.contains("\"attrId\":\"123\""));
            
        } catch (Exception e) {
            fail("null值处理测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 从对象中获取字段值
     */
    private Object getFieldValue(Object obj, String fieldName) {
        if (obj == null) {
            return null;
        }
        
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 从对象中获取字段值并转换为字符串
     */
    private String getFieldAsString(Object obj, String fieldName) {
        Object value = getFieldValue(obj, fieldName);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 模拟Feature对象
     */
    static class MockFeature {
        private String featureKey;
        private String[] featureValue;
        private String featureCn;
        
        public MockFeature(String featureKey, String[] featureValue, String featureCn) {
            this.featureKey = featureKey;
            this.featureValue = featureValue;
            this.featureCn = featureCn;
        }
        
        public String getFeatureKey() { return featureKey; }
        public String[] getFeatureValue() { return featureValue; }
        public String getFeatureCn() { return featureCn; }
    }
    
    /**
     * 模拟MultiCateProp对象
     */
    static class MockMultiCateProp {
        private String attrId;
        private String[] attrValues;
        private String[] attrValueAlias;
        private String[] expands;
        private String units;
        
        public MockMultiCateProp(String attrId, String[] attrValues, String[] attrValueAlias, String[] expands, String units) {
            this.attrId = attrId;
            this.attrValues = attrValues;
            this.attrValueAlias = attrValueAlias;
            this.expands = expands;
            this.units = units;
        }
        
        public String getAttrId() { return attrId; }
        public String[] getAttrValues() { return attrValues; }
        public String[] getAttrValueAlias() { return attrValueAlias; }
        public String[] getExpands() { return expands; }
        public String getUnits() { return units; }
    }
} 