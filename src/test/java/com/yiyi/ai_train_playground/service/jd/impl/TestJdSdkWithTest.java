package com.yiyi.ai_train_playground.service.jd.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.mall.ProductWrapService.response.get.ProductBase;
import com.jd.open.api.sdk.request.mall.NewWareBaseproductGetRequest;
import com.jd.open.api.sdk.request.ware.SkuReadSearchSkuListRequest;
import com.jd.open.api.sdk.request.ware.VcItemShopProductsSearchRequest;
import com.jd.open.api.sdk.request.ware.WareReadSearchWare4ValidRequest;
import com.jd.open.api.sdk.response.mall.NewWareBaseproductGetResponse;
import com.jd.open.api.sdk.response.ware.SkuReadSearchSkuListResponse;
import com.jd.open.api.sdk.response.ware.VcItemShopProductsSearchResponse;
import com.jd.open.api.sdk.response.ware.WareReadSearchWare4ValidResponse;
import org.junit.jupiter.api.Test;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;

public class TestJdSdkWithTest {
    static String  SERVER_URL = "https://api.jd.com/routerjson";
    static String appKey = "58EC57CE1D6C6B997519BA25E73A7228";
    static String appSecret = "dbff743f33fd4a46bfa3399cf189e252";
    static String accessToken = "89fd9dcc03d34c6d997fc66e019700bcy2mw";
    public static JdClient client=new DefaultJdClient(SERVER_URL,accessToken,appKey,appSecret);
    @Test
    public void test() {
        VcItemShopProductsSearchRequest request=new VcItemShopProductsSearchRequest();
        request.setOrderType(1);
        request.setProductId("100095480830,100087424857");
        request.setCreatedEndTime("2024-10-01 00:00:00");
        request.setModifiedStartTime("2024-10-01 00:00:00");
        request.setSkuStatus(1);
        request.setPageSize(30);
        request.setPageNum(1);
        request.setThirdCategoryId(887);
        request.setRootCategoryId(884);
        request.setSkuName("测试商品");
        request.setLastCategoryId(88412);
        request.setCreatedStartTime("2024-10-01 00:00:00");
        request.setBrandId(1878);
        request.setSecondCategoryId(7897);
        request.setModifiedEndTime("2024-10-01 00:00:00");
        VcItemShopProductsSearchResponse response= null;
        try {
            response =client.execute(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(response);
    }


    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void listPopProductsFromLf() throws Exception {
        /*String accessToken = "4c2ec54b108a402ca6161b5861ba127fwm1n";
        String appKey = "FFF33471617F20A58443C04AB2C8E915";
        String appSecret = "80ed03eb8bbf46a6a800ddd6e8cdff5f";*/

        DefaultJdClient defaultJdClient = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret, 0, 0);
        WareReadSearchWare4ValidRequest request = new WareReadSearchWare4ValidRequest();
        request.setField("brandId,brandName,categoryId,categorySecId,colType,costPrice,created,height,jdPrice,length,logo,marketPrice,modified,offlineTime,shopId,spuId,stockNum,templateId,title,wareId,features,multiCateProps,wareStatus,weight,width,wrap,wareLocation,introduction,mobileDesc,fitCaseHtmlApp");
//        request.setField("wareId,title,wareStatus,offlineTime,onlineTime,categoryId,transportId");
        request.setPageNo(1);
        request.setPageSize(10);
        WareReadSearchWare4ValidResponse response = defaultJdClient.execute(request);
        System.out.println(objectMapper.writeValueAsString(response.getPage().getData()));

        /*response.getPage().getData().forEach(t -> {
            try {
                System.out.println(objectMapper.writeValueAsString(t));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });*/
    }


    @Test
    public void searchSkuList() throws Exception {
//        String accessToken = "1485fb0e5de44eeeb637e70562d2782d4nwz";
        DefaultJdClient defaultJdClient = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret, 0, 0);
        SkuReadSearchSkuListRequest request = new SkuReadSearchSkuListRequest();
//        request.setWareId("10023728190109");
//        request.setSkuStatuValue("1");
        request.setField("barCode,categoryId,created,enable,jdPrice,logo,modified,skuId,skuName,status,stockNum,wareId,wareTitle");
        request.setPageNo(1);
        request.setPageSize(50);
        SkuReadSearchSkuListResponse response = defaultJdClient.execute(request);
        System.out.println(objectMapper.writeValueAsString(response.getPage().getData()));
        // response.getPage().getData();

        // {
        //     "barCode": "6932554407339",
        //     "categoryId": 655,
        //     "created": "2025-04-02 14:37:35",
        //     "enable": 1,
        //     "jdPrice": 1799.0,
        //     "logo": "jfs/t1/283487/5/25903/65575/681969a7F746cb66b/4de88f6e4e97fe44.jpg",
        //     "modified": "2025-05-13 09:37:16",
        //     "skuId": 10147019836845,
        //     "skuName": "小米（MI）Redmi 红米Turbo4 国家补贴 新品5G 小米红米5G手机 暗影黑 12GB+256GB 【官方标配】",
        //     "status": 1,
        //     "stockNum": 20,
        //     "wareId": 10028797145338,
        //     "wareTitle": "小米（MI）Redmi 红米Turbo4 国家补贴 新品5G 小米红米5G手机"
        // },
    }

    @Test
    public void getPopProducts() throws Exception {
//        String accessToken = "1485fb0e5de44eeeb637e70562d2782d4nwz";
        DefaultJdClient defaultJdClient = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret, 0, 0);
        NewWareBaseproductGetRequest request = new NewWareBaseproductGetRequest();
        request.setIds("10028834871455");
        request.setBasefields("color,packSpecification,shopCategorys,imagePath,sizeSequence,venderId,shopName,saleDate,colorSequence,saleUnit,cbrand,skuMark,productArea,valuePayFirst,cid2,model,state,valueWeight,skuId,height,maxPurchQty,safeDays,isDelete,pname,length,upc,weight,wserve,url,barCode,allnum,ebrand,site,size,issn,phone,brandId,name,width,erpPid,category,venderType,wareLocation");
        NewWareBaseproductGetResponse response = defaultJdClient.execute(request);
        System.out.println(objectMapper.writeValueAsString(response));
        //find the entity on the head of the Class
        List<ProductBase> result = response.getListproductbaseResult();
        result.forEach(t -> {
            try {
                System.out.println(objectMapper.writeValueAsString(t));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
    }

  /*  @Test
    public void getPopProducts() throws Exception {
        String accessToken = "1485fb0e5de44eeeb637e70562d2782d4nwz";
        DefaultJdClient defaultJdClient = new DefaultJdClient(SERVER_URL, accessToken, APP_ID_BOT, APP_SECRET_BOT, 0, 0);
        NewWareBaseproductGetRequest request = new NewWareBaseproductGetRequest();
        request.setIds("10028834871455");
        request.setBasefields("color,packSpecification,shopCategorys,imagePath,sizeSequence,venderId,shopName,saleDate,colorSequence,saleUnit,cbrand,skuMark,productArea,valuePayFirst,cid2,model,state,valueWeight,skuId,height,maxPurchQty,safeDays,isDelete,pname,length,upc,weight,wserve,url,barCode,allnum,ebrand,site,size,issn,phone,brandId,name,width,erpPid,category,venderType,wareLocation");
        NewWareBaseproductGetResponse response = defaultJdClient.execute(request);
        System.out.println(JSONObject.toJSONString(response));
        //find the entity on the head of the Class
        List<ProductBase> result = response.getListproductbaseResult();
        result.forEach(t -> {
            System.out.println(JSONObject.toJSONString(t));
        });
    }*/




}
