package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * YiYiJdService 单元测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
class YiYiJdServiceTest {

    @Autowired
    private YiYiJdService yiYiJdService;

    @Test
    void testGetWare4ValidProductList_WithParams() {
        // 测试带参数的方法
        List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList(1, 5);
        
        assertNotNull(result, "返回结果不能为空");
        
        // 验证返回的商品列表
        if (!result.isEmpty()) {
            System.out.println("✅ 京东商品接口调用成功，返回 " + result.size() + " 个商品");
            // 验证第一个商品的基本信息
            TrainJdProducts firstProduct = result.get(0);
            assertNotNull(firstProduct.getWareId(), "商品ID不能为空");
            assertNotNull(firstProduct.getTitle(), "商品标题不能为空");
            System.out.println("第一个商品: " + firstProduct.getTitle());
        } else {
            System.out.println("⚠️ 返回了空的商品列表（可能是网络或认证问题）");
        }
    }

    @Test
    void testGetWare4ValidProductList_NoParams() {
        // 测试无参数的方法（使用默认值）
        List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList();
        
        assertNotNull(result, "返回结果不能为空");
        
        // 基本验证
        if (!result.isEmpty()) {
            // 如果调用成功，验证基本结构
            System.out.println("接口调用成功，返回 " + result.size() + " 个商品");
            // 验证商品基本信息
            for (TrainJdProducts product : result) {
                assertNotNull(product.getWareId(), "商品ID不能为空");
                assertNotNull(product.getTitle(), "商品标题不能为空");
            }
        } else {
            System.out.println("接口调用返回空列表（这是正常的，因为可能网络或认证问题）");
        }
    }

    @Test 
    void testServiceNotNull() {
        // 验证服务注入正常
        assertNotNull(yiYiJdService, "YiYiJdService注入不能为空");
        assertTrue(yiYiJdService instanceof YiYiJdServiceImpl, "应该注入YiYiJdServiceImpl实例");
    }
} 