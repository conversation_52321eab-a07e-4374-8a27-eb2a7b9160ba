package com.yiyi.ai_train_playground.service.kb.impl;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatDtl;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatDtlService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模拟聊天室窗口明细表 Service测试类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("company")
@Transactional
public class TrainConvWinchatDtlServiceImplTest {

    @Autowired
    private TrainConvWinchatDtlService trainConvWinchatDtlService;

    private final Long TEAM_ID = 1L;
    private final Long CONV_WINCHAT_MAIN_ID = 1L;
    private final Long TASK_ID = 1L;
    private final String CREATOR = "test_user";

    @Test
    public void testCreateWinchatDtl() {
        // 生成唯一的sessionId
        String sessionId = "session_" + UUID.randomUUID().toString();
        
        // 测试创建聊天窗口明细记录
        TrainConvWinchatDtl result = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(CONV_WINCHAT_MAIN_ID, result.getConvWinchatMainId());
        assertEquals(TASK_ID, result.getTaskId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(TEAM_ID, result.getTeamId());
        assertEquals(CREATOR, result.getCreator());
        assertEquals(CREATOR, result.getUpdater());
        assertEquals(0L, result.getVersion());
        
        log.info("创建聊天窗口明细记录测试通过, ID: {}, sessionId: {}", result.getId(), sessionId);
    }

    @Test
    public void testGetById() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        assertNotNull(created.getId());
        
        // 测试根据ID查询
        TrainConvWinchatDtl result = trainConvWinchatDtlService.getById(created.getId());
        
        assertNotNull(result);
        assertEquals(created.getId(), result.getId());
        assertEquals(CONV_WINCHAT_MAIN_ID, result.getConvWinchatMainId());
        assertEquals(TASK_ID, result.getTaskId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(TEAM_ID, result.getTeamId());
        
        log.info("根据ID查询聊天窗口明细记录测试通过, ID: {}", result.getId());
    }

    @Test
    public void testGetBySessionId() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 测试根据sessionId查询
        TrainConvWinchatDtl result = trainConvWinchatDtlService.getBySessionId(sessionId);
        
        assertNotNull(result);
        assertEquals(created.getId(), result.getId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(CONV_WINCHAT_MAIN_ID, result.getConvWinchatMainId());
        assertEquals(TASK_ID, result.getTaskId());
        
        log.info("根据sessionId查询聊天窗口明细记录测试通过, sessionId: {}", sessionId);
    }

    @Test
    public void testGetBySessionIdNotFound() {
        // 测试查询不存在的sessionId
        TrainConvWinchatDtl result = trainConvWinchatDtlService.getBySessionId("non_existing_session_id");
        assertNull(result);
        
        log.info("查询不存在sessionId测试通过");
    }

    @Test
    public void testUpdateWinchatDtl() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 修改记录
        created.setTaskId(2L);
        created.setF1stRawChatlog("原始聊天记录内容");
        created.setUpdater("updated_user");
        
        // 测试更新
        boolean result = trainConvWinchatDtlService.updateWinchatDtl(created);
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatDtl updated = trainConvWinchatDtlService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(2L, updated.getTaskId());
        assertEquals("原始聊天记录内容", updated.getF1stRawChatlog());
        assertEquals("updated_user", updated.getUpdater());
        
        log.info("更新聊天窗口明细记录测试通过, ID: {}", created.getId());
    }

    @Test
    public void testDeleteById() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        Long createdId = created.getId();
        
        // 测试删除
        boolean result = trainConvWinchatDtlService.deleteById(createdId);
        assertTrue(result);
        
        // 验证删除结果
        TrainConvWinchatDtl deleted = trainConvWinchatDtlService.getById(createdId);
        assertNull(deleted);
        
        log.info("删除聊天窗口明细记录测试通过, ID: {}", createdId);
    }

    @Test
    public void testGetByConvWinchatMainId() {
        // 先创建两条记录
        String sessionId1 = "session_" + UUID.randomUUID().toString();
        String sessionId2 = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl record1 = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId1, TEAM_ID, CREATOR);
        TrainConvWinchatDtl record2 = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, 2L, sessionId2, TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        
        // 测试查询
        List<TrainConvWinchatDtl> result = trainConvWinchatDtlService.getByConvWinchatMainId(CONV_WINCHAT_MAIN_ID, TEAM_ID);
        
        assertNotNull(result);
        assertTrue(result.size() >= 2);
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record2.getId())));
        
        log.info("根据聊天窗口主表ID查询测试通过, 结果数量: {}", result.size());
    }

    @Test
    public void testGetByTaskId() {
        // 先创建两条记录
        String sessionId1 = "session_" + UUID.randomUUID().toString();
        String sessionId2 = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl record1 = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId1, TEAM_ID, CREATOR);
        TrainConvWinchatDtl record2 = trainConvWinchatDtlService.createWinchatDtl(2L, TASK_ID, sessionId2, TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        
        // 测试查询
        List<TrainConvWinchatDtl> result = trainConvWinchatDtlService.getByTaskId(TASK_ID, TEAM_ID);
        
        assertNotNull(result);
        assertTrue(result.size() >= 2);
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record2.getId())));
        
        log.info("根据任务ID查询测试通过, 结果数量: {}", result.size());
    }

    @Test
    public void testGetByConditions() {
        // 先创建几条记录
        String sessionId1 = "session_" + UUID.randomUUID().toString();
        String sessionId2 = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl record1 = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId1, TEAM_ID, CREATOR);
        TrainConvWinchatDtl record2 = trainConvWinchatDtlService.createWinchatDtl(2L, 2L, sessionId2, TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        
        // 测试按条件查询 - 只指定团队ID
        List<TrainConvWinchatDtl> allResults = trainConvWinchatDtlService.getByConditions(null, null, TEAM_ID);
        assertNotNull(allResults);
        assertTrue(allResults.size() >= 2);
        
        // 测试按条件查询 - 指定聊天窗口主表ID
        List<TrainConvWinchatDtl> mainResults = trainConvWinchatDtlService.getByConditions(CONV_WINCHAT_MAIN_ID, null, TEAM_ID);
        assertNotNull(mainResults);
        assertTrue(mainResults.size() >= 1);
        assertTrue(mainResults.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        
        // 测试按条件查询 - 指定任务ID
        List<TrainConvWinchatDtl> taskResults = trainConvWinchatDtlService.getByConditions(null, TASK_ID, TEAM_ID);
        assertNotNull(taskResults);
        assertTrue(taskResults.size() >= 1);
        
        log.info("根据条件查询测试通过, 全部结果: {}, 主表结果: {}, 任务结果: {}", 
            allResults.size(), mainResults.size(), taskResults.size());
    }

    @Test
    public void testUpdateRawChatlog() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 测试更新原始聊天记录
        String rawChatlog = "这是原始聊天记录内容";
        boolean result = trainConvWinchatDtlService.updateRawChatlog(created.getId(), rawChatlog, TEAM_ID, "updater");
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatDtl updated = trainConvWinchatDtlService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(rawChatlog, updated.getF1stRawChatlog());
        
        log.info("更新原始聊天记录测试通过, ID: {}", created.getId());
    }

    @Test
    public void testUpdateSysPrompt() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 测试更新系统提示词
        String sysPrompt = "这是系统提示词内容";
        boolean result = trainConvWinchatDtlService.updateSysPrompt(created.getId(), sysPrompt, TEAM_ID, "updater");
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatDtl updated = trainConvWinchatDtlService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(sysPrompt, updated.getS2ndAggSysPrompt());
        
        log.info("更新系统提示词测试通过, ID: {}", created.getId());
    }

    @Test
    public void testUpdateRewrite() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 测试更新重写后的聊天记录
        String rewrite = "这是重写后的聊天记录内容";
        boolean result = trainConvWinchatDtlService.updateRewrite(created.getId(), rewrite, TEAM_ID, "updater");
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatDtl updated = trainConvWinchatDtlService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(rewrite, updated.getT3rdRewrite());
        
        log.info("更新重写后聊天记录测试通过, ID: {}", created.getId());
    }

    @Test
    public void testUpdateFinalSysPrompt() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);

        // 测试更新最终系统提示词
        String finalSysPrompt = "这是最终系统提示词内容";
        boolean result = trainConvWinchatDtlService.updateFinalSysPrompt(created.getId(), finalSysPrompt, TEAM_ID, "updater");
        assertTrue(result);

        // 验证更新结果
        TrainConvWinchatDtl updated = trainConvWinchatDtlService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(finalSysPrompt, updated.getF4thFinalSysPrompt());

        log.info("更新最终系统提示词测试通过, ID: {}", created.getId());
    }

    @Test
    public void testCreateWinchatDtlWithFields() {
        // 生成唯一的sessionId
        String sessionId = "session_with_fields_" + UUID.randomUUID().toString();

        // 准备测试数据
        String f1stRawChatlog = "原始聊天记录内容";
        String s2ndAggSysPrompt = "聚合过后的系统提示词";
        String t3rdRewrite = "重写过后的聊天记录";
        String f4thFinalSysPrompt = "最终的系统提示词";

        // 测试创建聊天窗口明细记录（包含新字段）
        TrainConvWinchatDtl result = trainConvWinchatDtlService.createWinchatDtlWithFields(
            CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId,
            f1stRawChatlog, s2ndAggSysPrompt, t3rdRewrite, f4thFinalSysPrompt,
            TEAM_ID, CREATOR, 0L);

        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(CONV_WINCHAT_MAIN_ID, result.getConvWinchatMainId());
        assertEquals(TASK_ID, result.getTaskId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(f1stRawChatlog, result.getF1stRawChatlog());
        assertEquals(s2ndAggSysPrompt, result.getS2ndAggSysPrompt());
        assertEquals(t3rdRewrite, result.getT3rdRewrite());
        assertEquals(f4thFinalSysPrompt, result.getF4thFinalSysPrompt());
        assertEquals(TEAM_ID, result.getTeamId());
        assertEquals(CREATOR, result.getCreator());
        assertEquals(CREATOR, result.getUpdater());
        assertEquals(0L, result.getVersion());

        log.info("创建聊天窗口明细记录（包含新字段）测试通过, ID: {}, sessionId: {}", result.getId(), sessionId);
    }

    @Test
    public void testCreateWinchatDtlWithConvKbId() {
        // 生成唯一的sessionId
        String sessionId = "session_conv_kb_" + UUID.randomUUID().toString();
        Long convKbId = 123L; // train_task_conv_kb_dtl表的主键ID
        
        // 测试创建包含conv_kb_id字段的记录
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtlWithConvKbId(
            CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, convKbId, TEAM_ID, CREATOR);
        
        assertNotNull(created);
        assertNotNull(created.getId());
        assertEquals(CONV_WINCHAT_MAIN_ID, created.getConvWinchatMainId());
        assertEquals(TASK_ID, created.getTaskId());
        assertEquals(sessionId, created.getSessionId());
        assertEquals(convKbId, created.getConvKbId()); // 验证新字段
        assertEquals(TEAM_ID, created.getTeamId());
        assertEquals(CREATOR, created.getCreator());
        
        log.info("创建包含conv_kb_id字段的记录测试通过, ID: {}, convKbId: {}", created.getId(), created.getConvKbId());
    }

    @Test
    public void testUpdateConvKbId() {
        // 先创建一条记录
        String sessionId = "session_update_conv_kb_" + UUID.randomUUID().toString();
        TrainConvWinchatDtl created = trainConvWinchatDtlService.createWinchatDtl(CONV_WINCHAT_MAIN_ID, TASK_ID, sessionId, TEAM_ID, CREATOR);
        assertNotNull(created);
        assertNotNull(created.getId());
        
        // 更新conv_kb_id字段
        Long convKbId = 456L;
        created.setConvKbId(convKbId);
        created.setUpdater("updated_user");
        
        // 执行更新
        boolean result = trainConvWinchatDtlService.updateWinchatDtl(created);
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatDtl updated = trainConvWinchatDtlService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(convKbId, updated.getConvKbId()); // 验证conv_kb_id字段更新
        assertEquals("updated_user", updated.getUpdater());
        
        log.info("更新conv_kb_id字段测试通过, ID: {}, convKbId: {}", updated.getId(), updated.getConvKbId());
    }
}