package com.yiyi.ai_train_playground.service.kb.impl;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatLog;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatLogService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模拟聊天室窗口聊天记录表 Service测试类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("company")
@Transactional
public class TrainConvWinchatLogServiceImplTest {

    @Autowired
    private TrainConvWinchatLogService trainConvWinchatLogService;

    private final Long TEAM_ID = 1L;
    private final Long CONV_DTL_ID = 1L;
    private final String CREATOR = "test_user";

    @Test
    public void testCreateChatLog() {
        // 生成唯一的sessionId
        String sessionId = "session_" + UUID.randomUUID().toString();
        String sender = "测试用户";
        String content = "这是一条测试消息";
        LocalDateTime sendTime = LocalDateTime.now();
        String senderType = "buyer";
        
        // 测试创建聊天记录
        TrainConvWinchatLog result = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId, sender, content, sendTime, senderType, TEAM_ID, CREATOR);
        
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(CONV_DTL_ID, result.getConvDtlId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(sender, result.getSender());
        assertEquals(content, result.getContent());
        assertEquals(sendTime, result.getSendTime());
        assertEquals(senderType, result.getSenderType());
        assertEquals(TEAM_ID, result.getTeamId());
        assertEquals(CREATOR, result.getCreator());
        assertEquals(CREATOR, result.getUpdater());
        assertEquals(0L, result.getVersion());
        
        log.info("创建聊天记录测试通过, ID: {}, sessionId: {}", result.getId(), sessionId);
    }

    @Test
    public void testBatchCreateChatLog() {
        // 准备批量数据
        String sessionId = "session_" + UUID.randomUUID().toString();
        List<TrainConvWinchatLog> chatLogs = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            TrainConvWinchatLog chatLog = new TrainConvWinchatLog();
            chatLog.setConvDtlId(CONV_DTL_ID);
            chatLog.setSessionId(sessionId);
            chatLog.setSender("用户" + i);
            chatLog.setContent("测试消息" + i);
            chatLog.setSendTime(LocalDateTime.now().plusMinutes(i));
            chatLog.setSenderType(i % 2 == 0 ? "staff" : "buyer");
            chatLog.setTeamId(TEAM_ID);
            chatLog.setCreator(CREATOR);
            chatLog.setUpdater(CREATOR);
            chatLog.setVersion(0L);
            chatLogs.add(chatLog);
        }
        
        // 测试批量创建
        boolean result = trainConvWinchatLogService.batchCreateChatLog(chatLogs);
        assertTrue(result);
        
        // 验证创建结果
        List<TrainConvWinchatLog> createdLogs = trainConvWinchatLogService.getBySessionId(sessionId, TEAM_ID);
        assertNotNull(createdLogs);
        assertEquals(3, createdLogs.size());
        
        log.info("批量创建聊天记录测试通过, 创建数量: {}, sessionId: {}", chatLogs.size(), sessionId);
    }

    @Test
    public void testGetById() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatLog created = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId, "测试用户", "测试消息", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        assertNotNull(created);
        assertNotNull(created.getId());
        
        // 测试根据ID查询
        TrainConvWinchatLog result = trainConvWinchatLogService.getById(created.getId());
        
        assertNotNull(result);
        assertEquals(created.getId(), result.getId());
        assertEquals(CONV_DTL_ID, result.getConvDtlId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals("测试用户", result.getSender());
        assertEquals("测试消息", result.getContent());
        
        log.info("根据ID查询聊天记录测试通过, ID: {}", result.getId());
    }

    @Test
    public void testUpdateChatLog() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatLog created = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId, "测试用户", "测试消息", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 修改记录
        created.setSender("修改后用户");
        created.setContent("修改后消息");
        created.setSenderType("staff");
        created.setUpdater("updated_user");
        
        // 测试更新
        boolean result = trainConvWinchatLogService.updateChatLog(created);
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatLog updated = trainConvWinchatLogService.getById(created.getId());
        assertNotNull(updated);
        assertEquals("修改后用户", updated.getSender());
        assertEquals("修改后消息", updated.getContent());
        assertEquals("staff", updated.getSenderType());
        assertEquals("updated_user", updated.getUpdater());
        
        log.info("更新聊天记录测试通过, ID: {}", created.getId());
    }

    @Test
    public void testDeleteById() {
        // 先创建一条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        TrainConvWinchatLog created = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId, "测试用户", "测试消息", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        assertNotNull(created);
        Long createdId = created.getId();
        
        // 测试删除
        boolean result = trainConvWinchatLogService.deleteById(createdId);
        assertTrue(result);
        
        // 验证删除结果
        TrainConvWinchatLog deleted = trainConvWinchatLogService.getById(createdId);
        assertNull(deleted);
        
        log.info("删除聊天记录测试通过, ID: {}", createdId);
    }

    @Test
    public void testGetByConvDtlId() {
        // 先创建几条记录
        String sessionId1 = "session_" + UUID.randomUUID().toString();
        String sessionId2 = "session_" + UUID.randomUUID().toString();
        
        TrainConvWinchatLog record1 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId1, "用户1", "消息1", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        TrainConvWinchatLog record2 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId2, "用户2", "消息2", LocalDateTime.now().plusMinutes(1), "staff", TEAM_ID, CREATOR);
        TrainConvWinchatLog record3 = trainConvWinchatLogService.createChatLog(
            2L, sessionId1, "用户3", "消息3", LocalDateTime.now().plusMinutes(2), "buyer", TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        assertNotNull(record3);
        
        // 测试查询
        List<TrainConvWinchatLog> result = trainConvWinchatLogService.getByConvDtlId(CONV_DTL_ID, TEAM_ID);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record2.getId())));
        
        log.info("根据明细ID查询聊天记录测试通过, 结果数量: {}", result.size());
    }

    @Test
    public void testGetBySessionId() {
        // 先创建几条记录
        String sessionId = "session_" + UUID.randomUUID().toString();
        
        TrainConvWinchatLog record1 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId, "用户1", "消息1", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        TrainConvWinchatLog record2 = trainConvWinchatLogService.createChatLog(
            2L, sessionId, "用户2", "消息2", LocalDateTime.now().plusMinutes(1), "staff", TEAM_ID, CREATOR);
        TrainConvWinchatLog record3 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, "other_session", "用户3", "消息3", LocalDateTime.now().plusMinutes(2), "buyer", TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        assertNotNull(record3);
        
        // 测试查询
        List<TrainConvWinchatLog> result = trainConvWinchatLogService.getBySessionId(sessionId, TEAM_ID);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record2.getId())));
        
        log.info("根据sessionId查询聊天记录测试通过, sessionId: {}, 结果数量: {}", sessionId, result.size());
    }

    @Test
    public void testGetByConditions() {
        // 先创建几条记录
        String sessionId1 = "session_" + UUID.randomUUID().toString();
        String sessionId2 = "session_" + UUID.randomUUID().toString();
        
        TrainConvWinchatLog record1 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId1, "用户1", "消息1", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        TrainConvWinchatLog record2 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId2, "用户2", "消息2", LocalDateTime.now().plusMinutes(1), "staff", TEAM_ID, CREATOR);
        TrainConvWinchatLog record3 = trainConvWinchatLogService.createChatLog(
            2L, sessionId1, "用户3", "消息3", LocalDateTime.now().plusMinutes(2), "buyer", TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        assertNotNull(record3);
        
        // 测试按条件查询 - 只指定团队ID
        List<TrainConvWinchatLog> allResults = trainConvWinchatLogService.getByConditions(null, null, null, TEAM_ID);
        assertNotNull(allResults);
        assertTrue(allResults.size() >= 3);
        
        // 测试按条件查询 - 指定明细ID
        List<TrainConvWinchatLog> dtlResults = trainConvWinchatLogService.getByConditions(CONV_DTL_ID, null, null, TEAM_ID);
        assertNotNull(dtlResults);
        assertEquals(2, dtlResults.size());
        
        // 测试按条件查询 - 指定sessionId
        List<TrainConvWinchatLog> sessionResults = trainConvWinchatLogService.getByConditions(null, sessionId1, null, TEAM_ID);
        assertNotNull(sessionResults);
        assertEquals(2, sessionResults.size());
        
        // 测试按条件查询 - 指定发送者类型
        List<TrainConvWinchatLog> buyerResults = trainConvWinchatLogService.getByConditions(null, null, "buyer", TEAM_ID);
        assertNotNull(buyerResults);
        assertTrue(buyerResults.size() >= 2);
        assertTrue(buyerResults.stream().allMatch(r -> "buyer".equals(r.getSenderType())));
        
        log.info("根据条件查询测试通过, 全部: {}, 明细: {}, 会话: {}, 买家: {}", 
            allResults.size(), dtlResults.size(), sessionResults.size(), buyerResults.size());
    }

    @Test
    public void testDeleteByConvDtlId() {
        // 先创建几条记录
        String sessionId1 = "session_" + UUID.randomUUID().toString();
        String sessionId2 = "session_" + UUID.randomUUID().toString();
        
        TrainConvWinchatLog record1 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId1, "用户1", "消息1", LocalDateTime.now(), "buyer", TEAM_ID, CREATOR);
        TrainConvWinchatLog record2 = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId2, "用户2", "消息2", LocalDateTime.now().plusMinutes(1), "staff", TEAM_ID, CREATOR);
        TrainConvWinchatLog record3 = trainConvWinchatLogService.createChatLog(
            2L, sessionId1, "用户3", "消息3", LocalDateTime.now().plusMinutes(2), "buyer", TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        assertNotNull(record3);
        
        // 验证删除前的数据
        List<TrainConvWinchatLog> beforeDelete = trainConvWinchatLogService.getByConvDtlId(CONV_DTL_ID, TEAM_ID);
        assertEquals(2, beforeDelete.size());
        
        // 测试删除
        boolean result = trainConvWinchatLogService.deleteByConvDtlId(CONV_DTL_ID, TEAM_ID);
        assertTrue(result);
        
        // 验证删除结果
        List<TrainConvWinchatLog> afterDelete = trainConvWinchatLogService.getByConvDtlId(CONV_DTL_ID, TEAM_ID);
        assertEquals(0, afterDelete.size());
        
        // 验证其他明细ID的记录未被删除
        List<TrainConvWinchatLog> otherResults = trainConvWinchatLogService.getByConvDtlId(2L, TEAM_ID);
        assertTrue(otherResults.size() >= 1);
        
        log.info("根据明细ID删除聊天记录测试通过, convDtlId: {}", CONV_DTL_ID);
    }

    @Test
    public void testBatchCreateChatLogWithEmpty() {
        // 测试空列表
        boolean result1 = trainConvWinchatLogService.batchCreateChatLog(new ArrayList<>());
        assertFalse(result1);
        
        // 测试null
        boolean result2 = trainConvWinchatLogService.batchCreateChatLog(null);
        assertFalse(result2);
        
        log.info("批量创建空记录测试通过");
    }

    @Test
    public void testGetByInvalidParameters() {
        // 测试无效参数
        TrainConvWinchatLog result1 = trainConvWinchatLogService.getById(null);
        assertNull(result1);
        
        List<TrainConvWinchatLog> result2 = trainConvWinchatLogService.getByConvDtlId(null, TEAM_ID);
        assertEquals(0, result2.size());
        
        List<TrainConvWinchatLog> result3 = trainConvWinchatLogService.getBySessionId("", TEAM_ID);
        assertEquals(0, result3.size());
        
        List<TrainConvWinchatLog> result4 = trainConvWinchatLogService.getByConditions(null, null, null, null);
        assertEquals(0, result4.size());
        
        log.info("无效参数测试通过");
    }

    @Test
    public void testCreateChatLogWithProdReferAnswer() {
        // 生成唯一的sessionId
        String sessionId = "session_" + UUID.randomUUID().toString();
        String sender = "测试用户";
        String content = "这是一条测试消息";
        LocalDateTime sendTime = LocalDateTime.now();
        String senderType = "buyer";
        
        // 测试创建聊天记录
        TrainConvWinchatLog result = trainConvWinchatLogService.createChatLog(
            CONV_DTL_ID, sessionId, sender, content, sendTime, senderType, TEAM_ID, CREATOR);
        
        assertNotNull(result);
        assertNotNull(result.getId());
        
        // 添加商品参考答案
        result.setProdReferAnswer("这是商品参考答案：商品质量很好，价格合理。");
        boolean updateResult = trainConvWinchatLogService.updateChatLog(result);
        assertTrue(updateResult);
        
        // 验证商品参考答案字段
        TrainConvWinchatLog updated = trainConvWinchatLogService.getById(result.getId());
        assertNotNull(updated);
        assertEquals("这是商品参考答案：商品质量很好，价格合理。", updated.getProdReferAnswer());
        
        log.info("创建和更新包含商品参考答案的聊天记录测试通过, ID: {}, prodReferAnswer: {}", 
            result.getId(), updated.getProdReferAnswer());
    }

    @Test
    public void testCreateChatLogWithProdAnswerMethod() {
        // 生成唯一的sessionId
        String sessionId = "session_" + UUID.randomUUID().toString();
        String sender = "测试客服";
        String content = "这是一条带商品参考答案的测试消息";
        LocalDateTime sendTime = LocalDateTime.now();
        String senderType = "staff";
        String prodReferAnswer = "商品参考答案：该产品采用优质材料制作，具有良好的耐用性和性价比，建议客户根据实际需求选择。";
        Integer score = 85;
        String intentResult = "购买意图";
        
        // 测试使用新方法创建带商品参考答案的聊天记录
        TrainConvWinchatLog result = trainConvWinchatLogService.createChatLogWithProdAnswer(
            CONV_DTL_ID, sessionId, sender, content, sendTime, senderType, prodReferAnswer, 
            score, intentResult, TEAM_ID, CREATOR);
        
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(CONV_DTL_ID, result.getConvDtlId());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(sender, result.getSender());
        assertEquals(content, result.getContent());
        assertEquals(sendTime, result.getSendTime());
        assertEquals(senderType, result.getSenderType());
        assertEquals(prodReferAnswer, result.getProdReferAnswer());
        assertEquals(TEAM_ID, result.getTeamId());
        assertEquals(CREATOR, result.getCreator());
        assertEquals(CREATOR, result.getUpdater());
        assertEquals(0L, result.getVersion());
        
        // 验证数据库中的记录
        TrainConvWinchatLog dbRecord = trainConvWinchatLogService.getById(result.getId());
        assertNotNull(dbRecord);
        assertEquals(prodReferAnswer, dbRecord.getProdReferAnswer());
        
        log.info("使用createChatLogWithProdAnswer方法创建聊天记录测试通过, ID: {}, sessionId: {}, prodReferAnswer长度: {}", 
            result.getId(), sessionId, prodReferAnswer.length());
    }

    @Test
    public void testCreateChatLogWithProdAnswerNull() {
        // 测试prodReferAnswer为null的情况
        String sessionId = "session_" + UUID.randomUUID().toString();
        String sender = "测试客服";
        String content = "没有商品参考答案的消息";
        LocalDateTime sendTime = LocalDateTime.now();
        String senderType = "staff";
        Integer score = null;
        String intentResult = null;
        
        // 测试prodReferAnswer为null
        TrainConvWinchatLog result = trainConvWinchatLogService.createChatLogWithProdAnswer(
            CONV_DTL_ID, sessionId, sender, content, sendTime, senderType, null, 
            score, intentResult, TEAM_ID, CREATOR);
        
        assertNotNull(result);
        assertNotNull(result.getId());
        assertNull(result.getProdReferAnswer());
        
        // 验证数据库中的记录
        TrainConvWinchatLog dbRecord = trainConvWinchatLogService.getById(result.getId());
        assertNotNull(dbRecord);
        assertNull(dbRecord.getProdReferAnswer());
        
        log.info("创建prodReferAnswer为null的聊天记录测试通过, ID: {}", result.getId());
    }

    @Test
    public void testBatchCreateChatLogWithProdReferAnswer() {
        // 准备批量数据，包含商品参考答案
        String sessionId = "session_" + UUID.randomUUID().toString();
        List<TrainConvWinchatLog> chatLogs = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            TrainConvWinchatLog chatLog = new TrainConvWinchatLog();
            chatLog.setConvDtlId(CONV_DTL_ID);
            chatLog.setSessionId(sessionId);
            chatLog.setSender("用户" + i);
            chatLog.setContent("测试消息" + i);
            chatLog.setSendTime(LocalDateTime.now().plusMinutes(i));
            chatLog.setSenderType(i % 2 == 0 ? "staff" : "buyer");
            chatLog.setProdReferAnswer("商品参考答案" + i + "：产品功能完善，售后服务好。");
            chatLog.setTeamId(TEAM_ID);
            chatLog.setCreator(CREATOR);
            chatLog.setUpdater(CREATOR);
            chatLog.setVersion(0L);
            chatLogs.add(chatLog);
        }
        
        // 测试批量创建
        boolean result = trainConvWinchatLogService.batchCreateChatLog(chatLogs);
        assertTrue(result);
        
        // 验证创建结果包含商品参考答案
        List<TrainConvWinchatLog> createdLogs = trainConvWinchatLogService.getBySessionId(sessionId, TEAM_ID);
        assertNotNull(createdLogs);
        assertEquals(3, createdLogs.size());
        
        // 验证每条记录都包含商品参考答案
        for (int i = 0; i < createdLogs.size(); i++) {
            TrainConvWinchatLog log = createdLogs.get(i);
            assertNotNull(log.getProdReferAnswer());
            assertTrue(log.getProdReferAnswer().contains("商品参考答案"));
            assertTrue(log.getProdReferAnswer().contains("产品功能完善，售后服务好"));
        }
        
        log.info("批量创建包含商品参考答案的聊天记录测试通过, 创建数量: {}, sessionId: {}", 
            chatLogs.size(), sessionId);
    }
}