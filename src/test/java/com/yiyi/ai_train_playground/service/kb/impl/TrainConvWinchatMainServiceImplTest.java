package com.yiyi.ai_train_playground.service.kb.impl;

import com.yiyi.ai_train_playground.entity.kb.TrainConvWinchatMain;
import com.yiyi.ai_train_playground.service.kb.TrainConvWinchatMainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模拟聊天室窗口主表 Service测试类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("company")
@Transactional
public class TrainConvWinchatMainServiceImplTest {

    @Autowired
    private TrainConvWinchatMainService trainConvWinchatMainService;

    private final Long TEAM_ID = 1L;
    private final Long CHATROOM_ID = 1L;
    private final Long STAFF_ID = 1L;
    private final String CREATOR = "test_user";

    @Test
    public void testCreateWinchatMain() {
        // 测试创建聊天窗口主记录
        TrainConvWinchatMain result = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, STAFF_ID, TEAM_ID, CREATOR);
        
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(CHATROOM_ID, result.getChatroomId());
        assertEquals(STAFF_ID, result.getStaffId());
        assertEquals(TEAM_ID, result.getTeamId());
        assertEquals(CREATOR, result.getCreator());
        assertEquals(CREATOR, result.getUpdater());
        assertEquals(0L, result.getVersion());
        
        log.info("创建聊天窗口主记录测试通过, ID: {}", result.getId());
    }

    @Test
    public void testGetById() {
        // 先创建一条记录
        TrainConvWinchatMain created = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, STAFF_ID, TEAM_ID, CREATOR);
        assertNotNull(created);
        assertNotNull(created.getId());
        
        // 测试根据ID查询
        TrainConvWinchatMain result = trainConvWinchatMainService.getById(created.getId());
        
        assertNotNull(result);
        assertEquals(created.getId(), result.getId());
        assertEquals(CHATROOM_ID, result.getChatroomId());
        assertEquals(STAFF_ID, result.getStaffId());
        assertEquals(TEAM_ID, result.getTeamId());
        
        log.info("根据ID查询聊天窗口主记录测试通过, ID: {}", result.getId());
    }

    @Test
    public void testGetByIdNotFound() {
        // 测试查询不存在的记录
        TrainConvWinchatMain result = trainConvWinchatMainService.getById(999999L);
        assertNull(result);
        
        log.info("查询不存在记录测试通过");
    }

    @Test
    public void testUpdateWinchatMain() {
        // 先创建一条记录
        TrainConvWinchatMain created = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, STAFF_ID, TEAM_ID, CREATOR);
        assertNotNull(created);
        
        // 修改记录
        created.setStaffId(2L);
        created.setUpdater("updated_user");
        
        // 测试更新
        boolean result = trainConvWinchatMainService.updateWinchatMain(created);
        assertTrue(result);
        
        // 验证更新结果
        TrainConvWinchatMain updated = trainConvWinchatMainService.getById(created.getId());
        assertNotNull(updated);
        assertEquals(2L, updated.getStaffId());
        assertEquals("updated_user", updated.getUpdater());
        
        log.info("更新聊天窗口主记录测试通过, ID: {}", created.getId());
    }

    @Test
    public void testDeleteById() {
        // 先创建一条记录
        TrainConvWinchatMain created = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, STAFF_ID, TEAM_ID, CREATOR);
        assertNotNull(created);
        Long createdId = created.getId();
        
        // 测试删除
        boolean result = trainConvWinchatMainService.deleteById(createdId);
        assertTrue(result);
        
        // 验证删除结果
        TrainConvWinchatMain deleted = trainConvWinchatMainService.getById(createdId);
        assertNull(deleted);
        
        log.info("删除聊天窗口主记录测试通过, ID: {}", createdId);
    }

    @Test
    public void testDeleteByIdNotFound() {
        // 测试删除不存在的记录
        boolean result = trainConvWinchatMainService.deleteById(999999L);
        assertFalse(result);
        
        log.info("删除不存在记录测试通过");
    }

    @Test
    public void testGetByCharoomIdAndStaffId() {
        // 先创建两条记录
        TrainConvWinchatMain record1 = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, STAFF_ID, TEAM_ID, CREATOR);
        TrainConvWinchatMain record2 = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, 2L, TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        
        // 测试查询
        List<TrainConvWinchatMain> result = trainConvWinchatMainService.getByCharoomIdAndStaffId(CHATROOM_ID, STAFF_ID, TEAM_ID);
        
        assertNotNull(result);
        assertTrue(result.size() >= 1);
        assertTrue(result.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        
        log.info("根据聊天室ID和员工ID查询测试通过, 结果数量: {}", result.size());
    }

    @Test
    public void testGetByConditions() {
        // 先创建几条记录
        TrainConvWinchatMain record1 = trainConvWinchatMainService.createWinchatMain(CHATROOM_ID, STAFF_ID, TEAM_ID, CREATOR);
        TrainConvWinchatMain record2 = trainConvWinchatMainService.createWinchatMain(2L, STAFF_ID, TEAM_ID, CREATOR);
        
        assertNotNull(record1);
        assertNotNull(record2);
        
        // 测试按条件查询 - 只指定团队ID
        List<TrainConvWinchatMain> allResults = trainConvWinchatMainService.getByConditions(null, null, TEAM_ID);
        assertNotNull(allResults);
        assertTrue(allResults.size() >= 2);
        
        // 测试按条件查询 - 指定聊天室ID
        List<TrainConvWinchatMain> chatroomResults = trainConvWinchatMainService.getByConditions(CHATROOM_ID, null, TEAM_ID);
        assertNotNull(chatroomResults);
        assertTrue(chatroomResults.size() >= 1);
        assertTrue(chatroomResults.stream().anyMatch(r -> r.getId().equals(record1.getId())));
        
        // 测试按条件查询 - 指定员工ID
        List<TrainConvWinchatMain> staffResults = trainConvWinchatMainService.getByConditions(null, STAFF_ID, TEAM_ID);
        assertNotNull(staffResults);
        assertTrue(staffResults.size() >= 2);
        
        log.info("根据条件查询测试通过, 全部结果: {}, 聊天室结果: {}, 员工结果: {}", 
            allResults.size(), chatroomResults.size(), staffResults.size());
    }

    @Test
    public void testGetByConditionsWithNullTeamId() {
        // 测试团队ID为空的情况
        List<TrainConvWinchatMain> result = trainConvWinchatMainService.getByConditions(CHATROOM_ID, STAFF_ID, null);
        
        assertNotNull(result);
        assertEquals(0, result.size());
        
        log.info("团队ID为空查询测试通过");
    }
}
