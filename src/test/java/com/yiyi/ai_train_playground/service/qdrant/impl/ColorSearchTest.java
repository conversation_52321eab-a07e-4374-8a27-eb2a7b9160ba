package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.service.impl.VectorSearchServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 颜色搜索功能测试
 */
@SpringBootTest
@ActiveProfiles("home")
public class ColorSearchTest {

    @Autowired
    private VectorSearchServiceImpl vectorSearchService;

    @Test
    public void testSearchXiaomiColors() {
        System.out.println("=== 测试小米手环颜色选项搜索 ===");
        
        // 测试搜索颜色相关信息
        String queryText = "小米手环有什么颜色选项";
        String teamId = "1";
        String productId = "10030237827895";
        int prodType = 1; // 京东商品
        int limit = 5;
        
        System.out.println("查询文本: " + queryText);
        System.out.println("产品ID: " + productId);
        
        try {
            String result = vectorSearchService.searchByText(
                queryText, teamId, productId, limit, prodType, 0.6f);
            
            System.out.println("=== 搜索结果 ===");
            System.out.println("结果长度: " + result.length());
            
            // 检查是否包含颜色选项关键词
            boolean hasColorOptions = result.contains("颜色选项") || 
                                     result.contains("经典黑色") || 
                                     result.contains("天空蓝") ||
                                     result.contains("樱花粉") ||
                                     result.contains("薄荷绿");
            
            System.out.println("包含颜色选项信息: " + hasColorOptions);
            
            if (hasColorOptions) {
                System.out.println("✅ 颜色选项搜索功能正常");
                // 显示部分内容
                String preview = result.length() > 500 ? 
                    result.substring(0, 500) + "..." : result;
                System.out.println("内容预览: " + preview);
            } else {
                System.out.println("❌ 未找到颜色选项相关信息");
            }
            
        } catch (Exception e) {
            System.err.println("搜索出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }
    
    @Test
    public void testSearchSpecificColor() {
        System.out.println("=== 测试特定颜色搜索 ===");
        
        // 测试搜索具体颜色
        String queryText = "樱花粉颜色怎么样";
        String teamId = "1";
        String productId = "10030237827895";
        int prodType = 1;
        int limit = 3;
        
        System.out.println("查询文本: " + queryText);
        
        try {
            String result = vectorSearchService.searchByText(
                queryText, teamId, productId, limit, prodType, 0.6f);
            
            System.out.println("=== 搜索结果 ===");
            System.out.println("结果长度: " + result.length());
            
            // 检查是否包含樱花粉相关信息
            boolean hasPinkInfo = result.contains("樱花粉") ||
                                 result.contains("温柔浪漫") ||
                                 result.contains("女性喜爱");
            
            System.out.println("包含樱花粉信息: " + hasPinkInfo);
            
            if (hasPinkInfo) {
                System.out.println("✅ 特定颜色搜索功能正常");
            } else {
                System.out.println("❌ 未找到樱花粉相关信息");
            }
            
        } catch (Exception e) {
            System.err.println("搜索出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }
}