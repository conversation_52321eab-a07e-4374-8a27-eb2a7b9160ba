package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.service.QdrantService;
import com.yiyi.ai_train_playground.service.impl.DoubaoBigModelServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@SpringBootTest
//@ActiveProfiles("home")
public class GenerateXiaomiColorVectorTest {

    @Autowired
    private DoubaoBigModelServiceImpl doubaoBigModelService;

    @Autowired
    private QdrantService qdrantService;

    @Value("${product.processing.jd-collection-name:train_prod_jd_collection}")
    private String jdCollectionName;

    @Test
    public void testGenerateColorVector() throws Exception {
        System.out.println("=== 生成小米手环颜色选项向量 ===");
        
        // 创建包含颜色选项的内容
        String colorContent = createXiaomiColorsContent();
        System.out.println("内容长度: " + colorContent.length());
        System.out.println("内容预览:\n" + colorContent.substring(0, Math.min(300, colorContent.length())) + "...");
        
        // 生成向量 - 查找正确的方法
        try {
            // 尝试不同的方法名
            System.out.println("开始向量化...");
            List<List<Double>> vectors = doubaoBigModelService.embed(List.of(colorContent));
            
            if (vectors != null && !vectors.isEmpty()) {
                List<Double> vector = vectors.get(0);
                System.out.println("✅ 向量生成成功！");
                System.out.println("向量维度: " + vector.size());
                System.out.println("向量前10个值: " + vector.subList(0, Math.min(10, vector.size())));

                // 打印JSON格式用于curl命令
                printCurlJson(vector, colorContent);

                // 插入向量到Qdrant
                insertVectorToQdrant(vector, colorContent);
            } else {
                System.out.println("❌ 向量生成失败");
            }
            
        } catch (Exception e) {
            System.out.println("方法调用错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 插入向量到Qdrant
     */
    private void insertVectorToQdrant(List<Double> vector, String content) {
        try {
            System.out.println("\n=== 开始插入向量到Qdrant ===");
            System.out.println("集合名称: " + jdCollectionName);

            // 转换向量格式：Double -> Float
            List<Float> floatVector = vector.stream()
                    .map(Double::floatValue)
                    .collect(Collectors.toList());

            // 生成唯一的向量ID
            String vectorId = UUID.randomUUID().toString();
            System.out.println("向量ID: " + vectorId);

            // 构建payload，参考VectorSearchServiceImpl.processVectorForWare的逻辑
            Map<String, Object> payload = new HashMap<>();
            payload.put("productId", 10028797096978L); // 小米手环商品ID
            payload.put("teamId", 1L);
            payload.put("content", content);
            payload.put("imgUrl", "https://img10.360buyimg.com/img/jfs/t1/232495/26/30581/58732/685d095fF90f3471f/9a8a39548cf9fc13.jpg");
            payload.put("timestamp", System.currentTimeMillis());
            payload.put("colorOptions", true);
            payload.put("updateReason", "添加颜色选项详情");

            // 构建向量数据
            VectorData vectorData = VectorData.builder()
                    .id(vectorId)
                    .vector(floatVector)
                    .payload(payload)
                    .build();

            // 插入向量
            boolean result = qdrantService.insertVector(jdCollectionName, vectorData);

            if (result) {
                System.out.println("✅ 向量插入成功！");
                System.out.println("向量ID: " + vectorId);
                System.out.println("集合名称: " + jdCollectionName);
                System.out.println("向量维度: " + floatVector.size());
                System.out.println("Payload字段数: " + payload.size());
            } else {
                System.out.println("❌ 向量插入失败");
            }

        } catch (Exception e) {
            System.out.println("❌ 向量插入过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void printCurlJson(List<Double> vector, String content) {
        System.out.println("\n=== 生成curl命令所需的JSON ===");
        
        // 转换为Float列表并构建JSON字符串
        StringBuilder vectorStr = new StringBuilder("[");
        for (int i = 0; i < vector.size(); i++) {
            if (i > 0) vectorStr.append(",");
            vectorStr.append(vector.get(i).floatValue());
        }
        vectorStr.append("]");
        
        String pointId = "xiaomi-colors-" + System.currentTimeMillis();
        long timestamp = System.currentTimeMillis();
        
        // 转义内容中的特殊字符
        String escapedContent = content
            .replace("\\", "\\\\")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\t", "\\t");
        
        String jsonPayload = String.format("""
            {
              "points": [
                {
                  "id": "%s",
                  "vector": %s,
                  "payload": {
                    "productId": 10028797096978,
                    "teamId": 1,
                    "content": "%s",
                    "imgUrl": "https://img10.360buyimg.com/img/jfs/t1/232495/26/30581/58732/685d095fF90f3471f/9a8a39548cf9fc13.jpg",
                    "timestamp": %d,
                    "colorOptions": true,
                    "updateReason": "添加颜色选项详情"
                  }
                }
              ]
            }
            """, pointId, vectorStr.toString(), escapedContent, timestamp);
        
        // 将JSON保存到文件
        System.out.println("JSON payload已准备完成，点ID: " + pointId);
        System.out.println("向量维度: " + vector.size());
        System.out.println("内容长度: " + content.length());
        
        try {
            java.nio.file.Files.write(
                java.nio.file.Paths.get("/tmp/xiaomi_colors_vector.json"), 
                jsonPayload.getBytes()
            );
            System.out.println("✅ JSON已保存到 /tmp/xiaomi_colors_vector.json");
        } catch (Exception e) {
            System.out.println("❌ 保存JSON失败: " + e.getMessage());
        }
    }
    
    private String createXiaomiColorsContent() {
        return """
                |类别|详情|类别|详情|
                                     | ---- | ---- | ---- | ---- |
                                     |品牌|小米（MI）|商品编号|10147019721214|
                                     |CPU型号|天玑7300 - Ultra|机身颜色|红色|
                                     |特征特质|NFC，红外遥控|屏幕分辨率|1.5K|
                                     |风格|炫彩，轻奢，科技|屏幕材质|OLED曲面屏|
                                     |后摄主像素|5000万像素|三防标准|IP68|
                                     |机型|小米 Redmi Note14 Pro|入网型号|24090RA29C|
                                     |上市日期|2024 - 09 - 26|国补备案型号|Redmi Note 14 Pro|
                                     |机身颜色|红色|机身重量|190g|
                                     |机身内存|512GB|存储卡|不支持|
                                     |运行内存|12GB|屏幕刷新率|120Hz|
                                     |屏幕特色|SGS认证|屏幕尺寸|6.67英寸|
                                     |无线充电|以官网信息为准|充电功率|45W|
                                     |系统|小米澎湃os|5G网络|支持5G|
                                     |4G网络|4G FDD - LTE；4G TD - LTE|SIM卡数量|2个|
                                     |充电接口|Type - C|生物识别|屏幕指纹|
                                     |拍照特色|光学防抖|前置主像素|2000万像素|
        """;
    }
}