package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
@ActiveProfiles("home")
@DisplayName("向量相似度比较测试")
public class VectorCompareTest {

    @Autowired
    private SuperBigModelInterface bigModelService;

    @Test
    @DisplayName("测试两个字符串的余弦相似度")
    void testCosineSimilarity() {
        // Arrange
        String text1 = "中国是一个地大物博的国家，有悠久的五千年历史，有960万平方公里";
        String text2 = "你是个大美女";
        List<String> texts = List.of(text1, text2);

        // Act
        List<List<Double>> embeddings = bigModelService.embed(texts);

        // Assert
        assertNotNull(embeddings);
        assertEquals(2, embeddings.size());
        assertNotNull(embeddings.get(0));
        assertNotNull(embeddings.get(1));

        double similarity = cosineSimilarity(embeddings.get(0), embeddings.get(1));

        System.out.println("字符串1: \"" + text1 + "\"");
        System.out.println("字符串2: \"" + text2 + "\"");
        System.out.println("余弦相似度: " + similarity);

        // 可以根据预期设置一个断言阈值
        // assertTrue(similarity > 0.5, "预期相似度应大于0.5");
    }

    @Test
    @DisplayName("测试两个float向量的余弦相似度")
    void testCosineSimuWithFloat() {
        // Arrange
        String text1 = "这个商品的发货地点是哪里？";
        String text2 = "今天天气不错";
        List<String> texts = List.of(text1, text2);
        
        // Act
        List<List<Double>> embeddings = bigModelService.embed(texts);
        
        float[] vector1 = new float[embeddings.get(0).size()];
        for (int i = 0; i < embeddings.get(0).size(); i++) {
            vector1[i] = embeddings.get(0).get(i).floatValue();
        }
        
        float[] vector2 = new float[embeddings.get(1).size()];
        for (int i = 0; i < embeddings.get(1).size(); i++) {
            vector2[i] = embeddings.get(1).get(i).floatValue();
        }

        double similarity = calculateCosineSimilarity(vector1, vector2);

        // Assert
//        System.out.println("Float向量1: " + java.util.Arrays.toString(vector1));
//        System.out.println("Float向量2: " + java.util.Arrays.toString(vector2));
        System.out.println("字符串1: \"" + text1 + "\"");
        System.out.println("字符串2: \"" + text2 + "\"");
        System.out.println("余弦相似度 (float): " + similarity);
        assertNotNull(embeddings);
        assertEquals(2, embeddings.size());
    }

    private double cosineSimilarity(List<Double> vectorA, List<Double> vectorB) {
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        for (int i = 0; i < vectorA.size(); i++) {
            dotProduct += vectorA.get(i) * vectorB.get(i);
            normA += Math.pow(vectorA.get(i), 2);
            normB += Math.pow(vectorB.get(i), 2);
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }


    /**
     * 计算两个嵌入向量之间的余弦相似度
     */
    private double calculateCosineSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            throw new IllegalArgumentException("向量维度必须匹配");
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
}