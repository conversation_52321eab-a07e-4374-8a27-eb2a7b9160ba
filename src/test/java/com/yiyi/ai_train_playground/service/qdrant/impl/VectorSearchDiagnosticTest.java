package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.service.VectorSearchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 向量搜索详细诊断测试
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class VectorSearchDiagnosticTest {

    @Autowired
    private VectorSearchService vectorSearchService;

    @Test
    public void testSearchProductIdOnlyFilter() {
        // 测试只使用productId过滤，不使用teamId
        String targetProductId = "10030237827895";
        String queryText = "测试查询商品";
        int limit = 10;
        Integer prodType = 1; // 京东商品类型

        System.out.println("=== 诊断测试：只使用productId过滤 ===");
        System.out.println("目标productId: " + targetProductId);
        System.out.println("查询文本: " + queryText);

        try {
            // 尝试不传入teamId（使用null），看看是否会跳过teamId过滤
            String result = vectorSearchService.searchByText(queryText, null, targetProductId, limit, prodType, 0.6f);
            
            System.out.println("=== 搜索结果（teamId=null） ===");
            System.out.println("结果长度: " + (result != null ? result.length() : 0));
            System.out.println("结果内容: " + result);
            
            if (result == null || result.trim().isEmpty()) {
                System.out.println("❌ 搜索结果为空");
            } else {
                System.out.println("✅ 搜索成功，找到数据！");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 搜索过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }

    @Test 
    public void testSearchWithEmptyTeamId() {
        // 测试使用空字符串teamId
        String targetProductId = "10030237827895";
        String teamId = "";
        String queryText = "测试查询商品";
        int limit = 10;
        Integer prodType = 1;

        System.out.println("=== 诊断测试：空teamId ===");
        System.out.println("目标productId: " + targetProductId);
        System.out.println("teamId: '" + teamId + "'");

        try {
            String result = vectorSearchService.searchByText(queryText, teamId, targetProductId, limit, prodType, 0.6f);
            
            System.out.println("=== 搜索结果（teamId=''） ===");
            System.out.println("结果长度: " + (result != null ? result.length() : 0));
            System.out.println("结果内容: " + result);
            
            if (result == null || result.trim().isEmpty()) {
                System.out.println("❌ 搜索结果为空");
            } else {
                System.out.println("✅ 搜索成功，找到数据！");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 搜索过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }
}