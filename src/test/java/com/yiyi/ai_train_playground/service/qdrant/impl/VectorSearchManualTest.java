package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import com.yiyi.ai_train_playground.service.impl.VectorSearchServiceImpl;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 向量搜索手动测试
 * 专门用于测试productId=10030237827895的数据查询
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class VectorSearchManualTest {

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private VectorSearchServiceImpl vectorSearchServiceImpl;

    @Autowired
    private TrainJdProductsService trainJdProductsService;

    @Test
    public void testSearchProductId10030237827895() {
        // 测试目标：productId=10030237827895
        String targetProductId = "10030237827895";
        String teamId = "1";
        String queryText = "颜色有黄色、蓝色";
        int limit = 10;
        Integer prodType = 1; // 京东商品类型

        System.out.println("=== 开始测试向量搜索 ===");
        System.out.println("目标productId: " + targetProductId);
        System.out.println("teamId: " + teamId);
        System.out.println("查询文本: " + queryText);
        System.out.println("产品类型: " + prodType);
        System.out.println("限制结果数: " + limit);

        try {
            // 调用搜索方法
            String result = vectorSearchService.searchByText(queryText, teamId, targetProductId, limit, prodType, 0.65f);
            
            System.out.println("=== 搜索结果 ===");
            System.out.println("结果长度: " + (result != null ? result.length() : 0));
            System.out.println("结果内容: " + result);
//            System.out.println("分数为:"+);
            
            if (result == null || result.trim().isEmpty()) {
                System.out.println("❌ 搜索结果为空，未找到数据");
            } else {
                System.out.println("✅ 搜索成功，找到数据");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 搜索过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }

    @Test
    public void testSearchWithoutProductIdFilter() {
        // 不使用productId过滤，看看能否查到数据
        String teamId = "1";
        String queryText = "测试查询商品";
        int limit = 10;
        Integer prodType = 1; // 京东商品类型

        System.out.println("=== 开始测试向量搜索(无productId过滤) ===");
        System.out.println("teamId: " + teamId);
        System.out.println("查询文本: " + queryText);
        System.out.println("产品类型: " + prodType);
        System.out.println("限制结果数: " + limit);

        try {
            // 调用搜索方法，不指定externalProductId
            String result = vectorSearchService.searchByText(queryText, teamId, null, limit, prodType, 0.5f);
            
            System.out.println("=== 搜索结果(无过滤) ===");
            System.out.println("结果长度: " + (result != null ? result.length() : 0));
            System.out.println("结果内容: " + result);
            
            if (result == null || result.trim().isEmpty()) {
                System.out.println("❌ 搜索结果为空，向量库中可能没有数据");
            } else {
                System.out.println("✅ 搜索成功，向量库中有数据");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 搜索过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }

    @Test
    public void testGenerateVectorForXiaomiColors() {
        // 测试生成小米手环颜色向量数据
        Long wareId = 10030237827895L;
        Long teamId = 1L;
        String imgUrl = "https://miaobi-lite.bj.bcebos.com/miaobi/5mao/b%275bCP57GzOeaJi%2BeOr%2BS4iuW4guaXtumXtOWPiuS7t%2BagvF8xNzMxNjQxOTU0LjQwMzMxN18xNzMxNjQxOTU0LjczMjQxOTdfMTczMTY0MTk1NS4wOTkzMjQ%3D%27/2.png";
        
        // 生成小米手环颜色的创意Markdown文本（约300字）
        String recoTextWithMD = """
# 小米手环10 多彩配色选择指南

## 🎨 经典配色系列
小米手环10提供多种精美配色，满足不同用户的个性化需求：

### 经典色系
- **经典黑色**：沉稳大气，适合商务场合，百搭不挑衣服
- **天空蓝**：清新自然，展现青春活力，适合运动爱好者
- **樱花粉**：浪漫优雅，彰显女性魅力，时尚甜美首选

### 活力色系  
- **薄荷绿**：清新淡雅，护眼舒适，适合长时间佩戴
- **象牙白**：简约纯净，百搭经典，彰显品味格调
- **橙色活力**：热情奔放，充满活力，运动时尚单品

## 💎 材质升级选择
- **NFC版本**：支持门禁卡、公交卡功能，提供墨夜黑、深海蓝、玫瑰金、星空紫四色
- **陶瓷版本**：高端材质，陶瓷白和陶瓷黑两色可选，触感丝滑耐磨

选择适合自己的颜色，让智能穿戴成为时尚配饰的完美延伸！
                """;

        System.out.println("=== 开始生成小米手环颜色向量数据 ===");
        System.out.println("wareId: " + wareId);
        System.out.println("teamId: " + teamId);
        System.out.println("imgUrl: " + imgUrl);
        System.out.println("recoTextWithMD长度: " + recoTextWithMD.length() + " 字符");
        System.out.println("\n=== Markdown内容预览 ===");
        System.out.println(recoTextWithMD.substring(0, Math.min(200, recoTextWithMD.length())) + "...");

        try {
            // 1. 通过wareId和teamId查询TrainJdProducts
            System.out.println("\n=== 第1步：查询京东商品信息 ===");
            TrainJdProducts trainJdProducts = trainJdProductsService.findByWareIdAndTeamId(wareId, teamId);
            
            if (trainJdProducts == null) {
                System.out.println("❌ 未找到对应的京东商品，wareId=" + wareId + ", teamId=" + teamId);
                return;
            }
            
            System.out.println("✅ 成功查询到京东商品信息:");
            System.out.println("  - 商品ID: " + trainJdProducts.getWareId());
            System.out.println("  - 商品名称: " + trainJdProducts.getTitle());
            System.out.println("  - 团队ID: " + trainJdProducts.getTeamId());

            // 2. 调用processVectorForWare方法生成向量
            System.out.println("\n=== 第2步：生成向量数据 ===");
            boolean result = vectorSearchServiceImpl.processVectorForWare(
                trainJdProducts, 
                recoTextWithMD, 
                imgUrl, 
                teamId
            );
            
            System.out.println("\n=== 向量生成结果 ===");
            if (result) {
                System.out.println("✅ 向量数据生成成功！");
                System.out.println("  - 商品ID: " + trainJdProducts.getWareId());
                System.out.println("  - 团队ID: " + teamId);
                System.out.println("  - 图片URL: " + imgUrl);
                System.out.println("  - 内容长度: " + recoTextWithMD.length() + " 字符");
                System.out.println("  - 向量已存储到Qdrant向量库");
            } else {
                System.out.println("❌ 向量数据生成失败");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 生成向量过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
}