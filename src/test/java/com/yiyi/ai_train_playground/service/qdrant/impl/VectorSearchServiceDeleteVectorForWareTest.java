package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * VectorSearchService deleteVectorForWare方法测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class VectorSearchServiceDeleteVectorForWareTest {
    
    @Autowired
    private VectorSearchService vectorSearchService;
    
    // 测试数据
    private static final Long TEST_TEAM_ID = 1001L;
    private static final String TEST_IMG_URL = "https://example.com/test-image.jpg";
    private static final String TEST_RECO_TEXT = "这是一个测试商品的推荐文本，用于向量化测试。";
    
    private TrainJdProducts testProduct;
    
    @BeforeEach
    void setUp() {
        // 创建测试商品对象
        testProduct = new TrainJdProducts();
        testProduct.setId(12345L);
        testProduct.setWareId(67890L);
        testProduct.setTitle("测试商品标题");
        testProduct.setTeamId(TEST_TEAM_ID);
    }
    
    @Test
    @Order(1)
    @DisplayName("测试deleteVectorForWare方法 - 删除不存在的向量")
    void testDeleteVectorForWare_NotExists() {
        log.info("=== 测试deleteVectorForWare方法 - 删除不存在的向量 ===");
        
        try {
            boolean result = vectorSearchService.deleteVectorForWare(testProduct, TEST_IMG_URL, TEST_TEAM_ID);
            
            // 删除不存在的向量应该返回true（认为删除成功）
            Assertions.assertTrue(result, "删除不存在的向量应该返回true");
            
            log.info("✅ 删除不存在向量测试通过");
            
        } catch (Exception e) {
            log.error("删除不存在向量测试失败", e);
            Assertions.fail("删除不存在向量测试不应该抛出异常: " + e.getMessage());
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试deleteVectorForWare方法 - 先插入后删除")
    void testDeleteVectorForWare_InsertThenDelete() {
        log.info("=== 测试deleteVectorForWare方法 - 先插入后删除 ===");
        
        try {
            // 先尝试插入向量
            log.info("步骤1: 尝试插入向量");
            boolean insertResult = vectorSearchService.processVectorForWare(
                    testProduct, TEST_RECO_TEXT, TEST_IMG_URL, TEST_TEAM_ID);
            
            log.info("插入结果: {}", insertResult);
            
            // 然后删除向量
            log.info("步骤2: 删除向量");
            boolean deleteResult = vectorSearchService.deleteVectorForWare(
                    testProduct, TEST_IMG_URL, TEST_TEAM_ID);
            
            log.info("删除结果: {}", deleteResult);
            
            // 验证删除操作不抛异常
            Assertions.assertNotNull(deleteResult, "删除结果不应为null");
            
            log.info("✅ 先插入后删除测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("向量化失败")) {
                log.info("⚠️ 跳过测试：需要配置有效的向量化服务");
                Assumptions.assumeTrue(false, "需要配置有效的向量化服务");
            } else {
                throw e;
            }
        } catch (Exception e) {
            log.error("先插入后删除测试失败", e);
            Assertions.fail("先插入后删除测试不应该抛出异常: " + e.getMessage());
        }
    }
    
    @Test
    @Order(3)
    @DisplayName("测试deleteVectorForWare方法 - 参数验证")
    void testDeleteVectorForWare_ParameterValidation() {
        log.info("=== 测试deleteVectorForWare方法 - 参数验证 ===");
        
        try {
            // 测试null商品
            boolean result1 = vectorSearchService.deleteVectorForWare(null, TEST_IMG_URL, TEST_TEAM_ID);
            log.info("null商品测试结果: {}", result1);
            
            // 测试null图片URL
            boolean result2 = vectorSearchService.deleteVectorForWare(testProduct, null, TEST_TEAM_ID);
            log.info("null图片URL测试结果: {}", result2);
            
            // 测试null团队ID
            boolean result3 = vectorSearchService.deleteVectorForWare(testProduct, TEST_IMG_URL, null);
            log.info("null团队ID测试结果: {}", result3);
            
            log.info("✅ 参数验证测试通过");
            
        } catch (Exception e) {
            log.error("参数验证测试失败", e);
            // 参数验证可能会抛出异常，这是正常的
            log.info("⚠️ 参数验证抛出异常是正常的: {}", e.getMessage());
        }
    }
    
    @Test
    @Order(4)
    @DisplayName("测试deleteVectorForWare方法 - 不同条件组合")
    void testDeleteVectorForWare_DifferentConditions() {
        log.info("=== 测试deleteVectorForWare方法 - 不同条件组合 ===");
        
        try {
            // 测试不同的图片URL
            boolean result1 = vectorSearchService.deleteVectorForWare(
                    testProduct, "https://example.com/different-image.jpg", TEST_TEAM_ID);
            log.info("不同图片URL测试结果: {}", result1);
            
            // 测试不同的团队ID
            boolean result2 = vectorSearchService.deleteVectorForWare(
                    testProduct, TEST_IMG_URL, 2002L);
            log.info("不同团队ID测试结果: {}", result2);
            
            // 测试不同的商品
            TrainJdProducts anotherProduct = new TrainJdProducts();
            anotherProduct.setId(54321L);
            anotherProduct.setWareId(98765L);
            anotherProduct.setTitle("另一个测试商品");
            
            boolean result3 = vectorSearchService.deleteVectorForWare(
                    anotherProduct, TEST_IMG_URL, TEST_TEAM_ID);
            log.info("不同商品测试结果: {}", result3);
            
            // 所有删除操作都应该成功（即使没有找到向量）
            Assertions.assertTrue(result1, "删除操作应该成功");
            Assertions.assertTrue(result2, "删除操作应该成功");
            Assertions.assertTrue(result3, "删除操作应该成功");
            
            log.info("✅ 不同条件组合测试通过");
            
        } catch (Exception e) {
            log.error("不同条件组合测试失败", e);
            Assertions.fail("不同条件组合测试不应该抛出异常: " + e.getMessage());
        }
    }
    
    @Test
    @Order(5)
    @DisplayName("测试deleteVectorForWare方法 - 性能测试")
    void testDeleteVectorForWare_Performance() {
        log.info("=== 测试deleteVectorForWare方法 - 性能测试 ===");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行多次删除操作
            for (int i = 0; i < 5; i++) {
                TrainJdProducts product = new TrainJdProducts();
                product.setId((long) (10000 + i));
                product.setWareId((long) (20000 + i));
                product.setTitle("性能测试商品" + i);
                
                boolean result = vectorSearchService.deleteVectorForWare(
                        product, TEST_IMG_URL + "?v=" + i, TEST_TEAM_ID);
                
                log.debug("第{}次删除结果: {}", i + 1, result);
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("性能测试完成，总耗时: {}ms", duration);
            
            // 验证性能在合理范围内（这里设置为10秒，实际可根据需要调整）
            Assertions.assertTrue(duration < 10000, "删除操作耗时应该在合理范围内");
            
            log.info("✅ 性能测试通过");
            
        } catch (Exception e) {
            log.error("性能测试失败", e);
            Assertions.fail("性能测试不应该抛出异常: " + e.getMessage());
        }
    }
}
