package com.yiyi.ai_train_playground.service.qdrant.impl;

import com.yiyi.ai_train_playground.entity.jd.TrainJdProducts;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * VectorSearchService processVectorForWare 方法测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@TestPropertySource(properties = {
        "product.processing.jd-collection-name=test_train_prod_jd_collection"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class VectorSearchServiceProcessVectorForWareTest {
    
    @Autowired
    private VectorSearchService vectorSearchService;
    
    private TrainJdProducts testProduct;
    private String testRecoText;
    private String testImgUrl;
    private Long testTeamId;
    
    @BeforeEach
    public void setUp() {
        // 准备测试数据
        testProduct = new TrainJdProducts();
        testProduct.setId(12345L);
        testProduct.setWareId(67890L);
        testProduct.setTitle("测试京东商品");
        
        testRecoText = "# 测试商品推荐\n\n这是一个测试商品的推荐文本，包含Markdown格式。\n\n## 特点\n- 高质量\n- 性价比高\n- 用户好评";
        testImgUrl = "https://example.com/test-image.jpg";
        testTeamId = 1001L;
    }
    
    @Test
    @Order(1)
    @DisplayName("测试processVectorForWare方法 - 正常情况")
    public void testProcessVectorForWare_Success() {
        System.out.println("=== 测试processVectorForWare方法 - 正常情况 ===");
        
        try {
            boolean result = vectorSearchService.processVectorForWare(
                    testProduct, testRecoText, testImgUrl, testTeamId);
            
            System.out.println("处理结果: " + result);
            System.out.println("商品ID: " + testProduct.getId());
            System.out.println("团队ID: " + testTeamId);
            System.out.println("图片URL: " + testImgUrl);
            System.out.println("推荐文本长度: " + testRecoText.length());
            
            // 注意：由于可能没有配置有效的向量化服务，这里主要测试方法不抛异常
            Assertions.assertNotNull(result, "返回结果不应为null");
            System.out.println("✅ processVectorForWare方法测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("向量化失败")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的向量化服务");
                Assumptions.assumeTrue(false, "需要配置有效的向量化服务");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试processVectorForWare方法 - 空文本")
    public void testProcessVectorForWare_EmptyText() {
        System.out.println("=== 测试processVectorForWare方法 - 空文本 ===");
        
        boolean result = vectorSearchService.processVectorForWare(
                testProduct, "", testImgUrl, testTeamId);
        
        Assertions.assertFalse(result, "空文本应该返回false");
        System.out.println("✅ 空文本测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试processVectorForWare方法 - null文本")
    public void testProcessVectorForWare_NullText() {
        System.out.println("=== 测试processVectorForWare方法 - null文本 ===");
        
        boolean result = vectorSearchService.processVectorForWare(
                testProduct, null, testImgUrl, testTeamId);
        
        Assertions.assertFalse(result, "null文本应该返回false");
        System.out.println("✅ null文本测试通过");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试processVectorForWare方法 - 重复插入")
    public void testProcessVectorForWare_DuplicateInsert() {
        System.out.println("=== 测试processVectorForWare方法 - 重复插入 ===");
        
        try {
            // 第一次插入
            boolean result1 = vectorSearchService.processVectorForWare(
                    testProduct, testRecoText, testImgUrl, testTeamId);
            
            // 第二次插入相同数据（应该先删除再插入）
            boolean result2 = vectorSearchService.processVectorForWare(
                    testProduct, testRecoText, testImgUrl, testTeamId);
            
            System.out.println("第一次插入结果: " + result1);
            System.out.println("第二次插入结果: " + result2);
            
            // 两次插入都应该有明确的结果
            Assertions.assertNotNull(result1, "第一次插入结果不应为null");
            Assertions.assertNotNull(result2, "第二次插入结果不应为null");
            System.out.println("✅ 重复插入测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("向量化失败")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的向量化服务");
                Assumptions.assumeTrue(false, "需要配置有效的向量化服务");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(5)
    @DisplayName("测试processVectorForWare方法 - 不同参数组合")
    public void testProcessVectorForWare_DifferentParameters() {
        System.out.println("=== 测试processVectorForWare方法 - 不同参数组合 ===");
        
        try {
            // 测试不同的图片URL
            boolean result1 = vectorSearchService.processVectorForWare(
                    testProduct, testRecoText, "https://example.com/image1.jpg", testTeamId);
            
            // 测试不同的团队ID
            boolean result2 = vectorSearchService.processVectorForWare(
                    testProduct, testRecoText, testImgUrl, 2002L);
            
            // 测试不同的商品
            TrainJdProducts anotherProduct = new TrainJdProducts();
            anotherProduct.setId(54321L);
            anotherProduct.setWareId(98765L);
            anotherProduct.setTitle("另一个测试商品");
            
            boolean result3 = vectorSearchService.processVectorForWare(
                    anotherProduct, testRecoText, testImgUrl, testTeamId);
            
            System.out.println("不同图片URL结果: " + result1);
            System.out.println("不同团队ID结果: " + result2);
            System.out.println("不同商品结果: " + result3);
            
            Assertions.assertNotNull(result1, "不同图片URL结果不应为null");
            Assertions.assertNotNull(result2, "不同团队ID结果不应为null");
            Assertions.assertNotNull(result3, "不同商品结果不应为null");
            System.out.println("✅ 不同参数组合测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("向量化失败")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的向量化服务");
                Assumptions.assumeTrue(false, "需要配置有效的向量化服务");
            } else {
                throw e;
            }
        }
    }
}
