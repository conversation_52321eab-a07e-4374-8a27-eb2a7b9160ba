package com.yiyi.ai_train_playground.service.shortphrase.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseCreateRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseDetailDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseListDTO;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseQueryRequest;
import com.yiyi.ai_train_playground.dto.shortphrase.ShortcutPhraseUpdateRequest;
import com.yiyi.ai_train_playground.entity.shortphrase.ShortcutPhrase;
import com.yiyi.ai_train_playground.service.shortphrase.ShortcutPhraseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 快捷短语服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ShortcutPhraseServiceImplTest {
    
    @Autowired
    private ShortcutPhraseService shortcutPhraseService;
    
    private static final Long TEST_TEAM_ID = 1L;
    private static final Long TEST_SP_GROUP_ID = 1L;
    private static final String TEST_CREATOR = "test_user";
    
    @Test
    public void testCreatePhrase() {
        log.info("测试创建快捷短语");
        
        // 准备测试数据
        ShortcutPhraseCreateRequest request = new ShortcutPhraseCreateRequest();
        request.setSpGroupId(TEST_SP_GROUP_ID);
        request.setPhraseTitle("测试短语");
        request.setPhraseContent("这是一个测试短语内容");
        request.setPhraseType(0); // 文本类型
        request.setIsActive(true);
        request.setSortOrder(0);
        request.setTags("测试,短语");
        
        // 执行创建
        Long phraseId = shortcutPhraseService.createPhrase(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertNotNull(phraseId);
        assertTrue(phraseId > 0);
        
        log.info("创建快捷短语成功，phraseId: {}", phraseId);
    }
    
    @Test
    public void testGetPhraseDetail() {
        log.info("测试查询快捷短语详情");
        
        // 先创建一个短语
        Long phraseId = createTestPhrase();
        
        // 查询短语详情
        ShortcutPhraseDetailDTO detail = shortcutPhraseService.getPhraseDetail(phraseId, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(detail);
        assertEquals(phraseId, detail.getId());
        assertEquals("测试短语", detail.getPhraseTitle());
        assertEquals("这是一个测试短语内容", detail.getPhraseContent());
        assertEquals(Integer.valueOf(0), detail.getPhraseType());
        assertEquals("文本", detail.getPhraseTypeName());
        assertEquals(TEST_SP_GROUP_ID, detail.getSpGroupId());
        
        log.info("查询快捷短语详情成功，短语标题: {}", detail.getPhraseTitle());
    }
    
    @Test
    public void testGetPhraseList() {
        log.info("测试查询快捷短语列表");
        
        // 先创建一个短语
        createTestPhrase();
        
        // 准备查询条件
        ShortcutPhraseQueryRequest request = new ShortcutPhraseQueryRequest();
        request.setPhraseTitle("测试");
        request.setPage(1);
        request.setPageSize(10);
        
        // 执行查询
        PageResult<ShortcutPhraseListDTO> result = shortcutPhraseService.getPhraseList(request, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getRecords().size() > 0);
        
        ShortcutPhraseListDTO firstPhrase = result.getRecords().get(0);
        assertNotNull(firstPhrase.getId());
        assertNotNull(firstPhrase.getPhraseTitle());
        
        log.info("查询快捷短语列表成功，共 {} 条记录", result.getTotal());
    }
    
    @Test
    public void testUpdatePhrase() {
        log.info("测试更新快捷短语");
        
        // 先创建一个短语
        Long phraseId = createTestPhrase();
        
        // 准备更新数据
        ShortcutPhraseUpdateRequest request = new ShortcutPhraseUpdateRequest();
        request.setId(phraseId);
        request.setPhraseTitle("更新后的短语标题");
        request.setPhraseContent("更新后的短语内容");
        request.setSortOrder(10);
        
        // 执行更新
        boolean success = shortcutPhraseService.updatePhrase(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertTrue(success);
        
        // 查询更新后的短语
        ShortcutPhraseDetailDTO detail = shortcutPhraseService.getPhraseDetail(phraseId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals("更新后的短语标题", detail.getPhraseTitle());
        assertEquals("更新后的短语内容", detail.getPhraseContent());
        assertEquals(Integer.valueOf(10), detail.getSortOrder());
        
        log.info("更新快捷短语成功");
    }

    @Test
    public void testDeletePhrase() {
        log.info("测试删除快捷短语");

        // 先创建一个短语
        Long phraseId = createTestPhrase();

        // 验证短语存在
        ShortcutPhrase phrase = shortcutPhraseService.getPhraseById(phraseId, TEST_TEAM_ID);
        assertNotNull(phrase);

        // 执行删除
        boolean success = shortcutPhraseService.deletePhrase(phraseId, TEST_TEAM_ID);

        // 验证结果
        assertTrue(success);

        // 验证短语已被删除
        ShortcutPhrase deletedPhrase = shortcutPhraseService.getPhraseById(phraseId, TEST_TEAM_ID);
        assertNull(deletedPhrase);

        log.info("删除快捷短语成功");
    }

    @Test
    public void testBatchDeletePhrases() {
        log.info("测试批量删除快捷短语");

        // 先创建两个短语
        Long phraseId1 = createTestPhrase();
        Long phraseId2 = createTestPhrase();

        // 验证短语存在
        assertNotNull(shortcutPhraseService.getPhraseById(phraseId1, TEST_TEAM_ID));
        assertNotNull(shortcutPhraseService.getPhraseById(phraseId2, TEST_TEAM_ID));

        // 执行批量删除
        String ids = phraseId1 + "," + phraseId2;
        boolean success = shortcutPhraseService.batchDeletePhrases(ids, TEST_TEAM_ID);

        // 验证结果
        assertTrue(success);

        // 验证短语已被删除
        assertNull(shortcutPhraseService.getPhraseById(phraseId1, TEST_TEAM_ID));
        assertNull(shortcutPhraseService.getPhraseById(phraseId2, TEST_TEAM_ID));

        log.info("批量删除快捷短语成功");
    }

    @Test
    public void testIncrementUsageCount() {
        log.info("测试增加快捷短语使用次数");

        // 先创建一个短语
        Long phraseId = createTestPhrase();

        // 获取初始使用次数
        ShortcutPhraseDetailDTO detail = shortcutPhraseService.getPhraseDetail(phraseId, TEST_TEAM_ID);
        assertNotNull(detail);
        int initialUsageCount = detail.getUsageCount();

        // 执行增加使用次数
        boolean success = shortcutPhraseService.incrementUsageCount(phraseId, TEST_TEAM_ID);

        // 验证结果
        assertTrue(success);

        // 验证使用次数已增加
        ShortcutPhraseDetailDTO updatedDetail = shortcutPhraseService.getPhraseDetail(phraseId, TEST_TEAM_ID);
        assertNotNull(updatedDetail);
        assertEquals(initialUsageCount + 1, updatedDetail.getUsageCount().intValue());

        log.info("增加快捷短语使用次数成功");
    }

    @Test
    public void testGetPhrasesByGroupId() {
        log.info("测试根据分组ID查询快捷短语列表");

        // 先创建一个短语
        createTestPhrase();

        // 根据分组ID查询短语列表
        List<ShortcutPhraseListDTO> list = shortcutPhraseService.getPhrasesByGroupId(TEST_SP_GROUP_ID, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(list);
        assertTrue(list.size() > 0);

        ShortcutPhraseListDTO firstPhrase = list.get(0);
        assertNotNull(firstPhrase.getId());
        assertEquals(TEST_SP_GROUP_ID, firstPhrase.getSpGroupId());
        assertTrue(firstPhrase.getIsActive());

        log.info("根据分组ID查询快捷短语列表成功，共 {} 条记录", list.size());
    }

    @Test
    public void testGetPhraseByIdNotFound() {
        log.info("测试查询不存在的快捷短语");

        // 查询不存在的短语
        ShortcutPhrase phrase = shortcutPhraseService.getPhraseById(999999L, TEST_TEAM_ID);

        // 验证结果
        assertNull(phrase);

        log.info("查询不存在的快捷短语，返回null");
    }

    /**
     * 创建测试短语的辅助方法
     */
    private Long createTestPhrase() {
        ShortcutPhraseCreateRequest request = new ShortcutPhraseCreateRequest();
        request.setSpGroupId(TEST_SP_GROUP_ID);
        request.setPhraseTitle("测试短语");
        request.setPhraseContent("这是一个测试短语内容");
        request.setPhraseType(0); // 文本类型
        request.setIsActive(true);
        request.setSortOrder(0);
        request.setTags("测试,短语");

        return shortcutPhraseService.createPhrase(request, TEST_TEAM_ID, TEST_CREATOR);
    }
}
