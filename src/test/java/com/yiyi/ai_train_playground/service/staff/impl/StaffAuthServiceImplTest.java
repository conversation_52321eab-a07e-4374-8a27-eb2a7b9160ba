package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.staff.StaffLoginRequest;
import com.yiyi.ai_train_playground.dto.staff.StaffLoginResponse;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.mapper.staff.TrainStaffMapper;
import com.yiyi.ai_train_playground.service.staff.StaffAuthService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 员工认证服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-27
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class StaffAuthServiceImplTest {

    @Autowired
    private StaffAuthService staffAuthService;

    @Autowired
    private TrainStaffMapper trainStaffMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    private TrainStaff testStaff;

    @BeforeEach
    void setUp() {
        // 创建测试员工数据
        testStaff = new TrainStaff();
        testStaff.setUserId(1001L);
        testStaff.setUsername("teststaff");
        testStaff.setPasswordHash(passwordEncoder.encode("123456"));
        testStaff.setDisplayName("测试员工");
        testStaff.setIsLocked(false);
        testStaff.setFailedAttempts(0);
        testStaff.setTeamId(1L);
        testStaff.setCreator("test");
        testStaff.setUpdater("test");

        trainStaffMapper.insert(testStaff);
    }


    @Test
    public void testPasswordIsEqual(){

        String myHash="$2a$10$kJkQHstG1jbJE9MJeMOs2eKFeM05aMLvzhQi8dr5hIznC.8.C7uc2";
        myHash="$2a$10$jK0Zv32Lo1Ernv449m/joOoGG481.iLMoOefdR.fcDcqwfbxLO2Um";
        assertTrue(passwordEncoder.matches("123456",myHash));


    }


    @Test
    void testLoginSuccess() {
        // 测试成功登录
        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("teststaff");
        request.setPassword("123456");
        request.setRememberMe(false);

        StaffLoginResponse response = staffAuthService.login(request);

        assertNotNull(response);
        assertEquals("teststaff", response.getUsername());
        assertEquals("测试员工", response.getDisplayName());
        assertEquals(1L, response.getTeamId());
        assertEquals(Integer.valueOf(1), response.getStatus()); // 总是返回1（正常状态）
        assertNotNull(response.getToken());

        // 验证JWT Token是否有效
        assertTrue(jwtUtil.validateToken(response.getToken()));
        assertEquals("teststaff", jwtUtil.getUsernameFromToken(response.getToken()));
    }

    @Test
    void testLoginWithWrongPassword() {
        // 测试密码错误
        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("teststaff");
        request.setPassword("wrongpassword");
        request.setRememberMe(false);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffAuthService.login(request);
        });

        assertEquals("用户名或密码错误", exception.getMessage());

        // 验证失败次数增加
        TrainStaff updatedStaff = trainStaffMapper.selectById(testStaff.getId());
        assertEquals(1, updatedStaff.getFailedAttempts());
    }

    @Test
    void testLoginWithNonExistentUser() {
        // 测试用户不存在
        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("nonexistent");
        request.setPassword("123456");
        request.setRememberMe(false);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffAuthService.login(request);
        });

        assertEquals("用户名或密码错误", exception.getMessage());
    }

    @Test
    void testLoginWithLockedAccount() {
        // 设置账号为锁定状态
        testStaff.setIsLocked(true);
        testStaff.setLockTime(LocalDateTime.now());
        trainStaffMapper.updateById(testStaff);

        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("teststaff");
        request.setPassword("123456");
        request.setRememberMe(false);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffAuthService.login(request);
        });

        assertEquals("账号已被锁定，请30分钟后再试", exception.getMessage());
    }

    @Test
    void testLoginWithExpiredLock() {
        // 设置账号锁定时间为30分钟前（应该自动解锁）
        testStaff.setIsLocked(true);
        testStaff.setLockTime(LocalDateTime.now().minusMinutes(31));
        testStaff.setFailedAttempts(5);
        trainStaffMapper.updateById(testStaff);

        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("teststaff");
        request.setPassword("123456");
        request.setRememberMe(false);

        StaffLoginResponse response = staffAuthService.login(request);

        assertNotNull(response);
        assertEquals("teststaff", response.getUsername());

        // 验证锁定状态已重置
        TrainStaff updatedStaff = trainStaffMapper.selectById(testStaff.getId());
        assertFalse(updatedStaff.getIsLocked());
        assertEquals(0, updatedStaff.getFailedAttempts());
    }

    @Test
    void testAccountLockAfterMultipleFailedAttempts() {
        // 测试连续失败5次后账号被锁定
        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("teststaff");
        request.setPassword("wrongpassword");
        request.setRememberMe(false);

        // 失败4次
        for (int i = 0; i < 4; i++) {
            assertThrows(RuntimeException.class, () -> {
                staffAuthService.login(request);
            });
        }

        // 第5次失败应该触发锁定
        assertThrows(RuntimeException.class, () -> {
            staffAuthService.login(request);
        });

        // 验证账号已被锁定
        TrainStaff updatedStaff = trainStaffMapper.selectById(testStaff.getId());
        assertTrue(updatedStaff.getIsLocked());
        assertEquals(5, updatedStaff.getFailedAttempts());
        assertNotNull(updatedStaff.getLockTime());
    }

    @Test
    void testRememberMeToken() {
        // 测试记住我功能
        StaffLoginRequest request = new StaffLoginRequest();
        request.setUsername("teststaff");
        request.setPassword("123456");
        request.setRememberMe(true);

        StaffLoginResponse response = staffAuthService.login(request);

        assertNotNull(response);
        assertNotNull(response.getToken());
        assertTrue(jwtUtil.validateToken(response.getToken()));
    }
}