package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.entity.staff.TrainRole;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 角色管理Service单元测试
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainRoleServiceImplTest {

    @Autowired
    private TrainRoleServiceImpl trainRoleService;

    @Test
    public void testCreateRole() {
        // 准备测试数据
        TrainRoleCreateRequest request = new TrainRoleCreateRequest();
        request.setRoleName("测试角色");
        request.setRoleCode("TEST_ROLE");
        request.setDescription("这是一个测试角色");

        // 执行测试
        Long roleId = trainRoleService.createRole(request);

        // 验证结果
        assertNotNull(roleId);
        assertTrue(roleId > 0);

        // 验证创建的角色信息
        TrainRoleDetailResponse detail = trainRoleService.getRoleDetail(roleId);
        assertEquals("测试角色", detail.getRoleName());
        assertEquals("TEST_ROLE", detail.getRoleCode());
        assertEquals("这是一个测试角色", detail.getDescription());
    }

    @Test
    public void testCreateRoleWithDuplicateName() {
        // 先创建一个角色
        TrainRoleCreateRequest request1 = new TrainRoleCreateRequest();
        request1.setRoleName("重复角色");
        request1.setRoleCode("DUPLICATE_ROLE_1");
        request1.setDescription("重复测试1");

        trainRoleService.createRole(request1);

        // 尝试创建重复名称的角色
        TrainRoleCreateRequest request2 = new TrainRoleCreateRequest();
        request2.setRoleName("重复角色");
        request2.setRoleCode("DUPLICATE_ROLE_2");
        request2.setDescription("重复测试2");

        // 验证抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainRoleService.createRole(request2);
        });
        assertEquals("角色名称已存在", exception.getMessage());
    }

    @Test
    public void testCreateRoleWithDuplicateCode() {
        // 先创建一个角色
        TrainRoleCreateRequest request1 = new TrainRoleCreateRequest();
        request1.setRoleName("角色1");
        request1.setRoleCode("DUPLICATE_CODE");
        request1.setDescription("重复编码测试1");

        trainRoleService.createRole(request1);

        // 尝试创建重复编码的角色
        TrainRoleCreateRequest request2 = new TrainRoleCreateRequest();
        request2.setRoleName("角色2");
        request2.setRoleCode("DUPLICATE_CODE");
        request2.setDescription("重复编码测试2");

        // 验证抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainRoleService.createRole(request2);
        });
        assertEquals("角色编码已存在", exception.getMessage());
    }

    @Test
    public void testUpdateRole() {
        // 先创建一个角色
        TrainRoleCreateRequest createRequest = new TrainRoleCreateRequest();
        createRequest.setRoleName("更新前角色");
        createRequest.setRoleCode("UPDATE_TEST_ROLE");
        createRequest.setDescription("更新前描述");

        Long roleId = trainRoleService.createRole(createRequest);
        TrainRoleDetailResponse originalDetail = trainRoleService.getRoleDetail(roleId);

        // 准备更新数据
        TrainRoleUpdateRequest updateRequest = new TrainRoleUpdateRequest();
        updateRequest.setId(roleId);
        updateRequest.setRoleName("更新后角色");
        updateRequest.setDescription("更新后描述");
        updateRequest.setVersion(originalDetail.getVersion());

        // 执行更新
        trainRoleService.updateRole(updateRequest);

        // 验证更新结果
        TrainRoleDetailResponse updatedDetail = trainRoleService.getRoleDetail(roleId);
        assertEquals("更新后角色", updatedDetail.getRoleName());
        assertEquals("更新后描述", updatedDetail.getDescription());
        assertEquals("UPDATE_TEST_ROLE", updatedDetail.getRoleCode()); // 编码不能修改
        assertEquals(originalDetail.getVersion() + 1, updatedDetail.getVersion());
    }

    @Test
    public void testUpdateRoleWithWrongVersion() {
        // 先创建一个角色
        TrainRoleCreateRequest createRequest = new TrainRoleCreateRequest();
        createRequest.setRoleName("版本测试角色");
        createRequest.setRoleCode("VERSION_TEST_ROLE");
        createRequest.setDescription("版本测试");

        Long roleId = trainRoleService.createRole(createRequest);

        // 准备错误版本号的更新数据
        TrainRoleUpdateRequest updateRequest = new TrainRoleUpdateRequest();
        updateRequest.setId(roleId);
        updateRequest.setRoleName("错误版本更新");
        updateRequest.setVersion(999L); // 错误的版本号

        // 验证抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainRoleService.updateRole(updateRequest);
        });
        assertEquals("数据已被其他用户修改，请刷新后重试", exception.getMessage());
    }

    @Test
    public void testDeleteRole() {
        // 先创建一个角色
        TrainRoleCreateRequest createRequest = new TrainRoleCreateRequest();
        createRequest.setRoleName("删除测试角色");
        createRequest.setRoleCode("DELETE_TEST_ROLE");
        createRequest.setDescription("删除测试");

        Long roleId = trainRoleService.createRole(createRequest);

        // 验证角色存在
        TrainRoleDetailResponse detail = trainRoleService.getRoleDetail(roleId);
        assertNotNull(detail);

        // 执行删除
        trainRoleService.deleteRole(roleId);

        // 验证角色已被删除
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainRoleService.getRoleDetail(roleId);
        });
        assertEquals("角色不存在", exception.getMessage());
    }

    @Test
    public void testDeleteRoleBatch() {
        // 先创建多个角色
        TrainRoleCreateRequest request1 = new TrainRoleCreateRequest();
        request1.setRoleName("批量删除角色1");
        request1.setRoleCode("BATCH_DELETE_1");
        request1.setDescription("批量删除测试1");

        TrainRoleCreateRequest request2 = new TrainRoleCreateRequest();
        request2.setRoleName("批量删除角色2");
        request2.setRoleCode("BATCH_DELETE_2");
        request2.setDescription("批量删除测试2");

        Long roleId1 = trainRoleService.createRole(request1);
        Long roleId2 = trainRoleService.createRole(request2);

        // 验证角色存在
        assertNotNull(trainRoleService.getRoleDetail(roleId1));
        assertNotNull(trainRoleService.getRoleDetail(roleId2));

        // 执行批量删除
        List<Long> ids = Arrays.asList(roleId1, roleId2);
        trainRoleService.deleteRoleBatch(ids);

        // 验证角色已被删除
        assertThrows(RuntimeException.class, () -> trainRoleService.getRoleDetail(roleId1));
        assertThrows(RuntimeException.class, () -> trainRoleService.getRoleDetail(roleId2));
    }

    @Test
    public void testGetRolePageList() {
        // 先创建多个角色
        for (int i = 1; i <= 5; i++) {
            TrainRoleCreateRequest request = new TrainRoleCreateRequest();
            request.setRoleName("分页测试角色" + i);
            request.setRoleCode("PAGE_TEST_ROLE_" + i);
            request.setDescription("分页测试描述" + i);
            trainRoleService.createRole(request);
        }

        // 测试分页查询
        TrainRoleQueryRequest queryRequest = new TrainRoleQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(3);
        queryRequest.setRoleName("分页测试");

        PageResult<TrainRoleDetailResponse> result = trainRoleService.getRolePageList(queryRequest);

        // 验证分页结果
        assertNotNull(result);
        assertEquals(3, result.getRecords().size());
        assertTrue(result.getTotal() >= 5);
        assertEquals(1, result.getPage());
        assertEquals(3, result.getSize());
    }

    @Test
    public void testCheckRoleNameExists() {
        // 先创建一个角色
        TrainRoleCreateRequest createRequest = new TrainRoleCreateRequest();
        createRequest.setRoleName("存在性测试角色");
        createRequest.setRoleCode("EXISTS_TEST_ROLE");
        createRequest.setDescription("存在性测试");

        Long roleId = trainRoleService.createRole(createRequest);

        // 测试已存在的角色名称
        assertTrue(trainRoleService.checkRoleNameExists("存在性测试角色", null));

        // 测试不存在的角色名称
        assertFalse(trainRoleService.checkRoleNameExists("不存在的角色", null));

        // 测试排除自身的情况
        assertFalse(trainRoleService.checkRoleNameExists("存在性测试角色", roleId));
    }

    @Test
    public void testCheckRoleCodeExists() {
        // 先创建一个角色
        TrainRoleCreateRequest createRequest = new TrainRoleCreateRequest();
        createRequest.setRoleName("编码存在性测试");
        createRequest.setRoleCode("CODE_EXISTS_TEST");
        createRequest.setDescription("编码存在性测试");

        Long roleId = trainRoleService.createRole(createRequest);

        // 测试已存在的角色编码
        assertTrue(trainRoleService.checkRoleCodeExists("CODE_EXISTS_TEST", null));

        // 测试不存在的角色编码
        assertFalse(trainRoleService.checkRoleCodeExists("NOT_EXISTS_CODE", null));

        // 测试排除自身的情况
        assertFalse(trainRoleService.checkRoleCodeExists("CODE_EXISTS_TEST", roleId));
    }

    @Test
    public void testGetRoleByCode() {
        // 先创建一个角色
        TrainRoleCreateRequest createRequest = new TrainRoleCreateRequest();
        createRequest.setRoleName("根据编码查询");
        createRequest.setRoleCode("GET_BY_CODE_TEST");
        createRequest.setDescription("根据编码查询测试");

        trainRoleService.createRole(createRequest);

        // 根据编码查询
        TrainRole role = trainRoleService.getRoleByCode("GET_BY_CODE_TEST");

        // 验证结果
        assertNotNull(role);
        assertEquals("根据编码查询", role.getRoleName());
        assertEquals("GET_BY_CODE_TEST", role.getRoleCode());
    }

    @Test
    public void testGetAllRoles() {
        // 先创建多个角色
        int createCount = 3;
        for (int i = 1; i <= createCount; i++) {
            TrainRoleCreateRequest request = new TrainRoleCreateRequest();
            request.setRoleName("获取全部角色" + i);
            request.setRoleCode("GET_ALL_ROLE_" + i);
            request.setDescription("获取全部测试" + i);
            trainRoleService.createRole(request);
        }

        // 获取所有角色
        List<TrainRoleDetailResponse> allRoles = trainRoleService.getAllRoles();

        // 验证结果
        assertNotNull(allRoles);
        assertTrue(allRoles.size() >= createCount);

        // 验证包含创建的角色
        boolean foundTestRole = allRoles.stream()
            .anyMatch(role -> role.getRoleName().startsWith("获取全部角色"));
        assertTrue(foundTestRole);
    }
}