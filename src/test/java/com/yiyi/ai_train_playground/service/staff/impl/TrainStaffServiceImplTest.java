package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffCreateRequest;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffDetailResponse;
import com.yiyi.ai_train_playground.dto.staff.TrainStaffQueryRequest;
import com.yiyi.ai_train_playground.entity.staff.TrainStaff;
import com.yiyi.ai_train_playground.service.staff.TrainStaffService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainStaffServiceImplTest {

    @Autowired
    private TrainStaffService trainStaffService;

    private final Long testTeamId = 1L;
    private final Long testUserId = 100L;

    @BeforeEach
    void setUp() {
        // 由于@Transactional注解，每个测试方法结束后会自动回滚，无需手动清理
    }

    @Test
    void testCreateStaff() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            TrainStaffCreateRequest request = new TrainStaffCreateRequest();
            request.setUsername("testuser");
            request.setPassword("password123");
            request.setDisplayName("测试用户");

            int result = trainStaffService.createStaff(request);
            
            // MyBatis insert方法返回受影响的行数，应该大于0
            assertTrue(result > 0);

            // 验证数据是否正确保存 - 通过用户名查询
            TrainStaff staff = trainStaffService.getStaffByUsername("testuser");
            assertNotNull(staff);
            assertEquals("testuser", staff.getUsername());
            assertEquals("测试用户", staff.getDisplayName());
            assertEquals(testTeamId, staff.getTeamId());
        }
    }

    @Test
    void testCreateStaffWithDuplicateUsername() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 创建第一个员工
            TrainStaffCreateRequest request1 = new TrainStaffCreateRequest();
            request1.setUsername("duplicateuser");
            request1.setPassword("password123");
            request1.setDisplayName("用户1");

            trainStaffService.createStaff(request1);

            // 尝试创建用户名重复的员工
            TrainStaffCreateRequest request2 = new TrainStaffCreateRequest();
            request2.setUsername("duplicateuser");
            request2.setPassword("password456");
            request2.setDisplayName("用户2");

            assertThrows(RuntimeException.class, () -> {
                trainStaffService.createStaff(request2);
            });
        }
    }

    @Test
    void testGetStaffDetail() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 先创建一个员工
            TrainStaffCreateRequest createRequest = new TrainStaffCreateRequest();
            createRequest.setUsername("getdetailuser");
            createRequest.setPassword("password123");
            createRequest.setDisplayName("查询测试用户");

            int createResult = trainStaffService.createStaff(createRequest);
            assertTrue(createResult > 0);
            
            // 通过用户名获取员工信息
            TrainStaff staff = trainStaffService.getStaffByUsername("getdetailuser");
            assertNotNull(staff);

            // 查询员工详情
            TrainStaffDetailResponse response = trainStaffService.getStaffDetail(staff.getId());
            
            assertNotNull(response);
            assertEquals(staff.getId(), response.getId());
            assertEquals("getdetailuser", response.getUsername());
            assertEquals("查询测试用户", response.getDisplayName());
        }
    }

    @Test
    void testDeleteStaff() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 先创建一个员工
            TrainStaffCreateRequest createRequest = new TrainStaffCreateRequest();
            createRequest.setUsername("deleteuser");
            createRequest.setPassword("password123");
            createRequest.setDisplayName("删除测试用户");

            trainStaffService.createStaff(createRequest);
            TrainStaff staff = trainStaffService.getStaffByUsername("deleteuser");
            assertNotNull(staff);

            // 删除员工
            int result = trainStaffService.deleteStaff(staff.getId());
            assertEquals(1, result);

            // 确认员工已删除
            TrainStaff deletedStaff = trainStaffService.getStaffByUsername("deleteuser");
            assertNull(deletedStaff);
        }
    }

    @Test
    void testGetStaffPageList() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 先查询当前数据库中的员工数量
            TrainStaffQueryRequest initialQuery = new TrainStaffQueryRequest();
            initialQuery.setPage(1);
            initialQuery.setPageSize(100);
            PageResult<TrainStaffDetailResponse> initialResult = trainStaffService.getStaffPageList(initialQuery);
            long initialCount = initialResult.getTotal();

            // 创建测试数据
            for (int i = 1; i <= 5; i++) {
                TrainStaffCreateRequest createRequest = new TrainStaffCreateRequest();
                createRequest.setUsername("listuser" + i);
                createRequest.setPassword("password123");
                createRequest.setDisplayName("列表用户" + i);
                trainStaffService.createStaff(createRequest);
            }

            // 测试分页查询
            TrainStaffQueryRequest queryRequest = new TrainStaffQueryRequest();
            queryRequest.setPage(1);
            queryRequest.setPageSize(3);

            PageResult<TrainStaffDetailResponse> result = trainStaffService.getStaffPageList(queryRequest);

            assertNotNull(result);
            assertEquals(3, result.getRecords().size());
            assertEquals(initialCount + 5, result.getTotal()); // 原有数量 + 新增的5个
            assertEquals(1, result.getPage());
            assertEquals(3, result.getSize());
        }
    }

    @Test
    void testCheckUsernameExists() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 创建一个员工
            TrainStaffCreateRequest createRequest = new TrainStaffCreateRequest();
            createRequest.setUsername("existsuser");
            createRequest.setPassword("password123");
            createRequest.setDisplayName("存在检查用户");

            trainStaffService.createStaff(createRequest);

            // 检查用户名是否存在
            boolean exists = trainStaffService.checkUsernameExists("existsuser", null);
            assertTrue(exists);

            boolean notExists = trainStaffService.checkUsernameExists("notexistsuser", null);
            assertFalse(notExists);
        }
    }

    @Test
    void testResetPassword() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 先创建一个员工
            TrainStaffCreateRequest createRequest = new TrainStaffCreateRequest();
            createRequest.setUsername("resetuser");
            createRequest.setPassword("oldpassword");
            createRequest.setDisplayName("重置密码用户");

            trainStaffService.createStaff(createRequest);
            TrainStaff staff = trainStaffService.getStaffByUsername("resetuser");

            // 重置密码
            String newPassword = "newpassword123";
            int result = trainStaffService.resetPassword(staff.getId(), newPassword);
            assertEquals(1, result);
        }
    }

    @Test
    void testToggleAccountLock() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 先创建一个员工
            TrainStaffCreateRequest createRequest = new TrainStaffCreateRequest();
            createRequest.setUsername("lockuser");
            createRequest.setPassword("password123");
            createRequest.setDisplayName("锁定测试用户");

            trainStaffService.createStaff(createRequest);
            TrainStaff staff = trainStaffService.getStaffByUsername("lockuser");

            // 锁定员工
            int result = trainStaffService.toggleAccountLock(staff.getId(), true);
            assertEquals(1, result);

            // 解锁员工
            result = trainStaffService.toggleAccountLock(staff.getId(), false);
            assertEquals(1, result);
        }
    }

    @Test
    void testDeleteStaffBatchWithIts() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 创建多个测试员工
            TrainStaffCreateRequest createRequest1 = new TrainStaffCreateRequest();
            createRequest1.setUsername("tenant1");
            createRequest1.setPassword("password123");
            createRequest1.setDisplayName("租户测试用户1");
            trainStaffService.createStaff(createRequest1);

            TrainStaffCreateRequest createRequest2 = new TrainStaffCreateRequest();
            createRequest2.setUsername("tenant2");
            createRequest2.setPassword("password123");
            createRequest2.setDisplayName("租户测试用户2");
            trainStaffService.createStaff(createRequest2);

            // 获取创建的员工信息
            TrainStaff staff1 = trainStaffService.getStaffByUsername("tenant1");
            TrainStaff staff2 = trainStaffService.getStaffByUsername("tenant2");
            assertNotNull(staff1);
            assertNotNull(staff2);

            // 构造删除ID列表
            List<Long> ids = Arrays.asList(staff1.getId(), staff2.getId());

            // 使用拦截器方式删除员工，验证拦截器会自动添加team_id条件
            int result = trainStaffService.deleteStaffBatchWithIts(ids);
            assertEquals(2, result, "应该删除2条记录");

            // 验证员工已被删除
            TrainStaff deletedStaff1 = trainStaffService.getStaffByUsername("tenant1");
            TrainStaff deletedStaff2 = trainStaffService.getStaffByUsername("tenant2");
            assertNull(deletedStaff1, "员工1应该已被删除");
            assertNull(deletedStaff2, "员工2应该已被删除");
        }
    }

    @Test
    void testDeleteStaffBatchWithIts_realData() {
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);
            mockedSecurityUtil.when(SecurityUtil::getCurrentUserId).thenReturn(testUserId);

            // 构造删除ID列表
            List<Long> ids = Arrays.asList(110L, 111L);

            // 使用拦截器方式删除员工，验证拦截器会自动添加team_id条件
            int result = trainStaffService.deleteStaffBatchWithIts(ids);
            assertEquals(2, result, "应该删除2条记录");

            // 验证员工已被删除
            TrainStaff deletedStaff1 = trainStaffService.getStaffByUsername("tenant1");
            TrainStaff deletedStaff2 = trainStaffService.getStaffByUsername("tenant2");
            assertNull(deletedStaff1, "员工1应该已被删除");
            assertNull(deletedStaff2, "员工2应该已被删除");
        }
    }
}