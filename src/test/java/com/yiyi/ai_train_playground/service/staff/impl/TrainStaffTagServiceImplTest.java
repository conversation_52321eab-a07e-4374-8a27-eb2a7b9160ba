package com.yiyi.ai_train_playground.service.staff.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.staff.*;
import com.yiyi.ai_train_playground.service.staff.TrainStaffTagService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainStaffTagServiceImplTest {

    @Autowired
    private TrainStaffTagService staffTagService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_CREATOR = "test_user";

    @Test
    public void testCreateStaffTag() {
        // 准备测试数据
        StaffTagCreateRequest request = new StaffTagCreateRequest();
        request.setName("技术专家");

        // 执行创建
        Long staffTagId = staffTagService.createStaffTag(request, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertNotNull(staffTagId);
        assertTrue(staffTagId > 0);

        // 验证数据是否正确创建
        StaffTagDTO staffTag = staffTagService.getStaffTagById(staffTagId, TEST_TEAM_ID);
        assertNotNull(staffTag);
        assertEquals("技术专家", staffTag.getName());
        assertEquals(TEST_TEAM_ID, staffTag.getTeamId());
        assertEquals(TEST_CREATOR, staffTag.getCreator());

        log.info("创建员工标签测试通过，ID: {}", staffTagId);
    }

    @Test
    public void testCreateStaffTagWithDuplicateName() {
        // 先创建一个标签
        StaffTagCreateRequest request1 = new StaffTagCreateRequest();
        request1.setName("产品经理");
        staffTagService.createStaffTag(request1, TEST_TEAM_ID, TEST_CREATOR);

        // 尝试创建重名标签
        StaffTagCreateRequest request2 = new StaffTagCreateRequest();
        request2.setName("产品经理");

        // 应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffTagService.createStaffTag(request2, TEST_TEAM_ID, TEST_CREATOR);
        });

        assertEquals("标签名称已存在", exception.getMessage());
        log.info("重名标签创建测试通过");
    }

    @Test
    public void testUpdateStaffTag() {
        // 先创建一个标签
        StaffTagCreateRequest createRequest = new StaffTagCreateRequest();
        createRequest.setName("初级开发");
        Long staffTagId = staffTagService.createStaffTag(createRequest, TEST_TEAM_ID, TEST_CREATOR);

        // 获取创建后的详情（包含版本号）
        StaffTagDTO staffTag = staffTagService.getStaffTagById(staffTagId, TEST_TEAM_ID);

        // 准备更新数据
        StaffTagUpdateRequest updateRequest = new StaffTagUpdateRequest();
        updateRequest.setId(staffTagId);
        updateRequest.setName("高级开发");
        updateRequest.setVersion(staffTag.getVersion());

        // 执行更新
        staffTagService.updateStaffTag(updateRequest, TEST_TEAM_ID, "updater");

        // 验证更新结果
        StaffTagDTO updatedStaffTag = staffTagService.getStaffTagById(staffTagId, TEST_TEAM_ID);
        assertEquals("高级开发", updatedStaffTag.getName());
        assertEquals("updater", updatedStaffTag.getUpdater());
        assertEquals(staffTag.getVersion() + 1, updatedStaffTag.getVersion());

        log.info("更新员工标签测试通过");
    }

    @Test
    public void testUpdateStaffTagWithWrongVersion() {
        // 先创建一个标签
        StaffTagCreateRequest createRequest = new StaffTagCreateRequest();
        createRequest.setName("测试标签");
        Long staffTagId = staffTagService.createStaffTag(createRequest, TEST_TEAM_ID, TEST_CREATOR);

        // 准备更新数据（使用错误的版本号）
        StaffTagUpdateRequest updateRequest = new StaffTagUpdateRequest();
        updateRequest.setId(staffTagId);
        updateRequest.setName("更新后的标签");
        updateRequest.setVersion(999L); // 错误的版本号

        // 应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffTagService.updateStaffTag(updateRequest, TEST_TEAM_ID, "updater");
        });

        assertEquals("数据已被其他用户修改，请刷新后重试", exception.getMessage());
        log.info("乐观锁测试通过");
    }

    @Test
    public void testDeleteStaffTag() {
        // 先创建一个标签
        StaffTagCreateRequest request = new StaffTagCreateRequest();
        request.setName("待删除标签");
        Long staffTagId = staffTagService.createStaffTag(request, TEST_TEAM_ID, TEST_CREATOR);

        // 执行删除
        staffTagService.deleteStaffTag(staffTagId, TEST_TEAM_ID);

        // 验证删除结果
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffTagService.getStaffTagById(staffTagId, TEST_TEAM_ID);
        });

        assertEquals("员工标签不存在或无权限访问", exception.getMessage());
        log.info("删除员工标签测试通过");
    }

    @Test
    public void testBatchDeleteStaffTags() {
        // 先创建多个标签
        StaffTagCreateRequest request1 = new StaffTagCreateRequest();
        request1.setName("批量删除1");
        Long id1 = staffTagService.createStaffTag(request1, TEST_TEAM_ID, TEST_CREATOR);

        StaffTagCreateRequest request2 = new StaffTagCreateRequest();
        request2.setName("批量删除2");
        Long id2 = staffTagService.createStaffTag(request2, TEST_TEAM_ID, TEST_CREATOR);

        // 执行批量删除
        List<Long> ids = Arrays.asList(id1, id2);
        staffTagService.batchDeleteStaffTags(ids, TEST_TEAM_ID);

        // 验证删除结果
        assertThrows(RuntimeException.class, () -> {
            staffTagService.getStaffTagById(id1, TEST_TEAM_ID);
        });
        assertThrows(RuntimeException.class, () -> {
            staffTagService.getStaffTagById(id2, TEST_TEAM_ID);
        });

        log.info("批量删除员工标签测试通过");
    }

    @Test
    public void testGetStaffTagPage() {
        // 先创建多个标签
        for (int i = 1; i <= 5; i++) {
            StaffTagCreateRequest request = new StaffTagCreateRequest();
            request.setName("分页测试标签" + i);
            staffTagService.createStaffTag(request, TEST_TEAM_ID, TEST_CREATOR);
        }

        // 测试分页查询
        StaffTagQueryRequest queryRequest = new StaffTagQueryRequest();
        queryRequest.setTeamId(TEST_TEAM_ID);
        queryRequest.setPage(1);
        queryRequest.setPageSize(3);

        PageResult<StaffTagDTO> result = staffTagService.getStaffTagPage(queryRequest);

        // 验证分页结果
        assertNotNull(result);
        assertEquals(3, result.getRecords().size());
        assertTrue(result.getTotal() >= 5);
        assertEquals(1, result.getPage());
        assertEquals(3, result.getSize());

        log.info("分页查询测试通过，总数: {}", result.getTotal());
    }

    @Test
    public void testGetStaffTagPageWithNameFilter() {
        // 先创建标签
        StaffTagCreateRequest request1 = new StaffTagCreateRequest();
        request1.setName("Java开发工程师");
        staffTagService.createStaffTag(request1, TEST_TEAM_ID, TEST_CREATOR);

        StaffTagCreateRequest request2 = new StaffTagCreateRequest();
        request2.setName("Python开发工程师");
        staffTagService.createStaffTag(request2, TEST_TEAM_ID, TEST_CREATOR);

        StaffTagCreateRequest request3 = new StaffTagCreateRequest();
        request3.setName("产品经理");
        staffTagService.createStaffTag(request3, TEST_TEAM_ID, TEST_CREATOR);

        // 测试名称模糊查询
        StaffTagQueryRequest queryRequest = new StaffTagQueryRequest();
        queryRequest.setTeamId(TEST_TEAM_ID);
        queryRequest.setName("开发");
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);

        PageResult<StaffTagDTO> result = staffTagService.getStaffTagPage(queryRequest);

        // 验证查询结果
        assertNotNull(result);
        assertEquals(2, result.getRecords().size());
        assertTrue(result.getRecords().stream().allMatch(tag -> tag.getName().contains("开发")));

        log.info("名称模糊查询测试通过");
    }

    @Test
    public void testGetAllStaffTags() {
        // 先创建多个标签
        for (int i = 1; i <= 3; i++) {
            StaffTagCreateRequest request = new StaffTagCreateRequest();
            request.setName("全量查询标签" + i);
            staffTagService.createStaffTag(request, TEST_TEAM_ID, TEST_CREATOR);
        }

        // 测试获取所有标签
        List<StaffTagDTO> result = staffTagService.getAllStaffTags(TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() >= 3);

        log.info("获取所有标签测试通过，数量: {}", result.size());
    }

    @Test
    public void testCheckNameExists() {
        // 先创建一个标签
        StaffTagCreateRequest request = new StaffTagCreateRequest();
        request.setName("重名检查标签");
        Long staffTagId = staffTagService.createStaffTag(request, TEST_TEAM_ID, TEST_CREATOR);

        // 测试检查重名（应该存在）
        boolean exists1 = staffTagService.checkNameExists("重名检查标签", TEST_TEAM_ID, null);
        assertTrue(exists1);

        // 测试检查重名（排除自己，应该不存在）
        boolean exists2 = staffTagService.checkNameExists("重名检查标签", TEST_TEAM_ID, staffTagId);
        assertFalse(exists2);

        // 测试检查不存在的名称
        boolean exists3 = staffTagService.checkNameExists("不存在的标签", TEST_TEAM_ID, null);
        assertFalse(exists3);

        log.info("重名检查测试通过");
    }
}