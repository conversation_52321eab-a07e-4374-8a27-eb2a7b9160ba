package com.yiyi.ai_train_playground.service.system.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.system.*;
import com.yiyi.ai_train_playground.service.system.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;

/**
 * 系统配置Service测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Slf4j
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SystemConfigServiceImplTest {
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    private static Long testConfigId;
    private static final String TEST_NAMESPACE = "test";
    private static final String TEST_CONFIG_KEY = "test.config.key";
    private static final String TEST_CONFIG_VALUE = "test_value";
    private static final String TEST_DESCRIPTION = "测试配置项";
    
    @Test
    @Order(1)
    @DisplayName("测试创建系统配置")
    public void testCreateSystemConfig() {
        System.out.println("=== 测试创建系统配置 ===");
        
        SystemConfigCreateRequest request = new SystemConfigCreateRequest();
        request.setNamespace(TEST_NAMESPACE);
        request.setConfigKey(TEST_CONFIG_KEY);
        request.setConfigValue(TEST_CONFIG_VALUE);
        request.setDescription(TEST_DESCRIPTION);
        
        try {
            testConfigId = systemConfigService.createSystemConfig(request);
            
            System.out.println("创建成功，配置ID: " + testConfigId);
            Assertions.assertNotNull(testConfigId, "配置ID不应为null");
            Assertions.assertTrue(testConfigId > 0, "配置ID应大于0");
            
        } catch (Exception e) {
            System.out.println("创建失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("创建系统配置失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试查询系统配置详情")
    public void testGetSystemConfigDetail() {
        System.out.println("=== 测试查询系统配置详情 ===");
        
        if (testConfigId == null) {
            System.out.println("跳过测试：testConfigId为null");
            return;
        }
        
        try {
            SystemConfigResponse response = systemConfigService.getSystemConfigDetail(testConfigId);
            
            System.out.println("查询成功: " + response);
            Assertions.assertNotNull(response, "响应不应为null");
            Assertions.assertEquals(testConfigId, response.getId(), "配置ID应匹配");
            Assertions.assertEquals(TEST_NAMESPACE, response.getNamespace(), "命名空间应匹配");
            Assertions.assertEquals(TEST_CONFIG_KEY, response.getConfigKey(), "配置键应匹配");
            Assertions.assertEquals(TEST_CONFIG_VALUE, response.getConfigValue(), "配置值应匹配");
            
        } catch (Exception e) {
            System.out.println("查询失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("查询系统配置详情失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(3)
    @DisplayName("测试分页查询系统配置列表")
    public void testGetSystemConfigPageList() {
        System.out.println("=== 测试分页查询系统配置列表 ===");
        
        SystemConfigQueryRequest request = new SystemConfigQueryRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setNamespace(TEST_NAMESPACE);
        
        try {
            PageResult<SystemConfigResponse> result = systemConfigService.getSystemConfigPageList(request);
            
            System.out.println("查询成功，总数: " + result.getTotal() + ", 记录数: " + result.getRecords().size());
            Assertions.assertNotNull(result, "结果不应为null");
            Assertions.assertTrue(result.getTotal() >= 1, "应至少有1条记录");
            Assertions.assertFalse(result.getRecords().isEmpty(), "记录列表不应为空");
            
            // 验证第一条记录
            SystemConfigResponse firstRecord = result.getRecords().get(0);
            System.out.println("第一条记录: " + firstRecord);
            
        } catch (Exception e) {
            System.out.println("查询失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("分页查询系统配置列表失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(4)
    @DisplayName("测试检查配置键是否存在")
    public void testCheckConfigKeyExists() {
        System.out.println("=== 测试检查配置键是否存在 ===");
        
        try {
            // 检查存在的配置键
            boolean exists = systemConfigService.checkConfigKeyExists(TEST_NAMESPACE, TEST_CONFIG_KEY, null);
            System.out.println("配置键存在性检查: " + exists);
            Assertions.assertTrue(exists, "配置键应存在");
            
            // 检查不存在的配置键
            boolean notExists = systemConfigService.checkConfigKeyExists(TEST_NAMESPACE, "not.exist.key", null);
            System.out.println("不存在的配置键检查: " + notExists);
            Assertions.assertFalse(notExists, "不存在的配置键应返回false");
            
        } catch (Exception e) {
            System.out.println("检查失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("检查配置键是否存在失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(5)
    @DisplayName("测试获取配置值")
    public void testGetConfigValue() {
        System.out.println("=== 测试获取配置值 ===");
        
        try {
            // 获取存在的配置值
            String value = systemConfigService.getConfigValue(TEST_NAMESPACE, TEST_CONFIG_KEY);
            System.out.println("获取到的配置值: " + value);
            Assertions.assertEquals(TEST_CONFIG_VALUE, value, "配置值应匹配");
            
            // 获取不存在的配置值（带默认值）
            String defaultValue = "default_value";
            String valueWithDefault = systemConfigService.getConfigValue(TEST_NAMESPACE, "not.exist.key", defaultValue);
            System.out.println("带默认值的配置值: " + valueWithDefault);
            Assertions.assertEquals(defaultValue, valueWithDefault, "应返回默认值");
            
        } catch (Exception e) {
            System.out.println("获取失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("获取配置值失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(6)
    @DisplayName("测试更新系统配置")
    public void testUpdateSystemConfig() {
        System.out.println("=== 测试更新系统配置 ===");
        
        if (testConfigId == null) {
            System.out.println("跳过测试：testConfigId为null");
            return;
        }
        
        try {
            // 先查询当前配置获取版本号
            SystemConfigResponse currentConfig = systemConfigService.getSystemConfigDetail(testConfigId);
            
            SystemConfigUpdateRequest request = new SystemConfigUpdateRequest();
            request.setId(testConfigId);
            request.setNamespace(TEST_NAMESPACE);
            request.setConfigKey(TEST_CONFIG_KEY);
            request.setConfigValue("updated_value");
            request.setDescription("更新后的测试配置项");
            request.setVersion(currentConfig.getVersion());
            
            systemConfigService.updateSystemConfig(request);
            
            // 验证更新结果
            SystemConfigResponse updatedConfig = systemConfigService.getSystemConfigDetail(testConfigId);
            System.out.println("更新后的配置: " + updatedConfig);
            Assertions.assertEquals("updated_value", updatedConfig.getConfigValue(), "配置值应已更新");
            Assertions.assertEquals("更新后的测试配置项", updatedConfig.getDescription(), "描述应已更新");
            
        } catch (Exception e) {
            System.out.println("更新失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("更新系统配置失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(7)
    @DisplayName("测试删除系统配置")
    public void testDeleteSystemConfig() {
        System.out.println("=== 测试删除系统配置 ===");
        
        if (testConfigId == null) {
            System.out.println("跳过测试：testConfigId为null");
            return;
        }
        
        try {
            systemConfigService.deleteSystemConfig(testConfigId);
            System.out.println("删除成功，配置ID: " + testConfigId);
            
            // 验证删除结果 - 应该抛出异常
            Assertions.assertThrows(RuntimeException.class, () -> {
                systemConfigService.getSystemConfigDetail(testConfigId);
            }, "查询已删除的配置应抛出异常");
            
        } catch (Exception e) {
            System.out.println("删除失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("删除系统配置失败: " + e.getMessage());
        }
    }
    
    @Test
    @Order(8)
    @DisplayName("测试批量删除系统配置")
    public void testDeleteSystemConfigBatch() {
        System.out.println("=== 测试批量删除系统配置 ===");
        
        try {
            // 先创建几个测试配置
            Long id1 = createTestConfig("batch_test_1", "value1");
            Long id2 = createTestConfig("batch_test_2", "value2");
            
            System.out.println("创建的测试配置ID: " + id1 + ", " + id2);
            
            // 批量删除
            systemConfigService.deleteSystemConfigBatch(Arrays.asList(id1, id2));
            System.out.println("批量删除成功");
            
        } catch (Exception e) {
            System.out.println("批量删除失败: " + e.getMessage());
            e.printStackTrace();
            Assertions.fail("批量删除系统配置失败: " + e.getMessage());
        }
    }
    
    private Long createTestConfig(String configKey, String configValue) {
        SystemConfigCreateRequest request = new SystemConfigCreateRequest();
        request.setNamespace(TEST_NAMESPACE);
        request.setConfigKey(configKey);
        request.setConfigValue(configValue);
        request.setDescription("批量测试配置");
        
        return systemConfigService.createSystemConfig(request);
    }
}
