package com.yiyi.ai_train_playground.service.task;

import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskCreateRequest;
import com.yiyi.ai_train_playground.service.task.TrainReceptionTaskService;
import com.yiyi.ai_train_playground.service.converkb.TrainKbTplService;
import com.yiyi.ai_train_playground.service.converkb.TrainTaskConvKbDtlService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 接待任务集成测试
 * 测试任务创建时自动创建知识库会话明细的功能
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainReceptionTaskIntegrationTest {

    @Autowired
    private TrainReceptionTaskService trainReceptionTaskService;

    @Autowired
    private TrainKbTplService trainKbTplService;

    @Autowired
    private TrainTaskConvKbDtlService trainTaskConvKbDtlService;

    @Test
    public void testGetKbTplDetailsForTaskCreation() {
        // 测试获取知识库模板详情功能
        Long teamId = 1L;
        Long kbTplId = 1L; // 假设存在这个知识库模板

        try {
            List<TrainKbTplService.KbTplDetailForTaskCreation> details = 
                trainKbTplService.getKbTplDetailsForTaskCreation(kbTplId, teamId);
            
            assertNotNull(details);
            System.out.println("成功获取知识库模板详情，数量: " + details.size());
            
            if (!details.isEmpty()) {
                TrainKbTplService.KbTplDetailForTaskCreation firstDetail = details.get(0);
                assertNotNull(firstDetail.getId());
                assertNotNull(firstDetail.getContent());
                System.out.println("第一条明细 - ID: " + firstDetail.getId() + ", Content长度: " + firstDetail.getContent().length());
            }
        } catch (Exception e) {
            System.out.println("测试完成，异常信息: " + e.getMessage());
            // 这是正常的，因为可能没有测试数据
        }
    }

    @Test
    public void testTaskCreationLogicComponents() {
        // 测试任务创建逻辑的各个组件是否正常工作
        System.out.println("测试任务创建组件...");
        
        assertNotNull(trainReceptionTaskService);
        assertNotNull(trainKbTplService);
        assertNotNull(trainTaskConvKbDtlService);
        
        System.out.println("所有Service组件注入正常");
    }
}