package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.service.task.TemplateLearnService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 模板学习服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-12
 */
@Slf4j
@SpringBootTest
public class TemplateLearnServiceImplTest {
    
    @Autowired
    private TemplateLearnService templateLearnService;
    
    @Test
    public void testStartTemplateLearning() {
        log.info("测试启动模板学习");

        try {
            // 测试启动模板学习
            templateLearnService.startTemplateLearning(1L, 1L);

            // 等待一段时间让异步任务执行
            Thread.sleep(5000);

            log.info("模板学习测试完成");

        } catch (Exception e) {
            log.error("模板学习测试失败", e);
        }
    }

    @Test
    public void testProcessTemplateLearning() {
        log.info("测试处理模板学习");

        try {
            // 直接测试处理逻辑
            templateLearnService.processTemplateLearning(1L, 1L);

            log.info("处理模板学习测试完成");

        } catch (Exception e) {
            log.error("处理模板学习测试失败", e);
        }
    }

    @Test
    public void testUpdateTaskStatusToLearning() {
        log.info("测试更新任务状态为学习中功能");

        try {
            // 测试新的方法签名和功能
            Long testTaskId = 999L;
            Long testTeamId = 1L;

            log.info("测试参数：taskId={}, teamId={}", testTaskId, testTeamId);

            // 调用processTemplateLearning方法，它会在内部调用updateTaskStatusToLearning
            templateLearnService.processTemplateLearning(testTaskId, testTeamId);

            log.info("更新任务状态测试完成");

        } catch (Exception e) {
            log.error("更新任务状态测试失败", e);
        }
    }

    @Test
    public void testProcessTemplateLearningWithTaskId117() {
        log.info("测试处理taskId=117的模板学习");

        try {
            Long taskId = 117L;
            Long teamId = 1L;

            log.info("开始测试taskId=117的模板学习处理");
            log.info("测试参数：taskId={}, teamId={}", taskId, teamId);

            // 调用processTemplateLearning方法
            templateLearnService.processTemplateLearning(taskId, teamId);

            log.info("taskId=117的模板学习处理测试完成");

        } catch (Exception e) {
            log.error("taskId=117的模板学习处理测试失败", e);
        }
    }

}
