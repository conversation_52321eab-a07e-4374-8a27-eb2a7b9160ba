package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.dto.task.QaSimplelDtoWithUUID;
import com.yiyi.ai_train_playground.entity.task.TrainQaRdm;
import com.yiyi.ai_train_playground.service.task.TrainQaRdmService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaRdmService 测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@SpringBootTest
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/service/task/impl/train_qa_rdm.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
public class TrainQaRdmServiceTest {

    @Autowired
    private TrainQaRdmService trainQaRdmService;

    private static final Long TEST_TEAM_ID = 1L;

    @Test
    public void testSaveDtoList() {
        // 准备测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();

        // 执行保存
        boolean result = trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 验证结果
        assertTrue(result, "保存DTO列表应该成功");

        // 验证数据库中的记录
        List<TrainQaRdm> savedRecords = trainQaRdmService.getByTeamId(TEST_TEAM_ID);
        assertEquals(dtoList.size(), savedRecords.size(), "保存的记录数应该与DTO列表大小一致");

        // 验证第一条记录的内容
        TrainQaRdm firstRecord = savedRecords.get(0);
        QaSimplelDtoWithUUID firstDto = dtoList.get(0);
        assertEquals(firstDto.getUuid(), firstRecord.getUuid());
        assertEquals(firstDto.getQuestion(), firstRecord.getQuestion());
        assertEquals(firstDto.getAnswer(), firstRecord.getAnswer());
        assertEquals(firstDto.getQuesNo(), firstRecord.getQuesNo());
    }

    @Test
    public void testGetByUUID() {
        // 先保存测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 根据UUID查询
        String testUuid = dtoList.get(0).getUuid();
        TrainQaRdm result = trainQaRdmService.getByUUID(testUuid, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "应该能找到对应的记录");
        assertEquals(testUuid, result.getUuid());
        assertEquals(TEST_TEAM_ID, result.getTeamId());
    }

    @Test
    public void testUpdateByUUID() {
        // 先保存测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 获取第一条记录
        String testUuid = dtoList.get(0).getUuid();
        TrainQaRdm record = trainQaRdmService.getByUUID(testUuid, TEST_TEAM_ID);
        assertNotNull(record);

        // 修改记录
        record.setActualQuestion("修改后的实际问题");
        record.setActualAnswer("修改后的实际答案");
        record.setResolve("修改后的解决方案");

        // 执行更新
        boolean result = trainQaRdmService.updateByUUID(record);

        // 验证结果
        assertTrue(result, "更新应该成功");

        // 重新查询验证
        TrainQaRdm updatedRecord = trainQaRdmService.getByUUID(testUuid, TEST_TEAM_ID);
        assertEquals("修改后的实际问题", updatedRecord.getActualQuestion());
        assertEquals("修改后的实际答案", updatedRecord.getActualAnswer());
        assertEquals("修改后的解决方案", updatedRecord.getResolve());
    }

    @Test
    public void testGetPageList() {
        // 先保存测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 分页查询
        List<TrainQaRdm> result = trainQaRdmService.getPageList(1, 2, TEST_TEAM_ID, null);

        // 验证结果
        assertNotNull(result, "分页结果不应该为空");
        assertTrue(result.size() <= 2, "当前页记录数应该不超过2条");

        // 查询总记录数
        long totalCount = trainQaRdmService.countByCondition(TEST_TEAM_ID, null);
        assertTrue(totalCount >= 2, "总记录数应该大于等于2");
    }

    @Test
    public void testGetByQuesNo() {
        // 先保存测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 根据问题编号查询
        String testQuesNo = dtoList.get(0).getQuesNo();
        List<TrainQaRdm> result = trainQaRdmService.getByQuesNo(testQuesNo, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "查询结果不应该为空");
        assertFalse(result.isEmpty(), "应该能找到对应的记录");
        assertEquals(testQuesNo, result.get(0).getQuesNo());
    }

    @Test
    public void testDeleteByUUID() {
        // 先保存测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 删除第一条记录
        String testUuid = dtoList.get(0).getUuid();
        boolean result = trainQaRdmService.deleteByUUID(testUuid, TEST_TEAM_ID);

        // 验证结果
        assertTrue(result, "删除应该成功");

        // 验证记录已被删除
        TrainQaRdm deletedRecord = trainQaRdmService.getByUUID(testUuid, TEST_TEAM_ID);
        assertNull(deletedRecord, "记录应该已被删除");
    }

    @Test
    public void testDeleteByTeamId() {
        // 先保存测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 验证数据已保存
        List<TrainQaRdm> beforeDelete = trainQaRdmService.getByTeamId(TEST_TEAM_ID);
        assertFalse(beforeDelete.isEmpty(), "删除前应该有数据");

        // 删除团队的所有记录
        boolean result = trainQaRdmService.deleteByTeamId(TEST_TEAM_ID);

        // 验证结果
        assertTrue(result, "删除应该成功");

        // 验证所有记录已被删除
        List<TrainQaRdm> afterDelete = trainQaRdmService.getByTeamId(TEST_TEAM_ID);
        assertTrue(afterDelete.isEmpty(), "删除后应该没有数据");
    }

    @Test
    public void testGetByReportDtlId() {
        // 准备测试数据
        List<QaSimplelDtoWithUUID> dtoList = createTestDtoList();
        trainQaRdmService.saveDtoList(dtoList, TEST_TEAM_ID, "testUser");

        // 获取第一条记录并设置reportDtlId
        TrainQaRdm firstRecord = trainQaRdmService.getByUUID(dtoList.get(0).getUuid(), TEST_TEAM_ID);
        assertNotNull(firstRecord, "应该能找到第一条记录");

        // 设置reportDtlId
        Long testReportDtlId = 100L;
        firstRecord.setReportDtlId(testReportDtlId);
        boolean updateResult = trainQaRdmService.updateByUUID(firstRecord);
        assertTrue(updateResult, "更新应该成功");

        // 根据reportDtlId查询
        List<TrainQaRdm> result = trainQaRdmService.getByReportDtlId(testReportDtlId, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "结果不应该为空");
        assertEquals(1, result.size(), "应该有1条记录");
        assertEquals(testReportDtlId, result.get(0).getReportDtlId(), "reportDtlId应该匹配");
        assertEquals(firstRecord.getUuid(), result.get(0).getUuid(), "UUID应该匹配");

        log.info("测试根据reportDtlId查询记录 - 通过");
    }

    /**
     * 创建测试用的DTO列表
     */
    private List<QaSimplelDtoWithUUID> createTestDtoList() {
        List<QaSimplelDtoWithUUID> dtoList = new ArrayList<>();

        for (int i = 1; i <= 3; i++) {
            String uuid = UUID.randomUUID().toString();
            String quesNo = "Q" + String.format("%03d", i);
            
            QaSimplelDtoWithUUID dto = new QaSimplelDtoWithUUID(
                uuid,
                "测试问题" + i,
                "测试答案" + i,
                "实际问题" + i,
                "实际答案" + i,
                "解决方案" + i,
                quesNo
            );
            dtoList.add(dto);
        }

        return dtoList;
    }
}
