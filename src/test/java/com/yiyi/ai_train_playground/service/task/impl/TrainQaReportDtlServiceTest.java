package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportDtl;
import com.yiyi.ai_train_playground.service.task.TrainQaReportDtlService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainQaReportDtlService 测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@SpringBootTest
@Transactional
@Sql(scripts = "/db/com/yiyi/ai_train_playground/service/task/impl/train_qa_report_dtl.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
public class TrainQaReportDtlServiceTest {

    @Autowired
    private TrainQaReportDtlService trainQaReportDtlService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_CREATOR = "testUser";

    @Test
    public void testCreate() {
        // 准备测试数据
        TrainQaReportDtl record = createTestRecord();

        // 执行创建
        boolean result = trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertTrue(result, "创建应该成功");
        assertNotNull(record.getId(), "创建后应该有ID");

        log.info("测试创建记录 - 通过");
    }

    @Test
    public void testGetById() {
        // 先创建测试数据
        TrainQaReportDtl record = createTestRecord();
        trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);

        // 根据ID查询
        TrainQaReportDtl result = trainQaReportDtlService.getById(record.getId(), TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "应该能找到对应的记录");
        assertEquals(record.getId(), result.getId());
        assertEquals(record.getSessionId(), result.getSessionId());
        assertEquals(TEST_TEAM_ID, result.getTeamId());

        log.info("测试根据ID查询记录 - 通过");
    }

    @Test
    public void testUpdateById() {
        // 先创建测试数据
        TrainQaReportDtl record = createTestRecord();
        trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);

        // 修改记录
        TrainQaReportDtl updateRecord = new TrainQaReportDtl();
        updateRecord.setRpMainId(999L);
        updateRecord.setTaskId(888L);
        updateRecord.setVersion(record.getVersion());

        // 执行更新
        boolean result = trainQaReportDtlService.updateById(record.getId(), updateRecord, TEST_TEAM_ID, "updater");

        // 验证结果
        assertTrue(result, "更新应该成功");

        // 查询验证
        TrainQaReportDtl updated = trainQaReportDtlService.getById(record.getId(), TEST_TEAM_ID);
        assertEquals(999L, updated.getRpMainId());
        assertEquals(888L, updated.getTaskId());

        log.info("测试根据ID更新记录 - 通过");
    }

    @Test
    public void testDeleteById() {
        // 先创建测试数据
        TrainQaReportDtl record = createTestRecord();
        trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);

        // 执行删除
        boolean result = trainQaReportDtlService.deleteById(record.getId(), TEST_TEAM_ID);

        // 验证结果
        assertTrue(result, "删除应该成功");

        // 查询验证
        TrainQaReportDtl deleted = trainQaReportDtlService.getById(record.getId(), TEST_TEAM_ID);
        assertNull(deleted, "删除后应该查询不到记录");

        log.info("测试根据ID删除记录 - 通过");
    }

    @Test
    public void testGetBySessionId() {
        // 先创建测试数据
        TrainQaReportDtl record = createTestRecord();
        trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);

        // 根据sessionId查询
        TrainQaReportDtl result = trainQaReportDtlService.getBySessionId(record.getSessionId(), TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "应该能找到对应的记录");
        assertEquals(record.getSessionId(), result.getSessionId());
        assertEquals(TEST_TEAM_ID, result.getTeamId());

        log.info("测试根据sessionId查询记录 - 通过");
    }

    @Test
    public void testGetByTaskId() {
        // 先创建测试数据
        TrainQaReportDtl record1 = createTestRecord();
        record1.setTaskId(100L);
        record1.setSessionId("session-1");
        trainQaReportDtlService.create(record1, TEST_TEAM_ID, TEST_CREATOR);

        TrainQaReportDtl record2 = createTestRecord();
        record2.setTaskId(100L);
        record2.setSessionId("session-2");
        trainQaReportDtlService.create(record2, TEST_TEAM_ID, TEST_CREATOR);

        // 根据taskId查询
        List<TrainQaReportDtl> result = trainQaReportDtlService.getByTaskId(100L, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "结果不应该为空");
        assertEquals(2, result.size(), "应该有2条记录");

        log.info("测试根据taskId查询记录列表 - 通过");
    }

    @Test
    public void testGetPageList() {
        // 先创建测试数据
        for (int i = 0; i < 5; i++) {
            TrainQaReportDtl record = createTestRecord();
            record.setSessionId("session-" + i);
            trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);
        }

        // 分页查询
        List<TrainQaReportDtl> result = trainQaReportDtlService.getPageList(1, 3, TEST_TEAM_ID, null, null);

        // 验证结果
        assertNotNull(result, "结果不应该为空");
        assertTrue(result.size() <= 3, "分页大小应该不超过3");

        log.info("测试分页查询记录 - 通过");
    }

    @Test
    public void testCountByCondition() {
        // 先创建测试数据
        for (int i = 0; i < 3; i++) {
            TrainQaReportDtl record = createTestRecord();
            record.setSessionId("session-" + i);
            trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);
        }

        // 查询总数
        long count = trainQaReportDtlService.countByCondition(TEST_TEAM_ID, null, null);

        // 验证结果
        assertEquals(3, count, "总数应该为3");

        log.info("测试查询总记录数 - 通过");
    }

    @Test
    public void testBatchCreate() {
        // 准备测试数据
        List<TrainQaReportDtl> records = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            TrainQaReportDtl record = createTestRecord();
            record.setSessionId("batch-session-" + i);
            records.add(record);
        }

        // 执行批量创建
        boolean result = trainQaReportDtlService.batchCreate(records, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertTrue(result, "批量创建应该成功");

        // 验证数据库中的记录
        List<TrainQaReportDtl> allRecords = trainQaReportDtlService.getByTeamId(TEST_TEAM_ID);
        assertTrue(allRecords.size() >= 3, "应该至少有3条记录");

        log.info("测试批量创建记录 - 通过");
    }

    @Test
    public void testExistsBySessionId() {
        // 先创建测试数据
        TrainQaReportDtl record = createTestRecord();
        trainQaReportDtlService.create(record, TEST_TEAM_ID, TEST_CREATOR);

        // 检查存在性
        boolean exists = trainQaReportDtlService.existsBySessionId(record.getSessionId(), TEST_TEAM_ID);
        boolean notExists = trainQaReportDtlService.existsBySessionId("non-existent-session", TEST_TEAM_ID);

        // 验证结果
        assertTrue(exists, "应该存在");
        assertFalse(notExists, "不应该存在");

        log.info("测试检查sessionId是否存在 - 通过");
    }

    @Test
    public void testCreateByConditions() {
        // 执行根据条件创建
        boolean result = trainQaReportDtlService.createByConditions(
                100L, 200L, "test-session-conditions", 300L, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertTrue(result, "根据条件创建应该成功");

        // 验证数据
        TrainQaReportDtl created = trainQaReportDtlService.getBySessionId("test-session-conditions", TEST_TEAM_ID);
        assertNotNull(created, "应该能找到创建的记录");
        assertEquals(100L, created.getRpMainId());
        assertEquals(200L, created.getTaskId());
        assertEquals(300L, created.getQaMainId());

        log.info("测试根据条件创建记录 - 通过");
    }

    /**
     * 创建测试用的记录对象
     */
    private TrainQaReportDtl createTestRecord() {
        TrainQaReportDtl record = new TrainQaReportDtl();
        record.setRpMainId(1L);
        record.setTaskId(2L);
        record.setSessionId("test-session-" + System.currentTimeMillis());
        record.setQaMainId(3L);
        return record;
    }
}
