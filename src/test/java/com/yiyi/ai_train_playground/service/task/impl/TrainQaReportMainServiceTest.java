package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.entity.task.TrainQaReportMain;
import com.yiyi.ai_train_playground.service.task.TrainQaReportMainService;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.TrainQaReportMainQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 问答报告主表服务测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-31
 */
@Slf4j
@SpringBootTest
@Transactional
@Sql(scripts = "classpath:db/com/yiyi/ai_train_playground/service/task/impl/train_qa_report_main.sql")
public class TrainQaReportMainServiceTest {

    @Autowired
    private TrainQaReportMainService trainQaReportMainService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final Long TEST_CHATROOM_ID = 100L;
    private static final Long TEST_STAFF_ID = 200L;

    @BeforeEach
    public void setUp() {
        // 清理测试数据
        trainQaReportMainService.deleteByTeamId(TEST_TEAM_ID);
    }

    @Test
    public void testCreate() {
        // 创建测试记录
        TrainQaReportMain record = createTestRecord();
        TrainQaReportMain result = trainQaReportMainService.create(record, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "创建结果不应该为空");
        assertNotNull(result.getId(), "ID应该被自动生成");
        assertEquals(TEST_CHATROOM_ID, result.getChatroomId(), "聊天室ID应该匹配");
        assertEquals(TEST_STAFF_ID, result.getStaffId(), "员工ID应该匹配");
        assertEquals("张三", result.getExamUserRealName(), "考试者姓名应该匹配");
        assertEquals("EXAM001", result.getExamUserNo(), "考试者编号应该匹配");
        assertEquals(new BigDecimal("85.50"), result.getExamScore(), "考试分数应该匹配");
        assertEquals(TEST_TEAM_ID, result.getTeamId(), "团队ID应该匹配");

        log.info("测试创建记录 - 通过");
    }

    @Test
    public void testGetById() {
        // 先创建记录
        TrainQaReportMain record = createTestRecord();
        TrainQaReportMain created = trainQaReportMainService.create(record, TEST_TEAM_ID);

        // 根据ID查询
        TrainQaReportMain result = trainQaReportMainService.getById(created.getId(), TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "查询结果不应该为空");
        assertEquals(created.getId(), result.getId(), "ID应该匹配");
        assertEquals(TEST_CHATROOM_ID, result.getChatroomId(), "聊天室ID应该匹配");
        assertEquals(TEST_STAFF_ID, result.getStaffId(), "员工ID应该匹配");

        log.info("测试根据ID查询记录 - 通过");
    }

    @Test
    public void testUpdateById() {
        // 先创建记录
        TrainQaReportMain record = createTestRecord();
        TrainQaReportMain created = trainQaReportMainService.create(record, TEST_TEAM_ID);

        // 更新记录
        created.setExamUserRealName("李四");
        created.setExamScore(new BigDecimal("90.00"));
        boolean updateResult = trainQaReportMainService.updateById(created, TEST_TEAM_ID);

        // 验证更新结果
        assertTrue(updateResult, "更新应该成功");

        // 查询更新后的记录
        TrainQaReportMain updated = trainQaReportMainService.getById(created.getId(), TEST_TEAM_ID);
        assertNotNull(updated, "更新后的记录不应该为空");
        assertEquals("李四", updated.getExamUserRealName(), "考试者姓名应该已更新");
        assertEquals(new BigDecimal("90.00"), updated.getExamScore(), "考试分数应该已更新");

        log.info("测试根据ID更新记录 - 通过");
    }

    @Test
    public void testDeleteById() {
        // 先创建记录
        TrainQaReportMain record = createTestRecord();
        TrainQaReportMain created = trainQaReportMainService.create(record, TEST_TEAM_ID);

        // 删除记录
        boolean deleteResult = trainQaReportMainService.deleteById(created.getId(), TEST_TEAM_ID);

        // 验证删除结果
        assertTrue(deleteResult, "删除应该成功");

        // 验证记录已被删除
        TrainQaReportMain deleted = trainQaReportMainService.getById(created.getId(), TEST_TEAM_ID);
        assertNull(deleted, "删除后记录应该不存在");

        log.info("测试根据ID删除记录 - 通过");
    }

    @Test
    public void testGetPageList() {
        // 创建多条测试记录
        List<TrainQaReportMain> records = createTestRecords();
        trainQaReportMainService.batchCreate(records, TEST_TEAM_ID, "testUser");

        // 分页查询
        TrainQaReportMainQueryRequest request = new TrainQaReportMainQueryRequest();
        request.setPage(1);
        request.setPageSize(10);
        PageResult<TrainQaReportMain> result = trainQaReportMainService.getPageList(request, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "分页结果不应该为空");
        assertEquals(3, result.getTotal(), "总记录数应该为3");
        assertEquals(3, result.getRecords().size(), "当前页记录数应该为3");

        log.info("测试分页查询记录 - 通过");
    }

    @Test
    public void testGetByChatroomId() {
        // 创建测试记录
        TrainQaReportMain record = createTestRecord();
        trainQaReportMainService.create(record, TEST_TEAM_ID);

        // 根据聊天室ID查询
        List<TrainQaReportMain> result = trainQaReportMainService.getByChatroomId(TEST_CHATROOM_ID, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "查询结果不应该为空");
        assertEquals(1, result.size(), "应该有1条记录");
        assertEquals(TEST_CHATROOM_ID, result.get(0).getChatroomId(), "聊天室ID应该匹配");

        log.info("测试根据聊天室ID查询记录 - 通过");
    }

    @Test
    public void testGetByStaffId() {
        // 创建测试记录
        TrainQaReportMain record = createTestRecord();
        trainQaReportMainService.create(record, TEST_TEAM_ID);

        // 根据员工ID查询
        List<TrainQaReportMain> result = trainQaReportMainService.getByStaffId(TEST_STAFF_ID, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result, "查询结果不应该为空");
        assertEquals(1, result.size(), "应该有1条记录");
        assertEquals(TEST_STAFF_ID, result.get(0).getStaffId(), "员工ID应该匹配");

        log.info("测试根据员工ID查询记录 - 通过");
    }

    @Test
    public void testBatchCreate() {
        // 创建多条测试记录
        List<TrainQaReportMain> records = createTestRecords();

        // 批量创建
        boolean result = trainQaReportMainService.batchCreate(records, TEST_TEAM_ID, "testUser");

        // 验证结果
        assertTrue(result, "批量创建应该成功");

        // 验证记录已创建
        List<TrainQaReportMain> created = trainQaReportMainService.getByTeamId(TEST_TEAM_ID);
        assertEquals(3, created.size(), "应该有3条记录");

        log.info("测试批量创建记录 - 通过");
    }

    @Test
    public void testCountByCondition() {
        // 创建多条测试记录
        List<TrainQaReportMain> records = createTestRecords();
        trainQaReportMainService.batchCreate(records, TEST_TEAM_ID, "testUser");

        // 查询总数
        TrainQaReportMainQueryRequest request = new TrainQaReportMainQueryRequest();
        long total = trainQaReportMainService.countByCondition(request, TEST_TEAM_ID);

        // 验证结果
        assertEquals(3, total, "总记录数应该为3");

        // 按条件查询总数
        TrainQaReportMainQueryRequest chatroomRequest = new TrainQaReportMainQueryRequest();
        chatroomRequest.setChatroomId(TEST_CHATROOM_ID);
        long chatroomTotal = trainQaReportMainService.countByCondition(chatroomRequest, TEST_TEAM_ID);
        assertEquals(1, chatroomTotal, "聊天室相关记录数应该为1");

        log.info("测试条件统计记录数 - 通过");
    }

    @Test
    public void testDeleteByTeamId() {
        // 创建多条测试记录
        List<TrainQaReportMain> records = createTestRecords();
        trainQaReportMainService.batchCreate(records, TEST_TEAM_ID, "testUser");

        // 根据团队ID删除所有记录
        boolean result = trainQaReportMainService.deleteByTeamId(TEST_TEAM_ID);

        // 验证结果
        assertTrue(result, "删除应该成功");

        // 验证所有记录已被删除
        List<TrainQaReportMain> afterDelete = trainQaReportMainService.getByTeamId(TEST_TEAM_ID);
        assertTrue(afterDelete.isEmpty(), "删除后应该没有数据");

        log.info("测试根据团队ID删除所有记录 - 通过");
    }

    /**
     * 创建测试记录
     */
    private TrainQaReportMain createTestRecord() {
        TrainQaReportMain record = new TrainQaReportMain();
        record.setChatroomId(TEST_CHATROOM_ID);
        record.setStaffId(TEST_STAFF_ID);
        record.setExamUserRealName("张三");
        record.setExamUserNo("EXAM001");
        record.setExamScore(new BigDecimal("85.50"));
        return record;
    }

    /**
     * 创建多条测试记录
     */
    private List<TrainQaReportMain> createTestRecords() {
        List<TrainQaReportMain> records = new ArrayList<>();

        TrainQaReportMain record1 = new TrainQaReportMain();
        record1.setChatroomId(TEST_CHATROOM_ID);
        record1.setStaffId(TEST_STAFF_ID);
        record1.setExamUserRealName("张三");
        record1.setExamUserNo("EXAM001");
        record1.setExamScore(new BigDecimal("85.50"));
        records.add(record1);

        TrainQaReportMain record2 = new TrainQaReportMain();
        record2.setChatroomId(101L);
        record2.setStaffId(201L);
        record2.setExamUserRealName("李四");
        record2.setExamUserNo("EXAM002");
        record2.setExamScore(new BigDecimal("92.00"));
        records.add(record2);

        TrainQaReportMain record3 = new TrainQaReportMain();
        record3.setChatroomId(102L);
        record3.setStaffId(202L);
        record3.setExamUserRealName("王五");
        record3.setExamUserNo("EXAM003");
        record3.setExamScore(new BigDecimal("78.75"));
        records.add(record3);

        return records;
    }
}
