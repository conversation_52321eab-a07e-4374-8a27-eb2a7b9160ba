package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.ChatroomCreateRequest;
import com.yiyi.ai_train_playground.dto.task.ChatroomDetailDTO;
import com.yiyi.ai_train_playground.dto.task.ChatroomListDTO;
import com.yiyi.ai_train_playground.dto.task.ChatroomQueryRequest;
import com.yiyi.ai_train_playground.dto.task.ChatroomUpdateRequest;
import com.yiyi.ai_train_playground.entity.task.TrainChatroomStaff;
import com.yiyi.ai_train_playground.mapper.task.TrainChatroomStaffMapper;
import com.yiyi.ai_train_playground.mapper.task.TrainReceptionChatroomMapper;
import com.yiyi.ai_train_playground.service.task.impl.TrainReceptionChatroomServiceImpl;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 接待聊天室服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-26
 */
@SpringBootTest
@ActiveProfiles("home")
@Transactional
class TrainReceptionChatroomServiceImplTest {
    
    @Autowired
    private TrainReceptionChatroomServiceImpl chatroomService;
    
    @Autowired
    private TrainChatroomStaffMapper chatroomStaffMapper;

    @Autowired
    private TrainReceptionChatroomMapper chatroomMapper;
    
    private final Long TEST_TEAM_ID = 1L;
    private final String TEST_CREATOR = "test_user";
    
    /**
     * 创建测试用的聊天室请求对象
     */
    private ChatroomCreateRequest createTestChatroomRequest(String roomName, String receptionSkin, int sceneMode) {
        ChatroomCreateRequest request = new ChatroomCreateRequest();
        request.setRoomName(roomName);
        request.setReceptionSkin(receptionSkin);
        request.setSceneMode(sceneMode);
        request.setQuickPhrasesId(1L);
        request.setReceptionDuration(30);
        request.setTimerDisplay(false);
        request.setEntryFreqMin(2);
        request.setEntryFreqMax(8);
        return request;
    }
    
    @Test
    @Rollback
    void testCreateChatroomWithTasks() {
        // 准备测试数据
        ChatroomCreateRequest request = new ChatroomCreateRequest();
        request.setRoomName("测试聊天室名称");
        request.setReceptionSkin("0");
        request.setSceneMode(0);
        request.setQuickPhrasesId(1L);
        request.setReceptionDuration(30);
        request.setTimerDisplay(false);
        request.setEntryFreqMin(2);
        request.setEntryFreqMax(8);

        // 添加关联任务
        List<ChatroomCreateRequest.ChatroomTaskCreateDTO> taskList = new ArrayList<>();
        ChatroomCreateRequest.ChatroomTaskCreateDTO task1 = new ChatroomCreateRequest.ChatroomTaskCreateDTO();
        task1.setTaskId(1L);
        task1.setTrainingRecycleCnt(0);
        taskList.add(task1);
        
        ChatroomCreateRequest.ChatroomTaskCreateDTO task2 = new ChatroomCreateRequest.ChatroomTaskCreateDTO();
        task2.setTaskId(2L);
        task2.setTrainingRecycleCnt(1);
        taskList.add(task2);
        
        request.setTaskList(taskList);
        
        // 执行创建
        Long chatroomId = chatroomService.createChatroomWithTasks(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertNotNull(chatroomId);
        assertTrue(chatroomId > 0);
        
        // 验证创建的聊天室详情
        ChatroomDetailDTO detail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(chatroomId, detail.getId());
        assertEquals("测试聊天室名称", detail.getRoomName());
        assertEquals("0", detail.getReceptionSkin());
        assertEquals(Integer.valueOf(0), detail.getSceneMode());
        assertEquals(Long.valueOf(1L), detail.getQuickPhrasesId());
        assertEquals(Integer.valueOf(30), detail.getReceptionDuration());
        assertEquals(Boolean.FALSE, detail.getTimerDisplay());
        assertEquals(Integer.valueOf(2), detail.getEntryFreqMin());
        assertEquals(Integer.valueOf(8), detail.getEntryFreqMax());

        // 验证关联任务
        assertNotNull(detail.getTaskList());
        assertEquals(2, detail.getTaskList().size());
    }
    
    @Test
    @Rollback
    void testGetChatroomDetail() {
        // 先创建一个聊天室
        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setRoomName("详情测试聊天室");
        createRequest.setReceptionSkin("1");
        createRequest.setSceneMode(1);
        createRequest.setReceptionDuration(45);
        createRequest.setTimerDisplay(true);
        
        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        
        // 查询详情
        ChatroomDetailDTO detail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(detail);
        assertEquals(chatroomId, detail.getId());
        assertEquals("1", detail.getReceptionSkin());
        assertEquals("咩咩", detail.getReceptionSkinName());
        assertEquals(Integer.valueOf(1), detail.getSceneMode());
        assertEquals("压力考核", detail.getSceneModeName());
        assertEquals(Integer.valueOf(45), detail.getReceptionDuration());
        assertEquals(Boolean.TRUE, detail.getTimerDisplay());
        assertEquals(TEST_CREATOR, detail.getCreator());
        assertNotNull(detail.getCreateTime());
        assertNotNull(detail.getVersion());
    }
    
    @Test
    @Rollback
    void testGetChatroomList() {
        // 先创建几个聊天室
        for (int i = 0; i < 3; i++) {
            ChatroomCreateRequest request = new ChatroomCreateRequest();
            request.setReceptionSkin(String.valueOf(i % 3));
            request.setSceneMode(i % 2);
            request.setReceptionDuration(30 + i * 10);
            chatroomService.createChatroomWithTasks(request, TEST_TEAM_ID, TEST_CREATOR);
        }
        
        // 查询列表
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<ChatroomListDTO> pageResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(pageResult);
        assertNotNull(pageResult.getRecords());
        assertTrue(pageResult.getTotal() >= 3);
        assertTrue(pageResult.getRecords().size() >= 3);
        
        // 验证列表项数据
        ChatroomListDTO firstItem = pageResult.getRecords().get(0);
        assertNotNull(firstItem.getId());
        assertNotNull(firstItem.getReceptionSkin());
        assertNotNull(firstItem.getReceptionSkinName());
        assertNotNull(firstItem.getSceneMode());
        assertNotNull(firstItem.getSceneModeName());
        assertNotNull(firstItem.getReceptionDuration());
        assertNotNull(firstItem.getTimerDisplay());
        assertEquals(TEST_CREATOR, firstItem.getCreator());
    }
    
    @Test
    @Rollback
    void testUpdateChatroomWithTasks() {
        // 先创建一个聊天室
        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setReceptionSkin("0");
        createRequest.setSceneMode(0);
        createRequest.setReceptionDuration(30);
        
        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        
        // 查询当前版本
        ChatroomDetailDTO beforeUpdate = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        
        // 准备更新数据
        ChatroomUpdateRequest updateRequest = new ChatroomUpdateRequest();
        updateRequest.setId(chatroomId);
        updateRequest.setReceptionSkin("2");
        updateRequest.setSceneMode(2);
        updateRequest.setReceptionDuration(60);
        updateRequest.setTimerDisplay(true);
        updateRequest.setVersion(beforeUpdate.getVersion());
        
        // 更新关联任务
        List<ChatroomUpdateRequest.ChatroomTaskUpdateDTO> taskList = new ArrayList<>();
        ChatroomUpdateRequest.ChatroomTaskUpdateDTO task = new ChatroomUpdateRequest.ChatroomTaskUpdateDTO();
        task.setTaskId(3L);
        task.setTrainingRecycleCnt(2);
        taskList.add(task);
        updateRequest.setTaskList(taskList);
        
        // 执行更新
        boolean success = chatroomService.updateChatroomWithTasks(updateRequest, TEST_TEAM_ID, "updater");
        
        // 验证结果
        assertTrue(success);
        
        // 验证更新后的数据
        ChatroomDetailDTO afterUpdate = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(afterUpdate);
        assertEquals("2", afterUpdate.getReceptionSkin());
        assertEquals("抖音", afterUpdate.getReceptionSkinName());
        assertEquals(Integer.valueOf(2), afterUpdate.getSceneMode());
        assertEquals("自定义", afterUpdate.getSceneModeName());
        assertEquals(Integer.valueOf(60), afterUpdate.getReceptionDuration());
        assertEquals(Boolean.TRUE, afterUpdate.getTimerDisplay());
        
        // 验证版本号增加
        assertTrue(afterUpdate.getVersion() > beforeUpdate.getVersion());
        
        // 验证任务关联更新
        assertNotNull(afterUpdate.getTaskList());
        assertEquals(1, afterUpdate.getTaskList().size());
        assertEquals(Long.valueOf(3L), afterUpdate.getTaskList().get(0).getTaskId());
    }
    
    @Test
    @Rollback
    void testDeleteChatroom() {
        // 先创建一个聊天室
        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setReceptionSkin("0");
        createRequest.setSceneMode(0);
        
        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证聊天室存在
        ChatroomDetailDTO beforeDelete = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(beforeDelete);
        
        // 执行删除
        boolean success = chatroomService.deleteChatroom(chatroomId, TEST_TEAM_ID);
        
        // 验证删除成功
        assertTrue(success);
        
        // 验证聊天室已被删除
        assertThrows(RuntimeException.class, () -> {
            chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        });
    }
    
    @Test
    @Rollback
    void testBatchDeleteChatrooms() {
        // 先创建几个聊天室
        List<Long> chatroomIds = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            ChatroomCreateRequest request = new ChatroomCreateRequest();
            request.setReceptionSkin(String.valueOf(i));
            request.setSceneMode(0);
            Long chatroomId = chatroomService.createChatroomWithTasks(request, TEST_TEAM_ID, TEST_CREATOR);
            chatroomIds.add(chatroomId);
        }
        
        // 验证聊天室存在
        for (Long chatroomId : chatroomIds) {
            ChatroomDetailDTO detail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
            assertNotNull(detail);
        }
        
        // 执行批量删除
        String ids = String.join(",", chatroomIds.stream().map(String::valueOf).toArray(String[]::new));
        boolean success = chatroomService.batchDeleteChatrooms(ids, TEST_TEAM_ID);
        
        // 验证删除成功
        assertTrue(success);
        
        // 验证所有聊天室都已被删除
        for (Long chatroomId : chatroomIds) {
            assertThrows(RuntimeException.class, () -> {
                chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
            });
        }
    }
    
    @Test
    void testGetChatroomDetailNotExists() {
        // 测试查询不存在的聊天室
        assertThrows(RuntimeException.class, () -> {
            chatroomService.getChatroomDetail(99999L, TEST_TEAM_ID);
        });
    }
    
    @Test
    void testBatchDeleteChatroomsWithEmptyIds() {
        // 测试空ID列表删除
        assertThrows(RuntimeException.class, () -> {
            chatroomService.batchDeleteChatrooms("", TEST_TEAM_ID);
        });
        
        assertThrows(RuntimeException.class, () -> {
            chatroomService.batchDeleteChatrooms(null, TEST_TEAM_ID);
        });
    }
    
    @Test
    @Rollback
    void testGetMyTasks() {
        Long testStaffId = 1L; // 假设员工ID为1
        
        // 先创建一个聊天室
        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setReceptionSkin("1");
        createRequest.setSceneMode(1);
        createRequest.setQuickPhrasesId(1L);
        createRequest.setReceptionDuration(30);
        createRequest.setTimerDisplay(true);
        
        // 添加关联任务（使用已存在的任务ID）
        List<ChatroomCreateRequest.ChatroomTaskCreateDTO> taskList = new ArrayList<>();
        ChatroomCreateRequest.ChatroomTaskCreateDTO task1 = new ChatroomCreateRequest.ChatroomTaskCreateDTO();
        task1.setTaskId(1L); // 假设任务ID为1
        task1.setTrainingRecycleCnt(0);
        taskList.add(task1);
        
        createRequest.setTaskList(taskList);
        
        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(chatroomId);
        
        // 创建员工关联关系
        TrainChatroomStaff chatroomStaff = new TrainChatroomStaff(chatroomId, testStaffId, TEST_TEAM_ID, TEST_CREATOR);
        int insertResult = chatroomStaffMapper.insert(chatroomStaff);
        assertTrue(insertResult > 0);
        
        // 准备查询条件
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        // 测试查询我的任务
        PageResult<ChatroomListDTO> pageResult = chatroomService.getMyTasks(queryRequest, testStaffId, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(pageResult);
        assertNotNull(pageResult.getRecords());
        assertTrue(pageResult.getTotal() > 0);
        assertTrue(pageResult.getRecords().size() > 0);
        
        ChatroomListDTO firstTask = pageResult.getRecords().get(0);
        assertEquals(chatroomId, firstTask.getId());
        assertEquals("1", firstTask.getReceptionSkin());
        assertEquals(Integer.valueOf(1), firstTask.getSceneMode());
        assertEquals(Long.valueOf(1L), firstTask.getQuickPhrasesId());
        assertNotNull(firstTask.getCreateTime());
        
        // 验证分页信息
        assertEquals(Integer.valueOf(1), pageResult.getPage());
        assertEquals(Integer.valueOf(10), pageResult.getSize());
    }
    
    @Test
    void testGetMyTasksWithNonExistentStaff() {
        Long nonExistentStaffId = 99999L;
        
        // 准备查询条件
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        // 测试查询不存在员工的任务
        PageResult<ChatroomListDTO> pageResult = chatroomService.getMyTasks(queryRequest, nonExistentStaffId, TEST_TEAM_ID);
        
        // 验证结果为空列表
        assertNotNull(pageResult);
        assertNotNull(pageResult.getRecords());
        assertEquals(0, pageResult.getTotal());
        assertEquals(0, pageResult.getRecords().size());
    }

    @Test
    @Rollback
    void testEntryFrequencyFields() {
        // 测试进线频率字段的创建、查询和更新

        // 1. 创建聊天室时设置进线频率
        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setReceptionSkin("0");
        createRequest.setSceneMode(0);
        createRequest.setReceptionDuration(30);
        createRequest.setTimerDisplay(false);
        createRequest.setEntryFreqMin(3);
        createRequest.setEntryFreqMax(10);

        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(chatroomId);

        // 2. 验证创建时的进线频率字段
        ChatroomDetailDTO detail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(Integer.valueOf(3), detail.getEntryFreqMin());
        assertEquals(Integer.valueOf(10), detail.getEntryFreqMax());

        // 3. 测试更新进线频率字段
        ChatroomUpdateRequest updateRequest = new ChatroomUpdateRequest();
        updateRequest.setId(chatroomId);
        updateRequest.setEntryFreqMin(5);
        updateRequest.setEntryFreqMax(15);
        updateRequest.setVersion(detail.getVersion());

        boolean updateSuccess = chatroomService.updateChatroomWithTasks(updateRequest, TEST_TEAM_ID, "updater");
        assertTrue(updateSuccess);

        // 4. 验证更新后的进线频率字段
        ChatroomDetailDTO updatedDetail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(updatedDetail);
        assertEquals(Integer.valueOf(5), updatedDetail.getEntryFreqMin());
        assertEquals(Integer.valueOf(15), updatedDetail.getEntryFreqMax());

        // 5. 测试列表查询中的进线频率字段
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);

        PageResult<ChatroomListDTO> pageResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(pageResult);
        assertNotNull(pageResult.getRecords());
        assertTrue(pageResult.getRecords().size() > 0);

        // 找到我们创建的聊天室
        ChatroomListDTO foundChatroom = pageResult.getRecords().stream()
                .filter(c -> c.getId().equals(chatroomId))
                .findFirst()
                .orElse(null);

        assertNotNull(foundChatroom);
        assertEquals(Integer.valueOf(5), foundChatroom.getEntryFreqMin());
        assertEquals(Integer.valueOf(15), foundChatroom.getEntryFreqMax());
    }

    @Test
    @Rollback
    void testDefaultEntryFrequencyValues() {
        // 测试不设置进线频率字段时的默认值

        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setReceptionSkin("1");
        createRequest.setSceneMode(1);
        createRequest.setReceptionDuration(45);
        createRequest.setTimerDisplay(true);
        // 不设置 entryFreqMin 和 entryFreqMax，测试默认值

        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(chatroomId);

        // 验证默认值
        ChatroomDetailDTO detail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(Integer.valueOf(1), detail.getEntryFreqMin()); // 默认值为1
        assertEquals(Integer.valueOf(5), detail.getEntryFreqMax()); // 默认值为5
    }

    @Test
    void testGetChatRoomTaskListWithSrvSendCd() {
        // 测试查询聊天室任务列表，验证srv_send_cd字段
        Long receChatRoomId = 73L; // 使用我们在数据库中创建的测试数据

        List<com.yiyi.ai_train_playground.dto.task.ChatRoomTaskInfo> taskList = chatroomService.getChatRoomTaskList(receChatRoomId);

        assertNotNull(taskList);
        assertFalse(taskList.isEmpty());

        // 验证第一个任务包含srv_send_cd字段
        com.yiyi.ai_train_playground.dto.task.ChatRoomTaskInfo firstTask = taskList.get(0);
        assertNotNull(firstTask.getSrvSendCd());

        System.out.println("查询聊天室任务列表测试完成，任务数量：" + taskList.size());
        System.out.println("第一个任务的srv_send_cd值：" + firstTask.getSrvSendCd());

        // 验证任务的其他字段
        assertNotNull(firstTask.getTaskId());
        assertNotNull(firstTask.getTaskName());
        assertEquals("测试任务-客服倒计时", firstTask.getTaskName());

        // 验证srv_send_cd字段的值
        assertEquals(Long.valueOf(15), firstTask.getSrvSendCd());

        // 验证taskInfo对象包含完整的任务信息
        assertNotNull(firstTask.getScriptId());
        assertNotNull(firstTask.getScriptName());
        assertEquals("小米手环0801演示", firstTask.getScriptName());
        assertEquals(Long.valueOf(241), firstTask.getScriptId());
        assertEquals(Long.valueOf(126), firstTask.getTaskId());
        assertEquals("测试任务-客服倒计时", firstTask.getTaskName());
        assertEquals("咨询产品", firstTask.getIntentName());
        assertEquals("售前", firstTask.getParentIntentName());

        System.out.println("验证完成：ChatRoomTaskInfo包含完整的任务信息，包括srv_send_cd字段");
    }

    @Test
    @Rollback
    void testRoomNameFieldCRUD() {
        // 1. 测试创建聊天室时设置roomName
        ChatroomCreateRequest createRequest = new ChatroomCreateRequest();
        createRequest.setRoomName("RoomName测试聊天室");
        createRequest.setReceptionSkin("1");
        createRequest.setSceneMode(0);
        createRequest.setReceptionDuration(30);
        createRequest.setTimerDisplay(false);
        
        Long chatroomId = chatroomService.createChatroomWithTasks(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(chatroomId);
        
        // 2. 验证查询详情包含roomName字段
        ChatroomDetailDTO detail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals("RoomName测试聊天室", detail.getRoomName());
        
        // 3. 测试更新roomName字段
        ChatroomUpdateRequest updateRequest = new ChatroomUpdateRequest();
        updateRequest.setId(chatroomId);
        updateRequest.setRoomName("更新后的聊天室名称");
        updateRequest.setReceptionSkin("2");
        updateRequest.setSceneMode(1);
        
        chatroomService.updateChatroomWithTasks(updateRequest, TEST_TEAM_ID, TEST_CREATOR);
        
        // 4. 验证更新后的roomName
        ChatroomDetailDTO updatedDetail = chatroomService.getChatroomDetail(chatroomId, TEST_TEAM_ID);
        assertNotNull(updatedDetail);
        assertEquals("更新后的聊天室名称", updatedDetail.getRoomName());
        assertEquals("2", updatedDetail.getReceptionSkin());
        assertEquals(Integer.valueOf(1), updatedDetail.getSceneMode());
        
        // 5. 测试列表查询包含roomName字段
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<ChatroomListDTO> pageResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(pageResult);
        assertNotNull(pageResult.getRecords());
        assertTrue(pageResult.getRecords().size() > 0);
        
        // 找到刚创建的聊天室，验证roomName字段
        ChatroomListDTO foundChatroom = pageResult.getRecords().stream()
            .filter(room -> room.getId().equals(chatroomId))
            .findFirst()
            .orElse(null);
        
        assertNotNull(foundChatroom);
        assertEquals("更新后的聊天室名称", foundChatroom.getRoomName());
        
        System.out.println("✅ roomName字段CRUD操作测试通过");
    }
    
    @Test
    @Rollback
    void testGetChatroomListWithRoomNameFilter() {
        // 准备测试数据 - 创建多个聊天室
        ChatroomCreateRequest request1 = new ChatroomCreateRequest();
        request1.setRoomName("电商客服聊天室");
        request1.setReceptionSkin("qianniu_skin");
        request1.setSceneMode(0);
        
        ChatroomCreateRequest request2 = new ChatroomCreateRequest();
        request2.setRoomName("金融产品咨询室");
        request2.setReceptionSkin("dongdong_skin");
        request2.setSceneMode(1);
        
        ChatroomCreateRequest request3 = new ChatroomCreateRequest();
        request3.setRoomName("电商售后服务");
        request3.setReceptionSkin("default_skin");
        request3.setSceneMode(2);
        
        // 创建聊天室
        Long chatroom1Id = chatroomService.createChatroomWithTasks(request1, TEST_TEAM_ID, TEST_CREATOR);
        Long chatroom2Id = chatroomService.createChatroomWithTasks(request2, TEST_TEAM_ID, TEST_CREATOR);
        Long chatroom3Id = chatroomService.createChatroomWithTasks(request3, TEST_TEAM_ID, TEST_CREATOR);
        
        assertNotNull(chatroom1Id);
        assertNotNull(chatroom2Id);
        assertNotNull(chatroom3Id);
        
        // 测试1：不过滤roomName，应该返回所有聊天室
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<ChatroomListDTO> allResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(allResult);
        assertTrue(allResult.getTotal() >= 3);
        assertTrue(allResult.getRecords().size() >= 3);
        
        // 测试2：按roomName模糊查询"电商"，应该返回2个结果
        queryRequest.setRoomName("电商");
        PageResult<ChatroomListDTO> ecommerceResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(ecommerceResult);
        assertEquals(2, ecommerceResult.getTotal());
        assertEquals(2, ecommerceResult.getRecords().size());
        
        // 验证返回的结果都包含"电商"
        for (ChatroomListDTO chatroom : ecommerceResult.getRecords()) {
            assertTrue(chatroom.getRoomName().contains("电商"));
        }
        
        // 测试3：按roomName模糊查询"金融"，应该返回1个结果
        queryRequest.setRoomName("金融");
        PageResult<ChatroomListDTO> financeResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(financeResult);
        assertEquals(1, financeResult.getTotal());
        assertEquals(1, financeResult.getRecords().size());
        assertEquals("金融产品咨询室", financeResult.getRecords().get(0).getRoomName());
        
        // 测试4：按roomName模糊查询不存在的关键词，应该返回0个结果
        queryRequest.setRoomName("不存在的关键词");
        PageResult<ChatroomListDTO> noResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(noResult);
        assertEquals(0, noResult.getTotal());
        assertEquals(0, noResult.getRecords().size());
        
        // 测试5：组合查询 - roomName + receptionSkin
        queryRequest.setRoomName("电商");
        queryRequest.setReceptionSkin("qianniu_skin");
        PageResult<ChatroomListDTO> combinedResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(combinedResult);
        assertEquals(1, combinedResult.getTotal());
        assertEquals(1, combinedResult.getRecords().size());
        assertEquals("电商客服聊天室", combinedResult.getRecords().get(0).getRoomName());
        assertEquals("qianniu_skin", combinedResult.getRecords().get(0).getReceptionSkin());
        
        // 测试6：空字符串roomName，应该不影响查询（相当于不过滤）
        queryRequest.setRoomName("");
        queryRequest.setReceptionSkin(null);
        PageResult<ChatroomListDTO> emptyNameResult = chatroomService.getChatroomList(queryRequest, TEST_TEAM_ID);
        assertNotNull(emptyNameResult);
        assertTrue(emptyNameResult.getTotal() >= 3);
        
        System.out.println("✅ roomName查询过滤功能测试通过");
    }
    
    @Test
    @Rollback
    void testGetMyTasksWithRoomNameFilter() {
        // 准备测试数据 - 创建聊天室和员工关联
        ChatroomCreateRequest request1 = new ChatroomCreateRequest();
        request1.setRoomName("我的电商聊天室");
        request1.setReceptionSkin("qianniu_skin");
        request1.setSceneMode(0);
        
        ChatroomCreateRequest request2 = new ChatroomCreateRequest();
        request2.setRoomName("我的金融咨询室");
        request2.setReceptionSkin("dongdong_skin");
        request2.setSceneMode(1);
        
        // 创建聊天室
        Long chatroom1Id = chatroomService.createChatroomWithTasks(request1, TEST_TEAM_ID, TEST_CREATOR);
        Long chatroom2Id = chatroomService.createChatroomWithTasks(request2, TEST_TEAM_ID, TEST_CREATOR);
        
        // 模拟员工ID（假设存在）
        Long staffId = 1L;
        
        // 创建员工与聊天室的关联
        TrainChatroomStaff staff1 = new TrainChatroomStaff();
        staff1.setReceChatroomId(chatroom1Id);
        staff1.setStaffId(staffId);
        staff1.setTeamId(TEST_TEAM_ID);
        staff1.setCreator(TEST_CREATOR);
        staff1.setUpdater(TEST_CREATOR);
        chatroomStaffMapper.insert(staff1);
        
        TrainChatroomStaff staff2 = new TrainChatroomStaff();
        staff2.setReceChatroomId(chatroom2Id);
        staff2.setStaffId(staffId);
        staff2.setTeamId(TEST_TEAM_ID);
        staff2.setCreator(TEST_CREATOR);
        staff2.setUpdater(TEST_CREATOR);
        chatroomStaffMapper.insert(staff2);
        
        // 测试：按roomName模糊查询"电商"
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setRoomName("电商");
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<ChatroomListDTO> result = chatroomService.getMyTasks(queryRequest, staffId, TEST_TEAM_ID);
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        assertEquals("我的电商聊天室", result.getRecords().get(0).getRoomName());
        
        System.out.println("✅ 我的任务中roomName查询过滤功能测试通过");
    }

    @Test
    @Transactional
    @Rollback
    @DisplayName("测试聊天室列表按creator过滤功能")
    void testGetChatroomListWithCreator() {
        System.out.println("开始测试聊天室列表按creator过滤功能");

        // 1. 创建两个不同creator的聊天室
        // 聊天室1 - creator为user1
        ChatroomCreateRequest request1 = createTestChatroomRequest("测试聊天室user1", "dongdong_skin", 1);
        String creator1 = "user1";
        Long chatroom1Id = chatroomService.createChatroomWithTasks(request1, TEST_TEAM_ID, creator1);
        assertNotNull(chatroom1Id);
        System.out.println("创建聊天室1成功，ID=" + chatroom1Id + ", creator=" + creator1);

        // 聊天室2 - creator为user2
        ChatroomCreateRequest request2 = createTestChatroomRequest("测试聊天室user2", "qianniu_skin", 0);
        String creator2 = "user2";
        Long chatroom2Id = chatroomService.createChatroomWithTasks(request2, TEST_TEAM_ID, creator2);
        assertNotNull(chatroom2Id);
        System.out.println("创建聊天室2成功，ID=" + chatroom2Id + ", creator=" + creator2);

        // 2. 测试user1只能查询到自己创建的聊天室
        ChatroomQueryRequest queryRequest = new ChatroomQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        // 手动设置offset
        queryRequest.setOffset(0);

        // 直接调用Mapper方法模拟不同creator的查询
        List<ChatroomListDTO> user1Records = chatroomMapper.selectPageList(queryRequest, TEST_TEAM_ID, creator1);
        assertNotNull(user1Records);
        assertEquals(1, user1Records.size());
        assertEquals("测试聊天室user1", user1Records.get(0).getRoomName());
        assertEquals(chatroom1Id, user1Records.get(0).getId());
        System.out.println("✅ user1只能查询到自己创建的聊天室");

        // 3. 测试user2只能查询到自己创建的聊天室
        List<ChatroomListDTO> user2Records = chatroomMapper.selectPageList(queryRequest, TEST_TEAM_ID, creator2);
        assertNotNull(user2Records);
        assertEquals(1, user2Records.size());
        assertEquals("测试聊天室user2", user2Records.get(0).getRoomName());
        assertEquals(chatroom2Id, user2Records.get(0).getId());
        System.out.println("✅ user2只能查询到自己创建的聊天室");

        // 4. 验证统计功能也正确
        Long user1Count = chatroomMapper.selectPageCount(queryRequest, TEST_TEAM_ID, creator1);
        assertEquals(1L, user1Count);
        Long user2Count = chatroomMapper.selectPageCount(queryRequest, TEST_TEAM_ID, creator2);
        assertEquals(1L, user2Count);
        System.out.println("✅ creator过滤的统计功能正常");

        System.out.println("✅ 聊天室列表按creator过滤功能测试完全通过");
    }
}