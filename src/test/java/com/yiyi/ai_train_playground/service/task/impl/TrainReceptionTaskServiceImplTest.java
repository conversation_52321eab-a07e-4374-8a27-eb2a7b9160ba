package com.yiyi.ai_train_playground.service.task.impl;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskCreateRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskDetailDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskListDTO;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskQueryRequest;
import com.yiyi.ai_train_playground.dto.task.TrainReceptionTaskUpdateRequest;
import com.yiyi.ai_train_playground.entity.task.TrainReceptionTask;
import com.yiyi.ai_train_playground.mapper.task.TrainReceptionTaskMapper;
import com.yiyi.ai_train_playground.service.task.TrainReceptionTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 接待任务服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("home")
@Transactional
public class TrainReceptionTaskServiceImplTest {
    
    @Autowired
    private TrainReceptionTaskService trainReceptionTaskService;
    
    @Autowired
    private TrainReceptionTaskMapper trainReceptionTaskMapper;
    
    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_CREATOR = "test_user";
    
    @Test
    public void testCreateTask() {
        log.info("测试创建接待任务");
        
        // 准备测试数据
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试接待任务");
        request.setTaskDescription("这是一个测试任务");
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        
        // 执行创建
        Long taskId = trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertNotNull(taskId);
        assertTrue(taskId > 0);
        
        log.info("创建接待任务成功，taskId: {}", taskId);
    }
    
    @Test
    public void testGetTaskDetail() {
        log.info("测试查询接待任务详情");
        
        // 先创建一个任务
        Long taskId = createTestTask();
        
        // 查询任务详情
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(detail);
        assertEquals(taskId, detail.getId());
        assertEquals("测试接待任务", detail.getTaskName());
        assertEquals("这是一个测试任务", detail.getTaskDescription());
        assertEquals(Integer.valueOf(1), detail.getTaskMode());
        assertEquals("AI智训", detail.getTaskModeName());
        assertEquals(Integer.valueOf(0), detail.getTaskType());
        assertEquals("商品知识训练", detail.getTaskTypeName());
        
        log.info("查询接待任务详情成功，任务名称: {}", detail.getTaskName());
    }
    
    @Test
    public void testGetTaskList() {
        log.info("测试查询接待任务列表");

        // 先创建一个任务
        createTestTask();

        // 准备查询条件
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskName("测试");
        request.setPage(1);
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getRecords().size() > 0);

        TrainReceptionTaskListDTO firstTask = result.getRecords().get(0);
        assertNotNull(firstTask.getId());
        assertNotNull(firstTask.getTaskName());
        assertNotNull(firstTask.getConvKbId());
        
        // 验证新增的知识库名称字段
        log.info("验证知识库字段 - convKbId: {}, convKbIdValue: {}", 
            firstTask.getConvKbId(), firstTask.getConvKbIdValue());
        
        // 如果convKbId不为null，convKbIdValue也应该有值（除非数据库中对应记录不存在）

        log.info("查询接待任务列表成功，共 {} 条记录", result.getTotal());
    }

    @Test
    public void testGetTaskListWithKnowledgeBaseFields() {
        log.info("测试接待任务列表查询中的知识库字段");

        // 创建一个测试任务，关联知识库ID=1
        Long taskId = createTestTask();
        assertNotNull(taskId);

        // 查询任务列表
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setPage(1);
        request.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getRecords().size() > 0);

        // 找到我们创建的任务
        TrainReceptionTaskListDTO createdTask = result.getRecords().stream()
            .filter(task -> task.getId().equals(taskId))
            .findFirst()
            .orElse(null);

        assertNotNull(createdTask, "未找到创建的测试任务");

        // 验证知识库相关字段
        assertNotNull(createdTask.getConvKbId(), "convKbId不能为null");
        assertEquals(1L, createdTask.getConvKbId(), "convKbId应为1");

        // convKbIdValue字段应该从train_kb_tpl_main表中获取name字段的值
        // 由于这是测试环境，convKbIdValue可能为空（如果测试数据库中没有对应记录）
        log.info("测试任务的知识库字段 - convKbId: {}, convKbIdValue: {}", 
            createdTask.getConvKbId(), createdTask.getConvKbIdValue());

        // 验证其他必要字段
        assertNotNull(createdTask.getId());
        assertNotNull(createdTask.getTaskName());

        log.info("知识库字段测试完成");
    }

    @Test
    public void testGetTaskListWithAllFilters() {
        log.info("测试使用所有过滤条件查询接待任务列表");

        // 先创建一个任务
        createTestTask();

        // 准备查询条件 - 使用所有过滤条件
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试");
        request.setScriptId(1L); // 测试剧本ID
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setPage(1);
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getRecords().size() > 0);

        TrainReceptionTaskListDTO firstTask = result.getRecords().get(0);
        assertNotNull(firstTask.getId());
        assertEquals("测试接待任务", firstTask.getTaskName());
        assertEquals(Integer.valueOf(1), firstTask.getTaskMode());
        assertEquals("AI智训", firstTask.getTaskModeName());
        assertEquals(Integer.valueOf(0), firstTask.getTaskType());
        assertEquals("商品知识训练", firstTask.getTaskTypeName());
        assertEquals(Integer.valueOf(0), firstTask.getTaskPurposeTag());
        assertEquals("训练", firstTask.getTaskPurposeTagName());
        assertEquals(Integer.valueOf(0), firstTask.getJudgeType());
        assertEquals("单条会话打分", firstTask.getJudgeTypeName());

        log.info("使用所有过滤条件查询接待任务列表成功，共 {} 条记录", result.getTotal());
    }

    @Test
    public void testGetTaskListWithEmptyResult() {
        log.info("测试查询空结果的接待任务列表");

        // 准备查询条件 - 使用不存在的任务名称
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskName("不存在的任务名称");
        request.setPage(1);
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertEquals(0L, result.getTotal());
        assertEquals(0, result.getRecords().size());
        assertEquals(Integer.valueOf(1), result.getPage());
        assertEquals(Integer.valueOf(10), result.getSize());

        log.info("查询空结果的接待任务列表成功");
    }

    @Test
    public void testGetTaskListPagination() {
        log.info("测试接待任务列表分页功能");

        // 先创建多个任务
        createTestTask();
        createTestTaskWithName("测试任务2");
        createTestTaskWithName("测试任务3");

        // 测试第一页
        TrainReceptionTaskQueryRequest request1 = new TrainReceptionTaskQueryRequest();
        request1.setTaskName("测试");
        request1.setPage(1);
        request1.setPageSize(2);

        PageResult<TrainReceptionTaskListDTO> result1 = trainReceptionTaskService.getTaskList(request1, TEST_TEAM_ID);

        // 验证第一页结果
        assertNotNull(result1);
        assertNotNull(result1.getRecords());
        assertTrue(result1.getTotal() >= 3);
        assertEquals(2, result1.getRecords().size());
        assertEquals(Integer.valueOf(1), result1.getPage());
        assertEquals(Integer.valueOf(2), result1.getSize());

        // 测试第二页
        TrainReceptionTaskQueryRequest request2 = new TrainReceptionTaskQueryRequest();
        request2.setTaskName("测试");
        request2.setPage(2);
        request2.setPageSize(2);

        PageResult<TrainReceptionTaskListDTO> result2 = trainReceptionTaskService.getTaskList(request2, TEST_TEAM_ID);

        // 验证第二页结果
        assertNotNull(result2);
        assertNotNull(result2.getRecords());
        assertTrue(result2.getRecords().size() >= 1);
        assertEquals(Integer.valueOf(2), result2.getPage());
        assertEquals(Integer.valueOf(2), result2.getSize());

        // 验证两页的总数一致
        assertEquals(result1.getTotal(), result2.getTotal());

        log.info("接待任务列表分页功能测试成功，总记录数: {}", result1.getTotal());
    }

    @Test
    public void testGetTaskListWithDifferentTaskModes() {
        log.info("测试查询不同任务模式的接待任务列表");

        // 创建不同模式的任务
        createTestTaskWithMode(0, "原版案例任务"); // 原版案例
        createTestTaskWithMode(1, "AI智训任务"); // AI智训
        createTestTaskWithMode(2, "AI智训玩法任务"); // AI智训玩法

        // 测试查询原版案例任务
        TrainReceptionTaskQueryRequest request1 = new TrainReceptionTaskQueryRequest();
        request1.setTaskMode(0);
        request1.setPage(1);
        request1.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result1 = trainReceptionTaskService.getTaskList(request1, TEST_TEAM_ID);
        assertNotNull(result1);
        assertTrue(result1.getTotal() > 0);
        assertTrue(result1.getRecords().stream().allMatch(task -> task.getTaskMode().equals(0)));
        assertTrue(result1.getRecords().stream().allMatch(task -> "原版案例".equals(task.getTaskModeName())));

        // 测试查询AI智训任务
        TrainReceptionTaskQueryRequest request2 = new TrainReceptionTaskQueryRequest();
        request2.setTaskMode(1);
        request2.setPage(1);
        request2.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result2 = trainReceptionTaskService.getTaskList(request2, TEST_TEAM_ID);
        assertNotNull(result2);
        assertTrue(result2.getTotal() > 0);
        assertTrue(result2.getRecords().stream().allMatch(task -> task.getTaskMode().equals(1)));
        assertTrue(result2.getRecords().stream().allMatch(task -> "AI智训".equals(task.getTaskModeName())));

        log.info("查询不同任务模式的接待任务列表测试成功");
    }

    @Test
    public void testGetTaskListWithDifferentTaskTypes() {
        log.info("测试查询不同任务类型的接待任务列表");

        // 创建不同类型的任务
        createTestTaskWithType(0, "商品知识训练任务"); // 商品知识训练
        createTestTaskWithType(1, "实战进阶任务"); // 实战进阶任务
        createTestTaskWithType(2, "综合训练任务"); // 综合训练任务

        // 测试查询商品知识训练任务
        TrainReceptionTaskQueryRequest request1 = new TrainReceptionTaskQueryRequest();
        request1.setTaskType(0);
        request1.setPage(1);
        request1.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result1 = trainReceptionTaskService.getTaskList(request1, TEST_TEAM_ID);
        assertNotNull(result1);
        assertTrue(result1.getTotal() > 0);
        assertTrue(result1.getRecords().stream().allMatch(task -> task.getTaskType().equals(0)));
        assertTrue(result1.getRecords().stream().allMatch(task -> "商品知识训练".equals(task.getTaskTypeName())));

        // 测试查询实战进阶任务
        TrainReceptionTaskQueryRequest request2 = new TrainReceptionTaskQueryRequest();
        request2.setTaskType(1);
        request2.setPage(1);
        request2.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result2 = trainReceptionTaskService.getTaskList(request2, TEST_TEAM_ID);
        assertNotNull(result2);
        assertTrue(result2.getTotal() > 0);
        assertTrue(result2.getRecords().stream().allMatch(task -> task.getTaskType().equals(1)));
        assertTrue(result2.getRecords().stream().allMatch(task -> "实战进阶任务".equals(task.getTaskTypeName())));

        log.info("查询不同任务类型的接待任务列表测试成功");
    }

    @Test
    public void testGetTaskListWithTaskPurposeTag() {
        log.info("测试根据任务标签查询接待任务列表");

        // 创建不同标签的任务
        createTestTaskWithPurposeTag(0, "训练任务"); // 训练
        createTestTaskWithPurposeTag(1, "考核任务"); // 考核
        createTestTaskWithPurposeTag(2, "面试任务"); // 面试

        // 测试查询训练任务
        TrainReceptionTaskQueryRequest request1 = new TrainReceptionTaskQueryRequest();
        request1.setTaskPurposeTag(0);
        request1.setPage(1);
        request1.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result1 = trainReceptionTaskService.getTaskList(request1, TEST_TEAM_ID);
        assertNotNull(result1);
        assertTrue(result1.getTotal() > 0);
        assertTrue(result1.getRecords().stream().allMatch(task -> task.getTaskPurposeTag().equals(0)));
        assertTrue(result1.getRecords().stream().allMatch(task -> "训练".equals(task.getTaskPurposeTagName())));

        // 测试查询考核任务
        TrainReceptionTaskQueryRequest request2 = new TrainReceptionTaskQueryRequest();
        request2.setTaskPurposeTag(1);
        request2.setPage(1);
        request2.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result2 = trainReceptionTaskService.getTaskList(request2, TEST_TEAM_ID);
        assertNotNull(result2);
        assertTrue(result2.getTotal() > 0);
        assertTrue(result2.getRecords().stream().allMatch(task -> task.getTaskPurposeTag().equals(1)));
        assertTrue(result2.getRecords().stream().allMatch(task -> "考核".equals(task.getTaskPurposeTagName())));

        log.info("根据任务标签查询接待任务列表测试成功");
    }

    @Test
    public void testGetTaskListWithJudgeType() {
        log.info("测试根据打分响应类型查询接待任务列表");

        // 创建不同打分响应类型的任务
        createTestTaskWithJudgeType(0, "单条会话打分任务"); // 单条会话打分
        createTestTaskWithJudgeType(1, "会话结束打分任务"); // 会话结束打分
        createTestTaskWithJudgeType(2, "单条结束都要打分任务"); // 单条、结束都要打

        // 测试查询单条会话打分任务
        TrainReceptionTaskQueryRequest request1 = new TrainReceptionTaskQueryRequest();
        request1.setJudgeType(0);
        request1.setPage(1);
        request1.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result1 = trainReceptionTaskService.getTaskList(request1, TEST_TEAM_ID);
        assertNotNull(result1);
        assertTrue(result1.getTotal() > 0);
        assertTrue(result1.getRecords().stream().allMatch(task -> task.getJudgeType().equals(0)));
        assertTrue(result1.getRecords().stream().allMatch(task -> "单条会话打分".equals(task.getJudgeTypeName())));

        // 测试查询会话结束打分任务
        TrainReceptionTaskQueryRequest request2 = new TrainReceptionTaskQueryRequest();
        request2.setJudgeType(1);
        request2.setPage(1);
        request2.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result2 = trainReceptionTaskService.getTaskList(request2, TEST_TEAM_ID);
        assertNotNull(result2);
        assertTrue(result2.getTotal() > 0);
        assertTrue(result2.getRecords().stream().allMatch(task -> task.getJudgeType().equals(1)));
        assertTrue(result2.getRecords().stream().allMatch(task -> "会话结束打分".equals(task.getJudgeTypeName())));

        log.info("根据打分响应类型查询接待任务列表测试成功");
    }

    @Test
    public void testGetTaskListWithLargePageSize() {
        log.info("测试使用大页面大小查询接待任务列表");

        // 先创建一些任务
        createTestTask();
        createTestTaskWithName("测试任务2");
        createTestTaskWithName("测试任务3");

        // 准备查询条件 - 使用大页面大小
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskName("测试");
        request.setPage(1);
        request.setPageSize(1000); // 大页面大小

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() >= 3);
        assertEquals(Integer.valueOf(1), result.getPage());
        assertEquals(Integer.valueOf(1000), result.getSize());

        log.info("使用大页面大小查询接待任务列表测试成功，共 {} 条记录", result.getTotal());
    }

    @Test
    public void testGetTaskListWithZeroPage() {
        log.info("测试使用第0页查询接待任务列表");

        // 先创建一个任务
        createTestTask();

        // 准备查询条件 - 使用第0页
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskName("测试");
        request.setPage(0); // 第0页
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果 - 应该自动修正为第1页
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertEquals(Integer.valueOf(1), result.getPage()); // 应该被修正为第1页
        assertTrue(result.getTotal() > 0);

        log.info("使用第0页查询接待任务列表测试完成");
    }

    @Test
    public void testGetTaskListWithNullFilters() {
        log.info("测试使用null过滤条件查询接待任务列表");

        // 先创建一个任务
        createTestTask();

        // 准备查询条件 - 所有过滤条件都为null
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskMode(null);
        request.setTaskType(null);
        request.setTaskName(null);
        request.setScriptId(null);
        request.setTaskPurposeTag(null);
        request.setJudgeType(null);
        request.setPage(1);
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果 - 应该返回所有任务
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);

        log.info("使用null过滤条件查询接待任务列表测试成功，共 {} 条记录", result.getTotal());
    }

    @Test
    public void testGetTaskListWithEmptyStringFilter() {
        log.info("测试使用空字符串过滤条件查询接待任务列表");

        // 先创建一个任务
        createTestTask();

        // 准备查询条件 - 任务名称为空字符串
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setTaskName(""); // 空字符串
        request.setPage(1);
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果 - 应该返回所有任务（空字符串通常被忽略）
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);

        log.info("使用空字符串过滤条件查询接待任务列表测试成功，共 {} 条记录", result.getTotal());
    }

    @Test
    public void testGetTaskListWithNonExistentScriptId() {
        log.info("测试使用不存在的剧本ID查询接待任务列表");

        // 先创建一个任务
        createTestTask();

        // 准备查询条件 - 使用不存在的剧本ID
        TrainReceptionTaskQueryRequest request = new TrainReceptionTaskQueryRequest();
        request.setScriptId(999999L); // 不存在的剧本ID
        request.setPage(1);
        request.setPageSize(10);

        // 执行查询
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(request, TEST_TEAM_ID);

        // 验证结果 - 应该返回空结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertEquals(0L, result.getTotal());
        assertEquals(0, result.getRecords().size());

        log.info("使用不存在的剧本ID查询接待任务列表测试成功");
    }
    
    @Test
    public void testUpdateTask() {
        log.info("测试更新接待任务");
        
        // 先创建一个任务
        Long taskId = createTestTask();
        
        // 准备更新数据
        TrainReceptionTaskUpdateRequest request = new TrainReceptionTaskUpdateRequest();
        request.setId(taskId);
        request.setTaskName("更新后的任务名称");
        request.setTaskDescription("更新后的任务描述");
        request.setReceptionDuration(45);
        
        // 执行更新
        boolean success = trainReceptionTaskService.updateTask(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertTrue(success);
        
        // 查询更新后的任务
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals("更新后的任务名称", detail.getTaskName());
        assertEquals("更新后的任务描述", detail.getTaskDescription());
        assertEquals(Integer.valueOf(45), detail.getReceptionDuration());
        
        log.info("更新接待任务成功");
    }

    @Test
    public void testDeleteTask() {
        log.info("测试删除接待任务");

        // 先创建一个任务
        Long taskId = createTestTask();

        // 验证任务存在
        TrainReceptionTask task = trainReceptionTaskService.getTaskById(taskId, TEST_TEAM_ID);
        assertNotNull(task);

        // 执行删除
        boolean success = trainReceptionTaskService.deleteTask(taskId, TEST_TEAM_ID);

        // 验证结果
        assertTrue(success);

        // 验证任务已被删除
        TrainReceptionTask deletedTask = trainReceptionTaskService.getTaskById(taskId, TEST_TEAM_ID);
        assertNull(deletedTask);

        log.info("删除接待任务成功");
    }

    @Test
    public void testBatchDeleteTasks() {
        log.info("测试批量删除接待任务");

        // 先创建两个任务
        Long taskId1 = createTestTask();
        Long taskId2 = createTestTask();

        // 验证任务存在
        assertNotNull(trainReceptionTaskService.getTaskById(taskId1, TEST_TEAM_ID));
        assertNotNull(trainReceptionTaskService.getTaskById(taskId2, TEST_TEAM_ID));

        // 执行批量删除
        String ids = taskId1 + "," + taskId2;
        boolean success = trainReceptionTaskService.batchDeleteTasks(ids, TEST_TEAM_ID);

        // 验证结果
        assertTrue(success);

        // 验证任务已被删除
        assertNull(trainReceptionTaskService.getTaskById(taskId1, TEST_TEAM_ID));
        assertNull(trainReceptionTaskService.getTaskById(taskId2, TEST_TEAM_ID));

        log.info("批量删除接待任务成功");
    }

    @Test
    public void testGetTaskById() {
        log.info("测试根据ID查询接待任务");

        // 先创建一个任务
        Long taskId = createTestTask();

        // 查询任务
        TrainReceptionTask task = trainReceptionTaskService.getTaskById(taskId, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(task);
        assertEquals(taskId, task.getId());
        assertEquals("测试接待任务", task.getTaskName());
        assertEquals(TEST_TEAM_ID, task.getTeamId());

        log.info("根据ID查询接待任务成功");
    }

    @Test
    public void testGetTaskByIdNotFound() {
        log.info("测试查询不存在的接待任务");

        // 查询不存在的任务
        TrainReceptionTask task = trainReceptionTaskService.getTaskById(999999L, TEST_TEAM_ID);

        // 验证结果
        assertNull(task);

        log.info("查询不存在的接待任务，返回null");
    }

    /**
     * 创建测试任务的辅助方法
     */
    private Long createTestTask() {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试接待任务");
        request.setTaskDescription("这是一个测试任务");
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        return trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
    }

    /**
     * 创建指定名称的测试任务的辅助方法
     */
    private Long createTestTaskWithName(String taskName) {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName(taskName);
        request.setTaskDescription("这是一个测试任务：" + taskName);
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        return trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
    }

    /**
     * 创建指定任务模式的测试任务的辅助方法
     */
    private Long createTestTaskWithMode(Integer taskMode, String taskName) {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(taskMode);
        request.setTaskType(0); // 商品知识训练
        request.setTaskName(taskName);
        request.setTaskDescription("这是一个测试任务：" + taskName);
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        return trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
    }

    /**
     * 创建指定任务类型的测试任务的辅助方法
     */
    private Long createTestTaskWithType(Integer taskType, String taskName) {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(taskType);
        request.setTaskName(taskName);
        request.setTaskDescription("这是一个测试任务：" + taskName);
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        return trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
    }

    /**
     * 创建指定任务标签的测试任务的辅助方法
     */
    private Long createTestTaskWithPurposeTag(Integer taskPurposeTag, String taskName) {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName(taskName);
        request.setTaskDescription("这是一个测试任务：" + taskName);
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(taskPurposeTag);
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID

        return trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
    }

    /**
     * 创建指定打分响应类型的测试任务的辅助方法
     */
    private Long createTestTaskWithJudgeType(Integer judgeType, String taskName) {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName(taskName);
        request.setTaskDescription("这是一个测试任务：" + taskName);
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(judgeType);
        request.setConvKbId(1L); // 测试知识库ID

        return trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
    }

    @Test
    public void testLearningProgressPercentCalculation() {
        log.info("测试学习进度百分比计算逻辑");

        // 先创建一个任务
        Long taskId = createTestTask();

        // 查询任务详情，验证learningProgressPercent字段
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertNotNull(detail.getLearningProgressPercent());

        log.info("任务ID: {}, 待学习数量: {}, 已学习数量: {}, 学习进度: {}",
                taskId, detail.getAmtToBeLearned(), detail.getAmtHasLearned(), detail.getLearningProgressPercent());

        // 验证learningProgressPercent格式正确（应该包含%符号）
        assertTrue(detail.getLearningProgressPercent().endsWith("%"));

        // 查询任务列表，验证learningProgressPercent字段
        TrainReceptionTaskQueryRequest queryRequest = new TrainReceptionTaskQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> listResult = trainReceptionTaskService.getTaskList(queryRequest, TEST_TEAM_ID);
        assertNotNull(listResult);
        assertNotNull(listResult.getRecords());
        assertTrue(listResult.getRecords().size() > 0);

        // 找到刚创建的任务
        TrainReceptionTaskListDTO taskInList = listResult.getRecords().stream()
                .filter(task -> task.getId().equals(taskId))
                .findFirst()
                .orElse(null);

        assertNotNull(taskInList);
        assertNotNull(taskInList.getLearningProgressPercent());
        assertTrue(taskInList.getLearningProgressPercent().endsWith("%"));

        log.info("列表中的任务学习进度: {}", taskInList.getLearningProgressPercent());

        // 验证详情和列表中的学习进度一致
        assertEquals(detail.getLearningProgressPercent(), taskInList.getLearningProgressPercent());
    }

    @Test
    public void testCreateTaskWithQaMainId() {
        log.info("测试创建带有高频知识库ID的接待任务");

        // 准备测试数据
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试高频知识库任务");
        request.setTaskDescription("这是一个测试高频知识库的任务");
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setQaMainId(1L); // 测试高频知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        // 执行创建
        Long taskId = trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertNotNull(taskId);
        assertTrue(taskId > 0);

        // 查询任务详情，验证qaMainId字段
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(taskId, detail.getId());
        assertEquals(Long.valueOf(1L), detail.getQaMainId());

        log.info("创建带有高频知识库ID的接待任务成功，taskId: {}, qaMainId: {}", taskId, detail.getQaMainId());
    }

    @Test
    public void testGetTaskListWithQaMainFields() {
        log.info("测试接待任务列表查询中的高频知识库字段");

        // 创建一个测试任务，关联高频知识库ID=1
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试高频知识库列表任务");
        request.setTaskDescription("这是一个测试高频知识库列表的任务");
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setQaMainId(1L); // 测试高频知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        Long taskId = trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(taskId);

        // 查询任务列表
        TrainReceptionTaskQueryRequest queryRequest = new TrainReceptionTaskQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);

        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(queryRequest, TEST_TEAM_ID);

        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getRecords().size() > 0);

        // 找到我们创建的任务
        TrainReceptionTaskListDTO createdTask = result.getRecords().stream()
            .filter(task -> task.getId().equals(taskId))
            .findFirst()
            .orElse(null);

        assertNotNull(createdTask, "未找到创建的测试任务");

        // 验证高频知识库相关字段
        assertNotNull(createdTask.getQaMainId(), "qaMainId不能为null");
        assertEquals(1L, createdTask.getQaMainId(), "qaMainId应为1");

        // qaMainName字段应该从train_qa_import_main表中获取qa_im_name字段的值
        // 由于这是测试环境，qaMainName可能为空（如果测试数据库中没有对应记录）
        log.info("测试任务的高频知识库字段 - qaMainId: {}, qaMainName: {}",
            createdTask.getQaMainId(), createdTask.getQaMainName());

        // 验证其他必要字段
        assertNotNull(createdTask.getId());
        assertNotNull(createdTask.getTaskName());

        log.info("高频知识库字段测试完成");
    }

    @Test
    public void testGetTaskDetailWithQaMainFields() {
        log.info("测试接待任务详情查询中的高频知识库字段");

        // 创建一个测试任务，关联高频知识库ID=1
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试高频知识库详情任务");
        request.setTaskDescription("这是一个测试高频知识库详情的任务");
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setQaMainId(1L); // 测试高频知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时

        Long taskId = trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(taskId);

        // 查询任务详情
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);

        assertNotNull(detail);
        assertEquals(taskId, detail.getId());

        // 验证高频知识库相关字段
        assertNotNull(detail.getQaMainId(), "qaMainId不能为null");
        assertEquals(1L, detail.getQaMainId(), "qaMainId应为1");

        // qaMainName字段应该从train_qa_import_main表中获取qa_im_name字段的值
        // 由于这是测试环境，qaMainName可能为空（如果测试数据库中没有对应记录）
        log.info("测试任务详情的高频知识库字段 - qaMainId: {}, qaMainName: {}",
            detail.getQaMainId(), detail.getQaMainName());

        // 验证其他必要字段
        assertNotNull(detail.getId());
        assertNotNull(detail.getTaskName());

        log.info("高频知识库详情字段测试完成");
    }

    @Test
    public void testUpdateTaskWithQaMainId() {
        log.info("测试更新接待任务的高频知识库ID");

        // 先创建一个任务
        Long taskId = createTestTask();

        // 准备更新数据，包含qaMainId
        TrainReceptionTaskUpdateRequest request = new TrainReceptionTaskUpdateRequest();
        request.setId(taskId);
        request.setTaskName("更新后的任务名称");
        request.setTaskDescription("更新后的任务描述");
        request.setReceptionDuration(45);
        request.setQaMainId(2L); // 更新高频知识库ID

        // 执行更新
        boolean success = trainReceptionTaskService.updateTask(request, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertTrue(success);

        // 查询更新后的任务
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals("更新后的任务名称", detail.getTaskName());
        assertEquals("更新后的任务描述", detail.getTaskDescription());
        assertEquals(Integer.valueOf(45), detail.getReceptionDuration());
        assertEquals(Long.valueOf(2L), detail.getQaMainId());

        log.info("更新接待任务的高频知识库ID成功，新的qaMainId: {}", detail.getQaMainId());
    }

    @Test
    public void testCreateTaskWithNewFields() {
        log.info("测试创建包含新字段的接待任务");
        
        // 准备测试数据，包含新字段
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试新字段任务");
        request.setTaskDescription("测试新增字段功能");
        request.setScriptId(1L);
        request.setReceptionDuration(30);
        request.setConvKbId(1L);
        // 设置新字段
        request.setIsShowResolve(true);
        request.setIsShowCorrect(false);
        request.setFreqAuesCnt(15);
        
        // 执行创建
        Long taskId = trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertNotNull(taskId);
        assertTrue(taskId > 0);
        
        // 查询任务详情验证新字段
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(Boolean.TRUE, detail.getIsShowResolve());
        assertEquals(Boolean.FALSE, detail.getIsShowCorrect());
        assertEquals(Integer.valueOf(15), detail.getFreqAuesCnt());
        
        log.info("创建包含新字段的接待任务成功，taskId: {}, isShowResolve: {}, isShowCorrect: {}, freqAuesCnt: {}", 
            taskId, detail.getIsShowResolve(), detail.getIsShowCorrect(), detail.getFreqAuesCnt());
    }

    @Test
    public void testUpdateTaskNewFields() {
        log.info("测试更新接待任务新字段");
        
        // 先创建一个任务
        Long taskId = createTestTask();
        
        // 更新新字段
        TrainReceptionTaskUpdateRequest request = new TrainReceptionTaskUpdateRequest();
        request.setId(taskId);
        request.setIsShowResolve(true);
        request.setIsShowCorrect(true);
        request.setFreqAuesCnt(20);
        
        // 执行更新
        boolean success = trainReceptionTaskService.updateTask(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertTrue(success);
        
        // 查询更新后的任务
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(Boolean.TRUE, detail.getIsShowResolve());
        assertEquals(Boolean.TRUE, detail.getIsShowCorrect());
        assertEquals(Integer.valueOf(20), detail.getFreqAuesCnt());
        
        log.info("更新接待任务新字段成功，isShowResolve: {}, isShowCorrect: {}, freqAuesCnt: {}", 
            detail.getIsShowResolve(), detail.getIsShowCorrect(), detail.getFreqAuesCnt());
    }

    @Test
    public void testGetTaskListWithNewFields() {
        log.info("测试查询包含新字段的任务列表");
        
        // 创建一个包含新字段的任务
        TrainReceptionTaskCreateRequest createRequest = new TrainReceptionTaskCreateRequest();
        createRequest.setTaskMode(1);
        createRequest.setTaskType(0);
        createRequest.setTaskName("新字段列表测试任务");
        createRequest.setScriptId(1L);
        createRequest.setConvKbId(1L);
        createRequest.setIsShowResolve(true);
        createRequest.setIsShowCorrect(false);
        createRequest.setFreqAuesCnt(25);
        
        Long taskId = trainReceptionTaskService.createTask(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        
        // 查询任务列表
        TrainReceptionTaskQueryRequest queryRequest = new TrainReceptionTaskQueryRequest();
        queryRequest.setTaskName("新字段列表测试任务");
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(queryRequest, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        
        // 验证新字段在列表中正确返回
        TrainReceptionTaskListDTO task = result.getRecords().stream()
            .filter(t -> t.getId().equals(taskId))
            .findFirst()
            .orElse(null);
        
        assertNotNull(task);
        assertEquals(Boolean.TRUE, task.getIsShowResolve());
        assertEquals(Boolean.FALSE, task.getIsShowCorrect());
        assertEquals(Integer.valueOf(25), task.getFreqAuesCnt());
        
        log.info("查询包含新字段的任务列表成功，找到任务：isShowResolve: {}, isShowCorrect: {}, freqAuesCnt: {}",
            task.getIsShowResolve(), task.getIsShowCorrect(), task.getFreqAuesCnt());
    }

    @Test
    public void testTaskValidationByPurposeTag() {
        log.info("测试根据任务标签的验证逻辑");

        // 测试1：任务标签为训练(0)时，高频知识库ID不能为空
        TrainReceptionTaskCreateRequest request1 = new TrainReceptionTaskCreateRequest();
        request1.setTaskMode(1);
        request1.setTaskType(0);
        request1.setTaskName("训练任务测试");
        request1.setTaskPurposeTag(0); // 训练
        request1.setScriptId(1L);
        request1.setConvKbId(1L);
        request1.setQaMainId(1L); // 设置高频知识库ID

        // 应该创建成功
        Long taskId1 = trainReceptionTaskService.createTask(request1, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(taskId1);
        log.info("训练任务创建成功，taskId: {}", taskId1);

        // 测试2：任务标签为考核(1)时，剧本ID和会话知识库ID不能为空
        TrainReceptionTaskCreateRequest request2 = new TrainReceptionTaskCreateRequest();
        request2.setTaskMode(1);
        request2.setTaskType(0);
        request2.setTaskName("考核任务测试");
        request2.setTaskPurposeTag(1); // 考核
        request2.setScriptId(1L); // 设置剧本ID
        request2.setConvKbId(1L); // 设置会话知识库ID
        // 考核任务不需要高频知识库ID

        // 应该创建成功
        Long taskId2 = trainReceptionTaskService.createTask(request2, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(taskId2);
        log.info("考核任务创建成功，taskId: {}", taskId2);

        // 测试3：任务标签为面试(2)时，剧本ID和会话知识库ID不能为空
        TrainReceptionTaskCreateRequest request3 = new TrainReceptionTaskCreateRequest();
        request3.setTaskMode(1);
        request3.setTaskType(0);
        request3.setTaskName("面试任务测试");
        request3.setTaskPurposeTag(2); // 面试
        request3.setScriptId(1L); // 设置剧本ID
        request3.setConvKbId(1L); // 设置会话知识库ID
        // 面试任务不需要高频知识库ID

        // 应该创建成功
        Long taskId3 = trainReceptionTaskService.createTask(request3, TEST_TEAM_ID, TEST_CREATOR);
        assertNotNull(taskId3);
        log.info("面试任务创建成功，taskId: {}", taskId3);
    }

    @Test
    public void testCreateTaskWithIsShowInspect() {
        log.info("测试创建包含isShowInspect字段的接待任务");
        
        // 准备测试数据，包含isShowInspect字段
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试isShowInspect字段任务");
        request.setTaskDescription("测试新增isShowInspect字段功能");
        request.setScriptId(1L);
        request.setReceptionDuration(30);
        request.setConvKbId(1L);
        request.setSrvSendCd(30L);
        // 设置isShowInspect字段
        request.setIsShowInspect(true);
        
        // 执行创建
        Long taskId = trainReceptionTaskService.createTask(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertNotNull(taskId);
        assertTrue(taskId > 0);
        
        // 查询任务详情验证isShowInspect字段
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(Boolean.TRUE, detail.getIsShowInspect());
        
        log.info("创建包含isShowInspect字段的接待任务成功，taskId: {}, isShowInspect: {}", 
            taskId, detail.getIsShowInspect());
    }

    @Test
    public void testUpdateTaskIsShowInspect() {
        log.info("测试更新接待任务isShowInspect字段");
        
        // 先创建一个任务
        Long taskId = createTestTask();
        
        // 更新isShowInspect字段
        TrainReceptionTaskUpdateRequest request = new TrainReceptionTaskUpdateRequest();
        request.setId(taskId);
        request.setIsShowInspect(true);
        
        // 执行更新
        boolean success = trainReceptionTaskService.updateTask(request, TEST_TEAM_ID, TEST_CREATOR);
        
        // 验证结果
        assertTrue(success);
        
        // 查询更新后的任务
        TrainReceptionTaskDetailDTO detail = trainReceptionTaskService.getTaskDetail(taskId, TEST_TEAM_ID);
        assertNotNull(detail);
        assertEquals(Boolean.TRUE, detail.getIsShowInspect());
        
        log.info("更新接待任务isShowInspect字段成功，isShowInspect: {}", detail.getIsShowInspect());
    }

    @Test
    public void testGetTaskListWithIsShowInspect() {
        log.info("测试查询包含isShowInspect字段的任务列表");
        
        // 创建一个包含isShowInspect字段的任务
        TrainReceptionTaskCreateRequest createRequest = new TrainReceptionTaskCreateRequest();
        createRequest.setTaskMode(1);
        createRequest.setTaskType(0);
        createRequest.setTaskName("isShowInspect列表测试任务");
        createRequest.setScriptId(1L);
        createRequest.setConvKbId(1L);
        createRequest.setSrvSendCd(30L);
        createRequest.setIsShowInspect(true);
        
        Long taskId = trainReceptionTaskService.createTask(createRequest, TEST_TEAM_ID, TEST_CREATOR);
        
        // 查询任务列表
        TrainReceptionTaskQueryRequest queryRequest = new TrainReceptionTaskQueryRequest();
        queryRequest.setTaskName("isShowInspect");
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        PageResult<TrainReceptionTaskListDTO> result = trainReceptionTaskService.getTaskList(queryRequest, TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() > 0);
        
        TrainReceptionTaskListDTO firstTask = result.getRecords().get(0);
        assertNotNull(firstTask.getIsShowInspect());
        assertEquals(Boolean.TRUE, firstTask.getIsShowInspect());
        
        log.info("查询包含isShowInspect字段的任务列表成功，共 {} 条记录，isShowInspect: {}", 
            result.getTotal(), firstTask.getIsShowInspect());
    }

    @Test
    @DisplayName("测试creator参数查询功能")
    public void testGetTaskListWithCreator() {
        log.info("测试creator参数查询功能");
        
        // 创建第一个测试任务（creator="user1"）
        TrainReceptionTaskCreateRequest request1 = createTestTaskRequest();
        request1.setTaskName("测试任务user1");
        Long taskId1 = trainReceptionTaskService.createTask(request1, TEST_TEAM_ID, "user1");
        
        // 创建第二个测试任务（creator="user2"）
        TrainReceptionTaskCreateRequest request2 = createTestTaskRequest();
        request2.setTaskName("测试任务user2");  
        Long taskId2 = trainReceptionTaskService.createTask(request2, TEST_TEAM_ID, "user2");
        
        // 验证任务创建成功
        assertNotNull(taskId1, "任务1创建应该成功");
        assertNotNull(taskId2, "任务2创建应该成功");
        
        // 测试通过Mapper直接查询，指定creator="user1"
        TrainReceptionTaskQueryRequest queryRequest = new TrainReceptionTaskQueryRequest();
        queryRequest.setPage(1);
        queryRequest.setPageSize(10);
        
        // 直接调用Mapper方法测试creator过滤
        List<TrainReceptionTaskListDTO> listUser1 = trainReceptionTaskMapper.selectTaskList(
            queryRequest, TEST_TEAM_ID, "user1", 0, 10);
        
        List<TrainReceptionTaskListDTO> listUser2 = trainReceptionTaskMapper.selectTaskList(
            queryRequest, TEST_TEAM_ID, "user2", 0, 10);
        
        // 验证结果
        assertTrue(listUser1.size() > 0, "user1应该能查到至少一个任务");
        assertTrue(listUser2.size() > 0, "user2应该能查到至少一个任务");
        
        // 验证creator过滤是否正确
        boolean foundUser1Task = listUser1.stream()
            .anyMatch(task -> "测试任务user1".equals(task.getTaskName()) && "user1".equals(task.getCreator()));
        
        boolean foundUser2Task = listUser2.stream()
            .anyMatch(task -> "测试任务user2".equals(task.getTaskName()) && "user2".equals(task.getCreator()));
        
        assertTrue(foundUser1Task, "user1查询结果中应该包含user1创建的任务");
        assertTrue(foundUser2Task, "user2查询结果中应该包含user2创建的任务");
        
        // 验证user1看不到user2的任务
        boolean user1SeeUser2Task = listUser1.stream()
            .anyMatch(task -> "测试任务user2".equals(task.getTaskName()));
        assertFalse(user1SeeUser2Task, "user1不应该看到user2创建的任务");
        
        // 验证user2看不到user1的任务  
        boolean user2SeeUser1Task = listUser2.stream()
            .anyMatch(task -> "测试任务user1".equals(task.getTaskName()));
        assertFalse(user2SeeUser1Task, "user2不应该看到user1创建的任务");
        
        log.info("creator参数查询功能测试通过，user1查到{}个任务，user2查到{}个任务", 
            listUser1.size(), listUser2.size());
    }

    /**
     * 创建测试任务请求对象的辅助方法
     */
    private TrainReceptionTaskCreateRequest createTestTaskRequest() {
        TrainReceptionTaskCreateRequest request = new TrainReceptionTaskCreateRequest();
        request.setTaskMode(1); // AI智训
        request.setTaskType(0); // 商品知识训练
        request.setTaskName("测试接待任务");
        request.setTaskDescription("这是一个测试任务");
        request.setScriptId(1L); // 测试剧本ID
        request.setReceptionDuration(30);
        request.setQuestionIntervalType(0); // 随机
        request.setQuestionIntervalSeconds(3);
        request.setTrainingLimitEnabled(false);
        request.setTaskPurposeTag(0); // 训练
        request.setJudgeType(0); // 单条会话打分
        request.setConvKbId(1L); // 测试知识库ID
        request.setSrvSendCd(30L); // 客服发送消息倒计时
        return request;
    }
}
