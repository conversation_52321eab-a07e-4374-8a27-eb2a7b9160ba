package com.yiyi.ai_train_playground.tool;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class tempFile2TxtTool {

    private static final String INPUT_FILE_PATH = "C:\\projects\\训练场\\基地发来的聊天记录\\chatbot_chat_flow.xlsx";
    private static final String OUTPUT_FILE_PATH = "C:\\projects\\训练场\\基地发来的聊天记录\\xiaomi_chatlog_bygemini.txt";

    // Inner class to represent a single chat message
    private static class ChatMessage {
        String msgId;
        String sid;
        String agent;
        String customer;
        String content;
        int direction;
        LocalDateTime msgCreateTime;

        @Override
        public String toString() {
            return "ChatMessage{" +
                    "sid='" + sid + "'" +
                    ", agent='" + agent + "'" +
                    ", customer='" + customer + "'" +
                    ", direction=" + direction +
                    ", msgCreateTime=" + msgCreateTime +
                    ", content='" + content + "'" +
                    '}';
        }
    }

    public static void main(String[] args) {
        try {
            File inputFile = new File(INPUT_FILE_PATH);
            if (!inputFile.exists()) {
                System.err.println("Error: Input file not found at " + INPUT_FILE_PATH);
                return;
            }

            Map<String, List<ChatMessage>> conversations = readAndGroupConversations(inputFile);
            writeConversationsToTxt(conversations, OUTPUT_FILE_PATH);

            System.out.println("Conversion successful! Output written to " + OUTPUT_FILE_PATH);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Map<String, List<ChatMessage>> readAndGroupConversations(File inputFile) throws IOException {
        Map<String, List<ChatMessage>> conversations = new LinkedHashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try (FileInputStream fis = new FileInputStream(inputFile);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            DataFormatter dataFormatter = new DataFormatter();

            // Find header indices
            Row headerRow = sheet.getRow(0);
            int sidIndex = -1, agentIndex = -1, customerIndex = -1, contentIndex = -1, directionIndex = -1, timeIndex = -1;
            for (Cell cell : headerRow) {
                String header = cell.getStringCellValue();
                switch (header) {
                    case "sid": sidIndex = cell.getColumnIndex(); break;
                    case "agent": agentIndex = cell.getColumnIndex(); break;
                    case "customer": customerIndex = cell.getColumnIndex(); break;
                    case "content": contentIndex = cell.getColumnIndex(); break;
                    case "direction": directionIndex = cell.getColumnIndex(); break;
                    case "msg_create_time": timeIndex = cell.getColumnIndex(); break;
                }
            }

            if (sidIndex == -1) {
                 throw new IOException("Header 'sid' not found in the Excel file.");
            }


            // Read data rows
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                ChatMessage msg = new ChatMessage();
                msg.sid = dataFormatter.formatCellValue(row.getCell(sidIndex));
                msg.agent = dataFormatter.formatCellValue(row.getCell(agentIndex));
                msg.customer = dataFormatter.formatCellValue(row.getCell(customerIndex));
                msg.content = dataFormatter.formatCellValue(row.getCell(contentIndex));
                
                Cell directionCell = row.getCell(directionIndex);
                if (directionCell == null || directionCell.getCellType() == CellType.BLANK) {
                    System.err.println("Warning: Skipping row " + (i + 1) + " due to empty direction cell.");
                    continue;
                }
                msg.direction = (int) directionCell.getNumericCellValue();

                // Handle date/time parsing
                Cell timeCell = row.getCell(timeIndex);
                 if (timeCell == null || timeCell.getCellType() == CellType.BLANK) {
                    System.err.println("Warning: Skipping row " + (i + 1) + " due to empty time cell.");
                    continue;
                }
                if (timeCell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(timeCell)) {
                    msg.msgCreateTime = timeCell.getLocalDateTimeCellValue();
                } else {
                    msg.msgCreateTime = LocalDateTime.parse(dataFormatter.formatCellValue(timeCell), formatter);
                }


                conversations.computeIfAbsent(msg.sid, k -> new ArrayList<>()).add(msg);
            }
        }

        // Sort messages within each conversation
        for (List<ChatMessage> messages : conversations.values()) {
            messages.sort(Comparator.comparing(m -> m.msgCreateTime));
        }

        return conversations;
    }

    private static void writeConversationsToTxt(Map<String, List<ChatMessage>> conversations, String outputPath) throws IOException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputPath))) {
            for (Map.Entry<String, List<ChatMessage>> entry : conversations.entrySet()) {
                List<ChatMessage> messages = entry.getValue();
                if (messages.isEmpty()) {
                    continue;
                }

                writer.write("/*****************以下为一通会话************************************/");
                writer.newLine();

                for (ChatMessage msg : messages) {
                    String speaker = msg.direction == 1 ? msg.customer : msg.agent;
                    String time = msg.msgCreateTime.format(formatter);
                    // Sanitize content by removing newlines
                    String content = msg.content.replace("\n", " ").replace("\r", " ");
                    writer.write(String.format("%s %s", speaker, time));
                    writer.newLine();
                    writer.write(content);
                    writer.newLine();
                }

                LocalDateTime endTime = messages.get(messages.size() - 1).msgCreateTime;
                writer.write("/*****************会话结束_时间:" + endTime.format(formatter) + "******************************/");
                writer.newLine();
                writer.newLine();
            }
        }
    }
}