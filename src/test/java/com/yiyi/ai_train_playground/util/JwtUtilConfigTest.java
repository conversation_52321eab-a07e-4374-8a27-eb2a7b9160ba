package com.yiyi.ai_train_playground.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT配置测试类
 * 测试不同配置文件下的JWT过期时间设置
 */
@Slf4j
//@SpringBootTest
public class JwtUtilConfigTest {

    private JwtUtil jwtUtil;

    @BeforeEach
    public void setUp() {
        jwtUtil = new JwtUtil();

        // 模拟application-company.yml中的配置
        ReflectionTestUtils.setField(jwtUtil, "secret", "gJj0XQdx9yhNY3sQpOPcHLYV7rWmb8A4KtIZ2EMi6onFk1qlaDSeuTvRfzG5wU");
        ReflectionTestUtils.setField(jwtUtil, "expiration", 86400000L); // 24小时
        ReflectionTestUtils.setField(jwtUtil, "rememberMeExpiration", 2592000000L); // 30天
    }

    @Test
    public void testJwtConfigWithCompanyProfile() {
        log.info("=== 测试application-company.yml配置下的JWT过期时间 ===");

        // 获取JWT配置信息
        Map<String, Object> configInfo = jwtUtil.getJwtConfigInfo();
        
        // 打印所有配置信息
        log.info("JWT配置信息：");
        configInfo.forEach((key, value) -> log.info("  {}: {}", key, value));

        // 验证基本过期时间（非rememberMe）
        Long expiration = (Long) configInfo.get("expiration");
        Long expirationInHours = (Long) configInfo.get("expirationInHours");
        
        // 验证rememberMe过期时间
        Long rememberMeExpiration = (Long) configInfo.get("rememberMeExpiration");
        Long rememberMeExpirationInDays = (Long) configInfo.get("rememberMeExpirationInDays");

        // 断言验证
        assertEquals(86400000L, expiration, "基本过期时间应该是86400000毫秒（24小时）");
        assertEquals(24L, expirationInHours, "基本过期时间应该是24小时");
        assertEquals(2592000000L, rememberMeExpiration, "RememberMe过期时间应该是2592000000毫秒（30天）");
        assertEquals(30L, rememberMeExpirationInDays, "RememberMe过期时间应该是30天");

        log.info("✅ JWT配置验证通过");
        log.info("  - 基本过期时间: {} 毫秒 = {} 小时", expiration, expirationInHours);
        log.info("  - RememberMe过期时间: {} 毫秒 = {} 天", rememberMeExpiration, rememberMeExpirationInDays);
    }

    @Test
    public void testGenerateTokenWithRememberMeFalse() {
        log.info("=== 测试generateToken方法（rememberMe=false）===");

        // 生成JWT token（rememberMe=false）
        String token = jwtUtil.generateToken(1L, "testUser", 1L, false);
        
        assertNotNull(token, "生成的token不应为null");
        assertTrue(token.length() > 0, "生成的token不应为空");
        
        // 验证token有效性
        assertTrue(jwtUtil.validateToken(token), "生成的token应该是有效的");
        
        // 从token中提取信息
        String username = jwtUtil.getUsernameFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        
        assertEquals("testUser", username, "用户名应该匹配");
        assertEquals(1L, userId, "用户ID应该匹配");
        assertEquals(1L, teamId, "团队ID应该匹配");
        
        log.info("✅ rememberMe=false的token生成和验证通过");
        log.info("  - Token长度: {}", token.length());
        log.info("  - 用户名: {}", username);
        log.info("  - 用户ID: {}", userId);
        log.info("  - 团队ID: {}", teamId);
    }

    @Test
    public void testGenerateTokenWithRememberMeTrue() {
        log.info("=== 测试generateToken方法（rememberMe=true）===");

        // 生成JWT token（rememberMe=true）
        String token = jwtUtil.generateToken(2L, "testUserRemember", 2L, true);
        
        assertNotNull(token, "生成的token不应为null");
        assertTrue(token.length() > 0, "生成的token不应为空");
        
        // 验证token有效性
        assertTrue(jwtUtil.validateToken(token), "生成的token应该是有效的");
        
        // 从token中提取信息
        String username = jwtUtil.getUsernameFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        
        assertEquals("testUserRemember", username, "用户名应该匹配");
        assertEquals(2L, userId, "用户ID应该匹配");
        assertEquals(2L, teamId, "团队ID应该匹配");
        
        log.info("✅ rememberMe=true的token生成和验证通过");
        log.info("  - Token长度: {}", token.length());
        log.info("  - 用户名: {}", username);
        log.info("  - 用户ID: {}", userId);
        log.info("  - 团队ID: {}", teamId);
    }

    @Test
    public void testTokenExpirationLogic() {
        log.info("=== 测试Token过期时间逻辑 ===");

        // 获取配置信息
        Map<String, Object> configInfo = jwtUtil.getJwtConfigInfo();
        Long normalExpiration = (Long) configInfo.get("expiration");
        Long rememberMeExpiration = (Long) configInfo.get("rememberMeExpiration");

        log.info("配置的过期时间：");
        log.info("  - 普通token: {} 毫秒 = {} 小时", normalExpiration, normalExpiration / (1000 * 60 * 60));
        log.info("  - RememberMe token: {} 毫秒 = {} 天", rememberMeExpiration, rememberMeExpiration / (1000 * 60 * 60 * 24));

        // 验证过期时间计算
        assertEquals(24L, normalExpiration / (1000 * 60 * 60), "普通token应该是24小时");
        assertEquals(30L, rememberMeExpiration / (1000 * 60 * 60 * 24), "RememberMe token应该是30天");

        log.info("✅ Token过期时间逻辑验证通过");
    }

    @Test
    public void testJwtExpirationFromToken() {
        log.info("=== 测试从JWT Token中解析有效期 ===");

        // 定义一个JWT token变量（示例token）
//        String jwtToken = jwtUtil.generateToken(100L, "testUserExpiration", 10L, false);
        //以下是1个月的
//        String jwtToken = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjMsInVzZXJJZCI6MTExLCJzdWIiOiJjc2FkbWluIiwiaWF0IjoxNzU2OTc3NTA5LCJleHAiOjE3NTk1Njk1MDl9.ap_Wy_lp-NZv0Myhykl2Ah4yw79hqkRhEKV7egYJBAo";

        //设计的是5小时，结果变成24小时
        String jwtToken = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjMsInVzZXJJZCI6LTgyOTc0NSwic3ViIjoiYW5vX2Q3MzNiYjViLWEwYTgtNDk0NS04Mjk0LTk2ZDRiNjAyZWZkNSIsImlhdCI6MTc1Njk5NDI3NCwiZXhwIjoxNzU3MDgwNjc0fQ.KC7NvMZFdNdY75V4c2YOiU_W5wP-XCR6UqhKaJzo_6U";
//        String jwtToken = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjMsInVzZXJJZCI6LTEwNzE5Mywic3ViIjoiYW5vXzc1N2VlZmE0LWI2ZDMtNDhlNi1hNmVmLTcyY2UxMzllYzcwOSIsImlhdCI6MTc1NzAyNDkyNSwiZXhwIjoxNzU3MDQyOTI1fQ.U-F7RF-H7bx9SbszaZXgbH5DSH1oMThEwwBfLuwPwRQ";

        log.info("生成的JWT Token: {}", jwtToken);

        try {
            // 提取过期时间
            Date expirationDate = jwtUtil.extractExpiration(jwtToken);
            Date issuedAt = new Date(); // 当前时间作为发布时间的近似值
            
            // 计算有效期（毫秒）
            long expirationTimeMs = expirationDate.getTime();
            long currentTimeMs = System.currentTimeMillis();
            long remainingTimeMs = expirationTimeMs - currentTimeMs;
            
            // 转换为不同的时间单位
            long remainingSeconds = remainingTimeMs / 1000;
            long remainingMinutes = remainingTimeMs / (1000 * 60);
            long remainingHours = remainingTimeMs / (1000 * 60 * 60);
            long remainingDays = remainingTimeMs / (1000 * 60 * 60 * 24);

            log.info("JWT Token有效期信息：");
            log.info("  - 过期时间: {}", expirationDate);
            log.info("  - 过期时间戳: {}", expirationTimeMs);
            log.info("  - 当前时间戳: {}", currentTimeMs);
            log.info("  - 剩余有效期: {} 毫秒", remainingTimeMs);
            log.info("  - 剩余有效期: {} 秒", remainingSeconds);
            log.info("  - 剩余有效期: {} 分钟", remainingMinutes);
            log.info("  - 剩余有效期: {} 小时", remainingHours);
            log.info("  - 剩余有效期: {} 天", remainingDays);

            // 验证token是否有效
            boolean isValid = jwtUtil.validateToken(jwtToken);
            log.info("  - Token当前状态: {}", isValid ? "有效" : "已过期");

            // 验证剩余时间应该是正数（token还未过期）
            assertTrue(remainingTimeMs > 0, "Token应该还没有过期");
            assertTrue(isValid, "Token应该是有效的");

            // 从token中提取其他信息
            String username = jwtUtil.getUsernameFromToken(jwtToken);
            Long userId = jwtUtil.getUserIdFromToken(jwtToken);
            Long teamId = jwtUtil.getTeamIdFromToken(jwtToken);

            log.info("Token中的用户信息：");
            log.info("  - 用户名: {}", username);
            log.info("  - 用户ID: {}", userId);
            log.info("  - 团队ID: {}", teamId);

            // 验证提取的信息
            assertEquals("testUserExpiration", username);
            assertEquals(100L, userId);
            assertEquals(10L, teamId);

            log.info("✅ JWT Token有效期解析和验证通过");

        } catch (Exception e) {
            log.error("解析JWT Token过期时间失败", e);
            fail("解析JWT Token应该成功: " + e.getMessage());
        }
    }
}
