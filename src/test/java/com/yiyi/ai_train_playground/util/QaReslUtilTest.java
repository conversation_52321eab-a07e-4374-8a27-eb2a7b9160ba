package com.yiyi.ai_train_playground.util;

import com.yiyi.ai_train_playground.dto.task.QaResolveResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * QaReslUtil工具类测试
 */
@Slf4j
@SpringBootTest
public class QaReslUtilTest {

    @Test
    public void testParseResolve_Success() {
        // 准备测试数据
        String resolveJson = "{\"resp\":{\"msgId\":\"c9ec7269-55d9-4e99-b36e-b4c856be9509\",\"results\":[\"{\\\"分析\\\":\\\"完全不匹配，客服回答与正确答案无关\\\",\\\"得分\\\":0,\\\"扣分点\\\":[\\\"回答与正确答案无关，未提及防晒到货状态、处理方式及对功效的影响\\\"],\\\"正确答案\\\":\\\"应明确说明'亲爱哒~防晒到货是水油分离的状态哈~ 到货用力摇匀使水油融合即可哈~这个是正常现象哈~并不影响产品的功效哈~'\\\",\\\"下一题\\\":\\\"您好，我现在处于孕期，想了解下这款防晒产品孕妇能不能使用呢？\\\"}\"]}}";

        // 调用解析方法
        QaResolveResultDTO result = QaReslUtil.parseResolve(resolveJson);

        // 验证结果
        assertNotNull(result);
        assertEquals("完全不匹配，客服回答与正确答案无关", result.getAnalysis());
        assertEquals(0, result.getScore());
        assertNotNull(result.getDeductionPoints());
        assertEquals(1, result.getDeductionPoints().size());
        assertEquals("回答与正确答案无关，未提及防晒到货状态、处理方式及对功效的影响", result.getDeductionPoints().get(0));
        assertEquals("应明确说明'亲爱哒~防晒到货是水油分离的状态哈~ 到货用力摇匀使水油融合即可哈~这个是正常现象哈~并不影响产品的功效哈~'", result.getCorrectAnswer());
        assertEquals("您好，我现在处于孕期，想了解下这款防晒产品孕妇能不能使用呢？", result.getNextQuestion());

        log.info("解析结果验证成功：{}", result);
    }

    @Test
    public void testExtractNextQuestion_Success() {
        // 准备测试数据
        String resolveJson = "{\"resp\":{\"msgId\":\"c9ec7269-55d9-4e99-b36e-b4c856be9509\",\"results\":[\"{\\\"分析\\\":\\\"完全不匹配，客服回答与正确答案无关\\\",\\\"得分\\\":0,\\\"扣分点\\\":[\\\"回答与正确答案无关，未提及防晒到货状态、处理方式及对功效的影响\\\"],\\\"正确答案\\\":\\\"应明确说明'亲爱哒~防晒到货是水油分离的状态哈~ 到货用力摇匀使水油融合即可哈~这个是正常现象哈~并不影响产品的功效哈~'\\\",\\\"下一题\\\":\\\"您好，我现在处于孕期，想了解下这款防晒产品孕妇能不能使用呢？\\\"}\"]}}";

        // 调用提取下一题方法
        String nextQuestion = QaReslUtil.extractNextQuestion(resolveJson);

        // 验证结果
        assertEquals("您好，我现在处于孕期，想了解下这款防晒产品孕妇能不能使用呢？", nextQuestion);

        log.info("下一题提取成功：{}", nextQuestion);
    }

    @Test
    public void testParseResolve_InvalidJson() {
        // 准备无效的JSON数据
        String invalidJson = "invalid json string";

        // 调用解析方法
        QaResolveResultDTO result = QaReslUtil.parseResolve(invalidJson);

        // 验证结果：应该返回空对象而不是抛出异常
        assertNotNull(result);
        assertNull(result.getAnalysis());
        assertNull(result.getScore());
        assertNull(result.getDeductionPoints());
        assertNull(result.getCorrectAnswer());
        assertNull(result.getNextQuestion());

        log.info("无效JSON处理正常：返回空对象");
    }

    @Test
    public void testExtractNextQuestion_InvalidJson() {
        // 准备无效的JSON数据
        String invalidJson = "invalid json string";

        // 调用提取下一题方法
        String result = QaReslUtil.extractNextQuestion(invalidJson);

        // 验证结果：应该返回原字符串
        assertEquals(invalidJson, result);

        log.info("无效JSON处理正常：返回原字符串");
    }
}