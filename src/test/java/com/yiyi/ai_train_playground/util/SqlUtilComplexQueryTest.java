package com.yiyi.ai_train_playground.util;

import com.yiyi.ai_train_playground.interceptor.tenant.TenantCondition;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SqlUtil复杂SQL处理测试
 * 验证JSQLParser超时优化功能
 */
public class SqlUtilComplexQueryTest {

    @Test
    @DisplayName("测试复杂SQL自动跳过处理")
    public void testComplexQuerySkipped() {
        // 构造一个复杂的SQL，包含多个复杂特征
        String complexSql = """
            SELECT 
                trt.id,
                trt.task_name AS taskName,
                CASE trt.task_mode
                    WHEN 0 THEN '原版案例'
                    WHEN 1 THEN 'AI智训'
                    WHEN 2 THEN 'AI智训玩法'
                    ELSE '未知'
                END AS taskModeName,
                CASE trt.task_type
                    WHEN 0 THEN '商品知识训练'
                    WHEN 1 THEN '实战进阶任务'
                    WHEN 2 THEN '高频话术训练'
                    ELSE '未知'
                END AS taskTypeName,
                CASE
                    WHEN trt.amt_to_be_learned = 0 THEN '0%'
                    ELSE CONCAT(ROUND(((
                        SELECT COUNT(*) 
                        FROM train_task_conv_kb_dtl 
                        WHERE task_id = trt.id 
                        AND learning_status = 'learned'
                    ) * 100.0 / trt.amt_to_be_learned), 0), '%')
                END AS learningProgressPercent
            FROM train_reception_task trt
            LEFT JOIN train_script ts ON trt.script_id = ts.id
            LEFT JOIN train_qa_import_main tqim ON trt.qa_main_id = tqim.id
            LEFT JOIN train_chatroom_staff tcs ON trt.id = tcs.task_id
            LEFT JOIN train_staff staff ON tcs.staff_id = staff.id
            WHERE trt.team_id = 1
            AND trt.task_name LIKE '%测试%'
            AND trt.learning_status = 'learning'
            AND trt.create_time >= '2025-01-01'
            AND trt.create_time <= '2025-12-31'
            AND staff.status = 1
            AND staff.display_name IS NOT NULL
            AND (trt.task_mode = 0 OR trt.task_mode = 1 OR trt.task_mode = 2)
            AND (trt.task_type = 0 OR trt.task_type = 1)
            AND (tqim.qa_im_name IS NOT NULL OR tqim.qa_im_name != '')
            UNION ALL
            SELECT 
                trt2.id,
                trt2.task_name AS taskName,
                'historical' AS taskModeName,
                'archived' AS taskTypeName,
                '100%' AS learningProgressPercent
            FROM train_reception_task_history trt2
            WHERE trt2.team_id = 1
            ORDER BY taskName, id
            """;

        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);
        condition.setCreator("testuser");

        // 调用SqlUtil处理复杂SQL
        String result = SqlUtil.addTenantCondition(complexSql, condition);

        // 验证：复杂SQL应该被自动跳过，返回原始SQL
        assertEquals(complexSql, result, "复杂SQL应该被跳过处理，返回原始SQL");
    }

    @Test
    @DisplayName("测试超长SQL自动跳过处理")
    public void testVeryLongQuerySkipped() {
        // 构造一个超长SQL（超过5000字符）
        StringBuilder longSqlBuilder = new StringBuilder("SELECT * FROM train_staff WHERE ");
        
        // 添加大量条件使SQL超过5000字符
        for (int i = 0; i < 200; i++) {
            if (i > 0) {
                longSqlBuilder.append(" OR ");
            }
            longSqlBuilder.append("(id = ").append(i).append(" AND status = 1 AND display_name LIKE '%user").append(i).append("%')");
        }
        
        String longSql = longSqlBuilder.toString();
        assertTrue(longSql.length() > 5000, "SQL长度应该超过5000字符");

        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);

        // 调用SqlUtil处理超长SQL
        String result = SqlUtil.addTenantCondition(longSql, condition);

        // 验证：超长SQL应该被跳过，返回原始SQL
        assertEquals(longSql, result, "超长SQL应该被跳过处理，返回原始SQL");
    }

    @Test
    @DisplayName("测试普通SQL正常处理")
    public void testSimpleQueryProcessed() {
        String simpleSql = "SELECT * FROM train_staff WHERE status = 1";

        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);

        // 调用SqlUtil处理简单SQL
        String result = SqlUtil.addTenantCondition(simpleSql, condition);

        // 验证：简单SQL应该被正常处理，添加租户条件
        assertNotEquals(simpleSql, result, "简单SQL应该被处理，添加租户条件");
        assertTrue(result.contains("team_id = 123"), "结果应该包含租户条件");
    }

    @Test
    @DisplayName("测试INSERT语句跳过处理")
    public void testInsertStatementSkipped() {
        String insertSql = "INSERT INTO train_staff (name, status, team_id) VALUES ('test', 1, 123)";

        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);

        // 调用SqlUtil处理INSERT语句
        String result = SqlUtil.addTenantCondition(insertSql, condition);

        // 验证：INSERT语句应该被跳过
        assertEquals(insertSql, result, "INSERT语句应该被跳过处理");
    }

    @Test
    @DisplayName("测试DDL语句跳过处理")
    public void testDDLStatementSkipped() {
        String ddlSql = "CREATE TABLE test_table (id BIGINT PRIMARY KEY, name VARCHAR(255))";

        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);

        // 调用SqlUtil处理DDL语句
        String result = SqlUtil.addTenantCondition(ddlSql, condition);

        // 验证：DDL语句应该被跳过
        assertEquals(ddlSql, result, "DDL语句应该被跳过处理");
    }

    @Test
    @DisplayName("测试异常SQL处理")
    public void testMalformedSqlHandling() {
        String malformedSql = "SELECTFROM train_staff WHEREID="; // 故意写错的SQL

        TenantCondition condition = new TenantCondition();
        condition.setTeamId(123L);

        // 调用SqlUtil处理异常SQL
        String result = SqlUtil.addTenantCondition(malformedSql, condition);

        // 验证：异常SQL应该返回原始SQL，不抛出异常
        assertEquals(malformedSql, result, "异常SQL应该返回原始SQL，不影响业务执行");
    }
}