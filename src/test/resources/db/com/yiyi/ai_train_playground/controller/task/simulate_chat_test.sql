-- 模拟聊天WebSocket控制器测试数据库脚本

-- 1. 创建员工表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_staff` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint COMMENT '用户ID（关联用户表）',
  `username` varchar(50) NOT NULL COMMENT '用户名（登录名）',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希值',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `is_locked` tinyint(1) DEFAULT '0' COMMENT '是否锁定：0-未锁定，1-已锁定',
  `failed_attempts` int DEFAULT '0' COMMENT '登录失败次数',
  `lock_time` datetime COMMENT '账户锁定时间',
  `last_login_time` datetime COMMENT '最后登录时间',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username_team` (`username`, `team_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 2. 创建接待聊天室表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_reception_chatroom` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reception_skin` int DEFAULT '0' COMMENT '接待皮肤：0-干牛，1-咩咩，2-抖音',
  `scene_mode` int DEFAULT '0' COMMENT '场景模式：0-萌新友好，1-压力考核，2-自定义',
  `quick_phrases_id` bigint COMMENT '快捷短语ID',
  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
  `timer_display` tinyint(1) DEFAULT '0' COMMENT '读秒：0-不显示，1-显示',
  `entry_freq_min` int DEFAULT '1' COMMENT '进线频率下限（分钟）',
  `entry_freq_max` int DEFAULT '5' COMMENT '进线频率上限（分钟）',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待聊天室表';

-- 3. 创建聊天室员工关联表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_chatroom_staff` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rece_chatroom_id` bigint NOT NULL COMMENT '接待聊天室ID',
  `staff_id` bigint NOT NULL COMMENT '员工ID',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_rece_chatroom_id` (`rece_chatroom_id`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天室员工关联表';

-- 4. 创建剧本表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_script` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '剧本名称',
  `buyer_requirement` text COMMENT '买家需求及背景',
  `intent_id` bigint COMMENT '意图ID',
  `generation_type` int NOT NULL DEFAULT '0' COMMENT '生成方式：0-商品知识训练；1-实战能力进阶；2-自定义内容',
  `group_id` bigint COMMENT '分组ID',
  `evaluation_id` bigint COMMENT '评价方案ID',
  `order_priority` int COMMENT '订单优先级',
  `order_remark` varchar(500) COMMENT '订单备注',
  `retry_flow_node_counts` int COMMENT '流程节点重试次数',
  `retry_buyer_requirement_counts` int COMMENT '买家需求重试次数',
  `simulation_tool` varchar(100) COMMENT '模拟工具',
  `prod_type` int DEFAULT '0' COMMENT '商品类型：0-自主导入，1-JD，2-TB，3-DY，4-PDD，5-XHS，6-KS',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_intent_id` (`intent_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='剧本表';

-- 5. 创建接待任务表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_reception_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_name` varchar(255) NOT NULL COMMENT '接待任务名称',
  `task_description` text COMMENT '任务描述',
  `task_mode` int NOT NULL DEFAULT '0' COMMENT '接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法',
  `task_type` int NOT NULL DEFAULT '0' COMMENT '接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务',
  `script_id` bigint COMMENT '剧本ID，来自train_script表的主键，1对1映射',
  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
  `question_interval_type` int DEFAULT '0' COMMENT '顾客提问时间间隔：0-随机，1-固定',
  `question_interval_seconds` int DEFAULT '3' COMMENT '提问间隔秒数',
  `training_limit_enabled` tinyint(1) DEFAULT '0' COMMENT '训练次数限制：0-关，1-开',
  `task_purpose_tag` int DEFAULT '0' COMMENT '任务标签：0-训练，1-考核，2-面试，3-其他',
  `judge_type` int DEFAULT '0' COMMENT '打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待任务表';

-- 6. 创建聊天室任务关联表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_chatroom_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `chatroom_id` bigint NOT NULL COMMENT '聊天室ID，关联train_reception_chatroom表',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID，关联train_reception_task表的主键',
  `training_recycle_cnt` int NOT NULL DEFAULT '0' COMMENT '此任务循环次数',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_chatroom_id` (`chatroom_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天室任务关联表';

-- 7. 创建意图表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_intent` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '意图名称',
  `parent_id` bigint COMMENT '父意图ID',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='意图表';

-- 插入测试数据
INSERT IGNORE INTO `train_staff` (`id`, `username`, `password_hash`, `display_name`, `team_id`, `creator`, `updater`) 
VALUES (1, 'test_staff', '$2a$10$test_hash', '测试员工', 1, 'system', 'system');

INSERT IGNORE INTO `train_reception_chatroom` (`id`, `entry_freq_min`, `entry_freq_max`, `team_id`, `creator`, `updater`) 
VALUES (1, 1, 5, 1, 'system', 'system');

INSERT IGNORE INTO `train_chatroom_staff` (`id`, `rece_chatroom_id`, `staff_id`, `team_id`, `creator`, `updater`) 
VALUES (1, 1, 1, 1, 'system', 'system');

INSERT IGNORE INTO `train_intent` (`id`, `name`, `parent_id`, `team_id`, `creator`, `updater`) 
VALUES (1, '测试意图', NULL, 1, 'system', 'system');

INSERT IGNORE INTO `train_script` (`id`, `name`, `buyer_requirement`, `intent_id`, `team_id`, `creator`, `updater`) 
VALUES (1, '测试剧本', '测试买家需求', 1, 1, 'system', 'system');

INSERT IGNORE INTO `train_reception_task` (`id`, `task_name`, `script_id`, `team_id`, `creator`, `updater`) 
VALUES (1, '测试任务', 1, 1, 'system', 'system');

INSERT IGNORE INTO `train_chatroom_task` (`id`, `chatroom_id`, `task_id`, `training_recycle_cnt`, `team_id`, `creator`, `updater`) 
VALUES (1, 1, 1, 2, 1, 'system', 'system');
