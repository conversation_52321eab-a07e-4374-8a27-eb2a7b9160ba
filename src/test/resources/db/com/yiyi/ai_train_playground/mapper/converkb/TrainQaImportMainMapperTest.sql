-- 问答导入主表测试DDL
CREATE TABLE IF NOT EXISTS `train_qa_import_main` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `qa_im_name` varchar(200) NOT NULL COMMENT '知识库名称',
  `qa_im_desc` varchar(200) NOT NULL COMMENT '知识库描述',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答导入主表';

-- 清理测试数据
DELETE FROM train_qa_import_main WHERE team_id IN (1, 2);
