-- 问答随机表测试DDL
CREATE TABLE IF NOT EXISTS `train_qa_rdm` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uuid` varchar(64) NOT NULL COMMENT 'UUID',
  `question` varchar(500) NOT NULL COMMENT '问题',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '答案（支持表情存储）',
  `actual_question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '实际问题',
  `actual_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '实际答案',
  `resolve` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '解决方案',
  `ques_no` varchar(20) NOT NULL COMMENT '问题编号',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid_team_id` (`uuid`, `team_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_ques_no` (`ques_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答随机表';
