-- 文件导入服务测试DDL脚本

-- 创建测试用的会话知识库模板主表数据
INSERT INTO train_kb_tpl_main (
    id, name, tokens, file_type, learn_status, team_id, 
    create_time, update_time, creator, updater, version
) VALUES (
    1, '测试模板', 0, 'txt', 'un_learn', 1,
    NOW(), NOW(), 'test-user', 'test-user', 0
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    tokens = VALUES(tokens),
    file_type = VALUES(file_type),
    learn_status = VALUES(learn_status),
    update_time = NOW(),
    updater = VALUES(updater);

-- 清理可能存在的测试数据
DELETE FROM train_kb_tpl_pre WHERE team_id = 1 AND tpl_id = 1;

-- 插入一些测试用的预处理数据
INSERT INTO train_kb_tpl_pre (
    tpl_id, content, `index`, learn_status, team_id, 
    create_time, update_time, creator, updater, version
) VALUES 
(1, '以下为一通会话\n客服：您好，有什么可以帮助您的吗？\n客户：我想了解产品功能\n会话结束_2024-01-01 10:00:00', 
 0, 'un_learn', 1, NOW(), NOW(), 'test-user', 'test-user', 0),
(1, '以下为一通会话\n客服：欢迎咨询\n客户：我有问题需要解决\n会话结束_2024-01-01 11:00:00', 
 1, 'un_learn', 1, NOW(), NOW(), 'test-user', 'test-user', 0),
(1, '以下为一通会话\n客服：您好\n客户：请问如何使用这个功能\n会话结束_2024-01-01 12:00:00', 
 2, 'learning', 1, NOW(), NOW(), 'test-user', 'test-user', 0);

-- 创建其他团队的测试数据（用于验证数据隔离）
INSERT INTO train_kb_tpl_main (
    id, name, tokens, file_type, learn_status, team_id, 
    create_time, update_time, creator, updater, version
) VALUES (
    2, '其他团队模板', 0, 'csv', 'un_learn', 2,
    NOW(), NOW(), 'other-user', 'other-user', 0
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    tokens = VALUES(tokens),
    file_type = VALUES(file_type),
    learn_status = VALUES(learn_status),
    update_time = NOW(),
    updater = VALUES(updater);

INSERT INTO train_kb_tpl_pre (
    tpl_id, content, `index`, learn_status, team_id, 
    create_time, update_time, creator, updater, version
) VALUES 
(2, '其他团队的会话内容', 0, 'un_learn', 2, NOW(), NOW(), 'other-user', 'other-user', 0);
