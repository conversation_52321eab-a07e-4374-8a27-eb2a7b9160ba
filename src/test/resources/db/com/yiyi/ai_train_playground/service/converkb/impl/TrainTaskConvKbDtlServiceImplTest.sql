-- TrainTaskConvKbDtlServiceImplTest 测试数据库脚本

-- 创建任务会话明细表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_task_conv_kb_dtl` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `kb_dtl_id` bigint NOT NULL COMMENT 'train_kb_tpl_detail的主键ID',
  `learning_status` varchar(32) NOT NULL DEFAULT 'un_learn' COMMENT '学习状态',
  `f1st_raw_chatlog` longtext COMMENT '初始的聊天记录',
  `f2nd_agg_sys_prompt` longtext COMMENT '第二步聚合系统提示词',
  `final_chat_log` longtext COMMENT '最终生成的聊天记录',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_kb_dtl_id` (`kb_dtl_id`),
  KEY `idx_learning_status` (`learning_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务会话明细表';

-- 清理测试数据（可选）
-- DELETE FROM train_task_conv_kb_dtl WHERE team_id = 1 AND task_id = 1;

-- 插入测试数据
INSERT IGNORE INTO `train_task_conv_kb_dtl` 
(`task_id`, `kb_dtl_id`, `learning_status`, `f1st_raw_chatlog`, `final_chat_log`, `team_id`, `creator`, `updater`) 
VALUES 
(1, 1, 'un_learn', 
 '客户：你好，有什么推荐的产品吗？\n客服：您好，欢迎咨询我们的产品！', 
 '客户：你好，有什么优惠吗？\n客服：您好，我们目前有新用户专享优惠，iPhone 15 Pro现在有特价活动，原价9999元，现在只需8999元，还送无线充电器一个。您感兴趣的话我可以为您详细介绍一下产品特性。', 
 1, 'test_user', 'test_user'),

(1, 2, 'learning', 
 '客户：这个手机的电池续航怎么样？\n客服：iPhone 15 Pro的电池续航非常出色。', 
 '客户：这个手机的电池续航怎么样？\n客服：iPhone 15 Pro采用了最新的A17 Pro芯片，功耗控制非常优秀，正常使用可以支持一整天。视频播放可达23小时，音频播放可达75小时。而且支持快充和无线充电，15分钟可以充电50%。', 
 1, 'test_user', 'test_user'),

(1, 3, 'learned', 
 '客户：有什么颜色可以选择？\n客服：我们有多种颜色供您选择。', 
 '客户：有什么颜色可以选择？\n客服：iPhone 15 Pro有四种精美颜色：原色钛金属、蓝色钛金属、白色钛金属和黑色钛金属。每种颜色都采用了航空级钛金属材质，既轻盈又坚固。其中原色钛金属是今年的新色，非常受欢迎。您比较喜欢哪种颜色呢？', 
 1, 'test_user', 'test_user'),

(1, 4, 'un_learn', 
 '客户：这个价格包含什么服务？\n客服：我们提供全面的售后服务。', 
 '客户：这个价格包含什么服务？\n客服：这个价格包含：1年官方保修、免费技术支持、7天无理由退换、全国联保服务。另外我们还提供免费贴膜、免费设置服务，以及专属客服一对一跟进。如果您需要延保服务，我们也有相应的套餐可以选择。', 
 1, 'test_user', 'test_user'),

(1, 5, 'learning', 
 '客户：什么时候能发货？\n客服：我们会尽快为您安排发货。', 
 '客户：什么时候能发货？\n客服：现货充足，您下单后我们会在24小时内发货。选择顺丰快递的话，一般1-3个工作日就能到达。如果您在北京、上海、广州、深圳等一线城市，还可以选择当日达服务，下午3点前下单，当天就能收到。', 
 1, 'test_user', 'test_user');

-- 为其他任务ID插入一些测试数据
INSERT IGNORE INTO `train_task_conv_kb_dtl` 
(`task_id`, `kb_dtl_id`, `learning_status`, `f1st_raw_chatlog`, `final_chat_log`, `team_id`, `creator`, `updater`) 
VALUES 
(2, 6, 'un_learn', 
 '客户：你们有iPad吗？\n客服：有的，我们有最新的iPad产品。', 
 '客户：你们有iPad吗？\n客服：有的，我们有最新的iPad Air和iPad Pro系列。iPad Air 11英寸搭载M2芯片，性能强劲，适合日常办公和娱乐。iPad Pro系列有11英寸和12.9英寸两个尺寸，搭载M4芯片，支持Apple Pencil Pro，是专业创作的首选。您主要用来做什么呢？', 
 1, 'test_user', 'test_user'),

(3, 7, 'learned', 
 '客户：MacBook有什么型号？\n客服：我们有多款MacBook供您选择。', 
 '客户：MacBook有什么型号？\n客服：目前我们有MacBook Air和MacBook Pro两个系列。MacBook Air有13英寸和15英寸，搭载M3芯片，轻薄便携，续航可达18小时。MacBook Pro有14英寸和16英寸，搭载M3 Pro或M3 Max芯片，性能更强劲，适合专业用户。您是学生还是专业用户呢？', 
 1, 'test_user', 'test_user');
