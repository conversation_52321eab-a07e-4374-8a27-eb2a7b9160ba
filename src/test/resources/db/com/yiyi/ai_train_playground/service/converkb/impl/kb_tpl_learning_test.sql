-- 知识库模板学习服务测试数据库脚本

-- 1. 创建知识库模板主表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_kb_tpl_main` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '会话知识库模板名称',
  `tokens` bigint NOT NULL DEFAULT '0' COMMENT '本次会话累计消耗token数',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `learn_status` varchar(50) DEFAULT 'un_learn' COMMENT '学习状态',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_learn_status` (`learn_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话知识库模板主表';

-- 2. 创建知识库模板预处理表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_kb_tpl_pre` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tpl_id` bigint NOT NULL COMMENT '会话知识库模板主表ID',
  `content` longtext NOT NULL COMMENT '读取的块内容',
  `index` int NOT NULL COMMENT '读取的顺序',
  `learn_status` varchar(50) NOT NULL DEFAULT 'un_learn' COMMENT '学习状态',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_tpl_id` (`tpl_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_learn_status` (`learn_status`),
  KEY `idx_tpl_id_index` (`tpl_id`, `index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话知识库模板预处理表';

-- 3. 创建知识库模板详情表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_kb_tpl_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tpl_id` bigint NOT NULL COMMENT '会话知识库模板主表ID',
  `content` longtext NOT NULL COMMENT '对话内容',
  `tpl_type` varchar(50) NOT NULL COMMENT '模板类型：pre_sales-售前, saling-售中, after_sale-售后, other-其他',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_tpl_id` (`tpl_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_tpl_type` (`tpl_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话知识库模板详情表';

-- 4. 插入测试团队数据（如果不存在）
INSERT IGNORE INTO `train_team` (`id`, `name`, `creator`, `updater`) 
VALUES (1, '测试团队', 'test', 'test');

-- 5. 插入测试提示词数据（如果不存在）
INSERT IGNORE INTO `train_bigmodel_prompts` (`id`, `keyword`, `sys_prompt`, `usr_prompt`, `team_id`, `creator`, `updater`)
VALUES (1, 'gene_1st_chat_tpl', '你是一个聊天记录分析与生成专家，学习一下我给你提供的聊天记录的风格，输出相同风格的会话记录，要30条，注意会话的轮次不能变少，同时列出当前会话属于售前、售中、售后、其他。严格以json格式输出', '', 1, 'test', 'test');
