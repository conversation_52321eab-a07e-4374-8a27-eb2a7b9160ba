-- 知识库随机查询测试数据库脚本
-- 用于测试根据任务ID获取随机知识库明细内容功能

-- 1. 创建知识库模板主表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_kb_tpl_main` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '会话知识库模板名称',
  `tokens` bigint NOT NULL DEFAULT '0' COMMENT '本次会话累计消耗token数',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `learn_status` varchar(50) DEFAULT 'un_learn' COMMENT '学习状态',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话知识库模板主表';

-- 2. 创建知识库模板详情表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_kb_tpl_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tpl_id` bigint NOT NULL COMMENT '会话知识库模板主表ID',
  `content` longtext NOT NULL COMMENT '对话内容',
  `tpl_type` varchar(50) NOT NULL COMMENT '模板类型：pre_sales-售前, saling-售中, after_sale-售后, other-其他',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_tpl_id` (`tpl_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话知识库模板明细表';

-- 3. 创建接待任务表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_reception_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_mode` int NOT NULL DEFAULT '0' COMMENT '接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法',
  `task_type` int NOT NULL DEFAULT '0' COMMENT '接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务',
  `task_name` varchar(255) NOT NULL COMMENT '接待任务名称',
  `task_description` text COMMENT '任务描述',
  `script_id` bigint COMMENT '剧本ID，来自train_script表的主键，1对1映射',
  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
  `question_interval_type` int DEFAULT '0' COMMENT '顾客提问时间间隔：0-随机，1-固定',
  `question_interval_seconds` int DEFAULT '3' COMMENT '提问间隔秒数',
  `training_limit_enabled` tinyint(1) DEFAULT '0' COMMENT '训练次数限制：0-关，1-开',
  `task_purpose_tag` int DEFAULT '0' COMMENT '任务标签：0-训练，1-考核，2-面试，3-其他',
  `judge_type` int DEFAULT '0' COMMENT '打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打',
  `conv_kb_id` bigint DEFAULT '0' COMMENT '关联的知识库模板ID，关联train_kb_tpl_main表主键',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_conv_kb_id` (`conv_kb_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待任务表';

-- 清理测试数据
DELETE FROM train_kb_tpl_detail WHERE team_id = 1;
DELETE FROM train_kb_tpl_main WHERE team_id = 1;
DELETE FROM train_reception_task WHERE team_id = 1;

-- 插入测试数据
-- 1. 插入知识库模板主表数据
INSERT INTO train_kb_tpl_main (id, name, tokens, team_id, creator, updater) VALUES
(1, '测试知识库模板1', 0, 1, 'test_user', 'test_user'),
(2, '测试知识库模板2', 0, 1, 'test_user', 'test_user');

-- 2. 插入知识库模板明细数据
INSERT INTO train_kb_tpl_detail (tpl_id, content, tpl_type, team_id, creator, updater) VALUES
(1, '您好，欢迎来到我们的店铺！有什么可以帮助您的吗？', 'pre_sales', 1, 'test_user', 'test_user'),
(1, '这款产品的主要特点是高性能、低功耗，非常适合您的需求。', 'pre_sales', 1, 'test_user', 'test_user'),
(1, '我们提供7天无理由退换货服务，请您放心购买。', 'after_sale', 1, 'test_user', 'test_user'),
(2, '感谢您的咨询，我是您的专属客服小王。', 'pre_sales', 1, 'test_user', 'test_user'),
(2, '这个价格已经是我们的最优惠价格了，您可以考虑一下。', 'saling', 1, 'test_user', 'test_user');

-- 3. 插入接待任务数据
INSERT INTO train_reception_task (id, task_name, task_description, conv_kb_id, team_id, creator, updater) VALUES
(1, '测试任务1', '这是一个测试任务', 1, 1, 'test_user', 'test_user'),
(2, '测试任务2', '这是另一个测试任务', 2, 1, 'test_user', 'test_user'),
(3, '无知识库任务', '这个任务没有关联知识库', 0, 1, 'test_user', 'test_user');
