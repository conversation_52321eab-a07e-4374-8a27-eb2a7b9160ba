-- 快捷短语测试数据库脚本
-- 用于ShortcutPhraseServiceImplTest单元测试

-- 创建快捷短语分组表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_shortcut_phrases_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint NOT NULL DEFAULT 0 COMMENT '团队ID',
  `group_title` varchar(64) NOT NULL COMMENT '分组标题（唯一）',
  `parent_id` BIGINT DEFAULT NULL COMMENT '父分组ID（实现嵌套）',
  `is_official` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否官方预设（0-否，1-是）',
  `sort_order` INT DEFAULT 0 COMMENT '排序位置',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_group_title` (`group_title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快捷短语分组表';

-- 创建快捷短语表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_shortcut_phrases` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sp_group_id` bigint NOT NULL COMMENT '快捷短语分组表ID，来自于train_shortcut_phrases_group的主键',
  `phrase_title` varchar(255) NOT NULL COMMENT '短语标题',
  `phrase_content` text NOT NULL COMMENT '短语内容',
  `phrase_type` int NOT NULL DEFAULT '0' COMMENT '短语类型：0-文本，1-图片，2-链接',
  `usage_count` int NOT NULL DEFAULT '0' COMMENT '使用次数',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序位置',
  `tags` varchar(500) COMMENT '标签（逗号分隔）',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_sp_group_id` (`sp_group_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_phrase_type` (`phrase_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷短语表';

-- 插入测试分组数据
INSERT IGNORE INTO train_shortcut_phrases_group (id, team_id, group_title, is_official, sort_order, creator, updater) 
VALUES (1, 1, '测试分组', 0, 0, 'test_user', 'test_user');

-- 清理测试数据（可选）
-- DELETE FROM train_shortcut_phrases WHERE team_id = 1 AND creator = 'test_user';
-- DELETE FROM train_shortcut_phrases_group WHERE team_id = 1 AND creator = 'test_user';
