-- 接待聊天室相关表的测试数据
-- 注意：这些DDL语句用于测试环境，确保测试数据的一致性

-- 1. 创建接待聊天室表
CREATE TABLE IF NOT EXISTS `train_reception_chatroom` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reception_skin` int DEFAULT '0' COMMENT '接待皮肤：0-干牛，1-咩咩，2-抖音',
  `scene_mode` int DEFAULT '0' COMMENT '场景模式：0-萌新友好，1-压力考核，2-自定义',
  `quick_phrases_id` bigint COMMENT '快捷短语ID',
  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
  `timer_display` tinyint(1) DEFAULT '0' COMMENT '读秒：0-不显示，1-显示',
  `entry_freq_min` int DEFAULT '1' COMMENT '进线频率下限（分钟）',
  `entry_freq_max` int DEFAULT '5' COMMENT '进线频率上限（分钟）',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_reception_skin` (`reception_skin`),
  KEY `idx_scene_mode` (`scene_mode`),
  KEY `idx_quick_phrases_id` (`quick_phrases_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待聊天室表';

-- 2. 创建聊天室任务关联表
CREATE TABLE IF NOT EXISTS `train_chatroom_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `chatroom_id` bigint NOT NULL COMMENT '聊天室ID，关联train_reception_chatroom表',
  `task_id` bigint DEFAULT '0' COMMENT '任务ID，关联train_reception_task表的主键',
  `training_recycle_cnt` int DEFAULT '0' COMMENT '此任务循环次数',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_chatroom_id` (`chatroom_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天室任务关联表';

-- 3. 创建快捷短语表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_shortcut_phrases` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '短语名称',
  `content` text COMMENT '短语内容',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷短语表';

-- 4. 创建接待任务表（如果不存在）
CREATE TABLE IF NOT EXISTS `train_reception_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_name` varchar(255) NOT NULL COMMENT '接待任务名称',
  `task_description` text COMMENT '任务描述',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待任务表';

-- 5. 插入测试数据

-- 插入快捷短语测试数据
INSERT INTO `train_shortcut_phrases` (`id`, `name`, `content`, `team_id`, `creator`, `updater`) VALUES
(1, '标准问候语', '您好，欢迎光临！有什么可以帮助您的吗？', 1, 'test_user', 'test_user'),
(2, '商品介绍', '这款商品性价比很高，质量有保障...', 1, 'test_user', 'test_user'),
(3, '结束语', '感谢您的咨询，祝您购物愉快！', 1, 'test_user', 'test_user');

-- 插入接待任务测试数据
INSERT INTO `train_reception_task` (`id`, `task_name`, `task_description`, `team_id`, `creator`, `updater`) VALUES
(1, '商品知识训练', '针对商品特性的基础培训任务', 1, 'test_user', 'test_user'),
(2, '实战进阶任务', '模拟真实客户场景的高级训练', 1, 'test_user', 'test_user'),
(3, '综合训练任务', '综合性的客服技能训练', 1, 'test_user', 'test_user');