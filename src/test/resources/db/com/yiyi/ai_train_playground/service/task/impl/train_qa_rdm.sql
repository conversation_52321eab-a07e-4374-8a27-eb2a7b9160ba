-- 问答随机表
CREATE TABLE IF NOT EXISTS `train_qa_rdm` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uuid` varchar(64) NOT NULL COMMENT '全球唯一标识符',
  `question` text NOT NULL COMMENT '问题',
  `answer` text NOT NULL COMMENT '答案',
  `actual_question` text COMMENT '实际问题',
  `actual_answer` text COMMENT '实际答案',
  `resolve` text COMMENT '解决方案',
  `ques_no` varchar(32) COMMENT '问题编号',
  `report_dtl_id` bigint DEFAULT NULL COMMENT 'train_qa_report_dtl表的主键ID',
  `send_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_ques_no` (`ques_no`),
  KEY `idx_report_dtl_id` (`report_dtl_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答随机表';
