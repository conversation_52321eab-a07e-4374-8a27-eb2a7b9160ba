-- 模拟聊天室报告明细表测试DDL
CREATE TABLE IF NOT EXISTS `train_qa_report_dtl` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rp_main_id` bigint NOT NULL COMMENT '聊天窗口主表ID，关联train_conv_winchat_main',
  `task_id` bigint NOT NULL COMMENT '接待任务ID，关联train_reception_task',
  `session_id` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '当前窗口会话的在redis中的sessionId，全局唯一',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  `qa_main_id` bigint DEFAULT NULL COMMENT 'train_qa_import_main表的主键ID',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_rp_main_id` (`rp_main_id`),
  KEY `idx_qa_main_id` (`qa_main_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模拟聊天室报告明细表';
