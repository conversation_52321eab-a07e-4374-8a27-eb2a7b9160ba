-- 问答报告主表测试DDL
CREATE TABLE IF NOT EXISTS `train_qa_report_main` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `chatroom_id` bigint NOT NULL COMMENT '聊天室ID，关联train_reception_chatroom',
  `staff_id` bigint NOT NULL COMMENT '员工ID，关联train_staff',
  `exam_user_real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '考试者姓名',
  `exam_user_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '考试者编号',
  `exam_score` decimal(5,2) DEFAULT NULL COMMENT '考试分数',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_chatroom_id` (`chatroom_id`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问答报告主表';
