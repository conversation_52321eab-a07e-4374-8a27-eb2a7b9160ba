-- 创建评价分组表
CREATE TABLE IF NOT EXISTS train_evaluation_group (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    group_title VARCHAR(255) NOT NULL,
    parent_id BIGINT,
    is_official B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VA<PERSON><PERSON><PERSON>(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_evaluation_group_team_id ON train_evaluation_group(team_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_group_parent_id ON train_evaluation_group(parent_id);

-- 创建意图表
CREATE TABLE IF NOT EXISTS train_intent (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    parent_id BIGINT,
    team_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建剧本分组表
CREATE TABLE IF NOT EXISTS train_script_group (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    group_title VARCHAR(255) NOT NULL,
    parent_id BIGINT,
    is_official BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1,
    UNIQUE KEY uniq_team_group_title (team_id, group_title)
);

-- 创建剧本表
CREATE TABLE IF NOT EXISTS train_script (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    generation_type INT DEFAULT 0,
    group_id BIGINT,
    intent_id BIGINT,
    evaluation_id BIGINT,
    buyer_requirement TEXT,
    order_priority INT DEFAULT 0,
    order_remark VARCHAR(500),
    retry_buyer_requirement_counts INT DEFAULT 0,
    retry_flow_node_counts INT DEFAULT 0,
    simulation_tool VARCHAR(100),
    prod_type INT DEFAULT 0 COMMENT '商品类型：0-自主导入，1-JD，2-TB，3-DY，4-PDD，5-XHS，6-KS',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1,
    is_official BOOLEAN DEFAULT FALSE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_script_team_id ON train_script(team_id);
CREATE INDEX IF NOT EXISTS idx_script_group_id ON train_script(group_id);
CREATE INDEX IF NOT EXISTS idx_script_intent_id ON train_script(intent_id);

-- 创建评价方案表
CREATE TABLE IF NOT EXISTS train_evaluation_plan (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    group_id BIGINT,
    description TEXT,
    is_official BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建剧本商品关联表
CREATE TABLE IF NOT EXISTS train_script_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    external_product_id VARCHAR(255),
    external_product_name VARCHAR(255),
    external_product_link VARCHAR(500),
    external_product_image VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建流程节点表
CREATE TABLE IF NOT EXISTS train_flow_node (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    node_name VARCHAR(255) NOT NULL,
    node_buyer_requirement TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建剧本京东商品关联表
CREATE TABLE IF NOT EXISTS train_script_jd_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    tr_jd_sku_id BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 0
);

-- 创建关联图片表
CREATE TABLE IF NOT EXISTS train_related_image (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    recognized_text TEXT,
    upload_type INT DEFAULT 1,
    media_type INT DEFAULT 1,
    url VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_evaluation_plan_team_id ON train_evaluation_plan(team_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_plan_group_id ON train_evaluation_plan(group_id);
CREATE INDEX IF NOT EXISTS idx_script_products_team_id ON train_script_products(team_id);
CREATE INDEX IF NOT EXISTS idx_script_products_script_id ON train_script_products(script_id);
CREATE INDEX IF NOT EXISTS idx_flow_node_team_id ON train_flow_node(team_id);
CREATE INDEX IF NOT EXISTS idx_flow_node_script_id ON train_flow_node(script_id);
CREATE INDEX IF NOT EXISTS idx_related_image_team_id ON train_related_image(team_id);
CREATE INDEX IF NOT EXISTS idx_related_image_script_id ON train_related_image(script_id);

-- 创建JD商品表
CREATE TABLE IF NOT EXISTS train_jd_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    ware_id BIGINT NOT NULL,
    title VARCHAR(500),
    logo VARCHAR(500),
    status INT DEFAULT 0,
    online_time TIMESTAMP,
    offline_time TIMESTAMP,
    shop_id BIGINT,
    brand_id BIGINT,
    brand_name VARCHAR(255),
    jd_prod_dtl LONGTEXT,
    jd_prod_img_list LONGTEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建JD SKU表
CREATE TABLE IF NOT EXISTS train_jd_sku (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    ware_id BIGINT NOT NULL,
    sku_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_jd_products_team_id ON train_jd_products(team_id);
CREATE INDEX IF NOT EXISTS idx_jd_products_ware_id ON train_jd_products(ware_id);
CREATE INDEX IF NOT EXISTS idx_jd_sku_team_id ON train_jd_sku(team_id);
CREATE INDEX IF NOT EXISTS idx_jd_sku_ware_id ON train_jd_sku(ware_id);
CREATE INDEX IF NOT EXISTS idx_jd_sku_sku_id ON train_jd_sku(sku_id);

-- 创建接待任务表（最新版本）
CREATE TABLE IF NOT EXISTS train_reception_task (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_mode INT NOT NULL DEFAULT 0 COMMENT '接待任务模式：0-原版案例，1-AI智训，2-AI智训玩法',
    task_type INT NOT NULL DEFAULT 0 COMMENT '接待任务类型：0-商品知识训练，1-实战进阶任务，2-综合训练任务',
    task_name VARCHAR(255) NOT NULL,
    task_description TEXT,
    script_id BIGINT COMMENT '剧本ID，来自train_script表的主键，1对1映射',
    reception_duration INT DEFAULT 30,
    question_interval_type INT DEFAULT 0,
    question_interval_seconds INT DEFAULT 3,
    training_limit_enabled BOOLEAN DEFAULT FALSE,
    task_purpose_tag INT DEFAULT 0 COMMENT '任务标签：0-训练，1-考核，2-面试，3-其他',
    judge_type INT DEFAULT 0 COMMENT '打分响应：0-单条会话打分，1-会话结束打分，2-单条、结束都要打',
    srv_send_cd BIGINT COMMENT '客服发送消息倒计时',
    team_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 0
);

-- 创建接待任务表索引
CREATE INDEX IF NOT EXISTS idx_reception_task_team_id ON train_reception_task(team_id);
CREATE INDEX IF NOT EXISTS idx_reception_task_create_time ON train_reception_task(create_time);
CREATE INDEX IF NOT EXISTS idx_reception_task_task_type ON train_reception_task(task_type);
CREATE INDEX IF NOT EXISTS idx_reception_task_task_mode ON train_reception_task(task_mode);
CREATE INDEX IF NOT EXISTS idx_reception_task_script_id ON train_reception_task(script_id);
CREATE INDEX IF NOT EXISTS idx_reception_task_task_purpose_tag ON train_reception_task(task_purpose_tag);
CREATE INDEX IF NOT EXISTS idx_reception_task_judge_type ON train_reception_task(judge_type);

-- 创建快捷短语分组表
CREATE TABLE IF NOT EXISTS train_shortcut_phrases_group (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL DEFAULT 0,
    group_title VARCHAR(64) NOT NULL,
    parent_id BIGINT DEFAULT NULL,
    is_official BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(64) NOT NULL DEFAULT '0',
    updater VARCHAR(64) NOT NULL DEFAULT '0',
    version BIGINT NOT NULL DEFAULT 0,
    UNIQUE KEY uniq_group_title (group_title)
);

-- 创建快捷短语表
CREATE TABLE IF NOT EXISTS train_shortcut_phrases (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sp_group_id BIGINT NOT NULL,
    phrase_title VARCHAR(255) NOT NULL,
    phrase_content TEXT NOT NULL,
    phrase_type INT NOT NULL DEFAULT 0,
    usage_count INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INT NOT NULL DEFAULT 0,
    tags VARCHAR(500),
    team_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(64) NOT NULL DEFAULT '0',
    updater VARCHAR(64) NOT NULL DEFAULT '0',
    version BIGINT NOT NULL DEFAULT 0
);

-- 创建快捷短语表索引
CREATE INDEX IF NOT EXISTS idx_shortcut_phrases_sp_group_id ON train_shortcut_phrases(sp_group_id);
CREATE INDEX IF NOT EXISTS idx_shortcut_phrases_team_id ON train_shortcut_phrases(team_id);
CREATE INDEX IF NOT EXISTS idx_shortcut_phrases_create_time ON train_shortcut_phrases(create_time);
CREATE INDEX IF NOT EXISTS idx_shortcut_phrases_phrase_type ON train_shortcut_phrases(phrase_type);
CREATE INDEX IF NOT EXISTS idx_shortcut_phrases_is_active ON train_shortcut_phrases(is_active);
CREATE INDEX IF NOT EXISTS idx_shortcut_phrases_sort_order ON train_shortcut_phrases(sort_order);

-- 创建接待聊天室表
CREATE TABLE IF NOT EXISTS `train_reception_chatroom` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reception_skin` int DEFAULT '0' COMMENT '接待皮肤：0-干牛，1-咩咩，2-抖音',
  `scene_mode` int DEFAULT '0' COMMENT '场景模式：0-萌新友好，1-压力考核，2-自定义',
  `quick_phrases_id` bigint COMMENT '快捷短语ID',
  `reception_duration` int DEFAULT '30' COMMENT '接待时长（分钟）',
  `timer_display` tinyint(1) DEFAULT '0' COMMENT '读秒：0-不显示，1-显示',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_reception_skin` (`reception_skin`),
  KEY `idx_scene_mode` (`scene_mode`),
  KEY `idx_quick_phrases_id` (`quick_phrases_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待聊天室表';

-- 创建聊天室任务关联表
CREATE TABLE IF NOT EXISTS `train_chatroom_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `chatroom_id` bigint NOT NULL COMMENT '聊天室ID，关联train_reception_chatroom表',
  `task_id` bigint DEFAULT '0' COMMENT '任务ID，关联train_reception_task表的主键',
  `training_recycle_cnt` int DEFAULT '0' COMMENT '此任务循环次数',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_chatroom_id` (`chatroom_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天室任务关联表';

-- 插入测试分组数据
INSERT IGNORE INTO train_shortcut_phrases_group (id, team_id, group_title, is_official, sort_order, creator, updater)
VALUES (1, 1, '测试分组', FALSE, 0, 'test_user', 'test_user');
