# 配置文件更新验证

## 已更新的配置文件

### 1. src/main/resources/application.yml
```yaml
# AI Guide项目配置
ai-guide:
  base-url: ${AI_GUIDE_BASE_URL:http://localhost:7081}
  callback-page-url: ${AI_GUIDE_CALLBACK_PAGE_URL:http://training.yiyiai.com:5173/pg-callback}
```

### 2. src/main/resources/application-company.yml
```yaml
# AI Guide项目配置
ai-guide:
  base-url: ${AI_GUIDE_BASE_URL:http://localhost:7081}
  callback-page-url: ${AI_GUIDE_CALLBACK_PAGE_URL:http://training.yiyiai.com:5173/pg-callback}
```

### 3. src/main/resources/application-home.yml
```yaml
# AI Guide项目配置
ai-guide:
  base-url: ${AI_GUIDE_BASE_URL:http://localhost:7081}
  callback-page-url: ${AI_GUIDE_CALLBACK_PAGE_URL:http://training.yiyiai.com:5173/pg-callback}
```

### 4. src/test/resources/application-test.yml
```yaml
# AI Guide项目配置
ai-guide:
  base-url: ${AI_GUIDE_BASE_URL:http://localhost:7081}
  callback-page-url: ${AI_GUIDE_CALLBACK_PAGE_URL:http://training.yiyiai.com:5173/pg-callback}
```

## 更新内容

所有配置文件中的 `callback-page-url` 已从：
```
http://training.yiyiai.com:5171/PGcallbackLandPage
```

更新为：
```
http://training.yiyiai.com:5173/pg-callback
```

## 变更说明

1. **端口变更**: 从 `5171` 改为 `5173`
2. **路径变更**: 从 `/PGcallbackLandPage` 改为 `/pg-callback`
3. **环境变量支持**: 仍然支持通过 `AI_GUIDE_CALLBACK_PAGE_URL` 环境变量覆盖

## 影响范围

这个配置更新会影响：
- PGGuideCallbackController 中的跳转URL生成
- 所有环境（开发、测试、生产）的回调页面地址
- Vue页面的访问路径

## 验证方法

可以通过以下方式验证配置是否生效：
1. 启动应用后访问健康检查接口：`GET /pg-guide/health`
2. 检查返回的 `callbackPageUrl` 字段是否为新地址
3. 进行用户认证测试，验证跳转URL是否正确

## 注意事项

确保Vue前端项目也相应地更新了路由配置，以匹配新的路径 `/pg-callback`。
